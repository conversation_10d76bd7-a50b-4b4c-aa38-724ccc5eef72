# UED Server Test Results

## **SUMMARY TABLE**

| LAYER       | PASS | FAIL |
| ----------- | ---- | ---- |
| API         | 70   | 48   |
| Database    | 31   | 7    |
| Service     | 76   | 39   |
| Integration | 44   | 59   |
| ALL         | 785  | 212  |

## **ERROR SUMMARY**

======================================== short test summary info ========================================
FAILED tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_create_component_duplicate_error - assert 201 == 409
FAILED tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_update_component_success - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentCRUDEndpoints::test_delete_component_with_dependencies - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentSearchEndpoints::test_list_components_success - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentSearchEndpoints::test_list_components_with_filters - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentSearchEndpoints::test_advanced_search_components - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentCategoryEndpoints::test_list_component_categories - pydantic_core._pydantic_core.ValidationError: 9 validation errors for ComponentCategoryListResponseS...
FAILED tests/api/v1/test_component_routes.py::TestComponentCategoryEndpoints::test_list_component_types - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentCategoryEndpoints::test_list_component_types_filtered_by_category - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentCategoryEndpoints::test_get_components_by_category - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentCategoryEndpoints::test_get_components_by_type - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentCategoryEndpoints::test_get_preferred_components - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentStatsEndpoint::test_get_component_stats - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentAuthenticationAndAuthorization::test_create_component_without_auth - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentAuthenticationAndAuthorization::test_get_component_without_auth - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentAuthenticationAndAuthorization::test_list_components_without_auth - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentErrorHandling::test_invalid_component_id_format - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentErrorHandling::test_invalid_pagination_parameters - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentErrorHandling::test_invalid_enum_values - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentPerformance::test_large_component_list_pagination - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestComponentPerformance::test_complex_search_query_performance - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestAdvancedSearchEndpoints::test_advanced_search_endpoint_success - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestAdvancedSearchEndpoints::test_advanced_search_endpoint_validation_error - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestAdvancedSearchEndpoints::test_relevance_search_endpoint_success - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestAdvancedSearchEndpoints::test_relevance_search_endpoint_invalid_fields - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestEnhancedBulkOperationsEndpoints::test_enhanced_bulk_create_endpoint_success - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestEnhancedBulkOperationsEndpoints::test_selective_bulk_update_endpoint_success - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestEnhancedBulkOperationsEndpoints::test_bulk_delete_endpoint_success - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestPerformanceOptimizationEndpoints::test_get_performance_metrics_endpoint - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestPerformanceOptimizationEndpoints::test_optimize_system_performance_endpoint - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_routes.py::TestPerformanceOptimizationEndpoints::test_invalidate_cache_endpoint - NameError: name 'test_project' is not defined
FAILED tests/api/v1/test_component_type_routes.py::TestComponentTypeAPI::test_get_type_endpoint - AttributeError: 'coroutine' object has no attribute 'json'
FAILED tests/api/v1/test_component_type_routes.py::TestComponentTypeAPI::test_list_types_endpoint - AttributeError: 'coroutine' object has no attribute 'status_code'
FAILED tests/api/v1/test_component_type_routes.py::TestComponentTypeAPI::test_get_types_by_category_endpoint - AttributeError: 'coroutine' object has no attribute 'status_code'
FAILED tests/api/v1/test_component_type_routes.py::TestComponentTypeAPI::test_update_specifications_template_endpoint - AttributeError: 'coroutine' object has no attribute 'json'
FAILED tests/api/v1/test_project_routes.py::TestProjectRoutes::test_create_project_success - fastapi.exceptions.HTTPException: 500: API operation 'create_project' failed: Database error: Databa...
FAILED tests/api/v1/test_project_routes.py::TestProjectRoutes::test_get_project_success - fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected inte...
FAILED tests/api/v1/test_project_routes.py::TestProjectRoutes::test_get_project_not_found - fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected inte...
FAILED tests/api/v1/test_project_routes.py::TestProjectRoutes::test_update_project_success - fastapi.exceptions.HTTPException: 500: Database error: Database operation failed: An unexpected inte...
FAILED tests/api/v1/test_project_routes.py::TestProjectRoutes::test_update_project_not_found - fastapi.exceptions.HTTPException: 404: Project with ID '999' not found.
FAILED tests/api/v1/test_project_routes.py::TestProjectRoutes::test_delete_project_success - fastapi.exceptions.HTTPException: 500: Database error: Database infrastructure operation failed: del...
FAILED tests/api/v1/test_project_routes.py::TestProjectRoutes::test_delete_project_not_found - fastapi.exceptions.HTTPException: 404: Project with ID '999' not found.
FAILED tests/api/v1/test_project_routes.py::TestProjectRoutes::test_create_project_with_empty_name_returns_422 - fastapi.exceptions.HTTPException: 500: An unexpected internal error occurred.
FAILED tests/api/v1/test_project_routes.py::TestProjectRoutes::test_create_and_update_project_with_offline_status - fastapi.exceptions.HTTPException: 500: API operation 'create_project' failed: Database error: Databa...
FAILED tests/api/v1/test_user_routes.py::TestUserRoutes::test_update_current_user_profile - assert 500 == 200
FAILED tests/api/v1/test_user_routes.py::TestUserRoutes::test_update_current_user_profile_forbidden_fields - assert 500 == 200
FAILED tests/api/v1/test_user_routes.py::TestUserRoutes::test_deactivate_user_account_admin - assert 500 == 200
FAILED tests/api/v1/test_user_routes.py::TestUserRoutes::test_user_crud_operations_admin - fastapi.exceptions.HTTPException: 500: An unexpected internal error occurred.
FAILED tests/core/database/test_connection_manager_integration.py::TestDynamicConnectionManagerIntegration::test_connection_manager_lifecycle - src.core.errors.exceptions.DatabaseError: Database error: Failed to connect to async database: passw...
FAILED tests/core/database/test_connection_manager_integration.py::TestDynamicConnectionManagerIntegration::test_multiple_local_database_engines - AssertionError: assert 0 == 2
FAILED tests/core/database/test_connection_manager_integration.py::TestDynamicConnectionManagerIntegration::test_engine_creation_error_handling - assert 'Database connection failed' in "Can't load plugin: sqlalchemy.dialects:invalid"
FAILED tests/core/database/test_connection_manager_integration.py::TestDynamicConnectionManagerIntegration::test_concurrent_local_database_access - assert <coroutine object DynamicConnectionManager._get_local_session_factory at 0x7f414e03a200> is <...
FAILED tests/core/database/test_migration_rollback_scenarios.py::TestMigrationRollbackScenarios::test_single_migration_rollback_with_data_preservation - NameError: name 'engine' is not defined
FAILED tests/core/database/test_migration_rollback_scenarios.py::TestMigrationRollbackScenarios::test_data_modification_migration_rollback - NameError: name 'engine' is not defined
FAILED tests/core/database/test_migration_rollback_scenarios.py::TestMigrationRollbackScenarios::test_rollback_performance_and_safety_validation - NameError: name 'engine' is not defined
FAILED tests/core/models/test_synchronization_log_model.py::TestSynchronizationLog::test_synchronization_log_initialization_minimal - assert None == <SyncStatus.PENDING: 'Pending'>
FAILED tests/core/models/test_synchronization_log_model.py::TestSynchronizationConflict::test_synchronization_conflict_initialization_minimal - AssertionError: assert None is False
FAILED tests/core/repositories/test_component_category_repository.py::TestComponentCategoryRepository::test_get_by_id - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
FAILED tests/core/repositories/test_component_category_repository.py::TestComponentCategoryRepository::test_update_category - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
FAILED tests/core/repositories/test_component_category_repository.py::TestComponentCategoryRepository::test_delete_category - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
FAILED tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_components_by_specifications - AttributeError: 'ComponentRepository' object has no attribute 'get_components_by_specifications'
FAILED tests/core/repositories/test_component_repository.py::TestComponentRepository::test_update_component_status - AttributeError: 'ComponentRepository' object has no attribute 'update_component_status'
FAILED tests/core/repositories/test_component_repository.py::TestComponentRepository::test_update_preferred_status - AttributeError: 'ComponentRepository' object has no attribute 'update_preferred_status'
FAILED tests/core/repositories/test_component_repository.py::TestComponentRepository::test_soft_delete_component - AttributeError: 'ComponentRepository' object has no attribute 'soft_delete_component'
FAILED tests/core/repositories/test_component_repository.py::TestComponentRepository::test_restore_component - AttributeError: 'ComponentRepository' object has no attribute 'restore_component'
FAILED tests/core/repositories/test_component_repository.py::TestComponentRepository::test_repository_error_handling - Failed: DID NOT RAISE <class 'Exception'>
FAILED tests/core/repositories/test_component_type_repository.py::TestComponentTypeRepository::test_create_component_type - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
FAILED tests/core/repositories/test_component_type_repository.py::TestComponentTypeRepository::test_validate_category_exists - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
FAILED tests/core/services/test_component_category_service.py::TestComponentCategoryService::test_get_category_not_found - Failed: DID NOT RAISE <class 'src.core.errors.exceptions.NotFoundError'>
FAILED tests/core/services/test_component_category_service.py::TestComponentCategoryService::test_update_category_success - AttributeError: 'coroutine' object has no attribute 'name'
FAILED tests/core/services/test_component_category_service.py::TestComponentCategoryService::test_delete_category_success - assert <coroutine object ComponentCategoryService.delete_category at 0x7f416958e640> is True
FAILED tests/core/services/test_component_category_service.py::TestComponentCategoryService::test_delete_category_with_dependencies - Failed: DID NOT RAISE <class 'src.core.errors.exceptions.BusinessLogicError'>
FAILED tests/core/services/test_component_category_service.py::TestComponentCategoryService::test_list_categories - AttributeError: 'coroutine' object has no attribute 'total_count'
FAILED tests/core/services/test_component_service.py::TestComponentService::test_create_component_success - TypeError: object Mock can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_create_component_database_error - TypeError: object Mock can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_update_component_success - TypeError: object Mock can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_delete_component_success - TypeError: object Mock can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_bulk_create_components_success - AttributeError: 'ComponentService' object has no attribute 'db_session'
FAILED tests/core/services/test_component_service.py::TestComponentService::test_bulk_create_components_with_invalid_skip - AttributeError: 'ComponentService' object has no attribute 'db_session'
FAILED tests/core/services/test_component_service.py::TestComponentService::test_bulk_create_components_validation_failure - AttributeError: 'ComponentService' object has no attribute 'db_session'
FAILED tests/core/services/test_component_service.py::TestComponentService::test_bulk_update_components_success - AttributeError: 'ComponentService' object has no attribute 'db_session'
FAILED tests/core/services/test_component_service.py::TestComponentService::test_bulk_update_components_with_missing_skip - AttributeError: 'ComponentService' object has no attribute 'db_session'
FAILED tests/core/services/test_component_service.py::TestComponentService::test_validate_component_specifications_circuit_breaker - TypeError: object dict can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_validate_component_specifications_missing_rating - TypeError: object dict can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_validate_component_specifications_invalid_json - TypeError: object dict can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_validate_component_specifications_standards_format - TypeError: object dict can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_build_search_filters - TypeError: object dict can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_process_component_specifications_dict - TypeError: object dict can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_process_component_dimensions - TypeError: object dict can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_sanitize_component_data - TypeError: object dict can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_create_component_with_specification_schema - TypeError: object Mock can't be used in 'await' expression
FAILED tests/core/services/test_component_service.py::TestComponentService::test_update_component_with_empty_data - TypeError: object Mock can't be used in 'await' expression
FAILED tests/core/services/test_project_service_database_url.py::TestProjectServiceDatabaseURL::test_validate_database_url_invalid_scheme - TypeError: DataValidationError.**init**() got an unexpected keyword argument 'message'
FAILED tests/core/services/test_project_service_database_url.py::TestProjectServiceDatabaseURL::test_validate_database_url_connection_failure - Failed: DID NOT RAISE <class 'src.core.errors.exceptions.DataValidationError'>
FAILED tests/core/services/test_project_service_database_url.py::TestProjectServiceDatabaseURL::test_handle_database_url_in_creation_with_url - TypeError: object MagicMock can't be used in 'await' expression
FAILED tests/core/services/test_project_service_database_url.py::TestProjectServiceDatabaseURL::test_handle_database_url_in_update_changing_url - TypeError: object MagicMock can't be used in 'await' expression
FAILED tests/core/services/test_project_service_database_url.py::TestProjectServiceDatabaseURL::test_create_project_with_database_url - TypeError: object MagicMock can't be used in 'await' expression
FAILED tests/core/services/test_project_service_database_url.py::TestProjectServiceDatabaseURL::test_update_project_with_database_url - TypeError: object MagicMock can't be used in 'await' expression
FAILED tests/core/services/test_project_service_database_url.py::TestProjectServiceDatabaseURL::test_create_project_database_url_validation_failure - pydantic_core._pydantic_core.ValidationError: 1 validation error for ProjectCreateSchema
FAILED tests/core/services/test_project_service_database_url.py::TestProjectServiceDatabaseURL::test_update_project_database_url_validation_failure - pydantic_core._pydantic_core.ValidationError: 1 validation error for ProjectUpdateSchema
FAILED tests/core/services/test_synchronization_service_conflict_resolution.py::TestSynchronizationServiceConflictResolution::test_compare_timestamps_string_format - TypeError: can't compare offset-naive and offset-aware datetimes
FAILED tests/core/services/test_synchronization_service_main_orchestration.py::TestSynchronizationServiceMainOrchestration::test_synchronize_project_bidirectional_with_changes - src.core.errors.exceptions.SynchronizationError: Failed to synchronize project 123: object datetime....
FAILED tests/core/services/test_synchronization_service_utilities.py::TestSynchronizationServiceUtilities::test_get_last_sync_timestamp_placeholder - assert <coroutine object AsyncMockMixin._execute_mock_call at 0x7f414c7ac640> is None
FAILED tests/core/services/test_synchronization_service_utilities.py::TestSynchronizationServiceUtilities::test_compare_entity_data_invalid_input_types - src.core.errors.exceptions.ServiceError: Service operation failed: Input validation failed.
FAILED tests/core/services/test_user_service.py::TestUserService::test_authenticate_user_success - TypeError: object MagicMock can't be used in 'await' expression
FAILED tests/core/services/test_user_service.py::TestUserService::test_authenticate_user_needs_rehash - TypeError: object MagicMock can't be used in 'await' expression
FAILED tests/core/services/test_user_service.py::TestUserService::test_change_password_success - TypeError: object MagicMock can't be used in 'await' expression
FAILED tests/integration/test_component_management_workflow.py::TestComponentManagementWorkflow::test_complete_workflow_with_relational_fields - AttributeError: 'coroutine' object has no attribute 'status_code'
FAILED tests/integration/test_component_management_workflow.py::TestComponentManagementWorkflow::test_component_type_category_relationship_validation - assert 'Category does not exist or is inactive' in "'coroutine' object has no attribute 'status_code'"
FAILED tests/integration/test_component_management_workflow.py::TestComponentManagementWorkflow::test_component_creation_validation_errors - AttributeError: 'coroutine' object has no attribute 'status_code'
FAILED tests/integration/test_component_management_workflow.py::TestComponentManagementWorkflow::test_component_search_with_relational_fields - AttributeError: 'coroutine' object has no attribute 'status_code'
FAILED tests/integration/test_component_management_workflow.py::TestComponentManagementWorkflow::test_component_update_with_relational_fields - AttributeError: 'coroutine' object has no attribute 'status_code'
FAILED tests/integration/test_comprehensive_data_integrity.py::TestComplexBusinessRules::test_project_member_duplicate_prevention - TypeError: ProjectMemberService.**init**() missing 2 required positional arguments: 'user_repo' and ...
FAILED tests/integration/test_comprehensive_data_integrity.py::TestComplexBusinessRules::test_user_email_case_sensitivity - AttributeError: 'coroutine' object has no attribute 'email'
FAILED tests/integration/test_comprehensive_data_integrity.py::TestBoundaryConditions::test_empty_string_validation - AttributeError: 'coroutine' object has no attribute 'name'
FAILED tests/integration/test_comprehensive_data_integrity.py::TestBoundaryConditions::test_maximum_length_constraints - AttributeError: 'coroutine' object has no attribute 'name'
FAILED tests/integration/test_comprehensive_data_integrity.py::TestBoundaryConditions::test_special_characters_in_fields - AttributeError: 'coroutine' object has no attribute 'name'
FAILED tests/integration/test_comprehensive_data_integrity.py::TestMultiEntityInteractions::test_cascade_delete_complex_hierarchy - AssertionError: assert False is True
FAILED tests/integration/test_comprehensive_data_integrity.py::TestMultiEntityInteractions::test_concurrent_user_creation_simulation - Failed: DID NOT RAISE any of (<class 'sqlalchemy.exc.IntegrityError'>, <class 'src.core.errors.excep...
FAILED tests/integration/test_data_integrity.py::test_create_user_violates_unique_email - Failed: DID NOT RAISE any of (<class 'sqlalchemy.exc.IntegrityError'>, <class 'src.core.errors.excep...
FAILED tests/integration/test_data_integrity.py::test_create_user_violates_not_null_name - Failed: DID NOT RAISE any of (<class 'sqlalchemy.exc.IntegrityError'>, <class 'src.core.errors.excep...
FAILED tests/integration/test_data_integrity.py::test_create_project_member_violates_foreign_key - TypeError: ProjectMemberService.**init**() missing 2 required positional arguments: 'user_repo' and ...
FAILED tests/integration/test_data_integrity.py::test_delete_user_soft_deletes_user_and_handles_project_membership - sqlalchemy.exc.IntegrityError: (psycopg2.errors.ForeignKeyViolation) insert or update on table "Proj...
FAILED tests/integration/test_synchronization_service_cdc.py::TestSynchronizationServiceCDC::test_entity_to_dict_basic_types - TypeError: attribute name must be string, not 'MagicMock'
FAILED tests/integration/test_synchronization_service_cdc.py::TestSynchronizationServiceCDC::test_entity_to_dict_datetime_types - TypeError: attribute name must be string, not 'MagicMock'
FAILED tests/integration/test_synchronization_service_cdc.py::TestSynchronizationServiceCDC::test_entity_to_dict_enum_types - TypeError: attribute name must be string, not 'MagicMock'
FAILED tests/integration/test_synchronization_service_cdc.py::TestSynchronizationServiceCDC::test_get_entity_changes_by_timestamp_component_with_project_id - AssertionError: Expected 'execute' to have been called once. Called 0 times.
FAILED tests/integration/test_synchronization_service_conflict_integration.py::TestSynchronizationServiceConflictIntegration::test_synchronize_project_with_conflicts_last_write_wins - src.core.errors.exceptions.SynchronizationError: Failed to synchronize project 123: object datetime....
FAILED tests/integration/test_synchronization_service_conflict_integration.py::TestSynchronizationServiceConflictIntegration::test_synchronize_project_with_mixed_conflicts_and_non_conflicts - src.core.errors.exceptions.SynchronizationError: Failed to synchronize project 456: object datetime....
FAILED tests/integration/test_synchronization_service_conflict_integration.py::TestSynchronizationServiceConflictIntegration::test_synchronize_project_no_conflicts - src.core.errors.exceptions.SynchronizationError: Failed to synchronize project 789: object datetime....
FAILED tests/integration/test_synchronization_service_conflict_integration.py::TestSynchronizationServiceConflictIntegration::test_synchronize_project_double_delete_conflict - src.core.errors.exceptions.SynchronizationError: Failed to synchronize project 321: object datetime....
FAILED tests/integration/test_synchronization_service_conflict_integration.py::TestSynchronizationServiceConflictIntegration::test_synchronize_project_delete_vs_modify_conflict - src.core.errors.exceptions.SynchronizationError: Failed to synchronize project 654: object datetime....
FAILED tests/integration/test_synchronization_service_conflict_integration.py::TestSynchronizationServiceConflictIntegration::test_synchronize_project_conflict_resolution_error_handling - src.core.errors.exceptions.SynchronizationError: Failed to synchronize project 987: object datetime....
FAILED tests/integration/test_synchronization_service_conflict_integration.py::TestSynchronizationServiceConflictIntegration::test_synchronize_project_response_format_with_conflicts - src.core.errors.exceptions.SynchronizationError: Failed to synchronize project 555: object datetime....
FAILED tests/integration/test_synchronization_service_log_integration.py::TestSynchronizationServiceLogIntegration::test_get_last_sync_timestamp_with_existing_sync - assert None == datetime.datetime(2024, 1, 1, 12, 0)
FAILED tests/integration/test_synchronization_service_log_integration.py::TestSynchronizationServiceLogIntegration::test_get_last_sync_timestamp_no_previous_sync - AssertionError: Expected 'execute' to have been called once. Called 0 times.
FAILED tests/integration/test_synchronization_service_log_integration.py::TestSynchronizationServiceLogIntegration::test_get_last_sync_timestamp_database_error - AssertionError: Expected 'execute' to have been called once. Called 0 times.
FAILED tests/integration/test_synchronization_service_log_integration.py::TestSynchronizationServiceLogIntegration::test_get_last_sync_timestamp_filters_completed_status - AssertionError: Expected 'execute' to have been called once. Called 0 times.
FAILED tests/integration/test_synchronization_service_log_integration.py::TestSynchronizationServiceLogIntegration::test_get_last_sync_timestamp_orders_by_completion_desc - AssertionError: Expected 'execute' to have been called once. Called 0 times.
FAILED tests/integration/test_synchronization_service_log_integration.py::TestSynchronizationServiceLogIntegration::test_get_last_sync_timestamp_parameter_types - AssertionError: Expected 'execute' to have been called once. Called 0 times.
FAILED tests/integration/test_synchronization_service_log_integration.py::TestSynchronizationServiceLogIntegration::test_get_last_sync_timestamp_logging - AssertionError: assert 1 >= 2
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_create_sync_log_entry_success - AttributeError: <module 'src.core.services.general.synchronization_service' from '/home/<USER>/dev/ue...
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_complete_sync_log_entry_success - AssertionError: assert <MagicMock name='mock.execute().scalar_one_or_none().records_processed' id='1...
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_complete_sync_log_entry_with_error - AssertionError: assert <MagicMock name='mock.execute().scalar_one_or_none().error_message' id='13991...
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_complete_sync_log_entry_not_found - AssertionError: Expected 'execute' to have been called once. Called 0 times.
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_synchronize_project_with_sync_log_integration - TypeError: object int can't be used in 'await' expression
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_synchronize_project_with_sync_log_on_error - TypeError: object int can't be used in 'await' expression
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_transaction_management_central_database_error - TypeError: object int can't be used in 'await' expression
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_transaction_management_partial_errors - TypeError: object int can't be used in 'await' expression
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_sync_log_completion_error_handling - TypeError: object int can't be used in 'await' expression
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_bidirectional_transaction_isolation - TypeError: object int can't be used in 'await' expression
FAILED tests/integration/test_synchronization_service_transaction_management.py::TestSynchronizationServiceTransactionManagement::test_enhanced_error_context_in_exceptions - TypeError: object int can't be used in 'await' expression
FAILED tests/integration/test_validation_integration.py::TestForeignKeyConstraintsEnforcement::test_foreign_key_pragma_enabled - sqlalchemy.exc.ProgrammingError: (psycopg2.errors.SyntaxError) syntax error at or near "PRAGMA"
FAILED tests/integration/test_validation_integration.py::TestEmailNormalizationValidation::test_case_insensitive_email_uniqueness_service_layer - AttributeError: 'coroutine' object has no attribute 'email'
FAILED tests/integration/test_validation_integration.py::TestEmailNormalizationValidation::test_case_insensitive_email_lookup_repository_layer - AttributeError: 'coroutine' object has no attribute 'id'
FAILED tests/integration/test_validation_integration.py::TestEmailNormalizationValidation::test_email_existence_check_case_insensitive - AssertionError: Email existence check should return True for: <<EMAIL>>
FAILED tests/integration/test_validation_integration.py::TestEmailNormalizationValidation::test_email_update_case_insensitive_validation - AttributeError: 'coroutine' object has no attribute 'email'
FAILED tests/integration/test_validation_integration.py::TestStringLengthValidation::test_user_name_whitespace_validation - assert 'empty or just whitespace' in "1 validation error for UserCreateSchema\nname\n  String should...
FAILED tests/integration/test_validation_integration.py::TestEnhancedErrorMessages::test_user_duplicate_email_error_message_specificity - TypeError: UserService.**init**() missing 1 required positional argument: 'preference_repository'
FAILED tests/integration/test_validation_integration.py::TestEnhancedErrorMessages::test_project_duplicate_name_error_message_specificity - Failed: DID NOT RAISE any of (<class 'src.core.errors.exceptions.DataValidationError'>, <class 'src....
FAILED tests/integration/test_validation_integration.py::TestEnhancedErrorMessages::test_validation_error_message_structure - AssertionError: Missing keyword 'empty' in error: 1 validation error for UserCreateSchema
FAILED tests/integration/test_validation_integration.py::TestEnhancedErrorMessages::test_update_validation_error_messages - AttributeError: 'coroutine' object has no attribute 'email'
FAILED tests/integration/test_validation_integration.py::TestEnhancedErrorMessages::test_field_specific_error_context - AssertionError: Expected 'empty' in error: 1 validation error for ProjectUpdateSchema
FAILED tests/integration/test_validation_integration.py::TestRegressionPrevention::test_email_normalization_persistence - AttributeError: 'coroutine' object has no attribute 'email'
FAILED tests/integration/test_validation_integration.py::TestRegressionPrevention::test_comprehensive_validation_integration - AttributeError: 'coroutine' object has no attribute 'email'
FAILED tests/integration/test_validation_integration.py::TestRegressionPrevention::test_error_message_consistency_across_layers - Failed: DID NOT RAISE <class 'src.core.errors.exceptions.InvalidInputError'>
FAILED tests/performance/test_component_performance.py::TestComponentPerformance::test_repository_get_by_id_performance - AttributeError: 'coroutine' object has no attribute 'id'
FAILED tests/performance/test_component_performance.py::TestComponentPerformance::test_repository_search_performance - assert False
FAILED tests/performance/test_component_performance.py::TestComponentPerformance::test_repository_get_by_type_performance - TypeError: object of type 'coroutine' has no len()
FAILED tests/performance/test_component_performance.py::TestComponentPerformance::test_repository_pagination_performance - AttributeError: 'ComponentRepository' object has no attribute 'get_components_paginated_with_filters'
FAILED tests/performance/test_component_performance.py::TestComponentPerformance::test_repository_filtered_search_performance - AttributeError: 'ComponentRepository' object has no attribute 'get_components_paginated_with_filters'
FAILED tests/performance/test_component_performance.py::TestComponentPerformance::test_repository_count_operations_performance - AttributeError: 'ComponentRepository' object has no attribute 'count_components_by_category_id'
FAILED tests/performance/test_component_performance.py::TestComponentPerformance::test_repository_update_operations_performance - AttributeError: 'ComponentRepository' object has no attribute 'update_component_status'
FAILED tests/performance/test_component_performance.py::TestComponentPerformance::test_repository_bulk_operations_performance - sqlalchemy.exc.IntegrityError: (psycopg2.errors.ForeignKeyViolation) insert or update on table "Comp...
FAILED tests/performance/test_component_performance.py::TestComponentPerformance::test_repository_complex_query_performance - AttributeError: 'ComponentRepository' object has no attribute 'get_components_paginated_with_filters'
FAILED tests/performance/test_component_performance.py::TestComponentPerformance::test_memory_usage_during_large_operations - AttributeError: 'ComponentRepository' object has no attribute 'get_components_paginated_with_filters'
FAILED tests/performance/test_concurrent_validation_stress.py::TestConcurrentValidationStress::test_concurrent_email_uniqueness_validation_stress - AssertionError: Detection accuracy should be ≥99%, got 0.0%
FAILED tests/performance/test_concurrent_validation_stress.py::TestConcurrentValidationStress::test_validation_error_handling_under_concurrent_load - pydantic_core._pydantic_core.ValidationError: 1 validation error for UserCreateSchema
FAILED tests/performance/test_database_performance.py::TestDatabasePerformance::test_query_performance_with_large_dataset - AttributeError: 'coroutine' object has no attribute 'email'
FAILED tests/performance/test_database_performance.py::TestConcurrencyStress::test_duplicate_email_race_condition - assert 0 == 1
FAILED tests/performance/test_database_performance.py::TestMemoryAndResourceUsage::test_session_cleanup_after_errors - AttributeError: 'coroutine' object has no attribute 'email'
FAILED tests/performance/test_database_performance.py::TestIndexPerformance::test_email_index_performance - AttributeError: 'coroutine' object has no attribute 'email'
FAILED tests/performance/test_email_lookup_benchmarks.py::TestEmailLookupBenchmarks::test_email_lookup_scaling_analysis[1000] - AttributeError: 'coroutine' object has no attribute 'id'
FAILED tests/performance/test_email_lookup_benchmarks.py::TestEmailLookupBenchmarks::test_email_lookup_scaling_analysis[5000] - AttributeError: 'coroutine' object has no attribute 'id'
FAILED tests/performance/test_email_lookup_benchmarks.py::TestEmailLookupBenchmarks::test_email_lookup_scaling_analysis[10000] - AttributeError: 'coroutine' object has no attribute 'id'
FAILED tests/performance/test_email_lookup_benchmarks.py::TestEmailLookupBenchmarks::test_email_lookup_scaling_analysis[25000] - AttributeError: 'coroutine' object has no attribute 'id'
FAILED tests/performance/test_email_lookup_benchmarks.py::TestEmailLookupBenchmarks::test_email_lookup_scaling_analysis[50000] - AttributeError: 'coroutine' object has no attribute 'id'
FAILED tests/performance/test_email_lookup_benchmarks.py::TestEmailLookupBenchmarks::test_index_effectiveness_benchmark - sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedColumn) column "email" does not exist
FAILED tests/performance/test_email_lookup_benchmarks.py::TestEmailLookupBenchmarks::test_query_pattern_performance_comparison - sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedColumn) column "id" does not exist
FAILED tests/performance/test_email_lookup_scale_performance.py::TestEmailLookupScalePerformance::test_email_lookup_performance_at_scale[1000] - AttributeError: 'coroutine' object has no attribute 'id'
FAILED tests/performance/test_email_lookup_scale_performance.py::TestEmailLookupScalePerformance::test_email_lookup_performance_at_scale[5000] - AttributeError: 'coroutine' object has no attribute 'id'
FAILED tests/performance/test_email_lookup_scale_performance.py::TestEmailLookupScalePerformance::test_email_lookup_performance_at_scale[10000] - AttributeError: 'coroutine' object has no attribute 'id'
FAILED tests/performance/test_email_lookup_scale_performance.py::TestEmailLookupScalePerformance::test_case_sensitive_vs_case_insensitive_performance_comparison - sqlalchemy.exc.ProgrammingError: (psycopg2.errors.UndefinedColumn) column "email" does not exist
FAILED tests/performance/test_email_lookup_scale_performance.py::TestEmailLookupScalePerformance::test_email_uniqueness_check_performance_at_scale - AssertionError: Should detect existing email: <<EMAIL>>
FAILED tests/performance/test_email_lookup_scale_performance.py::TestEmailLookupScalePerformance::test_memory_usage_during_large_scale_email_operations - AssertionError: Should detect existing email
FAILED tests/performance/test_memory_usage_concurrency.py::TestMemoryUsageConcurrency::test_garbage_collection_effectiveness_during_high_concurrency - AssertionError: GC should free at least 20% of allocated memory, got 0.0%
FAILED tests/performance/test_memory_usage_concurrency.py::TestMemoryUsageConcurrency::test_memory_pressure_impact_on_validation_performance - AssertionError: Memory leak after pressure test should be <50MB, got 123.2MB
FAILED tests/performance/test_validation_pipeline_performance.py::TestValidationPipelinePerformance::test_end_to_end_user_validation_pipeline_performance - KeyError: 'expected_success'
FAILED tests/performance/test_validation_pipeline_performance.py::TestValidationPipelinePerformance::test_individual_validation_step_latency_analysis - AttributeError: 'coroutine' object has no attribute 'email'
FAILED tests/validation/test_advanced_validators.py::TestAdvancedElectricalValidator::test_invalid_power_factor - assert False
FAILED tests/validation/test_advanced_validators.py::TestValidationIntegration::test_full_project_validation_workflow - AssertionError: assert 1 == 0
FAILED tests/validation/test_compatibility_matrix.py::TestCompatibilityMatrix::test_voltage_incompatibility - AssertionError: assert 0.65 < 0.5
FAILED tests/validation/test_compatibility_matrix.py::TestCompatibilityMatrix::test_current_capacity_warning - AssertionError: assert 0.8716666666666667 < 0.8
FAILED tests/validation/test_compatibility_matrix.py::TestCompatibilityMatrix::test_environmental_rating_compatibility - AssertionError: assert 0.8300000000000001 < 0.8
FAILED tests/validation/test_compatibility_matrix.py::TestCompatibilityMatrix::test_optimization_suggestions - assert False
FAILED tests/validation/test_data_format_validator.py::TestMultiFormatDataValidator::test_electrical_validation_consistency - assert (False or False)
FAILED tests/validation/test_json_schema_validator.py::TestJsonSchemaValidator::test_missing_required_fields - assert 0.9 < 0.5
FAILED tests/validation/test_json_schema_validator.py::TestJsonSchemaValidator::test_array_validation - AssertionError: assert True is False
FAILED tests/validation/test_legacy_migration_validator.py::TestLegacyMigrationValidator::test_xml_v1_migration - assert <MigrationStatus.FAILED: 'failed'> == <MigrationStatus.COMPLETED: 'completed'>
FAILED tests/validation/test_legacy_migration_validator.py::TestLegacyMigrationValidator::test_negative_values_in_legacy_data - assert False
FAILED tests/validation/test_legacy_migration_validator.py::TestLegacyMigrationValidator::test_custom_field_mapping - AssertionError: assert 'custom_attribute' in {'CAT': 'category', 'CURR': 'current_rating', 'CUSTOM_F...
FAILED tests/validation/test_legacy_migration_validator.py::TestLegacyMigrationValidator::test_unit_conversion_in_legacy_data - AssertionError: assert <MigrationStatus.PARTIAL_SUCCESS: 'partial_success'> == <MigrationStatus.COMP...
ERROR tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_add_project_member_duplicate_entry - TypeError: object ChunkedIteratorResult can't be used in 'await' expression
ERROR tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_remove_project_member_success - TypeError: object ChunkedIteratorResult can't be used in 'await' expression
ERROR tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_update_project_member_success - TypeError: object ChunkedIteratorResult can't be used in 'await' expression
ERROR tests/api/v1/test_project_routes.py::TestProjectMemberRoutes::test_list_project_members_success - TypeError: object ChunkedIteratorResult can't be used in 'await' expression
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_by_type_id - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_by_category_id - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_by_manufacturer - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_preferred_components - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_search_components - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_components_in_price_range - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_count_components_by_category - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_count_active_components - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_components_paginated_with_filters - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_components_paginated_with_search - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_components_with_multiple_filters - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_pagination_edge_cases - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_search_case_insensitive - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/repositories/test_component_repository.py::TestComponentRepository::test_get_by_manufacturer_partial_match - sqlalchemy.exc.MissingGreenlet: greenlet_spawn has not been called; can't call await_only() here. Wa...
ERROR tests/core/services/test_project_member_service.py::TestProjectMemberService::test_add_member_to_project_duplicate_entry - TypeError: object ChunkedIteratorResult can't be used in 'await' expression
ERROR tests/core/services/test_project_member_service.py::TestProjectMemberService::test_remove_member_from_project_success - TypeError: object ChunkedIteratorResult can't be used in 'await' expression
ERROR tests/core/services/test_project_member_service.py::TestProjectMemberService::test_update_project_member_success - TypeError: object ChunkedIteratorResult can't be used in 'await' expression
ERROR tests/core/services/test_project_member_service.py::TestProjectMemberService::test_list_project_members_success - TypeError: object ChunkedIteratorResult can't be used in 'await' expression
ERROR tests/core/services/test_project_service.py::TestProjectService::test_create_and_update_project_with_offline_status
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackIntegration::test_full_stack_successful_request
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackIntegration::test_full_stack_cached_request
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackIntegration::test_full_stack_rate_limit_exceeded
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackIntegration::test_full_stack_error_handling
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackIntegration::test_full_stack_excluded_paths
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackIntegration::test_full_stack_locale_detection
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackIntegration::test_full_stack_etag_conditional_request
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackIntegration::test_full_stack_performance_slow_request
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackPerformance::test_middleware_stack_performance_overhead
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackPerformance::test_middleware_stack_memory_usage
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackPerformance::test_middleware_stack_concurrent_requests
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackErrorScenarios::test_middleware_stack_resilience_to_individual_failures
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackErrorScenarios::test_middleware_stack_with_malformed_headers
ERROR tests/integration/test_middleware_integration.py::TestMiddlewareStackErrorScenarios::test_middleware_stack_with_large_request
