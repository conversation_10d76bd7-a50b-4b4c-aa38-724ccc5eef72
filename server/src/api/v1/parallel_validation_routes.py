"""Parallel Validation API Endpoints.

This module provides REST API endpoints for parallel validation processing
including batch processing, async operations, and performance monitoring.
"""

import time
from datetime import datetime
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, BackgroundTasks, HTTPException, Query
from pydantic import BaseModel, Field

from src.config.logging_config import logger
from src.core.validation.advanced_validators import AdvancedElectricalValidator
from src.core.validation.compatibility_matrix import CompatibilityMatrix
from src.core.validation.constraint_validator import ComplexConstraintValidator
from src.core.validation.intelligent_caching import validation_cache
from src.core.validation.parallel_processor import (
    LoadBalancingStrategy,
    ParallelValidationProcessor,
    PerformanceMetrics,
    ProcessingStrategy,
    TaskPriority,
)
from src.core.validation.standards_validator import StandardsValidator

router = APIRouter(
    prefix="/projects/{project_id}/parallel-validation", tags=["parallel-validation"]
)

# Global processor instance - disabled in testing to avoid event loop conflicts
import os

processor = (
    None if os.environ.get("TESTING") == "true" else ParallelValidationProcessor()
)


class ValidationTaskRequest(BaseModel):
    """Request model for validation task submission."""

    task_type: str = Field(..., description="Type of validation task")
    data: Dict[str, Any] = Field(..., description="Validation data")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=lambda: {})
    priority: str = Field(default="NORMAL", description="Task priority")
    processing_strategy: Optional[str] = Field(
        default=None, description="Override processing strategy"
    )
    dependencies: Optional[List[str]] = Field(
        default_factory=lambda: [], description="Task dependencies"
    )


class BatchValidationRequest(BaseModel):
    """Request model for batch validation."""

    task_type: str = Field(..., description="Type of validation task")
    data_list: List[Dict[str, Any]] = Field(..., description="List of validation data")
    parameters: Optional[Dict[str, Any]] = Field(default_factory=lambda: {})
    priority: str = Field(default="NORMAL", description="Task priority")
    batch_size: Optional[int] = Field(default=50, description="Batch processing size")


class ValidationTaskResponse(BaseModel):
    """Response model for validation task submission."""

    task_id: str
    status: str
    estimated_processing_time_ms: float
    queue_position: int
    submitted_at: datetime


class ValidationResultResponse(BaseModel):
    """Response model for validation result."""

    task_id: str
    result: Optional[Dict[str, Any]]
    processing_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    success: bool
    error: Optional[str]
    completed_at: datetime


class BatchStatusResponse(BaseModel):
    """Response model for batch status."""

    batch_id: str
    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    in_progress_tasks: int
    overall_status: str
    estimated_completion_time: Optional[datetime]


class PerformanceStatsResponse(BaseModel):
    """Response model for performance statistics."""

    total_tasks_processed: int
    average_processing_time_ms: float
    peak_memory_usage_mb: float
    peak_cpu_usage_percent: float
    current_queue_length: int
    throughput_tasks_per_second: float
    active_workers: int


# Startup and shutdown handlers
@router.on_event("startup")
async def startup_event():
    """Initialize parallel processor on startup."""
    if processor is not None:
        await processor.start()
        logger.info("Parallel validation processor initialized")
    else:
        logger.info("Parallel validation processor disabled (testing mode)")


@router.on_event("shutdown")
async def shutdown_event():
    """Cleanup parallel processor on shutdown."""
    if processor is not None:
        await processor.stop()
        logger.info("Parallel validation processor shutdown")
    else:
        logger.info("Parallel validation processor shutdown (testing mode)")


def _check_processor_available():
    """Check if processor is available, raise HTTPException if not."""
    if processor is None:
        raise HTTPException(
            status_code=503,
            detail="Parallel validation processor is disabled (testing mode)",
        )


@router.post("/submit", response_model=ValidationTaskResponse)
async def submit_validation_task(project_id: int, request: ValidationTaskRequest):
    """Submit a single validation task for parallel processing."""
    _check_processor_available()
    try:
        # Map priority string to enum
        priority_map = {
            "CRITICAL": TaskPriority.CRITICAL,
            "HIGH": TaskPriority.HIGH,
            "NORMAL": TaskPriority.NORMAL,
            "LOW": TaskPriority.LOW,
            "BACKGROUND": TaskPriority.BACKGROUND,
        }
        priority = priority_map.get(request.priority.upper(), TaskPriority.NORMAL)

        # Override processing strategy if provided
        if request.processing_strategy:
            strategy_map = {
                "ASYNC": ProcessingStrategy.ASYNC,
                "THREAD_POOL": ProcessingStrategy.THREAD_POOL,
                "PROCESS_POOL": ProcessingStrategy.PROCESS_POOL,
                "HYBRID": ProcessingStrategy.HYBRID,
            }
            processor.processing_strategy = strategy_map.get(
                request.processing_strategy.upper(), ProcessingStrategy.HYBRID
            )

        task_id = await processor.submit_validation_task(
            task_type=request.task_type,
            data=request.data,
            parameters=request.parameters or {},
            priority=priority,
        )

        # Get queue position
        queue_position = processor.task_queue.qsize()

        # Estimate processing time
        estimated_time = 100.0  # Base estimate, will be refined

        return ValidationTaskResponse(
            task_id=task_id,
            status="QUEUED",
            estimated_processing_time_ms=estimated_time,
            queue_position=queue_position,
            submitted_at=datetime.utcnow(),
        )

    except Exception as e:
        logger.error(f"Error submitting validation task: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/batch/submit", response_model=Dict[str, Any])
async def submit_batch_validation(project_id: int, request: BatchValidationRequest):
    """Submit multiple validation tasks as a batch."""
    try:
        # Map priority
        priority_map = {
            "CRITICAL": TaskPriority.CRITICAL,
            "HIGH": TaskPriority.HIGH,
            "NORMAL": TaskPriority.NORMAL,
            "LOW": TaskPriority.LOW,
            "BACKGROUND": TaskPriority.BACKGROUND,
        }
        priority = priority_map.get(request.priority.upper(), TaskPriority.NORMAL)

        # Submit batch
        task_ids = await processor.submit_batch_validation(
            task_type=request.task_type,
            data_list=request.data_list,
            parameters=request.parameters or {},
            priority=priority,
        )

        batch_id = f"batch_{int(time.time())}"

        return {
            "batch_id": batch_id,
            "task_ids": task_ids,
            "total_tasks": len(task_ids),
            "submitted_at": datetime.utcnow(),
        }

    except Exception as e:
        logger.error(f"Error submitting batch validation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/result/{task_id}", response_model=ValidationResultResponse)
async def get_validation_result(
    project_id: int,
    task_id: str,
    timeout: float = Query(default=30.0, description="Timeout in seconds"),
):
    """Get result for a specific validation task."""
    _check_processor_available()
    try:
        result = await processor.get_task_result(task_id, timeout)

        if result is None:
            raise HTTPException(status_code=404, detail="Task not found or timeout")

        return ValidationResultResponse(
            task_id=result.task_id,
            result=result.result,
            processing_time_ms=result.processing_time_ms,
            memory_usage_mb=result.memory_usage_mb,
            cpu_usage_percent=result.cpu_usage_percent,
            success=result.success,
            error=result.error,
            completed_at=result.processed_at,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting validation result: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/batch/{batch_id}/status", response_model=BatchStatusResponse)
async def get_batch_status(project_id: int, batch_id: str):
    """Get status of a batch validation."""
    try:
        # For now, return combined status for all tasks
        # In a real implementation, batch_id would map to specific task groups

        metrics = await processor.get_performance_metrics()

        return BatchStatusResponse(
            batch_id=batch_id,
            total_tasks=metrics.total_tasks,
            completed_tasks=metrics.completed_tasks,
            failed_tasks=metrics.failed_tasks,
            in_progress_tasks=metrics.total_tasks
            - metrics.completed_tasks
            - metrics.failed_tasks,
            overall_status="COMPLETED"
            if metrics.completed_tasks == metrics.total_tasks
            else "IN_PROGRESS",
            estimated_completion_time=None,
        )

    except Exception as e:
        logger.error(f"Error getting batch status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/performance", response_model=PerformanceStatsResponse)
async def get_performance_stats(project_id: int):
    """Get system performance statistics."""
    try:
        metrics = await processor.get_performance_metrics()

        return PerformanceStatsResponse(
            total_tasks_processed=metrics.total_tasks,
            average_processing_time_ms=metrics.average_processing_time_ms,
            peak_memory_usage_mb=metrics.total_memory_usage_mb,
            peak_cpu_usage_percent=metrics.peak_cpu_usage_percent,
            current_queue_length=metrics.queue_length,
            throughput_tasks_per_second=metrics.throughput_tasks_per_second,
            active_workers=processor.max_workers,
        )

    except Exception as e:
        logger.error(f"Error getting performance stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bulk/validate-electrical")
async def bulk_electrical_validation(
    project_id: int, data_list: List[Dict[str, Any]], background_tasks: BackgroundTasks
):
    """Bulk electrical parameter validation."""
    try:
        task_ids = await processor.submit_batch_validation(
            "electrical_validation", data_list, {"validation_level": "complete"}
        )

        return {
            "validation_type": "electrical",
            "task_ids": task_ids,
            "total_components": len(data_list),
            "status": "PROCESSING",
            "submitted_at": datetime.utcnow(),
        }

    except Exception as e:
        logger.error(f"Error in bulk electrical validation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bulk/validate-compatibility")
async def bulk_compatibility_validation(
    project_id: int,
    components: List[Dict[str, Any]],
    background_tasks: BackgroundTasks,
    context: Optional[Dict[str, Any]] = None,
):
    """Bulk component compatibility validation."""
    try:
        task_ids = await processor.submit_batch_validation(
            "compatibility_check", components, {"context": context or {}}
        )

        return {
            "validation_type": "compatibility",
            "task_ids": task_ids,
            "total_components": len(components),
            "status": "PROCESSING",
            "submitted_at": datetime.utcnow(),
        }

    except Exception as e:
        logger.error(f"Error in bulk compatibility validation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bulk/validate-standards")
async def bulk_standards_validation(
    project_id: int,
    components: List[Dict[str, Any]],
    background_tasks: BackgroundTasks,
    standards: List[str] = Query(default=["IEEE", "IEC", "NEC"]),
    region: str = Query(default="global"),
):
    """Bulk standards compliance validation."""
    try:
        task_ids = await processor.submit_batch_validation(
            "standards_compliance",
            components,
            {"standards": standards, "region": region},
        )

        return {
            "validation_type": "standards",
            "task_ids": task_ids,
            "total_components": len(components),
            "standards": standards,
            "region": region,
            "status": "PROCESSING",
            "submitted_at": datetime.utcnow(),
        }

    except Exception as e:
        logger.error(f"Error in bulk standards validation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/bulk/validate-constraints")
async def bulk_constraint_validation(
    project_id: int,
    components: List[Dict[str, Any]],
    background_tasks: BackgroundTasks,
    rules: List[str] = Query(default=None),
):
    """Bulk constraint validation."""
    try:
        default_rules = [
            "power_balance",
            "voltage_compatibility",
            "current_balance",
            "temperature_derating",
        ]

        task_ids = await processor.submit_batch_validation(
            "constraint_validation", components, {"rules": rules or default_rules}
        )

        return {
            "validation_type": "constraints",
            "task_ids": task_ids,
            "total_components": len(components),
            "rules": rules or default_rules,
            "status": "PROCESSING",
            "submitted_at": datetime.utcnow(),
        }

    except Exception as e:
        logger.error(f"Error in bulk constraint validation: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check(project_id: int):
    """Health check endpoint for parallel validation system."""
    try:
        if not processor.is_running:
            return {"status": "DOWN", "message": "Processor not running"}

        metrics = await processor.get_performance_metrics()

        return {
            "status": "UP",
            "processor_running": processor.is_running,
            "max_workers": processor.max_workers,
            "current_queue_length": metrics.queue_length,
            "total_tasks_processed": metrics.total_tasks,
            "timestamp": datetime.utcnow(),
        }

    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {"status": "DOWN", "error": str(e)}
