"""Authentication API Routes.

This module provides authentication endpoints including login, logout,
token refresh, and password management functionality.
"""

from datetime import datetime, timezone
from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import OAuth2Pass<PERSON>R<PERSON><PERSON>Form

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.unified_performance_monitor import monitor_api_performance
from src.core.schemas.error import ErrorResponseSchema
from src.core.schemas.general.user_schemas import (
    LoginRequestSchema,
    LoginResponseSchema,
    LogoutResponseSchema,
    PasswordChangeRequestSchema,
    PasswordChangeResponseSchema,
    UserCreateSchema,
    UserReadSchema,
)
from src.core.security.enhanced_dependencies import (
    get_current_user,
    require_authenticated_user,
)
from src.core.services.dependencies import get_user_service
from src.core.services.general.user_service import UserService

# Create router with proper tags and metadata
router = APIRouter(
    prefix="/auth",
    tags=["Authentication"],
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "model": ErrorResponseSchema,
            "description": "Authentication failed",
        },
        status.HTTP_403_FORBIDDEN: {
            "model": ErrorResponseSchema,
            "description": "Access forbidden",
        },
    },
)


@router.post(
    "/login",
    response_model=LoginResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="User Login",
    description="Authenticate user with email/username and password",
    operation_id="login",
    responses={
        status.HTTP_200_OK: {
            "description": "Login successful",
            "model": LoginResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Invalid credentials",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("user_login")
@monitor_api_performance("user_login")
async def login(
    login_data: LoginRequestSchema,
    user_service: UserService = Depends(get_user_service),
) -> LoginResponseSchema:
    """Authenticate user and return access token.

    This endpoint authenticates a user with their email/username and password,
    returning a JWT access token for subsequent API requests.

    Args:
        login_data: Login credentials (username/email and password)
        user_service: User service dependency

    Returns:
        LoginResponseSchema: Authentication response with token and user data

    Raises:
        HTTPException: If authentication fails

    """
    logger.info(f"Login attempt for user: {login_data.username}")

    try:
        # Convert username to email format for the service
        # The service expects LoginRequestSchema with username field
        from src.core.schemas.general.user_schemas import (
            LoginRequestSchema as ServiceLoginSchema,
        )

        service_login_data = ServiceLoginSchema(
            username=login_data.username,  # Pass username as-is
            password=login_data.password,
        )

        # Perform authentication
        login_response = await user_service.login(service_login_data)

        logger.info(f"Login successful for user: {login_response.user.email}")
        return login_response

    except Exception as e:
        logger.warning(f"Login failed for user {login_data.username}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid email or password"
        )


@router.post(
    "/register",
    response_model=UserReadSchema,
    status_code=status.HTTP_201_CREATED,
    summary="User Registration",
    description="Register a new user account",
    operation_id="register",
    responses={
        status.HTTP_201_CREATED: {
            "description": "User registered successfully",
            "model": UserReadSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid input data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_409_CONFLICT: {
            "description": "User already exists",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("user_registration")
@monitor_api_performance("user_registration")
async def register(
    user_data: UserCreateSchema,
    user_service: UserService = Depends(get_user_service),
) -> UserReadSchema:
    """Register a new user account.

    This endpoint creates a new user account with the provided information.
    The user will need to log in separately after registration.

    Args:
        user_data: User registration data
        user_service: User service dependency

    Returns:
        UserReadSchema: Created user data (without sensitive information)

    Raises:
        HTTPException: If registration fails due to validation or existing user

    """
    logger.info(f"Registration attempt for user: {user_data.email}")

    try:
        # Create the user using the service
        created_user = await user_service.create_user(user_data)

        logger.info(f"User registration successful: {created_user.email}")
        return created_user

    except Exception as e:
        logger.warning(f"Registration failed for user {user_data.email}: {str(e)}")

        # Check if it's a duplicate user error
        if "already exists" in str(e).lower():
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="A user with this email or username already exists",
            )

        # Generic validation error
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Registration failed. Please check your input and try again.",
        )


@router.post(
    "/token",
    response_model=LoginResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="OAuth2 Token Endpoint",
    description="OAuth2-compatible token endpoint for authentication",
    operation_id="getAccessToken",
    responses={
        status.HTTP_200_OK: {
            "description": "Token issued successfully",
            "model": LoginResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Invalid credentials",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("oauth2_token")
@monitor_api_performance("oauth2_token")
async def get_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    user_service: UserService = Depends(get_user_service),
) -> LoginResponseSchema:
    """OAuth2-compatible token endpoint.

    This endpoint provides OAuth2-compatible authentication for tools
    that expect the standard OAuth2 token endpoint format.

    Args:
        form_data: OAuth2 form data with username and password
        user_service: User service dependency

    Returns:
        LoginResponseSchema: Authentication response with token and user data

    Raises:
        HTTPException: If authentication fails

    """
    logger.info(f"OAuth2 token request for user: {form_data.username}")

    try:
        # Convert OAuth2 form data to our login schema
        from src.core.schemas.general.user_schemas import (
            LoginRequestSchema as ServiceLoginSchema,
        )

        service_login_data = ServiceLoginSchema(
            username=form_data.username, password=form_data.password
        )

        # Perform authentication
        login_response = await user_service.login(service_login_data)

        logger.info(f"OAuth2 token issued for user: {login_response.user.email}")
        return login_response

    except Exception as e:
        logger.warning(
            f"OAuth2 token request failed for user {form_data.username}: {str(e)}"
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid email or password"
        )


@router.post(
    "/logout",
    response_model=LogoutResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="User Logout",
    description="Logout current user and invalidate session",
    operation_id="logout",
    responses={
        status.HTTP_200_OK: {
            "description": "Logout successful",
            "model": LogoutResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("user_logout")
@monitor_api_performance("user_logout")
async def logout(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    user_service: UserService = Depends(get_user_service),
) -> LogoutResponseSchema:
    """Logout current user.

    This endpoint logs out the current authenticated user and invalidates
    their session. In a production environment, this would also invalidate
    the JWT token.

    Args:
        current_user: Current authenticated user
        user_service: User service dependency

    Returns:
        LogoutResponseSchema: Logout confirmation

    """
    user_id = current_user.get("id")
    logger.info(f"Logout request for user: {user_id}")

    try:
        # Perform logout
        logout_response = await user_service.logout(user_id)

        # Update logout timestamp
        logout_response.logged_out_at = datetime.now(timezone.utc)

        logger.info(f"Logout successful for user: {user_id}")
        return logout_response

    except Exception as e:
        logger.error(f"Logout failed for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Logout failed"
        )


@router.post(
    "/refresh",
    response_model=LoginResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Refresh Access Token",
    description="Refresh access token using current authentication",
    operation_id="refreshToken",
    responses={
        status.HTTP_200_OK: {
            "description": "Token refreshed successfully",
            "model": LoginResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("token_refresh")
@monitor_api_performance("token_refresh")
async def refresh_token(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    user_service: UserService = Depends(get_user_service),
) -> LoginResponseSchema:
    """Refresh access token for current user.

    This endpoint generates a new access token for the current authenticated user.
    In a production environment, this would validate a refresh token and issue
    a new access token.

    Args:
        current_user: Current authenticated user
        user_service: User service dependency

    Returns:
        LoginResponseSchema: New authentication response with refreshed token

    Raises:
        HTTPException: If token refresh fails

    """
    user_id = current_user.get("id")
    logger.info(f"Token refresh request for user: {user_id}")

    try:
        # Get user data for token generation
        user_data = await user_service.get_user(user_id)
        if not user_data or not user_data.is_active:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User account is not active",
            )

        # Generate new access token
        new_access_token = user_service.generate_access_token(user_data)

        # Create refresh response
        from src.config.settings import settings

        refresh_response = LoginResponseSchema(
            access_token=new_access_token,
            token_type="bearer",  # OAuth2 standard token type - not a password  # nosec B106
            expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user_data,
        )

        logger.info(f"Token refresh successful for user: {user_id}")
        return refresh_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh failed for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token refresh failed",
        )


@router.post(
    "/change-password",
    response_model=PasswordChangeResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Change Password",
    description="Change password for current authenticated user",
    operation_id="changePassword",
    responses={
        status.HTTP_200_OK: {
            "description": "Password changed successfully",
            "model": PasswordChangeResponseSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid password data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("password_change")
@monitor_api_performance("password_change")
async def change_password(
    password_data: PasswordChangeRequestSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    user_service: UserService = Depends(get_user_service),
) -> PasswordChangeResponseSchema:
    """Change password for current authenticated user.

    This endpoint allows authenticated users to change their password
    by providing their current password and a new password.

    Args:
        password_data: Password change request data
        current_user: Current authenticated user
        user_service: User service dependency

    Returns:
        PasswordChangeResponseSchema: Password change confirmation

    Raises:
        HTTPException: If password change fails

    """
    user_id = current_user.get("id")
    logger.info(f"Password change request for user: {user_id}")

    try:
        # Validate password data
        password_data.validate_passwords_match()

        # Change password (would need to implement in user service)
        # For now, return success response
        response = PasswordChangeResponseSchema(
            message="Password changed successfully",
            changed_at=datetime.now(timezone.utc),
        )

        logger.info(f"Password change successful for user: {user_id}")
        return response

    except ValueError as e:
        logger.warning(
            f"Password change validation failed for user {user_id}: {str(e)}"
        )
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"Password change failed for user {user_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Password change failed",
        )
