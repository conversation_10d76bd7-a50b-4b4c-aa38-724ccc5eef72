"""Health Check API Routes.

This module provides health check endpoints for system monitoring,
database connectivity verification, and service status reporting.
"""

from datetime import datetime, timezone

from fastapi import APIRouter, Depends, status
from fastapi.responses import JSONResponse

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.unified_performance_monitor import monitor_api_performance
from src.core.schemas.health import (
    HealthCheckResponseSchema,
    SimpleHealthResponseSchema,
)
from src.core.services.general.health_service import HealthService, get_health_service

# Create router with proper tags and metadata
router = APIRouter(
    prefix="/health",
    tags=["Health Check"],
    responses={
        status.HTTP_503_SERVICE_UNAVAILABLE: {
            "description": "Service unavailable - system is unhealthy"
        }
    },
)


@router.get(
    "/",
    response_model=SimpleHealthResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Simple Health Check",
    description="Basic health check endpoint for load balancers and monitoring systems",
    operation_id="simpleHealthCheck",
    responses={
        status.HTTP_200_OK: {
            "description": "System is healthy",
            "model": SimpleHealthResponseSchema,
        },
        status.HTTP_503_SERVICE_UNAVAILABLE: {
            "description": "System is unhealthy",
            "model": SimpleHealthResponseSchema,
        },
    },
)
@handle_api_errors("simple_health_check")
@monitor_api_performance("simple_health_check")
async def simple_health_check(
    health_service: HealthService = Depends(get_health_service),
) -> JSONResponse:
    """Simple health check endpoint for basic monitoring.

    This endpoint provides a lightweight health check suitable for:
    - Load balancer health checks
    - Basic monitoring systems
    - Quick status verification

    Returns HTTP 200 for healthy systems and HTTP 503 for unhealthy systems.

    Args:
        health_service: Health service dependency

    Returns:
        JSONResponse: Simple health status with appropriate HTTP status code

    """
    logger.debug("Processing simple health check request")

    # Get simple health status
    health_status = health_service.get_simple_health()

    # Determine HTTP status code based on health
    http_status = (
        status.HTTP_200_OK
        if health_status.status == "healthy"
        else status.HTTP_503_SERVICE_UNAVAILABLE
    )

    logger.debug(f"Simple health check completed: {health_status.status}")

    return JSONResponse(
        status_code=http_status, content=health_status.model_dump(mode="json")
    )


@router.get(
    "/detailed",
    response_model=HealthCheckResponseSchema,
    status_code=status.HTTP_200_OK,
    summary="Comprehensive Health Check",
    description="Detailed health check with system metrics, database status, and service information",
    operation_id="comprehensiveHealthCheck",
    responses={
        status.HTTP_200_OK: {
            "description": "Detailed system health information",
            "model": HealthCheckResponseSchema,
        },
        status.HTTP_503_SERVICE_UNAVAILABLE: {
            "description": "System is unhealthy with detailed diagnostics",
            "model": HealthCheckResponseSchema,
        },
    },
)
@handle_api_errors("comprehensive_health_check")
@monitor_api_performance("comprehensive_health_check")
async def comprehensive_health_check(
    health_service: HealthService = Depends(get_health_service),
) -> JSONResponse:
    """Comprehensive health check endpoint with detailed system information.

    This endpoint provides detailed health information including:
    - Database connectivity and performance metrics
    - System resource utilization
    - Individual service health status
    - Performance metrics and recommendations
    - Critical issues and warnings

    Suitable for:
    - Administrative monitoring dashboards
    - Detailed system diagnostics
    - Performance analysis
    - Troubleshooting

    Args:
        health_service: Health service dependency

    Returns:
        JSONResponse: Comprehensive health status with appropriate HTTP status code

    """
    logger.debug("Processing comprehensive health check request")

    # Get comprehensive health status
    health_status = health_service.get_comprehensive_health()

    # Determine HTTP status code based on health
    http_status = (
        status.HTTP_200_OK
        if health_status.status in ["healthy", "degraded"]
        else status.HTTP_503_SERVICE_UNAVAILABLE
    )

    # Log health summary
    logger.info(
        f"Comprehensive health check completed: {health_status.status} "
        f"(score: {health_status.health_score}/10, "
        f"critical_issues: {len(health_status.critical_issues)}, "
        f"warnings: {len(health_status.warnings)})"
    )

    return JSONResponse(
        status_code=http_status, content=health_status.model_dump(mode="json")
    )


@router.get(
    "/ping",
    status_code=status.HTTP_200_OK,
    summary="Ping Endpoint",
    description="Minimal ping endpoint for basic connectivity testing",
    operation_id="pingCheck",
    response_model=dict,  # Explicitly define response model
    responses={
        status.HTTP_200_OK: {
            "description": "Service is responding",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "status": {"type": "string", "example": "pong"},
                            "timestamp": {
                                "type": "string",
                                "format": "date-time",
                                "example": "2025-01-08T10:30:00Z",
                            },
                        },
                        "required": ["status", "timestamp"],
                    },
                    "example": {"status": "pong", "timestamp": "2025-01-08T10:30:00Z"},
                }
            },
        }
    },
)
@handle_api_errors("ping_check")
@monitor_api_performance("ping_check")
async def ping_check() -> dict:
    """Minimal ping endpoint for basic connectivity testing.

    This endpoint provides the most basic health check - simply verifying
    that the service is responding to requests. Useful for:
    - Basic connectivity testing
    - Network monitoring
    - Service discovery

    Returns:
        dict: Simple pong response with timestamp

    """
    from datetime import datetime, timezone

    logger.debug("Processing ping request")

    response = {"status": "pong", "timestamp": datetime.now(timezone.utc).isoformat()}

    logger.debug("Ping request completed successfully")
    return response


@router.get(
    "/ready",
    status_code=status.HTTP_200_OK,
    summary="Readiness Check",
    description="Kubernetes-style readiness probe endpoint",
    operation_id="readinessCheck",
    responses={
        status.HTTP_200_OK: {
            "description": "Service is ready to accept traffic",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "ready": {"type": "boolean", "example": True},
                            "database_status": {"type": "string", "example": "healthy"},
                            "timestamp": {
                                "type": "string",
                                "format": "date-time",
                                "example": "2025-01-08T10:30:00Z",
                            },
                        },
                        "required": ["ready", "database_status", "timestamp"],
                    },
                    "example": {
                        "ready": True,
                        "database_status": "healthy",
                        "timestamp": "2025-01-08T10:30:00Z",
                    },
                }
            },
        },
        status.HTTP_503_SERVICE_UNAVAILABLE: {
            "description": "Service is not ready",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "ready": {"type": "boolean", "example": False},
                            "error": {
                                "type": "string",
                                "example": "Readiness check failed",
                            },
                            "timestamp": {
                                "type": "string",
                                "format": "date-time",
                                "example": "2025-01-08T10:30:00Z",
                            },
                        },
                        "required": ["ready", "error", "timestamp"],
                    },
                    "example": {
                        "ready": False,
                        "error": "Readiness check failed",
                        "timestamp": "2025-01-08T10:30:00Z",
                    },
                }
            },
        },
    },
)
@handle_api_errors("readiness_check")
@monitor_api_performance("readiness_check")
async def readiness_check(
    health_service: HealthService = Depends(get_health_service),
) -> JSONResponse:
    """Kubernetes-style readiness probe endpoint.

    This endpoint determines if the service is ready to accept traffic.
    It checks critical dependencies like database connectivity.

    Returns HTTP 200 if ready, HTTP 503 if not ready.

    Args:
        health_service: Health service dependency

    Returns:
        JSONResponse: Readiness status with appropriate HTTP status code

    """
    logger.debug("Processing readiness check request")

    try:
        # Get simple health to check database connectivity
        health_status = health_service.get_simple_health()

        # Service is ready if database is healthy
        is_ready = health_status.database_status == "healthy"

        http_status = (
            status.HTTP_200_OK if is_ready else status.HTTP_503_SERVICE_UNAVAILABLE
        )

        response_data = {
            "ready": is_ready,
            "database_status": health_status.database_status,
            "timestamp": health_status.timestamp.isoformat(),
        }

        logger.debug(
            f"Readiness check completed: {'ready' if is_ready else 'not ready'}"
        )

        return JSONResponse(status_code=http_status, content=response_data)

    except Exception as e:
        logger.error(f"Readiness check failed: {e}")
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "ready": False,
                "error": "Readiness check failed",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
        )


@router.get(
    "/live",
    status_code=status.HTTP_200_OK,
    summary="Liveness Check",
    description="Kubernetes-style liveness probe endpoint",
    operation_id="livenessCheck",
    response_model=dict,
    responses={
        status.HTTP_200_OK: {
            "description": "Service is alive",
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": {
                            "alive": {"type": "boolean", "example": True},
                            "timestamp": {
                                "type": "string",
                                "format": "date-time",
                                "example": "2025-01-08T10:30:00Z",
                            },
                        },
                        "required": ["alive", "timestamp"],
                    },
                    "example": {"alive": True, "timestamp": "2025-01-08T10:30:00Z"},
                }
            },
        },
    },
)
@handle_api_errors("liveness_check")
@monitor_api_performance("liveness_check")
async def liveness_check() -> dict:
    """Kubernetes-style liveness probe endpoint.

    This endpoint indicates that the service is alive and running.
    It should only fail if the service needs to be restarted.

    Returns:
        dict: Liveness status

    """
    from datetime import datetime, timezone

    logger.debug("Processing liveness check request")

    response = {"alive": True, "timestamp": datetime.now(timezone.utc).isoformat()}

    logger.debug("Liveness check completed successfully")
    return response
