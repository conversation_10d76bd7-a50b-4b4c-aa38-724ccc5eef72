"""User Preferences API Routes.

This module provides user preferences management endpoints including
getting, updating, resetting, and import/export functionality.
"""

from typing import Any, Dict

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.responses import JSONResponse

from src.core.auth.dependencies import require_authenticated_user
from src.core.errors.unified_error_handler import handle_api_errors
from src.core.monitoring.performance_monitor import monitor_api_performance
from src.core.schemas.error import ErrorResponseSchema
from src.core.schemas.general.user_schemas import (
    UserPreferenceReadSchema,
    UserPreferenceUpdateSchema,
)
from src.core.services.dependencies import get_user_service
from src.core.services.general.user_service import UserService
from src.core.utils.logger import get_logger

logger = get_logger(__name__)

# Create the user preferences router
router = APIRouter(
    prefix="/users/me/preferences",
    tags=["User Preferences"],
    responses={
        status.HTTP_401_UNAUTHORIZED: {
            "model": ErrorResponseSchema,
            "description": "Authentication required",
        },
    },
)


@router.get(
    "",
    response_model=UserPreferenceReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Get Current User Preferences",
    description="Get preferences for the currently authenticated user",
    operation_id="getCurrentUserPreferences",
    responses={
        status.HTTP_200_OK: {
            "description": "User preferences retrieved successfully",
            "model": UserPreferenceReadSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("get_current_user_preferences")
@monitor_api_performance("get_current_user_preferences")
async def get_current_user_preferences(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    user_service: UserService = Depends(get_user_service),
) -> UserPreferenceReadSchema:
    """Get preferences for the currently authenticated user.

    This endpoint allows users to retrieve their own preferences
    without requiring administrative privileges.

    Args:
        current_user: Current authenticated user
        user_service: User service dependency

    Returns:
        UserPreferenceReadSchema: Current user's preferences data

    """
    user_id = current_user.get("id")
    logger.debug(f"Getting preferences for current user: {user_id}")

    preferences = user_service.get_user_preferences(user_id)

    logger.debug(f"Retrieved preferences for user: {user_id}")
    return preferences


@router.put(
    "",
    response_model=UserPreferenceReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Update Current User Preferences",
    description="Update preferences for the currently authenticated user",
    operation_id="updateCurrentUserPreferences",
    responses={
        status.HTTP_200_OK: {
            "description": "Preferences updated successfully",
            "model": UserPreferenceReadSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid preferences data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("update_current_user_preferences")
@monitor_api_performance("update_current_user_preferences")
async def update_current_user_preferences(
    preferences_update: UserPreferenceUpdateSchema,
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    user_service: UserService = Depends(get_user_service),
) -> UserPreferenceReadSchema:
    """Update preferences for the currently authenticated user.

    This endpoint allows users to update their own preferences.

    Args:
        preferences_update: Preferences update data
        current_user: Current authenticated user
        user_service: User service dependency

    Returns:
        UserPreferenceReadSchema: Updated preferences

    Raises:
        HTTPException: If update fails

    """
    user_id = current_user.get("id")
    logger.info(f"Updating preferences for current user: {user_id}")

    # Update user preferences
    updated_preferences = user_service.create_or_update_user_preferences(
        user_id, preferences_update
    )

    logger.info(f"Preferences updated successfully for user: {user_id}")
    return updated_preferences


@router.delete(
    "",
    status_code=status.HTTP_200_OK,
    summary="Reset Current User Preferences",
    description="Reset preferences to defaults for the currently authenticated user",
    operation_id="resetCurrentUserPreferences",
    responses={
        status.HTTP_200_OK: {
            "description": "Preferences reset successfully",
            "content": {
                "application/json": {
                    "example": {
                        "message": "Preferences reset to defaults successfully",
                        "status": "success",
                    }
                }
            },
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("reset_current_user_preferences")
@monitor_api_performance("reset_current_user_preferences")
async def reset_current_user_preferences(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    user_service: UserService = Depends(get_user_service),
) -> JSONResponse:
    """Reset preferences to defaults for the currently authenticated user.

    This endpoint allows users to reset their preferences to system defaults.

    Args:
        current_user: Current authenticated user
        user_service: User service dependency

    Returns:
        JSONResponse: Success message

    Raises:
        HTTPException: If reset fails

    """
    user_id = current_user.get("id")
    logger.info(f"Resetting preferences for current user: {user_id}")

    # Delete user preferences (will fall back to defaults)
    success = user_service.delete_user_preferences(user_id, user_id)

    if success:
        logger.info(f"Preferences reset successfully for user: {user_id}")
        return JSONResponse(
            status_code=status.HTTP_200_OK,
            content={
                "message": "Preferences reset to defaults successfully",
                "status": "success",
            },
        )
    else:
        logger.error(f"Failed to reset preferences for user: {user_id}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset preferences",
        )


@router.post(
    "/export",
    status_code=status.HTTP_200_OK,
    summary="Export Current User Preferences",
    description="Export preferences as JSON for backup or migration",
    operation_id="exportCurrentUserPreferences",
    responses={
        status.HTTP_200_OK: {
            "description": "Preferences exported successfully",
            "content": {
                "application/json": {
                    "example": {
                        "preferences": {
                            "theme": "dark",
                            "language": "en",
                            "units_system": "metric",
                        },
                        "exported_at": "2025-07-17T10:30:00Z",
                        "version": "1.0",
                    }
                }
            },
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("export_current_user_preferences")
@monitor_api_performance("export_current_user_preferences")
async def export_current_user_preferences(
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    user_service: UserService = Depends(get_user_service),
) -> JSONResponse:
    """Export preferences as JSON for backup or migration.

    Args:
        current_user: Current authenticated user
        user_service: User service dependency

    Returns:
        JSONResponse: Exported preferences data

    """
    user_id = current_user.get("id")
    logger.debug(f"Exporting preferences for current user: {user_id}")

    preferences = user_service.get_user_preferences(user_id)

    # Create export format
    export_data = {
        "preferences": preferences.model_dump(
            exclude={"id", "user_id", "created_at", "updated_at"}
        ),
        "exported_at": preferences.updated_at.isoformat()
        if preferences.updated_at
        else None,
        "version": "1.0",
    }

    logger.debug(f"Exported preferences for user: {user_id}")
    return JSONResponse(status_code=status.HTTP_200_OK, content=export_data)


@router.post(
    "/import",
    response_model=UserPreferenceReadSchema,
    status_code=status.HTTP_200_OK,
    summary="Import Current User Preferences",
    description="Import preferences from JSON backup",
    operation_id="importCurrentUserPreferences",
    responses={
        status.HTTP_200_OK: {
            "description": "Preferences imported successfully",
            "model": UserPreferenceReadSchema,
        },
        status.HTTP_400_BAD_REQUEST: {
            "description": "Invalid import data",
            "model": ErrorResponseSchema,
        },
        status.HTTP_401_UNAUTHORIZED: {
            "description": "Authentication required",
            "model": ErrorResponseSchema,
        },
    },
)
@handle_api_errors("import_current_user_preferences")
@monitor_api_performance("import_current_user_preferences")
async def import_current_user_preferences(
    import_data: Dict[str, Any],
    current_user: Dict[str, Any] = Depends(require_authenticated_user),
    user_service: UserService = Depends(get_user_service),
) -> UserPreferenceReadSchema:
    """Import preferences from JSON backup.

    Args:
        import_data: Import data containing preferences
        current_user: Current authenticated user
        user_service: User service dependency

    Returns:
        UserPreferenceReadSchema: Imported preferences

    Raises:
        HTTPException: If import fails or data is invalid

    """
    user_id = current_user.get("id")
    logger.info(f"Importing preferences for current user: {user_id}")

    # Extract preferences from import data
    preferences_data = import_data.get("preferences", {})

    if not preferences_data:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No preferences data found in import",
        )

    # Create update schema from import data
    try:
        preferences_update = UserPreferenceUpdateSchema(**preferences_data)
    except Exception as e:
        logger.error(f"Invalid preferences data in import: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid preferences data: {str(e)}",
        )

    # Update user preferences
    updated_preferences = user_service.create_or_update_user_preferences(
        user_id, preferences_update
    )

    logger.info(f"Preferences imported successfully for user: {user_id}")
    return updated_preferences
