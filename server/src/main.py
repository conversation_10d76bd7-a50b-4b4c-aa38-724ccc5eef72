"""Main entrypoint for the application."""

import asyncio
import os
import sys

import typer
import uvicorn
from loguru import logger

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from src.app import app
from src.config.logging_config import setup_logging
from src.config.settings import Settings, settings

# Initialize Typer application
cli_app = typer.Typer(
    name="ultimate-electrical-designer",
    help="A CLI for managing the Ultimate Electrical Designer application backend.",
    pretty_exceptions_show_locals=False,  # Hide locals for cleaner exception messages
)

setup_logging()

DEBUG_MODE = os.getenv("DEBUG", "False").lower() == "true"
JSON_LOGS = os.getenv("JSON_LOGS", "False").lower() == "true"


# Now, use the logger globally
logger.info("Application starting up...")
logger.debug(f"Debug mode is {'ON' if DEBUG_MODE else 'OFF'}")
logger.debug(f"JSON logging is {'ON' if JSON_LOGS else 'OFF'}")


@cli_app.command()
def run(
    host: str = typer.Option(
        "0.0.0.0", help="Host address to bind the server to."
    ),  # Intentional for containerized deployment  # nosec B104
    port: int = typer.Option(settings.APP_PORT, help="Port to run the server on."),
    reload: bool = typer.Option(
        settings.DEBUG, help="Enable auto-reload for development."
    ),
) -> None:
    """Runs the FastAPI web server.
    Note: For direct Uvicorn invocation, use 'uvicorn src.app:app' from the server directory.
    """
    logger.info(f"Starting FastAPI server on http://{host}:{port}")
    logger.info(f"Application environment: {settings.ENVIRONMENT}")
    logger.info(f"Debug mode: {settings.DEBUG}")

    uvicorn.run(
        "app:app",
        host=host,
        port=port,
        reload=reload,
        log_level=settings.LOG_LEVEL.lower(),  # Use log_level from settings
    )


@cli_app.command()
def migrate() -> None:
    """Runs database migrations using Alembic."""
    logger.info("Starting database migrations...")
    original_dir = os.getcwd()
    try:
        # Ensure we're in the correct directory for alembic.ini
        if not os.path.exists("alembic.ini"):
            # If we're not in src directory, try to change to it
            if os.path.exists("src"):
                os.chdir("src")
                logger.debug(f"Changed current directory to: {os.getcwd()}")
            else:
                raise FileNotFoundError("Could not find alembic.ini or src directory")
        else:
            logger.debug(f"Already in correct directory: {os.getcwd()}")

        from alembic import command
        from alembic.config import Config

        # Alembic will now find alembic.ini in the current directory
        alembic_cfg = Config("alembic.ini")
        # Use upgrade to apply migrations
        command.upgrade(alembic_cfg, "head")
        logger.info("Database migrations completed successfully.")

    except Exception as e:
        logger.error(f"Error during database migration: {e}", exc_info=True)
        typer.echo(f"Migration failed: {e}", err=True)
        raise typer.Exit(code=1)
    finally:
        # Change back to the original directory
        os.chdir(original_dir)
        logger.debug(f"Changed back to original directory: {os.getcwd()}")

    logger.info(
        "Database migration command finished (check logs for actual Alembic output)."
    )


@cli_app.command("create-superuser")
def create_superuser_command(
    username: str = typer.Argument(..., help="Username for the superuser."),
    password: str = typer.Argument(..., help="Password for the superuser."),
    email: str = typer.Argument(..., help="Email for the superuser."),
) -> None:
    """Creates a new superuser in the database."""

    async def _create_superuser_async() -> None:
        """Helper async function to create a superuser."""
        logger.info(f"Attempting to create superuser: {username}")
        from src.core.database.initialization import initialize_database
        from src.core.database.session import get_async_db_session
        from src.core.services.general.user_service import UserService

        await initialize_database()
        async with get_async_db_session() as db_session:
            from src.core.repositories.user_repository import UserRepository
            from src.core.repositories.user_preference_repository import (
                UserPreferenceRepository,
            )

            user_repo = UserRepository(db_session)
            preference_repo = UserPreferenceRepository(db_session)
            user_service = UserService(user_repo, preference_repo)
            superuser = await user_service.create_superuser(
                username=username, password=password, email=email
            )
        typer.echo(f"✅ Superuser '{superuser.name}' created successfully!")
        typer.echo(f"   - ID: {superuser.id}")
        typer.echo(f"   - Email: {superuser.email}")
        typer.echo(f"   - Active: {superuser.is_active}")

    try:
        asyncio.run(_create_superuser_async())
    except Exception as e:
        logger.error(f"Error creating superuser: {e}", exc_info=True)
        typer.echo(f"❌ Failed to create superuser: {e}", err=True)
        raise typer.Exit(code=1)


@cli_app.command("seed-general-data")
def seed_general_data_command() -> None:
    """Seeds the database with general data."""
    logger.info("Attempting to seed general data...")
    try:
        from data.seed_general import seed_general_data

        asyncio.run(seed_general_data())
        typer.echo("✅ General data seeding completed successfully!")
    except Exception as e:
        logger.error(f"Error seeding general data: {e}", exc_info=True)
        typer.echo(f"❌ Failed to seed general data: {e}", err=True)
        raise typer.Exit(code=1)


if __name__ == "__main__":
    cli_app()

# Instantiate the settings object to make it globally available
settings = Settings()
