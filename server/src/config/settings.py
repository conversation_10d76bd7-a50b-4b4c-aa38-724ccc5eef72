"""Application Configuration Settings.

the configuration settings for the Ultimate Electrical Designer
backend application using Pydantic Settings. It handles environment variables,
database connections, security settings, and application parameters.

The settings support both development and production environments with automatic
fallback configurations and validation.
"""

# backend/config/settings.py
import os
from pathlib import Path
from typing import List, Optional

from pydantic import Field, model_validator
from pydantic_settings import BaseSettings, SettingsConfigDict

from src.config.logging_config import logger

# More robust path calculation that works regardless of where the module is imported from
import os
PROJECT_ROOT = Path(os.path.abspath(__file__)).parent.parent.parent.parent


class Settings(BaseSettings):
    """Application settings loaded from environment variables or a .env file.
    Pydantic's BaseSettings handles the loading and validation.
    """
    print(f"PROJECT_ROOT: {PROJECT_ROOT}")
    # --- Application Metadata ---
    APP_NAME: str = "Ultimate Electrical Designer"
    APP_VERSION: str = "1.0.0"
    APP_DESCRIPTION: str = "An engineering application for industrial electrical design, calculations, and management."
    APP_ROOT: str = Field(
        default="./src/",
        description="Root directory of the application.",
    )
    ENV_FILE: Optional[str] = Field(
        default=(f"{PROJECT_ROOT} / .env"),
        description="Path to the .env file for environment variables. If not set, defaults to '.env' in the project root.",
    )

    # Environment can be 'development', 'testing', 'production'
    ENVIRONMENT: str = Field(
        default="development",
        pattern="^(development|testing|production)$",
        description="Application environment (development, testing, production)",
    )
    DEBUG: bool = Field(
        default=True,
        description="Enable debug features (e.g., debug endpoints, detailed error messages)",
    )

    APP_PORT: int = Field(
        default=8000,
        description="Port to run the FastAPI server on.",
    )

    # --- Database Configuration ---
    # Primary database (PostgreSQL for online operations)
    DATABASE_URL: Optional[str] = Field(
        default=None,
        description="PostgreSQL database connection string for all operations.",
    )
    TEST_DATABASE_URL: Optional[str] = Field(
        default=None,
        description="PostgreSQL database connection string for testing. If not set, defaults to DATABASE_URL.",
    )

    DB_ECHO: bool = Field(
        default=False,
        description="Set to True to log all SQLAlchemy statements to stdout (useful for debugging DB queries).",
    )

    # --- Caching & Rate Limiting (Redis) ---
    REDIS_ENABLED: bool = Field(
        default=False,
        description="Set to True to enable Redis for caching and/or rate limiting.",
    )
    REDIS_URL: Optional[str] = Field(
        default=None,
        description="Redis connection URL (e.g., 'redis://localhost:6379/0'). Required if REDIS_ENABLED is True.",
    )
    RATE_LIMIT_ENABLED: bool = Field(
        default=True,  # Default to True for security, can be disabled for dev
        description="Set to True to enable API rate limiting.",
    )
    RATE_LIMIT_DEFAULT_REQUESTS_PER_MINUTE: int = Field(
        default=100,  # Default rate limit
        description="Default number of requests allowed per minute for rate limiting.",
    )

    # --- Security ---
    # !!! IMPORTANT !!!
    # This key MUST be changed in production. Generate a strong, random string (e.g., using secrets.token_urlsafe(32)).
    # It should be loaded from an environment variable in production deployments.
    SECRET_KEY: str = Field(
        default="&1DeDbTClEtRDKZI8zrb5FXN8o%Bgvqr",
        min_length=32,
        description="Secret key for cryptographic operations (e.g., JWT signing). Must be strong and secret.",
    )
    JWT_ALGORITHM: str = Field(
        default="HS256",
        description="Algorithm used for signing JSON Web Tokens (JWTs).",
    )
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES: int = Field(
        default=30, description="Expiration time for JWT access tokens in minutes."
    )

    # --- CORS Configuration ---
    CORS_ORIGINS: List[str] = Field(
        default=[
            "http://localhost:3000",
            "http://127.0.0.1:3000",
        ],
        description="List of allowed origins for CORS. Can be overridden by environment variable.",
    )
    CORS_ORIGINS_REGEX: Optional[str] = Field(
        default=None,
        description="Regex for allowing a dynamic set of origins. If set, this will be used by the CORS middleware.",
    )

    # --- Logging ---
    # Supported levels: CRITICAL, ERROR, WARNING, INFO, DEBUG, NOTSET
    LOG_LEVEL: str = Field(
        default="INFO",
        pattern="^(CRITICAL|ERROR|WARNING|INFO|DEBUG|NOTSET)$",
        description="Minimum logging level for the application.",
    )

    # --- Testing ---
    TESTING: bool = Field(
        default=False,
        description="Set to True when running in a testing environment.",
    )

    @model_validator(mode="after")
    def update_testing_settings(self) -> "Settings":
        """Automatically configure settings for the testing environment.
        Only applies when explicitly set to testing environment.
        """
        # Apply testing settings only if explicitly set to testing environment
        if self.ENVIRONMENT == "testing":
            self.TESTING = True
            self.RATE_LIMIT_ENABLED = False  # Disable rate limiting for testing
        return self

    # --- Pydantic Settings Configuration ---
    # This tells Pydantic's BaseSettings how to load the settings.
    print(f"PROJECT_ROOT: {PROJECT_ROOT}")
    model_config = SettingsConfigDict(
        env_file=(PROJECT_ROOT / ".env"),
        env_file_encoding="utf-8",  # Encoding for the .env file
        case_sensitive=True,  # Environment variable names are case-sensitive
        extra="ignore",  # Ignore extra environment variables not defined here
    )
    print(f"ENV_FILE: {PROJECT_ROOT / '.env'}")


# Instantiate the settings object to make it globally available
settings = Settings()


def get_settings() -> Settings:
    """Get the application settings instance."""
    return settings
