"""rename_tables_to_lowercase_snake_case

Revision ID: a1b2c3d4e5f6
Revises: 79a42545e8de
Create Date: 2025-08-06 15:55:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a1b2c3d4e5f6'
down_revision = '79a42545e8de'
branch_labels = None
depends_on = None


def upgrade():
    """Rename all tables from capitalized names to lowercase snake_case names"""
    
    # Rename tables from capitalized to lowercase plural forms
    # Note: Only rename tables that actually exist
    
    from sqlalchemy import inspect
    
    # Get current connection to inspect existing tables
    connection = op.get_bind()
    inspector = inspect(connection)
    existing_tables = inspector.get_table_names()
    
    # Table mappings: old_name -> new_name
    table_mappings = {
        'User': 'users',
        'UserRole': 'user_roles',
        'UserRoleAssignment': 'user_role_assignments',
        'UserPreference': 'user_preferences',
        'Component': 'components',
        'ComponentCategory': 'component_categories',
        'ComponentType': 'component_types',
        'Project': 'projects',
        'ProjectMember': 'project_members',
        'ActivityLog': 'activity_logs',
        'AuditTrail': 'audit_trails',
        'SynchronizationLog': 'synchronization_logs',
        'SynchronizationConflict': 'synchronization_conflicts'
    }
    
    # Rename only existing tables
    for old_name, new_name in table_mappings.items():
        if old_name in existing_tables:
            op.rename_table(old_name, new_name)


def downgrade():
    """Revert table names back to capitalized forms"""
    
    from sqlalchemy import inspect
    
    # Get current connection to inspect existing tables
    connection = op.get_bind()
    inspector = inspect(connection)
    existing_tables = inspector.get_table_names()
    
    # Reverse table mappings: new_name -> old_name
    reverse_table_mappings = {
        'users': 'User',
        'user_roles': 'UserRole',
        'user_role_assignments': 'UserRoleAssignment',
        'user_preferences': 'UserPreference',
        'components': 'Component',
        'component_categories': 'ComponentCategory',
        'component_types': 'ComponentType',
        'projects': 'Project',
        'project_members': 'ProjectMember',
        'activity_logs': 'ActivityLog',
        'audit_trails': 'AuditTrail',
        'synchronization_logs': 'SynchronizationLog',
        'synchronization_conflicts': 'SynchronizationConflict'
    }
    
    # Rename only existing tables
    for new_name, old_name in reverse_table_mappings.items():
        if new_name in existing_tables:
            op.rename_table(new_name, old_name)