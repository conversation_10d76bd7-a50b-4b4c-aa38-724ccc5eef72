"""Add synchronization log and conflict tables

Revision ID: f8e2b6c3d5a1
Revises: 30a40908dea4
Create Date: 2025-07-22 15:30:00.000000

"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision = "f8e2b6c3d5a1"
down_revision = "30a40908dea4"
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Create SynchronizationLog table
    op.create_table(
        "SynchronizationLog",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("project_id", sa.Integer(), nullable=False),
        sa.Column("session_id", sa.String(length=255), nullable=True),
        sa.Column("user_id", sa.Integer(), nullable=True),
        sa.Column("operation_type", sa.String(length=50), nullable=False),
        sa.Column("sync_direction", sa.String(length=50), nullable=False),
        sa.Column("status", sa.String(length=50), nullable=False),
        sa.Column("started_at", sa.DateTime(), nullable=False),
        sa.Column("completed_at", sa.DateTime(), nullable=True),
        sa.Column("duration_ms", sa.Integer(), nullable=True),
        sa.Column("source_database_url", sa.Text(), nullable=True),
        sa.Column("target_database_url", sa.Text(), nullable=True),
        sa.Column("records_processed", sa.Integer(), nullable=False),
        sa.Column("records_created", sa.Integer(), nullable=False),
        sa.Column("records_updated", sa.Integer(), nullable=False),
        sa.Column("records_deleted", sa.Integer(), nullable=False),
        sa.Column("conflicts_detected", sa.Integer(), nullable=False),
        sa.Column("conflicts_resolved", sa.Integer(), nullable=False),
        sa.Column("error_message", sa.Text(), nullable=True),
        sa.Column("error_details", sa.Text(), nullable=True),
        sa.Column("sync_metadata", sa.Text(), nullable=True),
        sa.Column("sync_config", sa.Text(), nullable=True),
        sa.Column("throughput_records_per_second", sa.Float(), nullable=True),
        sa.Column("memory_usage_peak_mb", sa.Integer(), nullable=True),
        sa.Column("network_bytes_transferred", sa.Integer(), nullable=True),
        sa.Column("is_automatic", sa.Boolean(), nullable=False),
        sa.Column("is_retry", sa.Boolean(), nullable=False),
        sa.Column("is_critical", sa.Boolean(), nullable=False),
        sa.Column("retry_count", sa.Integer(), nullable=False),
        sa.Column("max_retries", sa.Integer(), nullable=False),
        sa.Column("next_retry_at", sa.DateTime(), nullable=True),
        sa.Column("created_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["project_id"],
            ["Project.id"],
        ),
        sa.ForeignKeyConstraint(
            ["user_id"],
            ["User.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )

    # Create indexes for SynchronizationLog
    op.create_index(
        "ix_synchronizationlog_project_id", "SynchronizationLog", ["project_id"]
    )
    op.create_index("ix_synchronizationlog_status", "SynchronizationLog", ["status"])
    op.create_index(
        "ix_synchronizationlog_started_at", "SynchronizationLog", ["started_at"]
    )
    op.create_index(
        "ix_synchronizationlog_operation_type", "SynchronizationLog", ["operation_type"]
    )
    op.create_index("ix_synchronizationlog_user_id", "SynchronizationLog", ["user_id"])

    # Create SynchronizationConflict table
    op.create_table(
        "SynchronizationConflict",
        sa.Column("id", sa.Integer(), autoincrement=True, nullable=False),
        sa.Column("sync_log_id", sa.Integer(), nullable=False),
        sa.Column("entity_type", sa.String(length=100), nullable=False),
        sa.Column("entity_id", sa.String(length=255), nullable=False),
        sa.Column("table_name", sa.String(length=100), nullable=False),
        sa.Column("conflict_type", sa.String(length=100), nullable=False),
        sa.Column("field_name", sa.String(length=100), nullable=True),
        sa.Column("local_value", sa.Text(), nullable=True),
        sa.Column("central_value", sa.Text(), nullable=True),
        sa.Column("local_timestamp", sa.DateTime(), nullable=True),
        sa.Column("central_timestamp", sa.DateTime(), nullable=True),
        sa.Column("is_resolved", sa.Boolean(), nullable=False),
        sa.Column("resolution_strategy", sa.String(length=100), nullable=True),
        sa.Column("resolved_value", sa.Text(), nullable=True),
        sa.Column("resolved_at", sa.DateTime(), nullable=True),
        sa.Column("resolved_by_user_id", sa.Integer(), nullable=True),
        sa.Column("severity", sa.String(length=50), nullable=False),
        sa.Column("requires_manual_intervention", sa.Boolean(), nullable=False),
        sa.Column("conflict_metadata", sa.Text(), nullable=True),
        sa.Column("resolution_notes", sa.Text(), nullable=True),
        sa.Column("detected_at", sa.DateTime(), nullable=False),
        sa.Column("updated_at", sa.DateTime(), nullable=False),
        sa.ForeignKeyConstraint(
            ["resolved_by_user_id"],
            ["User.id"],
        ),
        sa.ForeignKeyConstraint(
            ["sync_log_id"],
            ["SynchronizationLog.id"],
        ),
        sa.PrimaryKeyConstraint("id"),
    )

    # Create indexes for SynchronizationConflict
    op.create_index(
        "ix_synchronizationconflict_sync_log_id",
        "SynchronizationConflict",
        ["sync_log_id"],
    )
    op.create_index(
        "ix_synchronizationconflict_entity_type",
        "SynchronizationConflict",
        ["entity_type"],
    )
    op.create_index(
        "ix_synchronizationconflict_entity_id", "SynchronizationConflict", ["entity_id"]
    )
    op.create_index(
        "ix_synchronizationconflict_is_resolved",
        "SynchronizationConflict",
        ["is_resolved"],
    )
    op.create_index(
        "ix_synchronizationconflict_severity", "SynchronizationConflict", ["severity"]
    )
    op.create_index(
        "ix_synchronizationconflict_detected_at",
        "SynchronizationConflict",
        ["detected_at"],
    )

    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###

    # Drop SynchronizationConflict table and indexes
    op.drop_index(
        "ix_synchronizationconflict_detected_at", table_name="SynchronizationConflict"
    )
    op.drop_index(
        "ix_synchronizationconflict_severity", table_name="SynchronizationConflict"
    )
    op.drop_index(
        "ix_synchronizationconflict_is_resolved", table_name="SynchronizationConflict"
    )
    op.drop_index(
        "ix_synchronizationconflict_entity_id", table_name="SynchronizationConflict"
    )
    op.drop_index(
        "ix_synchronizationconflict_entity_type", table_name="SynchronizationConflict"
    )
    op.drop_index(
        "ix_synchronizationconflict_sync_log_id", table_name="SynchronizationConflict"
    )
    op.drop_table("SynchronizationConflict")

    # Drop SynchronizationLog table and indexes
    op.drop_index("ix_synchronizationlog_user_id", table_name="SynchronizationLog")
    op.drop_index(
        "ix_synchronizationlog_operation_type", table_name="SynchronizationLog"
    )
    op.drop_index("ix_synchronizationlog_started_at", table_name="SynchronizationLog")
    op.drop_index("ix_synchronizationlog_status", table_name="SynchronizationLog")
    op.drop_index("ix_synchronizationlog_project_id", table_name="SynchronizationLog")
    op.drop_table("SynchronizationLog")

    # ### end Alembic commands ###
