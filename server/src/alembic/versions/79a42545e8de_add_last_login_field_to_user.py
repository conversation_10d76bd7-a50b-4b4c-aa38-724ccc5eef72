"""add_last_login_field_to_user

Revision ID: 79a42545e8de
Revises: 272130620d46
Create Date: 2025-07-29 02:57:39.770474

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '79a42545e8de'
down_revision = '272130620d46'
branch_labels = None
depends_on = None


def upgrade():
    # Add last_login column to User table
    op.add_column('User', sa.Column('last_login', sa.DateTime(timezone=True), nullable=True))


def downgrade():
    # Remove last_login column from User table
    op.drop_column('User', 'last_login')