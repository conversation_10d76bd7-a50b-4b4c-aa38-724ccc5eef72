"""Unified Performance Monitoring System.

This module provides a centralized performance monitoring system that consolidates
the different performance monitoring approaches currently used across the
Ultimate Electrical Designer backend.

The unified system provides consistent performance monitoring, metrics collection,
and reporting across all layers.
"""

import asyncio
import threading
import time
import tracemalloc
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Any, Callable, Dict, Generator, List, Optional

import psutil

from src.config.logging_config import logger
from src.core.enums import MonitoringContext


@dataclass
class PerformanceMetrics:
    """Comprehensive performance metrics."""

    operation_name: str
    context: MonitoringContext
    start_time: float
    end_time: Optional[float] = None
    execution_time_ms: Optional[float] = None
    start_memory_mb: Optional[float] = None
    end_memory_mb: Optional[float] = None
    peak_memory_mb: Optional[float] = None
    memory_delta_mb: Optional[float] = None
    cpu_usage_percent: Optional[float] = None
    cpu_time_delta: Optional[float] = None
    success: bool = True
    error_message: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "operation_name": self.operation_name,
            "context": self.context.value,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "execution_time_ms": self.execution_time_ms,
            "start_memory_mb": self.start_memory_mb,
            "end_memory_mb": self.end_memory_mb,
            "peak_memory_mb": self.peak_memory_mb,
            "memory_delta_mb": self.memory_delta_mb,
            "cpu_usage_percent": self.cpu_usage_percent,
            "cpu_time_delta": self.cpu_time_delta,
            "success": self.success,
            "error_message": self.error_message,
            "metadata": self.metadata,
        }


@dataclass
class AggregatedMetrics:
    """Aggregated performance metrics for analysis."""

    operation_name: str
    context: MonitoringContext
    total_executions: int
    successful_executions: int
    failed_executions: int
    avg_execution_time_ms: float
    min_execution_time_ms: float
    max_execution_time_ms: float
    avg_memory_usage_mb: float
    peak_memory_usage_mb: float
    total_cpu_time: float
    error_rate_percent: float
    throughput_per_second: float
    last_updated: datetime


class UnifiedPerformanceMonitor:
    """Unified performance monitoring system that consolidates all performance
    monitoring approaches into a single, comprehensive system.
    """

    def __init__(
        self,
        enable_memory_tracking: bool = True,
        enable_cpu_tracking: bool = True,
        enable_aggregation: bool = True,
        max_metrics_history: int = 10000,
        aggregation_window_seconds: int = 300,  # 5 minutes
    ):
        self.enable_memory_tracking = enable_memory_tracking
        self.enable_cpu_tracking = enable_cpu_tracking
        self.enable_aggregation = enable_aggregation
        self.max_metrics_history = max_metrics_history
        self.aggregation_window_seconds = aggregation_window_seconds

        # Metrics storage
        self.metrics_history: List[PerformanceMetrics] = []
        self.aggregated_metrics: Dict[str, AggregatedMetrics] = {}
        self.active_operations: Dict[str, PerformanceMetrics] = {}

        # Thread safety
        self._lock = threading.Lock()

        # Performance statistics
        self.stats = {
            "total_operations_monitored": 0,
            "total_execution_time_ms": 0.0,
            "total_memory_usage_mb": 0.0,
            "average_execution_time_ms": 0.0,
            "operations_per_second": 0.0,
        }

        logger.info("Unified Performance Monitor initialized")

    @contextmanager
    def monitor_operation(
        self,
        operation_name: str,
        context: MonitoringContext = MonitoringContext.OPERATION,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Generator[PerformanceMetrics, None, None]:
        """Context manager for monitoring operation performance.

        Args:
            operation_name: Name of the operation being monitored
            context: Monitoring context (API, service, repository, etc.)
            metadata: Additional metadata for the operation

        Yields:
            PerformanceMetrics: Real-time metrics object

        """
        metadata = metadata or {}
        operation_id = f"{context.value}:{operation_name}:{time.time()}"

        # Initialize metrics
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            context=context,
            start_time=time.time(),
            metadata=metadata,
        )

        # Start tracking
        if self.enable_memory_tracking:
            tracemalloc.start()
            metrics.start_memory_mb = self._get_memory_usage_mb()

        if self.enable_cpu_tracking:
            process = psutil.Process()
            initial_cpu_times = process.cpu_times()
            metrics.metadata["initial_cpu_times"] = initial_cpu_times

        # Store active operation
        with self._lock:
            self.active_operations[operation_id] = metrics

        try:
            yield metrics
            metrics.success = True
        except Exception as e:
            metrics.success = False
            metrics.error_message = str(e)
            logger.warning(f"Operation {operation_name} failed: {e}")
            raise
        finally:
            # Finalize metrics
            metrics.end_time = time.time()
            metrics.execution_time_ms = (metrics.end_time - metrics.start_time) * 1000

            if self.enable_memory_tracking:
                try:
                    current_memory, peak_memory = tracemalloc.get_traced_memory()
                    tracemalloc.stop()
                    metrics.end_memory_mb = self._get_memory_usage_mb()
                    metrics.peak_memory_mb = peak_memory / 1024 / 1024
                    if metrics.start_memory_mb:
                        metrics.memory_delta_mb = (
                            metrics.end_memory_mb - metrics.start_memory_mb
                        )
                except Exception as e:
                    logger.debug(f"Memory tracking error: {e}")

            if self.enable_cpu_tracking:
                try:
                    process = psutil.Process()
                    final_cpu_times = process.cpu_times()
                    initial_cpu_times_raw = metrics.metadata.get("initial_cpu_times")
                    if initial_cpu_times_raw:
                        metrics.cpu_time_delta = (
                            final_cpu_times.user + final_cpu_times.system
                        ) - (initial_cpu_times_raw.user + initial_cpu_times_raw.system)
                        if (
                            metrics.execution_time_ms
                            and metrics.execution_time_ms > 0
                            and metrics.cpu_time_delta is not None
                        ):
                            metrics.cpu_usage_percent = (
                                metrics.cpu_time_delta
                                / (metrics.execution_time_ms / 1000)
                                * 100
                            )
                        else:
                            metrics.cpu_usage_percent = 0.0
                except Exception as e:
                    logger.debug(f"CPU tracking error: {e}")

            # Store metrics
            self._store_metrics(metrics)

            # Remove from active operations
            with self._lock:
                self.active_operations.pop(operation_id, None)

            # Log performance summary
            self._log_performance_summary(metrics)

    def monitor_function(
        self,
        operation_name: Optional[str] = None,
        context: MonitoringContext = MonitoringContext.FUNCTION,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
        """Decorator for monitoring function performance.

        Args:
            operation_name: Optional operation name (defaults to function name)
            context: Monitoring context
            metadata: Additional metadata

        Returns:
            Decorated function with performance monitoring

        """

        def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
            import asyncio
            import inspect
            from functools import wraps

            @wraps(func)
            async def async_wrapper(*args: Any, **kwargs: Any) -> Any:
                op_name = operation_name or func.__name__
                op_metadata = metadata or {}
                op_metadata.update(
                    {
                        "function_name": func.__name__,
                        "module": func.__module__,
                    }
                )

                with self.monitor_operation(op_name, context, op_metadata) as metrics:
                    result = func(*args, **kwargs)
                    if inspect.iscoroutine(result):
                        return await result
                    return result

            @wraps(func)
            def sync_wrapper(*args: Any, **kwargs: Any) -> Any:
                op_name = operation_name or func.__name__
                op_metadata = metadata or {}
                op_metadata.update(
                    {
                        "function_name": func.__name__,
                        "module": func.__module__,
                    }
                )

                with self.monitor_operation(op_name, context, op_metadata) as metrics:
                    return func(*args, **kwargs)

            # Return appropriate wrapper based on function type
            if inspect.iscoroutinefunction(func):
                return async_wrapper
            else:
                return sync_wrapper

        return decorator

    def record_metrics(
        self,
        operation_name: str,
        context: MonitoringContext,
        execution_time_ms: float,
        memory_usage_mb: Optional[float] = None,
        cpu_usage_percent: Optional[float] = None,
        success: bool = True,
        error_message: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> None:
        """Record performance metrics manually.

        Args:
            operation_name: Name of the operation
            context: Monitoring context
            execution_time_ms: Execution time in milliseconds
            memory_usage_mb: Memory usage in MB
            cpu_usage_percent: CPU usage percentage
            success: Whether operation succeeded
            error_message: Error message if failed
            metadata: Additional metadata

        """
        current_time = time.time()
        metrics = PerformanceMetrics(
            operation_name=operation_name,
            context=context,
            start_time=current_time - (execution_time_ms / 1000),
            end_time=current_time,
            execution_time_ms=execution_time_ms,
            end_memory_mb=memory_usage_mb,
            peak_memory_mb=memory_usage_mb,  # Set peak memory for aggregation
            cpu_usage_percent=cpu_usage_percent,
            success=success,
            error_message=error_message,
            metadata=metadata or {},
        )

        self._store_metrics(metrics)
        self._log_performance_summary(metrics)

    def _store_metrics(self, metrics: PerformanceMetrics) -> None:
        """Store metrics in history and update aggregations."""
        with self._lock:
            # Add to history
            self.metrics_history.append(metrics)

            # Maintain history size limit
            if len(self.metrics_history) > self.max_metrics_history:
                self.metrics_history = self.metrics_history[-self.max_metrics_history :]

            # Update statistics
            self.stats["total_operations_monitored"] += 1
            if metrics.execution_time_ms:
                self.stats["total_execution_time_ms"] += metrics.execution_time_ms
                self.stats["average_execution_time_ms"] = (
                    self.stats["total_execution_time_ms"]
                    / self.stats["total_operations_monitored"]
                )

            if metrics.end_memory_mb:
                self.stats["total_memory_usage_mb"] += metrics.end_memory_mb

            # Update aggregated metrics
            if self.enable_aggregation:
                self._update_aggregated_metrics(metrics)

    def _update_aggregated_metrics(self, metrics: PerformanceMetrics) -> None:
        """Update aggregated metrics for the operation."""
        key = f"{metrics.context.value}:{metrics.operation_name}"

        if key not in self.aggregated_metrics:
            self.aggregated_metrics[key] = AggregatedMetrics(
                operation_name=metrics.operation_name,
                context=metrics.context,
                total_executions=0,
                successful_executions=0,
                failed_executions=0,
                avg_execution_time_ms=0.0,
                min_execution_time_ms=float("inf"),
                max_execution_time_ms=0.0,
                avg_memory_usage_mb=0.0,
                peak_memory_usage_mb=0.0,
                total_cpu_time=0.0,
                error_rate_percent=0.0,
                throughput_per_second=0.0,
                last_updated=datetime.now(timezone.utc),
            )

        agg = self.aggregated_metrics[key]
        agg.total_executions += 1

        if metrics.success:
            agg.successful_executions += 1
        else:
            agg.failed_executions += 1

        if metrics.execution_time_ms:
            # Update execution time statistics
            total_time = agg.avg_execution_time_ms * (agg.total_executions - 1)
            agg.avg_execution_time_ms = (
                total_time + metrics.execution_time_ms
            ) / agg.total_executions
            agg.min_execution_time_ms = min(
                agg.min_execution_time_ms, metrics.execution_time_ms
            )
            agg.max_execution_time_ms = max(
                agg.max_execution_time_ms, metrics.execution_time_ms
            )

        if metrics.end_memory_mb:
            # Update memory statistics
            total_memory = agg.avg_memory_usage_mb * (agg.total_executions - 1)
            agg.avg_memory_usage_mb = (
                total_memory + metrics.end_memory_mb
            ) / agg.total_executions

        if metrics.peak_memory_mb:
            agg.peak_memory_usage_mb = max(
                agg.peak_memory_usage_mb, metrics.peak_memory_mb
            )

        if metrics.cpu_time_delta:
            agg.total_cpu_time += metrics.cpu_time_delta

        # Update error rate
        agg.error_rate_percent = (agg.failed_executions / agg.total_executions) * 100

        # Update throughput (operations per second over last window)
        window_start = time.time() - self.aggregation_window_seconds
        recent_operations = sum(
            1
            for m in self.metrics_history
            if (
                m.context == metrics.context
                and m.operation_name == metrics.operation_name
                and m.start_time >= window_start
            )
        )
        agg.throughput_per_second = recent_operations / self.aggregation_window_seconds

        agg.last_updated = datetime.now(timezone.utc)

    def _get_memory_usage_mb(self) -> float:
        """Get current memory usage in MB."""
        try:
            process = psutil.Process()
            return float(process.memory_info().rss / 1024 / 1024)
        except Exception:
            return 0.0

    def _log_performance_summary(self, metrics: PerformanceMetrics) -> None:
        """Log performance summary."""
        if (
            metrics.execution_time_ms and metrics.execution_time_ms > 1000
        ):  # Log slow operations
            logger.warning(
                f"Slow operation detected: {metrics.operation_name} "
                f"({metrics.context.value}) took {metrics.execution_time_ms:.2f}ms"
            )
        else:
            logger.debug(
                f"Performance: {metrics.operation_name} "
                f"({metrics.context.value}) - {metrics.execution_time_ms:.2f}ms"
            )

    def get_performance_statistics(self) -> Dict[str, Any]:
        """Get comprehensive performance statistics."""
        with self._lock:
            recent_metrics = [
                m
                for m in self.metrics_history
                if m.start_time >= time.time() - 3600  # Last hour
            ]

            return {
                "overview": self.stats.copy(),
                "recent_operations": len(recent_metrics),
                "active_operations": len(self.active_operations),
                "aggregated_metrics": {
                    key: {
                        "operation_name": agg.operation_name,
                        "context": agg.context.value,
                        "total_executions": agg.total_executions,
                        "success_rate": (
                            (agg.successful_executions / agg.total_executions * 100)
                            if agg.total_executions > 0
                            else 0
                        ),
                        "avg_execution_time_ms": agg.avg_execution_time_ms,
                        "throughput_per_second": agg.throughput_per_second,
                        "error_rate_percent": agg.error_rate_percent,
                    }
                    for key, agg in self.aggregated_metrics.items()
                },
                "top_slow_operations": self._get_top_slow_operations(),
                "top_memory_consumers": self._get_top_memory_consumers(),
                "error_summary": self._get_error_summary(),
            }

    def _get_top_slow_operations(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top slow operations."""
        sorted_agg = sorted(
            self.aggregated_metrics.values(),
            key=lambda x: x.avg_execution_time_ms,
            reverse=True,
        )

        return [
            {
                "operation_name": agg.operation_name,
                "context": agg.context.value,
                "avg_execution_time_ms": agg.avg_execution_time_ms,
                "total_executions": agg.total_executions,
            }
            for agg in sorted_agg[:limit]
        ]

    def _get_top_memory_consumers(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top memory consuming operations."""
        sorted_agg = sorted(
            self.aggregated_metrics.values(),
            key=lambda x: x.peak_memory_usage_mb,
            reverse=True,
        )

        return [
            {
                "operation_name": agg.operation_name,
                "context": agg.context.value,
                "peak_memory_usage_mb": agg.peak_memory_usage_mb,
                "avg_memory_usage_mb": agg.avg_memory_usage_mb,
            }
            for agg in sorted_agg[:limit]
            if agg.peak_memory_usage_mb > 0
        ]

    def _get_error_summary(self) -> Dict[str, Any]:
        """Get error summary."""
        total_errors = sum(
            agg.failed_executions for agg in self.aggregated_metrics.values()
        )
        total_operations = sum(
            agg.total_executions for agg in self.aggregated_metrics.values()
        )

        error_by_context = {}
        for agg in self.aggregated_metrics.values():
            context = agg.context.value
            if context not in error_by_context:
                error_by_context[context] = {"total": 0, "errors": 0}
            error_by_context[context]["total"] += agg.total_executions
            error_by_context[context]["errors"] += agg.failed_executions

        return {
            "total_errors": total_errors,
            "total_operations": total_operations,
            "overall_error_rate": (
                round((total_errors / total_operations * 100), 2)
                if total_operations > 0
                else 0
            ),
            "error_by_context": {
                context: {
                    "error_count": data["errors"],
                    "error_rate": (
                        round((data["errors"] / data["total"] * 100), 2)
                        if data["total"] > 0
                        else 0
                    ),
                }
                for context, data in error_by_context.items()
            },
        }


# Global instance for easy access
unified_performance_monitor = UnifiedPerformanceMonitor()


def get_unified_performance_monitor() -> UnifiedPerformanceMonitor:
    """Dependency injection function for FastAPI."""
    return unified_performance_monitor


# Convenience decorators and functions
def monitor_api_performance(
    operation_name: Optional[str] = None,
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for monitoring API endpoint performance.

    Usage:
        @monitor_api_performance("get_user_list")
        async def get_users():
            # API endpoint logic
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        op_name = operation_name or func.__name__
        return unified_performance_monitor.monitor_function(
            op_name, MonitoringContext.API, {"endpoint": True}
        )(func)

    return decorator


def monitor_service_performance(
    operation_name: Optional[str] = None,
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for monitoring service method performance.

    Usage:
        @monitor_service_performance("create_user")
        def create_user(self, user_data):
            # Service logic
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        op_name = operation_name or func.__name__
        return unified_performance_monitor.monitor_function(
            op_name, MonitoringContext.SERVICE, {"service_method": True}
        )(func)

    return decorator


def monitor_repository_performance(
    operation_name: Optional[str] = None,
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for monitoring repository method performance.

    Usage:
        @monitor_repository_performance("user_query")
        def get_user(self, user_id):
            # Repository logic
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        op_name = operation_name or func.__name__
        return unified_performance_monitor.monitor_function(
            op_name, MonitoringContext.REPOSITORY, {"repository_method": True}
        )(func)

    return decorator


def monitor_calculation_performance(
    calculation_type: str = "calculation",
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for monitoring calculation performance.

    Usage:
        @monitor_calculation_performance("heat_loss")
        def calculate_heat_loss(self, inputs):
            # Calculation logic
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        op_name = f"{calculation_type}_calculation"
        return unified_performance_monitor.monitor_function(
            op_name,
            MonitoringContext.CALCULATION,
            {"calculation_type": calculation_type},
        )(func)

    return decorator


def monitor_database_performance(
    operation_name: Optional[str] = None,
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for monitoring database operation performance.

    Usage:
        @monitor_database_performance("bulk_insert")
        def bulk_insert_users(self, users):
            # Database operation logic
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        op_name = operation_name or func.__name__
        return unified_performance_monitor.monitor_function(
            op_name, MonitoringContext.DATABASE, {"database_operation": True}
        )(func)

    return decorator


def monitor_batch_performance(
    batch_size: Optional[int] = None,
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for monitoring batch operation performance.

    Usage:
        @monitor_batch_performance(100)
        def process_batch(self, items):
            # Batch processing logic
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        op_name = f"batch_{func.__name__}"
        metadata: Dict[str, Any] = {"batch_operation": True}
        if batch_size:
            metadata["batch_size"] = batch_size
        return unified_performance_monitor.monitor_function(
            op_name, MonitoringContext.BATCH, metadata
        )(func)

    return decorator


def monitor_middleware_performance(
    operation_name: Optional[str] = None,
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for monitoring middleware operation performance.

    Usage:
        @monitor_middleware_performance("authentication")
        async def authenticate_request(self, request):
            # Middleware logic here
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        return unified_performance_monitor.monitor_function(
            operation_name=operation_name or func.__name__,
            context=MonitoringContext.MIDDLEWARE,
        )(func)

    return decorator


def monitor_utility_performance(
    operation_name: Optional[str] = None,
) -> Callable[[Callable[..., Any]], Callable[..., Any]]:
    """Decorator for monitoring utility function performance.

    Provides specialized monitoring for utility functions including memory management,
    concurrent operations, file I/O, and other infrastructure utilities.

    Usage:
        @monitor_utility_performance("memory_cleanup")
        def cleanup_memory(self):
            # Utility logic

        @monitor_utility_performance("concurrent_task_execution")
        async def execute_concurrent_tasks(self, tasks):
            # Concurrent utility logic
    """

    def decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        op_name = operation_name or func.__name__
        return unified_performance_monitor.monitor_function(
            op_name, MonitoringContext.UTILITY, {"utility_function": True}
        )(func)

    return decorator


async def monitor_async_operation(
    operation_name: str,
    context: MonitoringContext,
    operation_func: Callable[..., Any],
    *args: Any,
    **kwargs: Any,
) -> Any:
    """Monitor async operation performance.

    Args:
        operation_name: Name of the operation
        context: Monitoring context
        operation_func: Async function to monitor
        *args: Arguments for the function
        **kwargs: Keyword arguments for the function

    Returns:
        Result of the operation

    """
    with unified_performance_monitor.monitor_operation(
        operation_name, context
    ) as metrics:
        if asyncio.iscoroutinefunction(operation_func):
            return await operation_func(*args, **kwargs)
        else:
            return operation_func(*args, **kwargs)


def record_external_metrics(
    operation_name: str,
    context: MonitoringContext,
    execution_time_ms: float,
    success: bool = True,
    **kwargs: Any,
) -> None:
    """Record metrics from external monitoring systems.

    Args:
        operation_name: Name of the operation
        context: Monitoring context
        execution_time_ms: Execution time in milliseconds
        success: Whether operation succeeded
        **kwargs: Additional metric data

    """
    unified_performance_monitor.record_metrics(
        operation_name=operation_name,
        context=context,
        execution_time_ms=execution_time_ms,
        success=success,
        metadata=kwargs,
    )


def get_performance_summary() -> Dict[str, Any]:
    """Get comprehensive performance summary."""
    return unified_performance_monitor.get_performance_statistics()


def get_operation_metrics(
    operation_name: str, context: Optional[MonitoringContext] = None
) -> List[Dict[str, Any]]:
    """Get metrics for specific operation.

    Args:
        operation_name: Name of the operation
        context: Optional context filter

    Returns:
        List of metrics for the operation

    """
    with unified_performance_monitor._lock:
        metrics = []
        for metric in unified_performance_monitor.metrics_history:
            if metric.operation_name == operation_name:
                if context is None or metric.context == context:
                    metrics.append(metric.to_dict())
        return metrics


def clear_performance_history() -> None:
    """Clear performance metrics history (useful for testing)."""
    with unified_performance_monitor._lock:
        unified_performance_monitor.metrics_history.clear()
        unified_performance_monitor.aggregated_metrics.clear()
        unified_performance_monitor.stats = {
            "total_operations_monitored": 0,
            "total_execution_time_ms": 0.0,
            "total_memory_usage_mb": 0.0,
            "average_execution_time_ms": 0.0,
            "operations_per_second": 0.0,
        }


def create_performance_report(
    time_window_hours: int = 24, include_details: bool = False
) -> Dict[str, Any]:
    """Create comprehensive performance report.

    Args:
        time_window_hours: Time window for the report in hours
        include_details: Whether to include detailed metrics

    Returns:
        Performance report dictionary

    """
    cutoff_time = time.time() - (time_window_hours * 3600)

    with unified_performance_monitor._lock:
        # Filter metrics by time window
        recent_metrics = [
            m
            for m in unified_performance_monitor.metrics_history
            if m.start_time >= cutoff_time
        ]

        # Calculate summary statistics
        total_operations = len(recent_metrics)
        successful_operations = sum(1 for m in recent_metrics if m.success)
        failed_operations = total_operations - successful_operations

        avg_execution_time = (
            sum(m.execution_time_ms for m in recent_metrics if m.execution_time_ms)
            / total_operations
            if total_operations > 0
            else 0
        )

        # Group by context
        context_stats = {}
        for metric in recent_metrics:
            context = metric.context.value
            if context not in context_stats:
                context_stats[context] = {
                    "total_operations": 0,
                    "successful_operations": 0,
                    "failed_operations": 0,
                    "avg_execution_time_ms": 0.0,
                    "total_execution_time_ms": 0.0,
                }

            stats = context_stats[context]
            stats["total_operations"] += 1
            if metric.success:
                stats["successful_operations"] += 1
            else:
                stats["failed_operations"] += 1

            if metric.execution_time_ms:
                stats["total_execution_time_ms"] += metric.execution_time_ms

        # Calculate averages
        for stats in context_stats.values():
            if stats["total_operations"] > 0:
                stats["avg_execution_time_ms"] = (
                    stats["total_execution_time_ms"] / stats["total_operations"]
                )

        report = {
            "report_period_hours": time_window_hours,
            "generated_at": datetime.now(timezone.utc).isoformat(),
            "summary": {
                "total_operations": total_operations,
                "successful_operations": successful_operations,
                "failed_operations": failed_operations,
                "success_rate_percent": (
                    (successful_operations / total_operations * 100)
                    if total_operations > 0
                    else 0
                ),
                "average_execution_time_ms": avg_execution_time,
            },
            "context_breakdown": context_stats,
            "top_slow_operations": unified_performance_monitor._get_top_slow_operations(),
            "top_memory_consumers": unified_performance_monitor._get_top_memory_consumers(),
            "error_summary": unified_performance_monitor._get_error_summary(),
        }

        if include_details:
            report["detailed_metrics"] = [
                m.to_dict() for m in recent_metrics[-100:]
            ]  # Last 100 operations

        return report
