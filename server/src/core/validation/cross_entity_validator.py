"""Cross-Entity Dependency Validation Engine.

This module provides advanced validation for complex relationships between entities,
including project-component compatibility, dependency chains, and business rule
enforcement across multiple entities.
"""

import asyncio
from typing import Any, Dict, List, Optional, Set, Tuple, Union
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import networkx as nx
from collections import defaultdict

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_validation_errors
from src.core.validation.compatibility_matrix import (
    CompatibilityMatrix,
    CompatibilityLevel,
)
from src.core.validation.standards_validator import StandardsValidator, ComplianceLevel


class DependencyType(Enum):
    """Types of dependencies between entities."""

    HARD_DEPENDENCY = "hard_dependency"  # Cannot exist without
    SOFT_DEPENDENCY = "soft_dependency"  # Can exist but may have issues
    MUTUAL_EXCLUSION = "mutual_exclusion"  # Cannot exist together
    COMPATIBILITY = "compatibility"  # Must be compatible
    REFERENCE = "reference"  # References another entity
    CONSTRAINT = "constraint"  # Constrained by another entity


class ValidationSeverity(Enum):
    """Severity levels for validation results."""

    ERROR = "error"
    WARNING = "warning"
    INFO = "info"
    SUGGESTION = "suggestion"


@dataclass
class DependencyValidationResult:
    """Result of cross-entity dependency validation."""

    is_valid: bool
    severity: ValidationSeverity
    message: str
    affected_entities: List[str]
    suggested_actions: List[str]
    metadata: Dict[str, Any]


@dataclass
class EntityNode:
    """Represents an entity in the dependency graph."""

    entity_type: str
    entity_id: str
    attributes: Dict[str, Any]
    version: Optional[str] = None
    last_modified: Optional[datetime] = None


class CrossEntityDependencyValidator:
    """Advanced cross-entity dependency validation engine."""

    def __init__(self) -> None:
        self.dependency_graph = nx.DiGraph()
        self.validation_rules: defaultdict[str, List[Dict[str, Any]]] = defaultdict(
            list
        )
        self.compatibility_matrix: Dict[str, Dict[str, Any]] = {}
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.advanced_matrix = CompatibilityMatrix()
        self.standards_validator = StandardsValidator()

        # Initialize electrical system compatibility rules
        self._initialize_electrical_compatibility_rules()
        self._initialize_project_component_rules()
        self._initialize_dependency_chains()
        self._initialize_standards_validation_rules()

    def _initialize_electrical_compatibility_rules(self) -> None:
        """Initialize compatibility rules for electrical systems."""
        # Voltage compatibility matrix
        self.compatibility_matrix["voltage"] = {
            "low_voltage": {"compatible_with": ["low_voltage", "medium_voltage"]},
            "medium_voltage": {
                "compatible_with": ["low_voltage", "medium_voltage", "high_voltage"]
            },
            "high_voltage": {"compatible_with": ["medium_voltage", "high_voltage"]},
        }

        # Current rating compatibility
        self.compatibility_matrix["current_rating"] = {
            "residential": {
                "max_current": 100,
                "compatible_with": ["residential", "commercial"],
            },
            "commercial": {
                "max_current": 400,
                "compatible_with": ["residential", "commercial", "industrial"],
            },
            "industrial": {
                "max_current": 3200,
                "compatible_with": ["commercial", "industrial"],
            },
        }

        # Frequency compatibility
        self.compatibility_matrix["frequency"] = {
            "50hz": {"compatible_with": ["50hz"]},
            "60hz": {"compatible_with": ["60hz"]},
            "400hz": {"compatible_with": ["400hz"]},
        }

    def _initialize_project_component_rules(self) -> None:
        """Initialize project-component validation rules."""
        self.validation_rules["project_component"] = [
            {
                "rule_id": "voltage_compatibility",
                "description": "Component voltage rating must be compatible with project system voltage",
                "severity": ValidationSeverity.ERROR,
                "check": self._check_voltage_compatibility,
            },
            {
                "rule_id": "current_capacity",
                "description": "Component current rating must meet project load requirements",
                "severity": ValidationSeverity.WARNING,
                "check": self._check_current_capacity,
            },
            {
                "rule_id": "environmental_rating",
                "description": "Component environmental rating must match project location conditions",
                "severity": ValidationSeverity.ERROR,
                "check": self._check_environmental_rating,
            },
            {
                "rule_id": "standards_compliance",
                "description": "Component must comply with project specified standards",
                "severity": ValidationSeverity.ERROR,
                "check": self._check_standards_compliance,
            },
        ]

    def _initialize_dependency_chains(self) -> None:
        """Initialize dependency chain validation rules."""
        self.validation_rules["dependency_chain"] = [
            {
                "rule_id": "cascading_impact",
                "description": "Changes to upstream components must be validated for downstream impact",
                "severity": ValidationSeverity.WARNING,
                "check": self._check_cascading_impact,
            },
            {
                "rule_id": "circular_dependency",
                "description": "Circular dependencies between components must be detected and resolved",
                "severity": ValidationSeverity.ERROR,
                "check": self._check_circular_dependencies,
            },
            {
                "rule_id": "resource_availability",
                "description": "Required resources must be available for all components in the chain",
                "severity": ValidationSeverity.ERROR,
                "check": self._check_resource_availability,
            },
        ]

    def _initialize_standards_validation_rules(self) -> None:
        """Initialize standards compliance validation rules."""
        self.validation_rules["standards"] = [
            {
                "rule_id": "standards_compliance",
                "description": "Component must comply with applicable electrical standards",
                "severity": ValidationSeverity.ERROR,
                "check": self._check_standards_compliance,
            },
            {
                "rule_id": "regional_standards",
                "description": "Component must meet regional electrical standards requirements",
                "severity": ValidationSeverity.ERROR,
                "check": self._check_regional_standards,
            },
            {
                "rule_id": "version_compatibility",
                "description": "Component standards versions must be compatible with project requirements",
                "severity": ValidationSeverity.WARNING,
                "check": self._check_version_compatibility,
            },
        ]

    @handle_validation_errors("cross_entity_validation")
    async def validate_project_components(
        self,
        project_data: Dict[str, Any],
        components: List[Dict[str, Any]],
        validation_rules: Optional[List[str]] = None,
    ) -> List[DependencyValidationResult]:
        """Validate project-component compatibility."""
        results = []

        try:
            # Build dependency graph
            await self._build_project_dependency_graph(project_data, components)

            # Apply validation rules
            active_rules = validation_rules or [
                rule["rule_id"] for rule in self.validation_rules["project_component"]
            ]

            for rule in self.validation_rules["project_component"]:
                if rule["rule_id"] in active_rules:
                    rule_results = await rule["check"](project_data, components)
                    results.extend(rule_results)

            # Advanced compatibility matrix validation
            matrix_results = await self._validate_compatibility_matrix(
                project_data, components
            )
            results.extend(matrix_results)

            # Standards compliance validation
            standards_results = await self._validate_standards_compliance(
                project_data, components
            )
            results.extend(standards_results)

            # Validate dependency chains
            chain_results = await self._validate_dependency_chains(
                project_data, components
            )
            results.extend(chain_results)

            # Cache results for performance
            cache_key = self._generate_cache_key(project_data, components)
            self.cache[cache_key] = {
                "results": results,
                "timestamp": datetime.utcnow(),
                "project_data": project_data,
                "components": components,
            }

            return results

        except Exception as e:
            logger.error(f"Error validating project components: {e}")
            return [
                DependencyValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Validation system error: {str(e)}",
                    affected_entities=[],
                    suggested_actions=["Contact system administrator"],
                    metadata={"error": str(e)},
                )
            ]

    async def validate_component_compatibility(
        self, components: List[Dict[str, Any]], compatibility_type: str = "electrical"
    ) -> List[DependencyValidationResult]:
        """Validate compatibility between components."""
        results = []

        try:
            # Build component compatibility graph
            await self._build_component_compatibility_graph(components)

            # Check pairwise compatibility
            for i, comp1 in enumerate(components):
                for j, comp2 in enumerate(components[i + 1 :], i + 1):
                    compatibility_results = (
                        await self._check_component_pair_compatibility(
                            comp1, comp2, compatibility_type
                        )
                    )
                    results.extend(compatibility_results)

            return results

        except Exception as e:
            logger.error(f"Error validating component compatibility: {e}")
            return [
                DependencyValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Compatibility validation error: {str(e)}",
                    affected_entities=[],
                    suggested_actions=["Review component specifications"],
                    metadata={"error": str(e)},
                )
            ]

    async def validate_dependency_impact(
        self, changed_entity: Dict[str, Any], affected_entities: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Validate the impact of changes on dependent entities."""
        results = []

        try:
            # Identify affected entities
            affected_nodes = await self._identify_affected_entities(
                changed_entity, affected_entities
            )

            # Validate each affected entity
            validation_results = [
                result
                for affected_node in affected_nodes
                for result in await self._validate_affected_entity(
                    changed_entity, affected_node
                )
            ]
            results.extend(validation_results)

            return results

        except Exception as e:
            logger.error(f"Error validating dependency impact: {e}")
            return [
                DependencyValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Impact validation error: {str(e)}",
                    affected_entities=[],
                    suggested_actions=["Review dependency chain"],
                    metadata={"error": str(e)},
                )
            ]

    async def _build_project_dependency_graph(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> None:
        """Build a dependency graph for project components."""
        self.dependency_graph.clear()

        # Add project node
        project_node = EntityNode(
            entity_type="project",
            entity_id=project_data.get("id", "project"),
            attributes=project_data,
        )
        self.dependency_graph.add_node("project", node=project_node)

        # Add component nodes
        for component in components:
            component_id = component.get("id", str(hash(str(component))))
            component_node = EntityNode(
                entity_type="component", entity_id=component_id, attributes=component
            )
            self.dependency_graph.add_node(component_id, node=component_node)

            # Add edges from project to component
            self.dependency_graph.add_edge("project", component_id, type="contains")

            # Add inter-component dependencies
            await self._add_component_dependencies(component, components)

    async def _build_component_compatibility_graph(
        self, components: List[Dict[str, Any]]
    ) -> None:
        """Build a compatibility graph between components."""
        self.dependency_graph.clear()

        for component in components:
            component_id = component.get("id", str(hash(str(component))))
            component_node = EntityNode(
                entity_type="component", entity_id=component_id, attributes=component
            )
            self.dependency_graph.add_node(component_id, node=component_node)

    async def _add_component_dependencies(
        self, component: Dict[str, Any], all_components: List[Dict[str, Any]]
    ) -> None:
        """Add dependencies between components based on specifications."""
        component_id = component.get("id", str(hash(str(component))))

        # Add voltage dependencies
        if "voltage_rating" in component:
            for other in all_components:
                if other != component:
                    other_id = other.get("id", str(hash(str(other))))
                    self.dependency_graph.add_edge(
                        component_id,
                        other_id,
                        type="voltage_dependency",
                        attribute="voltage_rating",
                    )

    async def _check_voltage_compatibility(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Check voltage compatibility between project and components."""
        results: List[DependencyValidationResult] = []

        project_voltage = project_data.get("system_voltage")
        if not project_voltage:
            return results

        for component in components:
            component_voltage = component.get("voltage_rating")
            if component_voltage and component_voltage != project_voltage:
                results.append(
                    DependencyValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Component voltage {component_voltage}V incompatible with project voltage {project_voltage}V",
                        affected_entities=[component.get("id", "unknown")],
                        suggested_actions=[
                            f"Select component with {project_voltage}V rating",
                            "Verify system voltage requirements",
                        ],
                        metadata={
                            "project_voltage": project_voltage,
                            "component_voltage": component_voltage,
                            "rule": "voltage_compatibility",
                        },
                    )
                )

        return results

    async def _check_current_capacity(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Check current capacity requirements."""
        results = []

        total_load = project_data.get("total_load_current", 0)

        for component in components:
            component_current = component.get("rated_current", 0)
            if component_current < total_load:
                results.append(
                    DependencyValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.WARNING,
                        message=f"Component current rating {component_current}A may be insufficient for total load {total_load}A",
                        affected_entities=[component.get("id", "unknown")],
                        suggested_actions=[
                            f"Upgrade to component with {total_load * 1.25}A rating",
                            "Add parallel components for load sharing",
                        ],
                        metadata={
                            "component_current": component_current,
                            "required_current": total_load,
                            "rule": "current_capacity",
                        },
                    )
                )

        return results

    async def _check_environmental_rating(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Check environmental rating compatibility."""
        results = []

        project_environment = project_data.get("environmental_conditions", {})

        for component in components:
            component_rating = component.get("environmental_rating", {})

            # Check IP rating
            required_ip = project_environment.get("ip_rating", "IP20")
            component_ip = component_rating.get("ip_rating", "IP20")

            if self._compare_ip_ratings(component_ip, required_ip) < 0:
                results.append(
                    DependencyValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Component IP rating {component_ip} insufficient for environment {required_ip}",
                        affected_entities=[component.get("id", "unknown")],
                        suggested_actions=[
                            f"Select component with {required_ip} or higher IP rating",
                            "Add environmental protection measures",
                        ],
                        metadata={
                            "component_ip": component_ip,
                            "required_ip": required_ip,
                            "rule": "environmental_rating",
                        },
                    )
                )

        return results

    async def _check_standards_compliance(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Check standards compliance."""
        results = []

        project_standards = project_data.get("applicable_standards", [])

        for component in components:
            component_standards = component.get("compliance_standards", [])

            missing_standards = set(project_standards) - set(component_standards)

            results.extend(
                [
                    DependencyValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Component missing required standard compliance: {standard}",
                        affected_entities=[component.get("id", "unknown")],
                        suggested_actions=[
                            f"Select component compliant with {standard}",
                            "Request manufacturer certification",
                        ],
                        metadata={
                            "missing_standard": standard,
                            "rule": "standards_compliance",
                        },
                    )
                    for standard in missing_standards
                ]
            )

        return results

    async def _validate_compatibility_matrix(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Validate using advanced compatibility matrix."""
        results = []

        try:
            for component in components:
                matrix_result = (
                    await self.advanced_matrix.calculate_compatibility_matrix(
                        project_data, component
                    )
                )

                # Convert matrix results to dependency validation format
                if matrix_result.overall_level in [
                    CompatibilityLevel.INCOMPATIBLE,
                    CompatibilityLevel.CONFLICT,
                ]:
                    results.append(
                        DependencyValidationResult(
                            is_valid=False,
                            severity=ValidationSeverity.ERROR,
                            message=f"Component {component.get('id', 'unknown')} has low compatibility score: {matrix_result.overall_score:.2f}",
                            affected_entities=[component.get("id", "unknown")],
                            suggested_actions=matrix_result.optimization_suggestions,
                            metadata={
                                "compatibility_score": matrix_result.overall_score,
                                "critical_issues": matrix_result.critical_issues,
                                "dimension_scores": {
                                    s.dimension.value: s.score
                                    for s in matrix_result.dimension_scores
                                },
                            },
                        )
                    )
                elif matrix_result.overall_level == CompatibilityLevel.MARGINAL:
                    results.append(
                        DependencyValidationResult(
                            is_valid=True,
                            severity=ValidationSeverity.WARNING,
                            message=f"Component {component.get('id', 'unknown')} has marginal compatibility",
                            affected_entities=[component.get("id", "unknown")],
                            suggested_actions=matrix_result.optimization_suggestions,
                            metadata={
                                "compatibility_score": matrix_result.overall_score,
                                "dimension_scores": {
                                    s.dimension.value: s.score
                                    for s in matrix_result.dimension_scores
                                },
                            },
                        )
                    )

        except Exception as e:
            logger.error(f"Error in compatibility matrix validation: {e}")
            results.append(
                DependencyValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Compatibility matrix validation error: {str(e)}",
                    affected_entities=[],
                    suggested_actions=["Review component specifications"],
                    metadata={"error": str(e)},
                )
            )

        return results

    async def _validate_dependency_chains(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Validate dependency chains between components."""
        results = []

        # Check for circular dependencies
        try:
            cycles = list(nx.simple_cycles(self.dependency_graph))
            results.extend(
                [
                    DependencyValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Circular dependency detected: {' → '.join(cycle)}",
                        affected_entities=list(cycle),
                        suggested_actions=[
                            "Review component interconnections",
                            "Break circular dependency with buffer or isolator",
                        ],
                        metadata={"cycle": cycle, "rule": "circular_dependency"},
                    )
                    for cycle in cycles
                ]
            )
        except nx.NetworkXError:
            pass  # No cycles found

        return results

    async def _validate_standards_compliance(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Validate standards compliance for components."""
        results = []

        try:
            region = project_data.get("region", "global")
            required_standards = project_data.get(
                "applicable_standards", ["IEEE", "IEC"]
            )

            for component in components:
                compliance_results = (
                    await self.standards_validator.validate_standards_compliance(
                        component,
                        required_standards,
                        region,
                        project_data.get("application_type", "industrial"),
                    )
                )

                for compliance_result in compliance_results:
                    if (
                        compliance_result.overall_compliance
                        == ComplianceLevel.NOT_COMPLIANT
                    ):
                        results.append(
                            DependencyValidationResult(
                                is_valid=False,
                                severity=ValidationSeverity.ERROR,
                                message=f"Component {component.get('id', 'unknown')} not compliant with {compliance_result.standard_type.value}",
                                affected_entities=[component.get("id", "unknown")],
                                suggested_actions=compliance_result.critical_issues
                                + compliance_result.warnings,
                                metadata={
                                    "standard": compliance_result.standard_type.value,
                                    "compliance_score": compliance_result.compliance_score,
                                    "non_compliant_requirements": [
                                        nc.requirement_id
                                        for nc in compliance_result.non_compliant_requirements
                                    ],
                                    "regional_variations": compliance_result.regional_variations,
                                },
                            )
                        )
                    elif (
                        compliance_result.overall_compliance
                        == ComplianceLevel.PARTIALLY_COMPLIANT
                    ):
                        results.append(
                            DependencyValidationResult(
                                is_valid=True,
                                severity=ValidationSeverity.WARNING,
                                message=f"Component {component.get('id', 'unknown')} partially compliant with {compliance_result.standard_type.value}",
                                affected_entities=[component.get("id", "unknown")],
                                suggested_actions=compliance_result.warnings,
                                metadata={
                                    "standard": compliance_result.standard_type.value,
                                    "compliance_score": compliance_result.compliance_score,
                                    "warnings": compliance_result.warnings,
                                },
                            )
                        )

        except Exception as e:
            logger.error(f"Error in standards compliance validation: {e}")
            results.append(
                DependencyValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Standards validation error: {str(e)}",
                    affected_entities=[],
                    suggested_actions=["Review standards configuration"],
                    metadata={"error": str(e)},
                )
            )

        return results

    async def _check_regional_standards(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Check regional standards compliance."""
        results = []

        try:
            region = project_data.get("region", "global")
            regional_standards = self.standards_validator.get_applicable_standards(
                region
            )

            for component in components:
                # Check if component meets regional requirements
                component_standards = component.get("compliance_standards", [])
                missing_standards = [
                    standard_info["standard"]
                    for standard_info in regional_standards
                    if standard_info["standard"]
                    not in [s.upper() for s in component_standards]
                ]

                if missing_standards:
                    results.append(
                        DependencyValidationResult(
                            is_valid=False,
                            severity=ValidationSeverity.ERROR,
                            message=f"Component {component.get('id', 'unknown')} missing regional standards: {', '.join(missing_standards)}",
                            affected_entities=[component.get("id", "unknown")],
                            suggested_actions=[
                                f"Ensure compliance with regional standards: {', '.join(missing_standards)}",
                                "Update component specifications for regional requirements",
                            ],
                            metadata={
                                "missing_standards": missing_standards,
                                "region": region,
                                "component_standards": component_standards,
                            },
                        )
                    )

        except Exception as e:
            logger.error(f"Error in regional standards validation: {e}")
            results.append(
                DependencyValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Regional standards validation error: {str(e)}",
                    affected_entities=[],
                    suggested_actions=["Review regional configuration"],
                    metadata={"error": str(e)},
                )
            )

        return results

    async def _check_version_compatibility(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Check standards version compatibility."""
        results = []

        try:
            project_standards = project_data.get("applicable_standards", [])

            for component in components:
                component_standards = component.get("compliance_standards", [])

                # Check version compatibility
                results.extend(
                    [
                        DependencyValidationResult(
                            is_valid=True,
                            severity=ValidationSeverity.WARNING,
                            message=f"Component {component.get('id', 'unknown')} may not support latest {standard} version",
                            affected_entities=[component.get("id", "unknown")],
                            suggested_actions=[
                                f"Verify {standard} version compatibility",
                                "Consider updating to latest standard version",
                            ],
                            metadata={
                                "standard": standard,
                                "project_standards": project_standards,
                                "component_standards": component_standards,
                            },
                        )
                        for standard in project_standards
                        if standard not in component_standards
                    ]
                )

        except Exception as e:
            logger.error(f"Error in version compatibility validation: {e}")
            results.append(
                DependencyValidationResult(
                    is_valid=True,
                    severity=ValidationSeverity.WARNING,
                    message=f"Version compatibility validation error: {str(e)}",
                    affected_entities=[],
                    suggested_actions=["Review standards configuration"],
                    metadata={"error": str(e)},
                )
            )

        return results

    async def _check_component_pair_compatibility(
        self, comp1: Dict[str, Any], comp2: Dict[str, Any], compatibility_type: str
    ) -> List[DependencyValidationResult]:
        """Check compatibility between two components."""
        results = []

        if compatibility_type == "electrical":
            # Check voltage compatibility
            v1 = comp1.get("voltage_rating")
            v2 = comp2.get("voltage_rating")

            if v1 and v2 and abs(v1 - v2) > max(v1, v2) * 0.1:  # 10% tolerance
                results.append(
                    DependencyValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.WARNING,
                        message=f"Voltage mismatch between components: {v1}V vs {v2}V",
                        affected_entities=[
                            comp1.get("id", "unknown"),
                            comp2.get("id", "unknown"),
                        ],
                        suggested_actions=[
                            "Use voltage matching components",
                            "Add voltage conversion equipment",
                        ],
                        metadata={
                            "voltage1": v1,
                            "voltage2": v2,
                            "type": "voltage_mismatch",
                        },
                    )
                )

        return results

    async def _identify_affected_entities(
        self, changed_entity: Dict[str, Any], affected_entities: List[Dict[str, Any]]
    ) -> List[EntityNode]:
        """Identify entities affected by changes."""
        affected_nodes = []

        # Simple dependency analysis - can be enhanced with graph traversal
        for entity in affected_entities:
            node = EntityNode(
                entity_type=entity.get("type", "unknown"),
                entity_id=entity.get("id", "unknown"),
                attributes=entity,
            )
            affected_nodes.append(node)

        return affected_nodes

    async def _check_cascading_impact(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Check cascading impact of component changes."""
        results = []

        # Analyze potential cascading effects
        for component in components:
            component_id = component.get("id", "unknown")

            # Check if component has upstream dependencies
            upstream_deps = self._get_upstream_dependencies(component, components)
            if upstream_deps:
                results.extend(
                    [
                        DependencyValidationResult(
                            is_valid=True,
                            severity=ValidationSeverity.WARNING,
                            message=f"Component {component_id} has upstream dependency on {dep}",
                            affected_entities=[component_id, dep],
                            suggested_actions=[
                                "Review impact on dependent components",
                                "Validate all downstream components",
                            ],
                            metadata={
                                "component_id": component_id,
                                "dependency": dep,
                                "impact_type": "cascading",
                            },
                        )
                        for dep in upstream_deps
                    ]
                )

            # Check for downstream impacts
            downstream_deps = self._get_downstream_dependencies(component, components)
            if downstream_deps:
                results.extend(
                    [
                        DependencyValidationResult(
                            is_valid=True,
                            severity=ValidationSeverity.WARNING,
                            message=f"Component {component_id} affects downstream component {dep}",
                            affected_entities=[component_id, dep],
                            suggested_actions=[
                                "Review downstream impact",
                                "Validate all affected components",
                            ],
                            metadata={
                                "component_id": component_id,
                                "affected_component": dep,
                                "impact_type": "downstream",
                            },
                        )
                        for dep in downstream_deps
                    ]
                )

        return results

    def _get_upstream_dependencies(
        self, component: Dict[str, Any], all_components: List[Dict[str, Any]]
    ) -> List[str]:
        """Get upstream dependencies for a component."""
        dependencies = []

        # Simple dependency detection based on voltage and current relationships
        component_voltage = component.get("voltage_rating")
        component_current = component.get("rated_current")

        for other in all_components:
            if other != component:
                other_voltage = other.get("voltage_rating")
                other_current = other.get("rated_current")

                # Voltage dependency: higher voltage components depend on lower ones
                if (
                    component_voltage
                    and other_voltage
                    and other_voltage > component_voltage
                ):
                    dependencies.append(other.get("id", "unknown"))

                # Current dependency: higher current components depend on lower ones
                if (
                    component_current
                    and other_current
                    and other_current > component_current
                ):
                    dep_id = other.get("id", "unknown")
                    if dep_id not in dependencies:
                        dependencies.append(dep_id)

        return dependencies

    def _get_downstream_dependencies(
        self, component: Dict[str, Any], all_components: List[Dict[str, Any]]
    ) -> List[str]:
        """Get downstream dependencies for a component."""
        dependencies = []

        component_voltage = component.get("voltage_rating")
        component_current = component.get("rated_current")

        for other in all_components:
            if other != component:
                other_voltage = other.get("voltage_rating")
                other_current = other.get("rated_current")

                # Voltage dependency: lower voltage components are downstream
                if (
                    component_voltage
                    and other_voltage
                    and other_voltage < component_voltage
                ):
                    dependencies.append(other.get("id", "unknown"))

                # Current dependency: lower current components are downstream
                if (
                    component_current
                    and other_current
                    and other_current < component_current
                ):
                    dep_id = other.get("id", "unknown")
                    if dep_id not in dependencies:
                        dependencies.append(dep_id)

        return dependencies

    async def _check_circular_dependencies(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Check for circular dependencies in the system."""
        results = []

        try:
            # Build dependency relationships
            voltage_dependencies = defaultdict(list)
            current_dependencies = defaultdict(list)

            for component in components:
                component_id = component.get("id", "unknown")
                voltage = component.get("voltage_rating")
                current = component.get("rated_current")

                # Create voltage-based dependencies
                if voltage:
                    for other in components:
                        other_id = other.get("id", "unknown")
                        other_voltage = other.get("voltage_rating")
                        if (
                            other_id != component_id
                            and other_voltage
                            and other_voltage > voltage
                        ):
                            voltage_dependencies[component_id].append(other_id)

                # Create current-based dependencies
                if current:
                    for other in components:
                        other_id = other.get("id", "unknown")
                        other_current = other.get("rated_current")
                        if (
                            other_id != component_id
                            and other_current
                            and other_current > current
                        ):
                            current_dependencies[component_id].append(other_id)

            # Check for cycles in voltage dependencies
            voltage_cycles = self._find_cycles(voltage_dependencies)
            results.extend(
                [
                    DependencyValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Voltage circular dependency detected: {' -> '.join(cycle)}",
                        affected_entities=cycle,
                        suggested_actions=[
                            "Review voltage relationships between components",
                            "Consider voltage isolation or transformation",
                        ],
                        metadata={
                            "cycle": cycle,
                            "type": "voltage_circular_dependency",
                        },
                    )
                    for cycle in voltage_cycles
                ]
            )

            # Check for cycles in current dependencies
            current_cycles = self._find_cycles(current_dependencies)
            results.extend(
                [
                    DependencyValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Current circular dependency detected: {' -> '.join(cycle)}",
                        affected_entities=cycle,
                        suggested_actions=[
                            "Review current flow relationships",
                            "Consider separate circuits or isolation",
                        ],
                        metadata={
                            "cycle": cycle,
                            "type": "current_circular_dependency",
                        },
                    )
                    for cycle in current_cycles
                ]
            )

        except Exception as e:
            logger.error(f"Error checking circular dependencies: {e}")
            results.append(
                DependencyValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Circular dependency check error: {str(e)}",
                    affected_entities=[],
                    suggested_actions=["Review component relationships"],
                    metadata={"error": str(e)},
                )
            )

        return results

    def _find_cycles(self, dependency_graph: Dict[str, List[str]]) -> List[List[str]]:
        """Find cycles in a directed graph represented as adjacency list."""
        visited = set()
        recursion_stack = set()
        cycles = []

        def dfs(node: str, path: List[str]) -> None:
            if node in recursion_stack:
                # Found a cycle
                cycle_start = path.index(node)
                cycle = path[cycle_start:] + [node]
                if cycle not in cycles:
                    cycles.append(cycle)
                return

            if node in visited:
                return

            visited.add(node)
            recursion_stack.add(node)

            for neighbor in dependency_graph.get(node, []):
                dfs(neighbor, path + [node])

            recursion_stack.remove(node)

        for node in dependency_graph:
            if node not in visited:
                dfs(node, [])

        return cycles

    async def _check_resource_availability(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> List[DependencyValidationResult]:
        """Check resource availability for all components."""
        results = []

        try:
            # Check power capacity
            total_power = sum(comp.get("rated_power", 0) for comp in components)
            project_capacity = project_data.get("system_capacity", 0)

            if project_capacity > 0 and total_power > project_capacity:
                results.append(
                    DependencyValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Total component power {total_power}W exceeds project capacity {project_capacity}W",
                        affected_entities=[
                            comp.get("id", "unknown") for comp in components
                        ],
                        suggested_actions=[
                            "Reduce component power requirements",
                            "Increase project power capacity",
                            "Implement load management",
                        ],
                        metadata={
                            "total_power": total_power,
                            "project_capacity": project_capacity,
                            "rule": "resource_availability",
                        },
                    )
                )

            # Check space requirements
            total_space = sum(
                comp.get("dimensions", {}).get("width", 0)
                * comp.get("dimensions", {}).get("height", 0)
                * comp.get("dimensions", {}).get("depth", 0)
                for comp in components
            )
            available_space = project_data.get("available_space", 0)

            if available_space > 0 and total_space > available_space:
                results.append(
                    DependencyValidationResult(
                        is_valid=False,
                        severity=ValidationSeverity.ERROR,
                        message=f"Total component space {total_space} exceeds available space {available_space}",
                        affected_entities=[
                            comp.get("id", "unknown") for comp in components
                        ],
                        suggested_actions=[
                            "Reduce component size requirements",
                            "Increase available installation space",
                            "Consider remote installation",
                        ],
                        metadata={
                            "total_space": total_space,
                            "available_space": available_space,
                            "rule": "space_availability",
                        },
                    )
                )

        except Exception as e:
            logger.error(f"Error checking resource availability: {e}")
            results.append(
                DependencyValidationResult(
                    is_valid=False,
                    severity=ValidationSeverity.ERROR,
                    message=f"Resource availability check error: {str(e)}",
                    affected_entities=[],
                    suggested_actions=["Review project specifications"],
                    metadata={"error": str(e)},
                )
            )

        return results

    async def _validate_affected_entity(
        self, changed_entity: Dict[str, Any], affected_entity: EntityNode
    ) -> List[DependencyValidationResult]:
        """Validate an entity affected by changes."""
        results = []

        # Detailed impact validation
        change_type = changed_entity.get("change_type", "unknown")
        affected_id = affected_entity.entity_id

        results.append(
            DependencyValidationResult(
                is_valid=True,
                severity=ValidationSeverity.INFO,
                message=f"Entity {affected_id} validated for {change_type} changes",
                affected_entities=[affected_id],
                suggested_actions=[
                    "Perform detailed electrical analysis",
                    "Update system documentation",
                ],
                metadata={
                    "impact_analysis": "completed",
                    "change_type": change_type,
                    "affected_entity": affected_id,
                },
            )
        )

        return results

    def _compare_ip_ratings(self, ip1: str, ip2: str) -> int:
        """Compare two IP ratings. Returns 1 if ip1 >= ip2, 0 if equal, -1 if ip1 < ip2."""
        try:
            # Extract numeric parts
            num1 = int(ip1.split("IP")[1])
            num2 = int(ip2.split("IP")[1])
            return 1 if num1 >= num2 else -1
        except (IndexError, ValueError):
            return 0

    def _generate_cache_key(
        self, project_data: Dict[str, Any], components: List[Dict[str, Any]]
    ) -> str:
        """Generate a cache key for validation results."""
        import hashlib

        data_str = str(sorted(project_data.items())) + str(
            sorted(components, key=lambda x: x.get("id", ""))
        )
        return hashlib.md5(data_str.encode()).hexdigest()

    def clear_cache(self) -> None:
        """Clear the validation cache."""
        self.cache.clear()
        logger.info("Cross-entity validation cache cleared")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "cache_size": len(self.cache),
            "cache_keys": list(self.cache.keys()),
            "graph_nodes": len(self.dependency_graph.nodes()),
            "graph_edges": len(self.dependency_graph.edges()),
        }


# Global validator instance
cross_entity_validator = CrossEntityDependencyValidator()
