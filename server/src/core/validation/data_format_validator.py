"""Multi-format Data Compatibility Validation.

This module provides comprehensive validation for multiple data formats including
CSV, JSON, XML, and legacy formats with intelligent format detection and validation.
"""

import asyncio
import csv
import json
import xml.etree.ElementTree as ET
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
import re
import io

from src.config.logging_config import logger
from src.core.validation.json_schema_validator import AdvancedJsonSchemaValidator
from src.core.validation.advanced_validators import AdvancedElectricalValidator


class DataFormat(Enum):
    """Supported data formats."""

    CSV = "csv"
    JSON = "json"
    XML = "xml"
    YAML = "yaml"
    EXCEL = "excel"
    LEGACY = "legacy"
    UNKNOWN = "unknown"


class ValidationLevel(Enum):
    """Validation levels for data format checking."""

    BASIC = "basic"
    SYNTAX = "syntax"
    STRUCTURE = "structure"
    SEMANTIC = "semantic"
    BUSINESS_RULES = "business_rules"
    COMPLETE = "complete"


@dataclass
class FormatValidationError:
    """Detailed format validation error."""

    format_type: DataFormat
    level: ValidationLevel
    line: Optional[int]
    column: Optional[int]
    path: str
    message: str
    severity: str
    suggestion: str
    context: Dict[str, Any]


@dataclass
class FormatValidationResult:
    """Complete format validation result."""

    is_valid: bool
    format_type: DataFormat
    errors: List[FormatValidationError]
    warnings: List[str]
    data_preview: Dict[str, Any]
    validation_level: ValidationLevel
    processing_time_ms: float
    record_count: int
    field_mapping: Dict[str, str]
    compatibility_score: float  # 0.0 to 1.0


class MultiFormatDataValidator:
    """Advanced multi-format data validation system."""

    def __init__(self) -> None:
        self.json_validator = AdvancedJsonSchemaValidator()
        self.electrical_validator = AdvancedElectricalValidator()
        self.format_detectors: Dict[DataFormat, Callable[[str], bool]] = {
            DataFormat.CSV: self._detect_csv_format,
            DataFormat.JSON: self._detect_json_format,
            DataFormat.XML: self._detect_xml_format,
            DataFormat.YAML: self._detect_yaml_format,
            DataFormat.EXCEL: self._detect_excel_format,
            DataFormat.LEGACY: self._detect_legacy_format,
        }
        self.format_validators: Dict[DataFormat, Callable[..., Any]] = {
            DataFormat.CSV: self._validate_csv_data,
            DataFormat.JSON: self._validate_json_data,
            DataFormat.XML: self._validate_xml_data,
            DataFormat.YAML: self._validate_yaml_data,
            DataFormat.EXCEL: self._validate_excel_data,
            DataFormat.LEGACY: self._validate_legacy_data,
        }
        self.field_mappings: Dict[str, List[str]] = {
            "voltage": ["voltage", "voltage_rating", "rated_voltage", "V", "U"],
            "current": ["current", "current_rating", "rated_current", "I", "Amps"],
            "power": ["power", "power_rating", "rated_power", "P", "Watts", "kW"],
            "frequency": [
                "frequency",
                "frequency_rating",
                "rated_frequency",
                "f",
                "Hz",
            ],
            "temperature": ["temperature", "temp", "operating_temp", "T", "°C"],
            "resistance": ["resistance", "R", "Ohm", "Ω", "impedance"],
            "capacitance": ["capacitance", "C", "Farad", "F", "cap"],
            "inductance": ["inductance", "L", "Henry", "H", "ind"],
        }

    async def validate_data_format(
        self,
        data: Union[str, bytes, Path],
        expected_format: Optional[DataFormat] = None,
        validation_level: ValidationLevel = ValidationLevel.COMPLETE,
        schema_name: Optional[str] = None,
    ) -> FormatValidationResult:
        """Validate data in multiple formats."""
        start_time = datetime.utcnow()

        try:
            # Auto-detect format if not specified
            if expected_format is None:
                detected_format = await self._detect_format(data)
            else:
                detected_format = expected_format

            # Read and parse data
            parsed_data = await self._parse_data(data, detected_format)

            # Validate format
            validation_result = await self.format_validators[detected_format](
                parsed_data, validation_level, schema_name
            )

            # Generate preview
            data_preview = await self._generate_data_preview(
                parsed_data, detected_format
            )

            # Calculate compatibility score
            compatibility_score = await self._calculate_compatibility_score(
                validation_result, detected_format
            )

            processing_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            return FormatValidationResult(
                is_valid=len(validation_result.get("errors", [])) == 0,
                format_type=detected_format,
                errors=validation_result.get("errors", []),
                warnings=validation_result.get("warnings", []),
                data_preview=data_preview,
                validation_level=validation_level,
                processing_time_ms=processing_time,
                record_count=validation_result.get("record_count", 0),
                field_mapping=validation_result.get("field_mapping", {}),
                compatibility_score=compatibility_score,
            )

        except Exception as e:
            logger.error(f"Error validating data format: {e}")
            return FormatValidationResult(
                is_valid=False,
                format_type=DataFormat.UNKNOWN,
                errors=[
                    FormatValidationError(
                        format_type=DataFormat.UNKNOWN,
                        level=ValidationLevel.SYNTAX,
                        line=None,
                        column=None,
                        path="validation",
                        message=f"Validation error: {str(e)}",
                        severity="error",
                        suggestion="Check data format and structure",
                        context={"error": str(e)},
                    )
                ],
                warnings=[],
                data_preview={},
                validation_level=validation_level,
                processing_time_ms=(datetime.utcnow() - start_time).total_seconds()
                * 1000,
                record_count=0,
                field_mapping={},
                compatibility_score=0.0,
            )

    async def _detect_format(self, data: Union[str, bytes, Path]) -> DataFormat:
        """Automatically detect data format."""
        try:
            if isinstance(data, Path):
                content = data.read_text(encoding="utf-8")
            elif isinstance(data, bytes):
                content = data.decode("utf-8")
            else:
                content = str(data)

            content = content.strip()

            # Try each format detector
            for format_type, detector in self.format_detectors.items():
                if detector(content):
                    return format_type

            return DataFormat.UNKNOWN

        except Exception:
            return DataFormat.UNKNOWN

    def _detect_csv_format(self, content: str) -> bool:
        """Detect CSV format."""
        try:
            # Check for CSV characteristics
            lines = content.strip().split("\n")
            if len(lines) < 2:
                return False

            # Check for comma-separated values
            first_line = lines[0]
            if "," in first_line and len(first_line.split(",")) > 1:
                # Try to parse as CSV
                reader = csv.reader(io.StringIO(content))
                rows = list(reader)
                return len(rows) > 0 and len(rows[0]) > 1

            return False
        except Exception:
            return False

    def _detect_json_format(self, content: str) -> bool:
        """Detect JSON format."""
        try:
            json.loads(content)
            return True
        except (json.JSONDecodeError, ValueError):
            return False

    def _detect_xml_format(self, content: str) -> bool:
        """Detect XML format."""
        try:
            ET.fromstring(content.strip())
            return True
        except ET.ParseError:
            return False

    def _detect_yaml_format(self, content: str) -> bool:
        """Detect YAML format."""
        try:
            # Basic YAML detection
            lines = content.strip().split("\n")
            for line in lines:
                line = line.strip()
                if line.startswith("- ") or ":" in line and not line.startswith("#"):
                    return True
            return False
        except Exception:
            return False

    def _detect_excel_format(self, content: str) -> bool:
        """Detect Excel format (basic detection based on file extension)."""
        # This is a placeholder - actual Excel detection would need additional libraries
        return False

    def _detect_legacy_format(self, content: str) -> bool:
        """Detect legacy proprietary formats."""
        # Look for legacy format signatures
        legacy_patterns = [
            r"\[BEGIN\]",  # Custom legacy format markers
            r"\*\*\*",  # Old format delimiters
            r"\|\|",  # Pipe-delimited legacy
        ]

        for pattern in legacy_patterns:
            if re.search(pattern, content):
                return True
        return False

    async def _parse_data(
        self, data: Union[str, bytes, Path], format_type: DataFormat
    ) -> Any:
        """Parse data based on format type."""
        if isinstance(data, Path):
            content = data.read_text(encoding="utf-8")
        elif isinstance(data, bytes):
            content = data.decode("utf-8")
        else:
            content = str(data)

        if format_type == DataFormat.CSV:
            return list(csv.DictReader(io.StringIO(content)))
        elif format_type == DataFormat.JSON:
            try:
                return json.loads(content)
            except json.JSONDecodeError as e:
                raise ValueError(
                    f"JSON syntax error: {e.msg} at line {e.lineno}, column {e.colno}"
                )
        elif format_type == DataFormat.XML:
            root = ET.fromstring(content)
            return self._xml_to_dict(root)
        elif format_type == DataFormat.YAML:
            # Basic YAML parsing - would need PyYAML for full support
            return self._parse_yaml_content(content)
        elif format_type == DataFormat.LEGACY:
            return self._parse_legacy_content(content)
        else:
            return content

    def _xml_to_dict(self, element: ET.Element) -> Dict[str, Any]:
        """Convert XML element to dictionary."""
        result: Dict[str, Any] = {}

        # Add attributes
        if element.attrib:
            result.update(element.attrib)

        # Add text content
        if element.text and element.text.strip():
            result["text"] = element.text.strip()

        # Add child elements
        for child in element:
            child_data = self._xml_to_dict(child)
            if child.tag in result:
                if not isinstance(result[child.tag], list):
                    result[child.tag] = [result[child.tag]]
                result[child.tag].append(child_data)
            else:
                result[child.tag] = child_data

        return result

    def _parse_yaml_content(self, content: str) -> List[Dict[str, Any]]:
        """Parse basic YAML content."""
        # Simplified YAML parsing
        lines = content.strip().split("\n")
        result = []
        current_obj: Dict[str, Any] = {}

        for line in lines:
            line = line.strip()
            if line.startswith("- "):
                if current_obj:
                    result.append(current_obj)
                    current_obj = {}
                line = line[2:]  # Remove '- '

            if ":" in line and not line.startswith("#"):
                key, value = line.split(":", 1)
                key = key.strip()
                value = value.strip()
                current_obj[key] = value

        if current_obj:
            result.append(current_obj)

        return result

    def _parse_legacy_content(self, content: str) -> List[Dict[str, Any]]:
        """Parse legacy data formats."""
        # Handle various legacy formats
        lines = content.strip().split("\n")
        result = []

        # Detect delimiter
        if "|" in content:
            delimiter = "|"
        elif "\t" in content:
            delimiter = "\t"
        else:
            delimiter = ","

        # Parse header
        if lines:
            headers = [h.strip() for h in lines[0].split(delimiter)]

            # Parse data rows
            for line in lines[1:]:
                if line.strip():
                    values = [v.strip() for v in line.split(delimiter)]
                    if len(values) == len(headers):
                        result.append(dict(zip(headers, values)))

        return result

    async def _validate_csv_data(
        self,
        data: List[Dict[str, str]],
        validation_level: ValidationLevel,
        schema_name: Optional[str],
    ) -> Dict[str, Any]:
        """Validate CSV data."""
        errors: List[FormatValidationError] = []
        warnings: List[str] = []

        if not data:
            errors.append(
                FormatValidationError(
                    format_type=DataFormat.CSV,
                    level=ValidationLevel.SYNTAX,
                    line=1,
                    column=None,
                    path="data",
                    message="No data found in CSV",
                    severity="error",
                    suggestion="Check CSV file content",
                    context={"row_count": 0},
                )
            )
            return {
                "errors": errors,
                "warnings": warnings,
                "record_count": 0,
                "field_mapping": {},
            }

        # Validate structure
        headers = list(data[0].keys())
        field_mapping = await self._map_csv_fields(headers)

        # Validate each row
        for i, row in enumerate(data, 1):
            row_errors = await self._validate_csv_row(row, i, validation_level)
            errors.extend(row_errors)

        # Electrical validation if advanced level
        if validation_level in [
            ValidationLevel.BUSINESS_RULES,
            ValidationLevel.COMPLETE,
        ]:
            electrical_errors = await self._validate_electrical_data(
                data, DataFormat.CSV
            )
            errors.extend(electrical_errors)

        return {
            "errors": errors,
            "warnings": warnings,
            "record_count": len(data),
            "field_mapping": field_mapping,
        }

    async def _validate_json_data(
        self, data: Any, validation_level: ValidationLevel, schema_name: Optional[str]
    ) -> Dict[str, Any]:
        """Validate JSON data."""
        errors: List[FormatValidationError] = []
        warnings: List[str] = []

        if isinstance(data, list):
            records = data
        elif isinstance(data, dict):
            records = [data]
        else:
            records = [data]

        # Validate against schema if provided
        if schema_name and isinstance(data, dict):
            schema_result = await self.json_validator.validate_with_schema(
                data, schema_name
            )
            errors.extend(
                [
                    FormatValidationError(
                        format_type=DataFormat.JSON,
                        level=ValidationLevel.STRUCTURE,
                        line=None,
                        column=None,
                        path=error.path,
                        message=error.message,
                        severity=error.severity,
                        suggestion=error.suggestion,
                        context={"value": error.value},
                    )
                    for error in schema_result.errors
                ]
            )

        # Electrical validation
        if validation_level in [
            ValidationLevel.BUSINESS_RULES,
            ValidationLevel.COMPLETE,
        ]:
            electrical_errors = await self._validate_electrical_data(
                records, DataFormat.JSON
            )
            errors.extend(electrical_errors)

        return {
            "errors": errors,
            "warnings": warnings,
            "record_count": len(records),
            "field_mapping": {},
        }

    async def _validate_xml_data(
        self,
        data: Dict[str, Any],
        validation_level: ValidationLevel,
        schema_name: Optional[str],
    ) -> Dict[str, Any]:
        """Validate XML data."""
        errors: List[FormatValidationError] = []
        warnings: List[str] = []

        # Convert to list of records
        records = [data] if isinstance(data, dict) else data

        # Validate structure
        if not records:
            errors.append(
                FormatValidationError(
                    format_type=DataFormat.XML,
                    level=ValidationLevel.STRUCTURE,
                    line=None,
                    column=None,
                    path="root",
                    message="Invalid XML structure",
                    severity="error",
                    suggestion="Check XML format",
                    context={},
                )
            )

        # Electrical validation
        if validation_level in [
            ValidationLevel.BUSINESS_RULES,
            ValidationLevel.COMPLETE,
        ]:
            electrical_errors = await self._validate_electrical_data(
                records, DataFormat.XML
            )
            errors.extend(electrical_errors)

        return {
            "errors": errors,
            "warnings": warnings,
            "record_count": len(records),
            "field_mapping": {},
        }

    async def _validate_yaml_data(
        self,
        data: List[Dict[str, Any]],
        validation_level: ValidationLevel,
        schema_name: Optional[str],
    ) -> Dict[str, Any]:
        """Validate YAML data."""
        return await self._validate_json_data(data, validation_level, schema_name)

    async def _validate_excel_data(
        self,
        data: List[Dict[str, Any]],
        validation_level: ValidationLevel,
        schema_name: Optional[str],
    ) -> Dict[str, Any]:
        """Validate Excel data."""
        return await self._validate_json_data(data, validation_level, schema_name)

    async def _validate_legacy_data(
        self,
        data: List[Dict[str, str]],
        validation_level: ValidationLevel,
        schema_name: Optional[str],
    ) -> Dict[str, Any]:
        """Validate legacy data formats."""
        errors: List[FormatValidationError] = []
        warnings: List[str] = []

        # Convert string values to appropriate types
        converted_data: List[Dict[str, Any]] = []
        for i, record in enumerate(data, 1):
            converted_record: Dict[str, Any] = {}
            for key, value in record.items():
                try:
                    # Attempt to convert to number
                    if value.replace(".", "", 1).isdigit():
                        converted_record[key] = float(value)
                    else:
                        converted_record[key] = value
                except (ValueError, AttributeError):
                    converted_record[key] = value
            converted_data.append(converted_record)

        # Electrical validation
        if validation_level in [
            ValidationLevel.BUSINESS_RULES,
            ValidationLevel.COMPLETE,
        ]:
            electrical_errors = await self._validate_electrical_data(
                converted_data, DataFormat.LEGACY
            )
            errors.extend(electrical_errors)

        return {
            "errors": errors,
            "warnings": warnings,
            "record_count": len(converted_data),
            "field_mapping": {},
        }

    async def _validate_csv_row(
        self, row: Dict[str, str], row_number: int, validation_level: ValidationLevel
    ) -> List[FormatValidationError]:
        """Validate individual CSV row."""
        errors = []

        # Check for empty values
        for key, value in row.items():
            if not value or value.strip() == "":
                errors.append(
                    FormatValidationError(
                        format_type=DataFormat.CSV,
                        level=ValidationLevel.SYNTAX,
                        line=row_number,
                        column=None,
                        path=key,
                        message=f"Empty value in field '{key}'",
                        severity="warning",
                        suggestion="Provide valid value",
                        context={"field": key, "value": value},
                    )
                )

        # Validate electrical values
        if validation_level in [
            ValidationLevel.SEMANTIC,
            ValidationLevel.BUSINESS_RULES,
            ValidationLevel.COMPLETE,
        ]:
            for key, value in row.items():
                if any(
                    electrical_key in key.lower()
                    for electrical_key in ["voltage", "current", "power"]
                ):
                    try:
                        float_value = float(value)
                        if float_value < 0:
                            errors.append(
                                FormatValidationError(
                                    format_type=DataFormat.CSV,
                                    level=ValidationLevel.SEMANTIC,
                                    line=row_number,
                                    column=None,
                                    path=key,
                                    message=f"Negative value for electrical parameter '{key}'",
                                    severity="error",
                                    suggestion="Provide positive value",
                                    context={"field": key, "value": value},
                                )
                            )
                    except ValueError:
                        errors.append(
                            FormatValidationError(
                                format_type=DataFormat.CSV,
                                level=ValidationLevel.SEMANTIC,
                                line=row_number,
                                column=None,
                                path=key,
                                message=f"Invalid numeric value for '{key}': {value}",
                                severity="error",
                                suggestion="Provide valid number",
                                context={"field": key, "value": value},
                            )
                        )

        return errors

    async def _validate_electrical_data(
        self, data: List[Dict[str, Any]], format_type: DataFormat
    ) -> List[FormatValidationError]:
        """Validate electrical data across formats."""
        errors = []

        for i, record in enumerate(data, 1):
            # Validate electrical parameters
            voltage = record.get("voltage") or record.get("voltage_rating")
            current = record.get("current") or record.get("current_rating")
            power = record.get("power") or record.get("power_rating")

            # Check power calculation consistency
            if voltage is not None and current is not None and power is not None:
                try:
                    calculated_power = float(voltage) * float(current)
                    actual_power = float(power)

                    if (
                        abs(calculated_power - actual_power)
                        > max(calculated_power, actual_power) * 0.1
                    ):
                        errors.append(
                            FormatValidationError(
                                format_type=format_type,
                                level=ValidationLevel.BUSINESS_RULES,
                                line=i,
                                column=None,
                                path="power",
                                message=f"Power value inconsistent with voltage and current",
                                severity="warning",
                                suggestion="Verify power calculation: P = V × I",
                                context={
                                    "voltage": voltage,
                                    "current": current,
                                    "given_power": power,
                                    "calculated_power": calculated_power,
                                },
                            )
                        )
                except (ValueError, TypeError):
                    pass

        return errors

    async def _map_csv_fields(self, headers: List[str]) -> Dict[str, str]:
        """Map CSV fields to standard electrical parameters."""
        mapping = {}

        for header in headers:
            header_lower = header.lower().strip()
            for standard_field, variations in self.field_mappings.items():
                if any(variation.lower() in header_lower for variation in variations):
                    mapping[header] = standard_field
                    break

        return mapping

    async def _generate_data_preview(
        self, data: Any, format_type: DataFormat
    ) -> Dict[str, Any]:
        """Generate data preview."""
        if isinstance(data, list):
            preview_records = data[:5]  # First 5 records
            return {
                "format": format_type.value,
                "record_count": len(data),
                "preview": preview_records,
                "fields": list(data[0].keys()) if data else [],
            }
        elif isinstance(data, dict):
            return {
                "format": format_type.value,
                "record_count": 1,
                "preview": [data],
                "fields": list(data.keys()),
            }
        else:
            return {
                "format": format_type.value,
                "record_count": 1,
                "preview": [str(data)[:100]],
                "fields": [],
            }

    async def _calculate_compatibility_score(
        self, validation_result: Dict[str, Any], format_type: DataFormat
    ) -> float:
        """Calculate compatibility score."""
        error_count = len(validation_result.get("errors", []))
        warning_count = len(validation_result.get("warnings", []))
        record_count = validation_result.get("record_count", 1)

        # Base score
        score = 1.0

        # Penalty for errors
        score -= error_count * 0.2

        # Penalty for warnings
        score -= warning_count * 0.05

        # Bonus for data presence
        if record_count > 0:
            score += 0.1

        return max(0.0, min(1.0, score))

    async def convert_format(
        self,
        source_data: Any,
        source_format: DataFormat,
        target_format: DataFormat,
        preserve_metadata: bool = True,
    ) -> str:
        """Convert data between formats."""
        try:
            # Parse source data
            parsed = await self._parse_data(source_data, source_format)

            # Convert to target format
            if target_format == DataFormat.JSON:
                return json.dumps(parsed, indent=2)
            elif target_format == DataFormat.CSV:
                if isinstance(parsed, list) and parsed:
                    output = io.StringIO()
                    writer = csv.DictWriter(output, fieldnames=parsed[0].keys())
                    writer.writeheader()
                    writer.writerows(parsed)
                    return output.getvalue()
                return ""
            elif target_format == DataFormat.XML:
                return self._dict_to_xml(parsed, "root")
            else:
                return str(parsed)

        except Exception as e:
            logger.error(f"Error converting format: {e}")
            return str(e)

    def _dict_to_xml(self, data: Any, root_name: str) -> str:
        """Convert dictionary to XML."""
        if isinstance(data, list):
            root = ET.Element(root_name)
            for item in data:
                child = ET.SubElement(root, "item")
                self._dict_to_xml_element(item, child)
        elif isinstance(data, dict):
            root = ET.Element(root_name)
            self._dict_to_xml_element(data, root)
        else:
            root = ET.Element(root_name)
            root.text = str(data)

        return ET.tostring(root, encoding="unicode")

    def _dict_to_xml_element(self, data: Dict[str, Any], parent: ET.Element) -> None:
        """Convert dictionary to XML element."""
        for key, value in data.items():
            child = ET.SubElement(parent, key)
            if isinstance(value, dict):
                self._dict_to_xml_element(value, child)
            elif isinstance(value, list):
                for item in value:
                    item_elem = ET.SubElement(child, "item")
                    if isinstance(item, dict):
                        self._dict_to_xml_element(item, item_elem)
                    else:
                        item_elem.text = str(item)
            else:
                child.text = str(value)


# Global validator instance
multi_format_validator = MultiFormatDataValidator()
