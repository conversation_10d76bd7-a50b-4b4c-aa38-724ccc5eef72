"""Parallel Validation Processing System.

This module provides high-performance parallel processing capabilities for validation
tasks with async processing, batch validation, load balancing, and resource optimization.
"""

import asyncio
import concurrent.futures
import multiprocessing
import os
import threading
import time
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

import psutil

from src.config.logging_config import logger
from src.core.validation.advanced_validators import AdvancedElectricalValidator
from src.core.validation.compatibility_matrix import CompatibilityMatrix
from src.core.validation.constraint_validator import ComplexConstraintValidator
from src.core.validation.intelligent_caching import C<PERSON><PERSON><PERSON>, validation_cache
from src.core.validation.standards_validator import StandardsValidator


class ProcessingStrategy(Enum):
    """Parallel processing strategies."""

    ASYNC = "async"
    THREAD_POOL = "thread_pool"
    PROCESS_POOL = "process_pool"
    HYBRID = "hybrid"
    DISTRIBUTED = "distributed"


class LoadBalancingStrategy(Enum):
    """Load balancing strategies."""

    ROUND_ROBIN = "round_robin"
    LEAST_CONNECTIONS = "least_connections"
    RESOURCE_BASED = "resource_based"
    PRIORITY_QUEUE = "priority_queue"


class TaskPriority(Enum):
    """Task priority levels."""

    CRITICAL = 1
    HIGH = 2
    NORMAL = 3
    LOW = 4
    BACKGROUND = 5


@dataclass
class ValidationTask:
    """Validation task for parallel processing."""

    task_id: str
    task_type: str
    data: Any
    parameters: Dict[str, Any]
    priority: TaskPriority
    created_at: datetime
    estimated_complexity: float  # 0.0 to 1.0
    dependencies: List[str]
    callback: Optional[Callable[..., Any]] = None


@dataclass
class ProcessingResult:
    """Result of parallel validation processing."""

    task_id: str
    result: Any
    processing_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float
    success: bool
    error: Optional[str]
    processed_at: datetime


@dataclass
class PerformanceMetrics:
    """Performance metrics for parallel processing."""

    total_tasks: int
    completed_tasks: int
    failed_tasks: int
    average_processing_time_ms: float
    total_memory_usage_mb: float
    peak_cpu_usage_percent: float
    queue_length: int
    throughput_tasks_per_second: float


class ParallelValidationProcessor:
    """High-performance parallel validation processing system."""

    def __init__(
        self,
        max_workers: Optional[int] = None,
        max_memory_mb: int = 2048,
        processing_strategy: ProcessingStrategy = ProcessingStrategy.HYBRID,
        load_balancing: LoadBalancingStrategy = LoadBalancingStrategy.RESOURCE_BASED,
    ):
        self.max_workers = max_workers or min(32, multiprocessing.cpu_count() * 2)
        self.max_memory_mb = max_memory_mb
        self.processing_strategy = processing_strategy
        self.load_balancing = load_balancing

        # Thread pool for CPU-bound tasks
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(
            max_workers=self.max_workers
        )

        # Process pool for heavy computation
        self.process_pool = concurrent.futures.ProcessPoolExecutor(
            max_workers=max(1, multiprocessing.cpu_count() - 1)
        )

        # Async task queue
        self.task_queue: asyncio.Queue[ValidationTask] = asyncio.Queue()
        self.results_queue: asyncio.Queue[ProcessingResult] = asyncio.Queue()

        # Task tracking
        self.active_tasks: Dict[str, ValidationTask] = {}
        self.completed_tasks: Dict[str, ProcessingResult] = {}
        self.failed_tasks: Dict[str, str] = {}

        # Resource monitoring
        self.resource_monitor = ResourceMonitor()
        self.load_balancer = LoadBalancer(self.load_balancing)

        # Validation components
        self.electrical_validator = AdvancedElectricalValidator()
        self.compatibility_matrix = CompatibilityMatrix()
        self.standards_validator = StandardsValidator()
        self.constraint_validator = ComplexConstraintValidator()

        # Processing statistics
        self.stats = ProcessingStats()

        # Control flags
        self.is_running = False
        self.shutdown_event = asyncio.Event()

        logger.info(
            f"Initialized ParallelValidationProcessor with {self.max_workers} workers"
        )

    async def start(self) -> None:
        """Start the parallel processing system."""
        if self.is_running:
            return

        self.is_running = True
        self.shutdown_event.clear()

        # Start worker coroutines
        asyncio.create_task(self._task_scheduler())
        asyncio.create_task(self._resource_monitor())
        asyncio.create_task(self._result_processor())

        logger.info("Parallel validation processor started")

    async def stop(self) -> None:
        """Stop the parallel processing system."""
        if not self.is_running:
            return

        self.is_running = False
        self.shutdown_event.set()

        # Shutdown executors
        self.thread_pool.shutdown(wait=True)
        self.process_pool.shutdown(wait=True)

        logger.info("Parallel validation processor stopped")

    async def submit_validation_task(
        self,
        task_type: str,
        data: Any,
        parameters: Dict[str, Any],
        priority: TaskPriority = TaskPriority.NORMAL,
        callback: Optional[Callable[..., Any]] = None,
    ) -> str:
        """Submit a validation task for parallel processing."""
        task_id = f"val_{int(time.time() * 1000)}_{hash(str(data)) % 10000}"

        # Estimate complexity
        complexity = self._estimate_complexity(task_type, data, parameters)

        task = ValidationTask(
            task_id=task_id,
            task_type=task_type,
            data=data,
            parameters=parameters,
            priority=priority,
            created_at=datetime.utcnow(),
            estimated_complexity=complexity,
            dependencies=parameters.get("dependencies", []),
            callback=callback,
        )

        # Add to queue
        await self.task_queue.put(task)
        self.active_tasks[task_id] = task

        logger.debug(f"Submitted validation task: {task_id} ({task_type})")
        return task_id

    async def submit_batch_validation(
        self,
        task_type: str,
        data_list: List[Any],
        parameters: Dict[str, Any],
        priority: TaskPriority = TaskPriority.NORMAL,
    ) -> List[str]:
        """Submit multiple validation tasks as a batch."""
        task_ids = []

        for data in data_list:
            task_id = await self.submit_validation_task(
                task_type, data, parameters, priority
            )
            task_ids.append(task_id)

        logger.info(f"Submitted batch of {len(task_ids)} validation tasks")
        return task_ids

    async def get_task_result(
        self, task_id: str, timeout: float = 30.0
    ) -> Optional[ProcessingResult]:
        """Get result for a specific task."""
        start_time = time.time()

        while time.time() - start_time < timeout:
            if task_id in self.completed_tasks:
                return self.completed_tasks[task_id]

            if task_id in self.failed_tasks:
                return ProcessingResult(
                    task_id=task_id,
                    result=None,
                    processing_time_ms=0,
                    memory_usage_mb=0,
                    cpu_usage_percent=0,
                    success=False,
                    error=self.failed_tasks[task_id],
                    processed_at=datetime.utcnow(),
                )

            await asyncio.sleep(0.1)

        return None

    async def get_performance_metrics(self) -> PerformanceMetrics:
        """Get current performance metrics."""
        return PerformanceMetrics(
            total_tasks=len(self.active_tasks)
            + len(self.completed_tasks)
            + len(self.failed_tasks),
            completed_tasks=len(self.completed_tasks),
            failed_tasks=len(self.failed_tasks),
            average_processing_time_ms=self.stats.get_average_processing_time(),
            total_memory_usage_mb=self.resource_monitor.get_current_memory_usage(),
            peak_cpu_usage_percent=self.resource_monitor.get_peak_cpu_usage(),
            queue_length=self.task_queue.qsize(),
            throughput_tasks_per_second=self.stats.get_throughput(),
        )

    async def _task_scheduler(self) -> None:
        """Main task scheduler coroutine."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Get task with timeout
                task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)

                # Check dependencies
                if not await self._check_dependencies(task):
                    # Re-queue task for later processing
                    await asyncio.sleep(1)
                    await self.task_queue.put(task)
                    continue

                # Select processing strategy
                processing_method = self._select_processing_method(task)

                # Process task
                asyncio.create_task(self._process_task(task, processing_method))

            except asyncio.TimeoutError:
                continue
            except Exception as e:
                logger.error(f"Error in task scheduler: {e}")

    async def _process_task(
        self, task: ValidationTask, processing_method: ProcessingStrategy
    ) -> None:
        """Process a single validation task."""
        start_time = time.time()

        try:
            # Check resource availability
            if not await self._check_resource_availability(task):
                # Re-queue task
                await self.task_queue.put(task)
                return

            # Execute based on processing method
            if processing_method == ProcessingStrategy.ASYNC:
                result = await self._execute_async(task)
            elif processing_method == ProcessingStrategy.THREAD_POOL:
                result = await self._execute_thread_pool(task)
            elif processing_method == ProcessingStrategy.PROCESS_POOL:
                result = await self._execute_process_pool(task)
            elif processing_method == ProcessingStrategy.HYBRID:
                result = await self._execute_hybrid(task)
            else:
                result = await self._execute_async(task)

            # Store result
            processing_time = (time.time() - start_time) * 1000
            memory_usage = self.resource_monitor.get_current_memory_usage()
            cpu_usage = self.resource_monitor.get_current_cpu_usage()

            processing_result = ProcessingResult(
                task_id=task.task_id,
                result=result,
                processing_time_ms=processing_time,
                memory_usage_mb=memory_usage,
                cpu_usage_percent=cpu_usage,
                success=True,
                error=None,
                processed_at=datetime.utcnow(),
            )

            self.completed_tasks[task.task_id] = processing_result
            self.stats.record_completion(processing_result)

            # Execute callback if provided
            if task.callback:
                try:
                    if asyncio.iscoroutinefunction(task.callback):
                        await task.callback(processing_result)
                    else:
                        task.callback(processing_result)
                except Exception as e:
                    logger.error(f"Error in task callback: {e}")

            logger.debug(f"Completed task {task.task_id} in {processing_time:.2f}ms")

        except Exception as e:
            error_msg = str(e)
            self.failed_tasks[task.task_id] = error_msg

            processing_result = ProcessingResult(
                task_id=task.task_id,
                result=None,
                processing_time_ms=(time.time() - start_time) * 1000,
                memory_usage_mb=0,
                cpu_usage_percent=0,
                success=False,
                error=error_msg,
                processed_at=datetime.utcnow(),
            )

            logger.error(f"Failed task {task.task_id}: {error_msg}")

    async def _execute_async(self, task: ValidationTask) -> Any:
        """Execute task using async coroutines."""
        task_map = {
            "electrical_validation": self._async_electrical_validation,
            "compatibility_check": self._async_compatibility_check,
            "standards_compliance": self._async_standards_compliance,
            "constraint_validation": self._async_constraint_validation,
            "legacy_migration": self._async_legacy_migration,
        }

        handler = task_map.get(task.task_type)
        if handler:
            return await handler(task.data, task.parameters)
        else:
            raise ValueError(f"Unknown task type: {task.task_type}")

    async def _execute_thread_pool(self, task: ValidationTask) -> Any:
        """Execute task using thread pool."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.thread_pool,
            self._sync_validation_worker,
            task.task_type,
            task.data,
            task.parameters,
        )

    async def _execute_process_pool(self, task: ValidationTask) -> Any:
        """Execute task using process pool."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            self.process_pool,
            self._sync_validation_worker,
            task.task_type,
            task.data,
            task.parameters,
        )

    async def _execute_hybrid(self, task: ValidationTask) -> Any:
        """Execute task using hybrid strategy."""
        # Use async for I/O bound, thread pool for CPU bound
        if task.estimated_complexity < 0.3:
            return await self._execute_async(task)
        elif task.estimated_complexity < 0.7:
            return await self._execute_thread_pool(task)
        else:
            return await self._execute_process_pool(task)

    async def _async_electrical_validation(
        self, data: Any, parameters: Dict[str, Any]
    ) -> Any:
        """Async electrical validation."""
        return await self.electrical_validator.validate_parameters_async(
            data, parameters
        )

    async def _async_compatibility_check(
        self, data: Any, parameters: Dict[str, Any]
    ) -> Any:
        """Async compatibility check."""
        return await self.compatibility_matrix.validate_compatibility_async(
            data, parameters.get("context", {})
        )

    async def _async_standards_compliance(
        self, data: Any, parameters: Dict[str, Any]
    ) -> Any:
        """Async standards compliance."""
        return await self.standards_validator.validate_standards_compliance(
            data,
            parameters.get("standards", []),
            parameters.get("region", "global"),
            parameters.get("application_type", "industrial"),
        )

    async def _async_constraint_validation(
        self, data: Any, parameters: Dict[str, Any]
    ) -> Any:
        """Async constraint validation."""
        return await self.constraint_validator.validate_constraints_async(
            data, parameters.get("rules", [])
        )

    async def _async_legacy_migration(
        self, data: Any, parameters: Dict[str, Any]
    ) -> Any:
        """Async legacy migration validation."""
        from src.core.validation.legacy_migration_validator import (
            LegacyFormatType,
            legacy_migration_validator,
        )

        source_format_val = parameters.get("source_format")
        source_format = LegacyFormatType.PROPRIETARY  # Default to auto-detection
        if isinstance(source_format_val, str):
            try:
                source_format = LegacyFormatType(source_format_val)
            except ValueError:
                logger.warning(
                    f"Invalid legacy format string: '{source_format_val}'. Defaulting to auto-detection."
                )
        elif isinstance(source_format_val, LegacyFormatType):
            source_format = source_format_val

        return await legacy_migration_validator.validate_legacy_migration(
            data,
            source_format,
            parameters.get("target_format", "json"),
        )

    def _sync_validation_worker(
        self, task_type: str, data: Any, parameters: Dict[str, Any]
    ) -> Any:
        """Synchronous validation worker for thread/process pools."""
        # This worker runs in a separate thread/process, so we need a new event loop.
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            if task_type == "electrical_validation":
                electrical_validator = AdvancedElectricalValidator()
                return loop.run_until_complete(
                    electrical_validator.validate_parameters_async(data, parameters)
                )
            elif task_type == "compatibility_check":
                compatibility_validator = CompatibilityMatrix()
                return loop.run_until_complete(
                    compatibility_validator.validate_compatibility_async(
                        data, parameters.get("context", {})
                    )
                )
            elif task_type == "standards_compliance":
                standards_validator = StandardsValidator()
                return loop.run_until_complete(
                    standards_validator.validate_standards_compliance(
                        data,
                        parameters.get("standards", []),
                        parameters.get("region", "global"),
                        parameters.get("application_type", "industrial"),
                    )
                )
            elif task_type == "constraint_validation":
                constraint_validator = ComplexConstraintValidator()
                return loop.run_until_complete(
                    constraint_validator.validate_constraints_async(
                        data, parameters.get("rules", [])
                    )
                )
            elif task_type == "legacy_migration":
                from src.core.validation.legacy_migration_validator import (
                    LegacyFormatType,
                    legacy_migration_validator,
                )

                source_format_val = parameters.get("source_format")
                source_format = (
                    LegacyFormatType.PROPRIETARY
                )  # Default to auto-detection
                if isinstance(source_format_val, str):
                    try:
                        source_format = LegacyFormatType(source_format_val)
                    except ValueError:
                        logger.warning(
                            f"Invalid legacy format string: '{source_format_val}'. Defaulting to auto-detection."
                        )
                elif isinstance(source_format_val, LegacyFormatType):
                    source_format = source_format_val

                return loop.run_until_complete(
                    legacy_migration_validator.validate_legacy_migration(
                        data,
                        source_format,
                        parameters.get("target_format", "json"),
                    )
                )
        finally:
            loop.close()

        logger.warning(f"Unknown task type in sync worker: {task_type}")
        return None

    def _select_processing_method(self, task: ValidationTask) -> ProcessingStrategy:
        """Select optimal processing method based on task characteristics."""
        # Check system resources
        cpu_usage = psutil.cpu_percent()
        memory_usage = psutil.virtual_memory().percent

        # Resource-based selection
        if cpu_usage > 80 or memory_usage > 80:
            return ProcessingStrategy.ASYNC
        elif task.estimated_complexity > 0.8:
            return ProcessingStrategy.PROCESS_POOL
        elif task.estimated_complexity > 0.5:
            return ProcessingStrategy.THREAD_POOL
        else:
            return ProcessingStrategy.ASYNC

    def _estimate_complexity(
        self, task_type: str, data: Any, parameters: Dict[str, Any]
    ) -> float:
        """Estimate task complexity (0.0 to 1.0)."""
        base_complexity = 0.5

        # Data size factor
        if isinstance(data, list):
            data_size = len(data)
            base_complexity += min(0.3, data_size / 1000)
        elif isinstance(data, dict):
            data_size = len(str(data))
            base_complexity += min(0.2, data_size / 10000)

        # Task type factor
        type_factors = {
            "electrical_validation": 0.1,
            "compatibility_check": 0.3,
            "standards_compliance": 0.4,
            "constraint_validation": 0.5,
            "legacy_migration": 0.6,
        }

        base_complexity += type_factors.get(task_type, 0.2)

        # Parameter complexity
        if parameters.get("deep_analysis", False):
            base_complexity += 0.2

        return min(1.0, base_complexity)

    async def _check_dependencies(self, task: ValidationTask) -> bool:
        """Check if task dependencies are satisfied."""
        for dep_id in task.dependencies:
            if dep_id not in self.completed_tasks:
                return False
        return True

    async def _check_resource_availability(self, task: ValidationTask) -> bool:
        """Check if system has resources to process task."""
        memory_usage = psutil.virtual_memory().percent
        cpu_usage = psutil.cpu_percent()

        return memory_usage < 90 and cpu_usage < 90

    async def _resource_monitor(self) -> None:
        """Monitor system resources."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                self.resource_monitor.update()
                await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"Error in resource monitor: {e}")

    async def _result_processor(self) -> None:
        """Process and distribute results."""
        while self.is_running and not self.shutdown_event.is_set():
            try:
                # Process any available results
                await asyncio.sleep(0.1)
            except Exception as e:
                logger.error(f"Error in result processor: {e}")


class ResourceMonitor:
    """System resource monitoring."""

    def __init__(self) -> None:
        self.metrics_history: List[Dict[str, Any]] = []
        self.peak_memory = 0.0
        self.peak_cpu = 0.0

    def update(self) -> None:
        """Update resource metrics."""
        memory = psutil.virtual_memory()
        cpu = psutil.cpu_percent()

        self.peak_memory = max(self.peak_memory, memory.percent)
        self.peak_cpu = max(self.peak_cpu, cpu)

        self.metrics_history.append(
            {
                "timestamp": datetime.utcnow(),
                "memory_percent": memory.percent,
                "cpu_percent": cpu,
                "available_memory_mb": memory.available / 1024 / 1024,
            }
        )

        # Keep only last 100 records
        if len(self.metrics_history) > 100:
            self.metrics_history.pop(0)

    def get_current_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        return psutil.virtual_memory().used / 1024 / 1024

    def get_current_cpu_usage(self) -> float:
        """Get current CPU usage percentage."""
        return psutil.cpu_percent()

    def get_peak_cpu_usage(self) -> float:
        """Get peak CPU usage."""
        return self.peak_cpu


class LoadBalancer:
    """Load balancing for validation tasks."""

    def __init__(self, strategy: LoadBalancingStrategy):
        self.strategy = strategy
        self.worker_loads: Dict[str, int] = {}

    def select_worker(self, task: ValidationTask) -> str:
        """Select optimal worker for task."""
        if self.strategy == LoadBalancingStrategy.ROUND_ROBIN:
            return self._round_robin_selection()
        elif self.strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            return self._least_connections_selection()
        elif self.strategy == LoadBalancingStrategy.RESOURCE_BASED:
            return self._resource_based_selection(task)
        else:
            return "default"

    def _round_robin_selection(self) -> str:
        """Round-robin worker selection."""
        # Simple round-robin implementation
        workers = list(self.worker_loads.keys()) or ["default"]
        return workers[0] if workers else "default"

    def _least_connections_selection(self) -> str:
        """Select worker with least connections."""
        if not self.worker_loads:
            return "default"

        return str(min(self.worker_loads.items(), key=lambda x: x[1])[0])

    def _resource_based_selection(self, task: ValidationTask) -> str:
        """Select worker based on resource availability."""
        cpu_usage = psutil.cpu_percent()
        memory_usage = psutil.virtual_memory().percent

        # Simple resource-based decision
        if cpu_usage < 50 and memory_usage < 70:
            return "high_performance"
        elif cpu_usage < 80 and memory_usage < 85:
            return "medium_performance"
        else:
            return "low_priority"


class ProcessingStats:
    """Processing statistics tracking."""

    def __init__(self) -> None:
        self.processing_times: List[float] = []
        self.task_counts: Dict[str, int] = {}
        self.start_time = datetime.utcnow()

    def record_completion(self, result: ProcessingResult) -> None:
        """Record task completion."""
        self.processing_times.append(result.processing_time_ms)

        # Track by task type
        if result.task_id not in self.task_counts:
            self.task_counts[result.task_id] = 0
        self.task_counts[result.task_id] += 1

    def get_average_processing_time(self) -> float:
        """Get average processing time."""
        if not self.processing_times:
            return 0.0
        return float(sum(self.processing_times) / len(self.processing_times))

    def get_throughput(self) -> float:
        """Get throughput in tasks per second."""
        elapsed = (datetime.utcnow() - self.start_time).total_seconds()
        if elapsed == 0:
            return 0.0
        return len(self.processing_times) / elapsed


# Global processor instance
parallel_processor = ParallelValidationProcessor()
