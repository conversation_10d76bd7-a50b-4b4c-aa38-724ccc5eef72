"""Advanced JSON Schema Validation with JSON Path Queries.

This module provides sophisticated JSON schema validation with support for complex
nested data structures, JSON path queries, and dynamic schema generation based on
electrical engineering requirements.
"""

import asyncio
import json
import re
from typing import Any, Dict, List, Optional, Set, Tuple, Union, Callable
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from jsonpath_ng import parse as jsonpath_parse
from jsonpath_ng.ext import parse as jsonpath_ext_parse

from src.config.logging_config import logger


class ValidationComplexity(Enum):
    """Complexity levels for schema validation."""

    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"
    CUSTOM = "custom"


class SchemaType(Enum):
    """Types of JSON schemas supported."""

    DRAFT_07 = "http://json-schema.org/draft-07/schema#"
    DRAFT_2019_09 = "https://json-schema.org/draft/2019-09/schema"
    DRAFT_2020_12 = "https://json-schema.org/draft/2020-12/schema"
    CUSTOM_ELECTRICAL = "custom:electrical"


@dataclass
class ValidationError:
    """Detailed validation error information."""

    path: str
    message: str
    value: Any
    expected_type: str
    constraint: str
    severity: str
    suggestion: str
    json_path: str


@dataclass
class ValidationResult:
    """Complete validation result with JSON path support."""

    is_valid: bool
    errors: List[ValidationError]
    warnings: List[str]
    data_quality_score: float  # 0.0 to 1.0
    processed_paths: List[str]
    validation_time_ms: float
    schema_version: str


@dataclass
class JsonPathQuery:
    """JSON path query for data extraction and validation."""

    path: str
    query: Any
    expected_type: str
    constraints: Dict[str, Any]
    validation_function: Optional[Callable[..., Any]] = None


class AdvancedJsonSchemaValidator:
    """Advanced JSON schema validator with JSON path support."""

    def __init__(self) -> None:
        self.custom_validators: Dict[str, Callable[..., Any]] = {}
        self.schema_cache: Dict[str, Dict[str, Any]] = {}
        self.electrical_schemas: Dict[str, Dict[str, Any]] = {}
        self.json_path_cache: Dict[str, Any] = {}

        self._initialize_electrical_schemas()
        self._register_custom_validators()

    def _initialize_electrical_schemas(self) -> None:
        """Initialize electrical-specific JSON schemas."""
        # Component schema
        self.electrical_schemas["component"] = {
            "$schema": SchemaType.DRAFT_2020_12.value,
            "type": "object",
            "required": ["id", "type", "voltage_rating", "current_rating"],
            "properties": {
                "id": {"type": "string", "pattern": "^[a-zA-Z0-9_-]+$"},
                "type": {
                    "type": "string",
                    "enum": ["breaker", "transformer", "motor", "cable", "switch"],
                },
                "voltage_rating": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 1000000,
                    "description": "Rated voltage in volts",
                },
                "current_rating": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 10000,
                    "description": "Rated current in amperes",
                },
                "power_rating": {
                    "type": ["number", "null"],
                    "minimum": 0,
                    "maximum": 10000000,
                    "description": "Rated power in watts",
                },
                "frequency_rating": {
                    "type": "number",
                    "enum": [50, 60, 400],
                    "description": "Rated frequency in Hz",
                },
                "environmental_rating": {
                    "type": "object",
                    "properties": {
                        "ip_rating": {"type": "string", "pattern": "^IP[0-9]{2}$"},
                        "temperature_range": {
                            "type": "object",
                            "properties": {
                                "min": {
                                    "type": "number",
                                    "minimum": -50,
                                    "maximum": 200,
                                },
                                "max": {
                                    "type": "number",
                                    "minimum": -50,
                                    "maximum": 200,
                                },
                            },
                            "required": ["min", "max"],
                        },
                    },
                },
                "compliance_standards": {
                    "type": "array",
                    "items": {"type": "string"},
                    "minItems": 1,
                },
            },
            "allOf": [
                {
                    "if": {"properties": {"type": {"const": "transformer"}}},
                    "then": {"required": ["power_rating", "voltage_ratio"]},
                },
                {
                    "if": {"properties": {"type": {"const": "motor"}}},
                    "then": {"required": ["power_rating", "efficiency"]},
                },
            ],
        }

        # Project schema
        self.electrical_schemas["project"] = {
            "$schema": SchemaType.DRAFT_2020_12.value,
            "type": "object",
            "required": ["id", "name", "system_voltage", "system_frequency"],
            "properties": {
                "id": {"type": "string"},
                "name": {"type": "string", "minLength": 3, "maxLength": 100},
                "description": {"type": "string", "maxLength": 1000},
                "system_voltage": {
                    "type": "number",
                    "minimum": 12,
                    "maximum": 1000000,
                    "description": "System voltage in volts",
                },
                "system_frequency": {
                    "type": "number",
                    "enum": [50, 60],
                    "description": "System frequency in Hz",
                },
                "total_load_current": {
                    "type": "number",
                    "minimum": 0,
                    "maximum": 10000,
                    "description": "Total load current in amperes",
                },
                "environmental_conditions": {
                    "type": "object",
                    "properties": {
                        "location_type": {"enum": ["indoor", "outdoor", "hazardous"]},
                        "ambient_temperature": {
                            "type": "number",
                            "minimum": -40,
                            "maximum": 60,
                        },
                        "humidity": {"type": "number", "minimum": 0, "maximum": 100},
                        "altitude": {"type": "number", "minimum": 0, "maximum": 5000},
                    },
                },
                "applicable_standards": {
                    "type": "array",
                    "items": {"type": "string"},
                    "minItems": 1,
                },
            },
        }

    def _register_custom_validators(self) -> None:
        """Register custom electrical validators."""
        self.custom_validators.update(
            {
                "voltage_compatibility": self._validate_voltage_compatibility,
                "current_adequacy": self._validate_current_adequacy,
                "power_factor_range": self._validate_power_factor_range,
                "electrical_clearance": self._validate_electrical_clearance,
                "temperature_derating": self._validate_temperature_derating,
                "harmonic_distortion": self._validate_harmonic_distortion,
                "arc_flash_category": self._validate_arc_flash_category,
                "grounding_impedance": self._validate_grounding_impedance,
                "ip_rating_adequacy": self._validate_ip_rating_adequacy,
                "frequency_tolerance": self._validate_frequency_tolerance,
                "cable_ampacity": self._validate_cable_ampacity,
                "motor_efficiency": self._validate_motor_efficiency,
                "transformer_impedance": self._validate_transformer_impedance,
                "protection_coordination": self._validate_protection_coordination,
                "load_balance": self._validate_load_balance,
                "voltage_regulation": self._validate_voltage_regulation,
                "short_circuit_rating": self._validate_short_circuit_rating,
                "insulation_resistance": self._validate_insulation_resistance,
                "contact_rating": self._validate_contact_rating,
                "switching_capacity": self._validate_switching_capacity,
                "surge_protection": self._validate_surge_protection,
                "temporal_sequence": self._validate_temporal_sequence,
            }
        )

    def _validate_current_adequacy(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate current adequacy for electrical components."""
        if not isinstance(value, (int, float)):
            return False, "Current must be a number"

        required_current = constraints.get("required_current", 0)
        if value < required_current:
            return (
                False,
                f"Current {value}A insufficient for required {required_current}A",
            )

        safety_margin = constraints.get("safety_margin", 1.25)
        if value < required_current * safety_margin:
            return False, f"Current {value}A below recommended safety margin"

        return True, ""

    def _validate_voltage_compatibility(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate voltage compatibility between project and component."""
        if not isinstance(value, (int, float)):
            return False, "Voltage must be a number"

        project_voltage = constraints.get("project_voltage", 0)
        tolerance = constraints.get("tolerance", 0.1)  # 10% default tolerance

        if project_voltage == 0:
            return True, ""

        min_voltage = project_voltage * (1 - tolerance)
        max_voltage = project_voltage * (1 + tolerance)

        if value < min_voltage or value > max_voltage:
            return (
                False,
                f"Voltage {value}V outside acceptable range {min_voltage}-{max_voltage}V",
            )

        return True, ""

    async def validate_with_schema(
        self,
        data: Dict[str, Any],
        schema_name: str,
        complexity: ValidationComplexity = ValidationComplexity.ADVANCED,
    ) -> ValidationResult:
        """Validate data against a specific schema."""
        start_time = datetime.utcnow()

        try:
            schema = self.electrical_schemas.get(schema_name)
            if not schema:
                return ValidationResult(
                    is_valid=False,
                    errors=[
                        ValidationError(
                            path="schema",
                            message=f"Unknown schema: {schema_name}",
                            value=None,
                            expected_type="valid schema",
                            constraint="schema must exist",
                            severity="error",
                            suggestion="Use a valid schema name",
                            json_path="$",
                        )
                    ],
                    warnings=[],
                    data_quality_score=0.0,
                    processed_paths=[],
                    validation_time_ms=0.0,
                    schema_version=schema_name,
                )

            # Perform basic schema validation
            basic_result = await self._basic_schema_validation(data, schema)

            if complexity == ValidationComplexity.BASIC:
                return basic_result

            # Perform advanced validation with JSON path queries
            advanced_result = await self._advanced_validation(data, schema_name)

            # Combine results
            combined_errors = basic_result.errors + advanced_result.errors
            combined_warnings = basic_result.warnings + advanced_result.warnings

            # Calculate quality score
            quality_score = self._calculate_data_quality_score(
                data, combined_errors, combined_warnings
            )

            validation_time = (datetime.utcnow() - start_time).total_seconds() * 1000

            return ValidationResult(
                is_valid=len(combined_errors) == 0,
                errors=combined_errors,
                warnings=combined_warnings,
                data_quality_score=quality_score,
                processed_paths=advanced_result.processed_paths,
                validation_time_ms=validation_time,
                schema_version=schema_name,
            )

        except Exception as e:
            logger.error(f"Error in schema validation: {e}")
            return ValidationResult(
                is_valid=False,
                errors=[
                    ValidationError(
                        path="validation",
                        message=f"Validation error: {str(e)}",
                        value=None,
                        expected_type="valid validation",
                        constraint="no validation errors",
                        severity="error",
                        suggestion="Check validation configuration",
                        json_path="$",
                    )
                ],
                warnings=[],
                data_quality_score=0.0,
                processed_paths=[],
                validation_time_ms=0.0,
                schema_version=schema_name,
            )

    async def _basic_schema_validation(
        self, data: Dict[str, Any], schema: Dict[str, Any]
    ) -> ValidationResult:
        """Perform basic JSON schema validation."""
        # This is a simplified implementation - in production, use jsonschema library
        errors = []
        warnings: List[str] = []

        # Check required fields
        required_fields = schema.get("required", [])
        errors.extend(
            [
                ValidationError(
                    path=field,
                    message=f"Required field '{field}' is missing",
                    value=None,
                    expected_type="present field",
                    constraint="field must exist",
                    severity="error",
                    suggestion=f"Add required field '{field}'",
                    json_path=f"$.{field}",
                )
                for field in required_fields
                if field not in data
            ]
        )

        # Check property types
        properties = schema.get("properties", {})
        for prop_name, prop_schema in properties.items():
            if prop_name in data:
                prop_errors = self._validate_property_type(
                    data[prop_name], prop_schema, prop_name
                )
                errors.extend(prop_errors)

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            data_quality_score=1.0 - (len(errors) * 0.1),
            processed_paths=list(data.keys()),
            validation_time_ms=0.0,
            schema_version="basic",
        )

    def _validate_property_type(
        self, value: Any, schema: Dict[str, Any], path: str
    ) -> List[ValidationError]:
        """Validate a single property against its schema."""
        errors = []
        expected_type = schema.get("type")

        if expected_type:
            type_valid = self._check_type(value, expected_type)
            if not type_valid:
                errors.append(
                    ValidationError(
                        path=path,
                        message=f"Type mismatch: expected {expected_type}, got {type(value).__name__}",
                        value=value,
                        expected_type=expected_type,
                        constraint=f"type must be {expected_type}",
                        severity="error",
                        suggestion=f"Convert to {expected_type} type",
                        json_path=f"$.{path}",
                    )
                )

        # Check numeric constraints
        if isinstance(value, (int, float)):
            if "minimum" in schema and value < schema["minimum"]:
                errors.append(
                    ValidationError(
                        path=path,
                        message=f"Value {value} below minimum {schema['minimum']}",
                        value=value,
                        expected_type=str(type(value).__name__),
                        constraint=f"minimum: {schema['minimum']}",
                        severity="error",
                        suggestion=f"Increase value to at least {schema['minimum']}",
                        json_path=f"$.{path}",
                    )
                )

            if "maximum" in schema and value > schema["maximum"]:
                errors.append(
                    ValidationError(
                        path=path,
                        message=f"Value {value} above maximum {schema['maximum']}",
                        value=value,
                        expected_type=str(type(value).__name__),
                        constraint=f"maximum: {schema['maximum']}",
                        severity="error",
                        suggestion=f"Decrease value to at most {schema['maximum']}",
                        json_path=f"$.{path}",
                    )
                )

        # Check enum constraints
        if "enum" in schema:
            allowed_values = schema["enum"]
            if value not in allowed_values:
                errors.append(
                    ValidationError(
                        path=path,
                        message=f"Value '{value}' not in allowed values: {allowed_values}",
                        value=value,
                        expected_type=str(type(value).__name__),
                        constraint=f"enum: {allowed_values}",
                        severity="error",
                        suggestion=f"Use one of: {', '.join(map(str, allowed_values))}",
                        json_path=f"$.{path}",
                    )
                )

        return errors

    def _check_type(self, value: Any, expected_type: Union[str, List[str]]) -> bool:
        """Check if value matches expected type."""
        from typing import Type, Union as TypingUnion

        type_mapping: Dict[str, TypingUnion[Type[Any], tuple[Type[Any], ...]]] = {
            "string": str,
            "number": (int, float),
            "integer": int,
            "boolean": bool,
            "array": list,
            "object": dict,
            "null": type(None),
        }

        if isinstance(expected_type, list):
            return any(self._check_type(value, t) for t in expected_type)

        if expected_type in type_mapping:
            expected_class = type_mapping[expected_type]
            if isinstance(expected_class, tuple):
                return isinstance(value, expected_class)
            else:
                return isinstance(value, expected_class)

        return True

    async def _advanced_validation(
        self, data: Dict[str, Any], schema_name: str
    ) -> ValidationResult:
        """Perform advanced validation with JSON path queries."""
        errors = []
        warnings: List[str] = []
        processed_paths = []

        # Create JSON path queries based on schema
        queries = self._create_json_path_queries(schema_name)

        for query in queries:
            try:
                # Execute JSON path query
                matches = query.query.find(data)

                for match in matches:
                    path_str = str(match.full_path)
                    value = match.value
                    processed_paths.append(path_str)

                    # Apply custom validation
                    if query.validation_function:
                        is_valid, message = query.validation_function(
                            value, query.constraints
                        )
                        if not is_valid:
                            errors.append(
                                ValidationError(
                                    path=path_str,
                                    message=message,
                                    value=value,
                                    expected_type=query.expected_type,
                                    constraint=str(query.constraints),
                                    severity="error",
                                    suggestion=self._get_suggestion_for_error(message),
                                    json_path=query.path,
                                )
                            )

            except Exception as e:
                warnings.append(f"JSON path query error: {e}")

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            data_quality_score=0.0,
            processed_paths=processed_paths,
            validation_time_ms=0.0,
            schema_version=schema_name,
        )

    def _create_json_path_queries(self, schema_name: str) -> List[JsonPathQuery]:
        """Create JSON path queries for advanced validation."""
        queries = []

        if schema_name == "component":
            queries.extend(
                [
                    JsonPathQuery(
                        path="$.voltage_rating",
                        query=jsonpath_parse("$.voltage_rating"),
                        expected_type="number",
                        constraints={"min": 12, "max": 1000000},
                        validation_function=self._validate_voltage_range,
                    ),
                    JsonPathQuery(
                        path="$.current_rating",
                        query=jsonpath_parse("$.current_rating"),
                        expected_type="number",
                        constraints={"min": 0.1, "max": 10000},
                        validation_function=self._validate_current_range,
                    ),
                    JsonPathQuery(
                        path="$.power_rating",
                        query=jsonpath_parse("$.power_rating"),
                        expected_type="number",
                        constraints={"min": 0, "max": 10000000},
                        validation_function=self._validate_power_range,
                    ),
                    JsonPathQuery(
                        path="$.environmental_rating.ip_rating",
                        query=jsonpath_parse("$.environmental_rating.ip_rating"),
                        expected_type="string",
                        constraints={"pattern": "^IP[0-9]{2}$"},
                        validation_function=self._validate_ip_rating,
                    ),
                    JsonPathQuery(
                        path="$.compliance_standards[*]",
                        query=jsonpath_parse("$.compliance_standards[*]"),
                        expected_type="string",
                        constraints={"min_length": 2},
                        validation_function=self._validate_standard_name,
                    ),
                    JsonPathQuery(
                        path="$.environmental_rating.temperature_range",
                        query=jsonpath_parse(
                            "$.environmental_rating.temperature_range"
                        ),
                        expected_type="object",
                        constraints={"min": -50, "max": 200},
                        validation_function=self._validate_temperature_range,
                    ),
                ]
            )

        elif schema_name == "project":
            queries.extend(
                [
                    JsonPathQuery(
                        path="$.system_voltage",
                        query=jsonpath_parse("$.system_voltage"),
                        expected_type="number",
                        constraints={"min": 12, "max": 1000000},
                        validation_function=self._validate_system_voltage,
                    ),
                    JsonPathQuery(
                        path="$.system_frequency",
                        query=jsonpath_parse("$.system_frequency"),
                        expected_type="number",
                        constraints={"values": [50, 60]},
                        validation_function=self._validate_frequency,
                    ),
                    JsonPathQuery(
                        path="$.environmental_conditions.location_type",
                        query=jsonpath_parse(
                            "$.environmental_conditions.location_type"
                        ),
                        expected_type="string",
                        constraints={"values": ["indoor", "outdoor", "hazardous"]},
                        validation_function=self._validate_location_type,
                    ),
                    JsonPathQuery(
                        path="$.applicable_standards[*]",
                        query=jsonpath_parse("$.applicable_standards[*]"),
                        expected_type="string",
                        constraints={"min_length": 3},
                        validation_function=self._validate_applicable_standards,
                    ),
                ]
            )

        return queries

    def _validate_voltage_range(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate voltage range."""
        if not isinstance(value, (int, float)):
            return False, "Voltage must be a number"

        if value < constraints["min"] or value > constraints["max"]:
            return (
                False,
                f"Voltage {value}V outside valid range {constraints['min']}-{constraints['max']}V",
            )

        return True, ""

    def _validate_current_range(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate current range."""
        if not isinstance(value, (int, float)):
            return False, "Current must be a number"

        if value < constraints["min"] or value > constraints["max"]:
            return (
                False,
                f"Current {value}A outside valid range {constraints['min']}-{constraints['max']}A",
            )

        return True, ""

    def _validate_power_range(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate power range."""
        if not isinstance(value, (int, float)):
            return False, "Power must be a number"

        if value < constraints["min"] or value > constraints["max"]:
            return (
                False,
                f"Power {value}W outside valid range {constraints['min']}-{constraints['max']}W",
            )

        return True, ""

    def _validate_power_factor_range(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate power factor range."""
        if not isinstance(value, (int, float)):
            return False, "Power factor must be a number"

        min_pf = constraints.get("min", 0.0)
        max_pf = constraints.get("max", 1.0)

        if value < min_pf or value > max_pf:
            return False, f"Power factor {value} outside valid range {min_pf}-{max_pf}"

        return True, ""

    def _validate_ip_rating(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate IP rating format."""
        if not isinstance(value, str):
            return False, "IP rating must be a string"

        pattern = constraints["pattern"]
        if not re.match(pattern, value):
            return False, f"Invalid IP rating format: {value}"

        return True, ""

    def _validate_standard_name(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate standard name."""
        if not isinstance(value, str):
            return False, "Standard name must be a string"

        if len(value) < constraints["min_length"]:
            return False, f"Standard name too short: {value}"

        return True, ""

    def _validate_temperature_range(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate temperature range."""
        if not isinstance(value, dict):
            return False, "Temperature range must be an object"

        min_temp = value.get("min")
        max_temp = value.get("max")

        if min_temp is None or max_temp is None:
            return False, "Temperature range must have min and max values"

        if min_temp > max_temp:
            return False, "Minimum temperature must be less than maximum temperature"

        if min_temp < constraints["min"] or max_temp > constraints["max"]:
            return (
                False,
                f"Temperature range {min_temp}°C to {max_temp}°C outside valid range",
            )

        return True, ""

    def _validate_system_voltage(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate system voltage."""
        return self._validate_voltage_range(value, constraints)

    def _validate_frequency(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate frequency."""
        if not isinstance(value, (int, float)):
            return False, "Frequency must be a number"

        if value not in constraints["values"]:
            return (
                False,
                f"Frequency {value}Hz not in allowed values {constraints['values']}",
            )

        return True, ""

    def _validate_location_type(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate location type."""
        if not isinstance(value, str):
            return False, "Location type must be a string"

        if value not in constraints["values"]:
            return (
                False,
                f"Location type '{value}' not in allowed values {constraints['values']}",
            )

        return True, ""

    def _validate_applicable_standards(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate applicable standards."""
        return self._validate_standard_name(value, constraints)

    def _calculate_data_quality_score(
        self, data: Dict[str, Any], errors: List[ValidationError], warnings: List[str]
    ) -> float:
        """Calculate overall data quality score."""
        if not errors and not warnings:
            return 1.0

        # Higher penalty for critical errors like missing required fields
        critical_errors = sum(
            1
            for error in errors
            if "required" in error.message.lower() or "missing" in error.message.lower()
        )
        regular_errors = len(errors) - critical_errors

        critical_penalty = (
            critical_errors * 0.6
        )  # Heavy penalty for required field violations
        error_penalty = regular_errors * 0.2
        warning_penalty = len(warnings) * 0.05

        score = max(0.0, 1.0 - critical_penalty - error_penalty - warning_penalty)
        return score

    def _get_suggestion_for_error(self, error_message: str) -> str:
        """Get suggestion for fixing validation error."""
        suggestions = {
            "voltage": "Check voltage rating and ensure it's within standard ranges",
            "current": "Verify current rating meets application requirements",
            "power": "Confirm power rating is appropriate for the load",
            "temperature": "Review temperature ratings and environmental conditions",
            "ip": "Ensure IP rating matches installation environment",
            "frequency": "Verify frequency matches system requirements",
            "standard": "Check compliance with relevant electrical standards",
        }

        for key, suggestion in suggestions.items():
            if key.lower() in error_message.lower():
                return suggestion

        return "Review the data and correct the validation error"

    def _validate_electrical_clearance(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate electrical clearance distances."""
        if not isinstance(value, (int, float)):
            return False, "Electrical clearance must be a number"

        min_clearance = constraints.get("min_clearance_mm", 25)
        voltage_level = constraints.get("voltage_level", 1000)

        # IEC 61936-1 clearance requirements
        if voltage_level <= 1000:
            required_clearance = 25
        elif voltage_level <= 12000:
            required_clearance = 125
        elif voltage_level <= 245000:
            required_clearance = 1800
        else:
            required_clearance = 3800

        if value < required_clearance:
            return (
                False,
                f"Electrical clearance {value}mm insufficient for {voltage_level}V system (minimum: {required_clearance}mm)",
            )

        return True, ""

    def _validate_temperature_derating(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate temperature derating calculations."""
        if not isinstance(value, (int, float)):
            return False, "Temperature must be a number"

        ambient_temp = value
        rated_temp = constraints.get("rated_temperature", 40)
        derating_factor = constraints.get("derating_factor", 0.005)

        if ambient_temp > rated_temp:
            derated_capacity = 1 - (ambient_temp - rated_temp) * derating_factor
            if derated_capacity < 0.7:  # Maximum 30% derating
                return (
                    False,
                    f"Temperature {ambient_temp}°C exceeds maximum derating limit for {rated_temp}°C rated equipment",
                )

        return True, ""

    def _validate_harmonic_distortion(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate harmonic distortion levels."""
        if not isinstance(value, (int, float)):
            return False, "Harmonic distortion must be a number"

        thd_limit = constraints.get("thd_limit", 5.0)  # IEEE 519 standard

        if value > thd_limit:
            return (
                False,
                f"Total harmonic distortion {value}% exceeds IEEE 519 limit of {thd_limit}%",
            )

        return True, ""

    def _validate_arc_flash_category(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate arc flash hazard category."""
        if not isinstance(value, (int, float)):
            return False, "Arc flash category must be a number"

        max_category = constraints.get("max_category", 4)
        incident_energy = constraints.get("incident_energy", 0)

        # IEEE 1584 arc flash categories
        if incident_energy > 0:
            if incident_energy <= 1.2:
                calculated_category = 1
            elif incident_energy <= 8:
                calculated_category = 2
            elif incident_energy <= 25:
                calculated_category = 3
            else:
                calculated_category = 4

            if value != calculated_category:
                return (
                    False,
                    f"Arc flash category {value} does not match calculated category {calculated_category}",
                )

        if value > max_category:
            return (
                False,
                f"Arc flash category {value} exceeds maximum allowed {max_category}",
            )

        return True, ""

    def _validate_grounding_impedance(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate grounding impedance values."""
        if not isinstance(value, (int, float)):
            return False, "Grounding impedance must be a number"

        max_impedance = constraints.get("max_impedance_ohms", 25)
        system_voltage = constraints.get("system_voltage", 480)

        # IEEE 142 grounding requirements
        if system_voltage <= 1000:
            max_allowed = 25
        elif system_voltage <= 5000:
            max_allowed = 5
        else:
            max_allowed = 1

        if value > max_allowed:
            return (
                False,
                f"Grounding impedance {value}Ω exceeds maximum {max_allowed}Ω for {system_voltage}V system",
            )

        return True, ""

    def _validate_ip_rating_adequacy(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate IP rating adequacy for installation environment."""
        if not isinstance(value, str):
            return False, "IP rating must be a string"

        environment = constraints.get("environment", "indoor")
        ip_rating = value

        # IP rating requirements by environment
        required_ratings = {
            "indoor": "IP20",
            "outdoor": "IP54",
            "hazardous": "IP65",
            "dusty": "IP55",
            "wet": "IP67",
        }

        required = required_ratings.get(environment, "IP20")
        # Simple comparison - actual implementation would need more sophisticated parsing
        if ip_rating < required:
            return (
                False,
                f"IP rating {ip_rating} insufficient for {environment} environment (required: {required})",
            )

        return True, ""

    def _validate_frequency_tolerance(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate frequency tolerance for electrical systems."""
        if not isinstance(value, (int, float)):
            return False, "Frequency must be a number"

        nominal_freq = constraints.get("nominal_frequency", 60)
        tolerance_percent = constraints.get("tolerance_percent", 5)

        min_freq = nominal_freq * (1 - tolerance_percent / 100)
        max_freq = nominal_freq * (1 + tolerance_percent / 100)

        if value < min_freq or value > max_freq:
            return (
                False,
                f"Frequency {value}Hz outside tolerance range {min_freq}-{max_freq}Hz",
            )

        return True, ""

    def _validate_cable_ampacity(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate cable ampacity based on NEC/CEC standards."""
        if not isinstance(value, (int, float)):
            return False, "Cable ampacity must be a number"

        load_current = constraints.get("load_current", 0)
        ambient_temp = constraints.get("ambient_temperature", 30)
        cable_size = constraints.get("cable_size_awg", 12)

        # Basic NEC ampacity table (simplified)
        ampacity_table = {
            14: 15,
            12: 20,
            10: 30,
            8: 40,
            6: 55,
            4: 70,
            3: 85,
            2: 95,
            1: 110,
            1 / 0: 125,
            2 / 0: 145,
            3 / 0: 165,
            4 / 0: 195,
        }

        base_ampacity = ampacity_table.get(cable_size, 20)

        # Temperature derating
        if ambient_temp > 30:
            derating_factor = 0.9 ** ((ambient_temp - 30) / 10)
            adjusted_ampacity = base_ampacity * derating_factor
        else:
            adjusted_ampacity = base_ampacity

        if load_current > adjusted_ampacity * 0.8:  # 80% loading rule
            return (
                False,
                f"Cable ampacity {adjusted_ampacity}A insufficient for load {load_current}A (AWG {cable_size})",
            )

        return True, ""

    def _validate_motor_efficiency(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate motor efficiency against NEMA standards."""
        if not isinstance(value, (int, float)):
            return False, "Motor efficiency must be a number"

        motor_size_hp = constraints.get("motor_size_hp", 10)
        efficiency_class = constraints.get("efficiency_class", "premium")

        # NEMA efficiency requirements (simplified)
        min_efficiency = {"standard": 0.85, "energy_efficient": 0.89, "premium": 0.92}

        required_efficiency = min_efficiency.get(efficiency_class, 0.85)

        if value < required_efficiency:
            return (
                False,
                f"Motor efficiency {value} below NEMA {efficiency_class} requirement {required_efficiency}",
            )

        return True, ""

    def _validate_transformer_impedance(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate transformer impedance percentage."""
        if not isinstance(value, (int, float)):
            return False, "Transformer impedance must be a number"

        kva_rating = constraints.get("kva_rating", 150)

        # Standard impedance ranges by kVA rating
        if kva_rating <= 15:
            expected_range = (2.5, 5.0)
        elif kva_rating <= 150:
            expected_range = (4.0, 6.0)
        elif kva_rating <= 500:
            expected_range = (4.5, 6.5)
        else:
            expected_range = (5.0, 7.5)

        min_impedance, max_impedance = expected_range

        if value < min_impedance or value > max_impedance:
            return (
                False,
                f"Transformer impedance {value}% outside standard range {min_impedance}-{max_impedance}%",
            )

        return True, ""

    def _validate_protection_coordination(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate protection device coordination."""
        if not isinstance(value, dict):
            return False, "Protection coordination must be an object"

        upstream_device = value.get("upstream_device")
        downstream_device = value.get("downstream_device")
        coordination_ratio = value.get("coordination_ratio", 1.0)

        if not upstream_device or not downstream_device:
            return False, "Both upstream and downstream devices must be specified"

        # IEEE 242 coordination requirements
        if coordination_ratio < 1.5:
            return (
                False,
                f"Coordination ratio {coordination_ratio} insufficient (minimum 1.5 required)",
            )

        return True, ""

    def _validate_load_balance(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate three-phase load balance."""
        if not isinstance(value, dict):
            return False, "Load balance must be an object"

        phase_a = value.get("phase_a_current", 0)
        phase_b = value.get("phase_b_current", 0)
        phase_c = value.get("phase_c_current", 0)

        avg_current = (phase_a + phase_b + phase_c) / 3

        if avg_current == 0:
            return True, ""

        max_deviation = max(
            abs(phase_a - avg_current),
            abs(phase_b - avg_current),
            abs(phase_c - avg_current),
        )

        balance_percentage = (max_deviation / avg_current) * 100
        max_imbalance = constraints.get("max_imbalance_percent", 10)

        if balance_percentage > max_imbalance:
            return (
                False,
                f"Load imbalance {balance_percentage:.1f}% exceeds maximum {max_imbalance}%",
            )

        return True, ""

    def _validate_voltage_regulation(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate voltage regulation limits."""
        if not isinstance(value, (int, float)):
            return False, "Voltage regulation must be a number"

        max_regulation = constraints.get("max_regulation_percent", 5)

        if abs(value) > max_regulation:
            return (
                False,
                f"Voltage regulation {value}% exceeds maximum {max_regulation}%",
            )

        return True, ""

    def _validate_short_circuit_rating(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate short circuit current rating."""
        if not isinstance(value, (int, float)):
            return False, "Short circuit rating must be a number"

        system_fault_current = constraints.get("system_fault_current", 0)
        safety_factor = constraints.get("safety_factor", 1.1)

        if system_fault_current > 0 and value < system_fault_current * safety_factor:
            return (
                False,
                f"Short circuit rating {value}kA insufficient for system fault current {system_fault_current}kA",
            )

        return True, ""

    def _validate_insulation_resistance(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate insulation resistance values."""
        if not isinstance(value, (int, float)):
            return False, "Insulation resistance must be a number"

        rated_voltage = constraints.get("rated_voltage", 1000)
        min_resistance_megaohms = max(1, rated_voltage / 1000)

        if value < min_resistance_megaohms:
            return (
                False,
                f"Insulation resistance {value}MΩ below minimum {min_resistance_megaohms}MΩ",
            )

        return True, ""

    def _validate_contact_rating(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate contact rating for switching devices."""
        if not isinstance(value, (int, float)):
            return False, "Contact rating must be a number"

        load_current = constraints.get("load_current", 0)
        safety_factor = constraints.get("safety_factor", 1.25)

        if load_current > 0 and value < load_current * safety_factor:
            return (
                False,
                f"Contact rating {value}A insufficient for load current {load_current}A",
            )

        return True, ""

    def _validate_switching_capacity(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate switching capacity of circuit breakers."""
        if not isinstance(value, (int, float)):
            return False, "Switching capacity must be a number"

        prospective_current = constraints.get("prospective_current", 0)
        safety_factor = constraints.get("safety_factor", 1.1)

        if prospective_current > 0 and value < prospective_current * safety_factor:
            return (
                False,
                f"Switching capacity {value}kA insufficient for prospective current {prospective_current}kA",
            )

        return True, ""

    def _validate_surge_protection(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate surge protection device ratings."""
        if not isinstance(value, dict):
            return False, "Surge protection must be an object"

        rated_voltage = value.get("rated_voltage", 0)
        max_continuous_voltage = value.get("max_continuous_voltage", 0)
        nominal_discharge_current = value.get("nominal_discharge_current", 0)

        system_voltage = constraints.get("system_voltage", 480)
        required_discharge_current = constraints.get("required_discharge_current", 20)

        if max_continuous_voltage < system_voltage * 1.1:
            return (
                False,
                f"MCOV {max_continuous_voltage}V insufficient for system voltage {system_voltage}V",
            )

        if nominal_discharge_current < required_discharge_current:
            return (
                False,
                f"Nominal discharge current {nominal_discharge_current}kA below required {required_discharge_current}kA",
            )

        return True, ""

    def _validate_temporal_sequence(
        self, value: Any, constraints: Dict[str, Any]
    ) -> Tuple[bool, str]:
        """Validate temporal sequence of events or operations."""
        if not isinstance(value, dict):
            return False, "Temporal sequence must be provided as a dictionary"

        required_keys = ["start_time", "end_time", "sequence"]
        missing_keys = [key for key in required_keys if key not in value]
        if missing_keys:
            return False, f"Missing required temporal sequence keys: {missing_keys}"

        start_time = value.get("start_time")
        end_time = value.get("end_time")
        sequence = value.get("sequence", [])

        if start_time and end_time and start_time >= end_time:
            return False, "Start time must be before end time"

        if sequence and len(sequence) > 1:
            for i in range(1, len(sequence)):
                if sequence[i - 1].get("timestamp", 0) > sequence[i].get(
                    "timestamp", 0
                ):
                    return (
                        False,
                        f"Temporal sequence violation at step {i}: events out of order",
                    )

        max_duration = constraints.get("max_duration_hours", 24)
        if start_time and end_time:
            duration = (end_time - start_time).total_seconds() / 3600
            if duration > max_duration:
                return (
                    False,
                    f"Sequence duration {duration:.1f}h exceeds maximum {max_duration}h",
                )

        return True, ""

    async def validate_with_json_path_queries(
        self, data: Dict[str, Any], queries: List[JsonPathQuery]
    ) -> ValidationResult:
        """Validate data using custom JSON path queries."""
        start_time = datetime.utcnow()
        errors = []
        warnings: List[str] = []
        processed_paths = []

        for query in queries:
            try:
                # Execute JSON path query
                if query.path not in self.json_path_cache:
                    self.json_path_cache[query.path] = jsonpath_parse(query.path)

                query_obj = self.json_path_cache[query.path]
                matches = query_obj.find(data)

                for match in matches:
                    path_str = str(match.full_path)
                    value = match.value
                    processed_paths.append(
                        query.path
                    )  # Use the original JSONPath query format

                    # Apply validation
                    if query.validation_function:
                        is_valid, message = query.validation_function(
                            value, query.constraints
                        )
                        if not is_valid:
                            errors.append(
                                ValidationError(
                                    path=path_str,
                                    message=message,
                                    value=value,
                                    expected_type=query.expected_type,
                                    constraint=str(query.constraints),
                                    severity="error",
                                    suggestion=self._get_suggestion_for_error(message),
                                    json_path=query.path,
                                )
                            )

            except Exception as e:
                warnings.append(f"JSON path query error: {e}")

        validation_time = (datetime.utcnow() - start_time).total_seconds() * 1000

        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            data_quality_score=self._calculate_data_quality_score(
                data, errors, warnings
            ),
            processed_paths=processed_paths,
            validation_time_ms=validation_time,
            schema_version="custom",
        )

    def add_custom_schema(self, schema_name: str, schema: Dict[str, Any]) -> None:
        """Add custom schema to validator."""
        self.electrical_schemas[schema_name] = schema

    def add_custom_validator(self, name: str, validator: Callable[..., Any]) -> None:
        """Add custom validator function."""
        self.custom_validators[name] = validator

    def get_available_schemas(self) -> List[str]:
        """Get list of available schemas."""
        return list(self.electrical_schemas.keys())

    def get_schema_definition(self, schema_name: str) -> Optional[Dict[str, Any]]:
        """Get schema definition."""
        return self.electrical_schemas.get(schema_name)


# Global validator instance
json_schema_validator = AdvancedJsonSchemaValidator()
