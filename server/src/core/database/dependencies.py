"""Database Dependencies.

This module provides database-related dependency injection providers for FastAPI.
Implements per-request database session management with proper lifecycle handling.
"""

from collections.abc import AsyncGenerator, Generator
from typing import Any, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import Session
from fastapi import Depends

from src.core.database.session import get_db_session
from src.core.database.connection_manager import get_contextual_db_session, get_project_repository_dependency
from src.core.repositories.general.project_repository import ProjectRepository


def get_db() -> Generator[Session, None, None]:
    """FastAPI dependency provider for database sessions.

    Provides a per-request SQLAlchemy Session with automatic lifecycle management.
    The session is created at the start of the request and automatically closed
    when the request completes, ensuring proper resource cleanup.

    Yields:
        Session: SQLAlchemy database session

    Example:
        @router.get("/users/")
        async def get_users(db: Session = Depends(get_db)):
            return user_service.get_all_users()

    """
    with get_db_session() as session:
        yield session


# Alias for backward compatibility and clarity
get_database_session = get_db


async def get_project_db_session(
    project_id: Optional[int] = None,
    project_repo: ProjectRepository = Depends(get_project_repository_dependency),
) -> AsyncGenerator[AsyncSession, None]:
    """FastAPI dependency provider for project-aware database sessions.

    Provides a per-request async SQLAlchemy Session that routes to the appropriate
    database based on project context. Uses the connection manager to determine
    whether to use central or project-specific database.

    Args:
        project_id: Optional project ID to determine database routing
        project_repo: Project repository dependency for project lookups

    Yields:
        AsyncSession: Async SQLAlchemy database session

    Example:
        @router.get("/projects/{project_id}/components/")
        async def get_components(
            project_id: int,
            db: AsyncSession = Depends(get_project_db_session)
        ):
            return await component_service.get_components()
    """
    from src.core.database.connection_manager import _connection_manager
    async for session in _connection_manager.get_session(project_repo, project_id):
        yield session
