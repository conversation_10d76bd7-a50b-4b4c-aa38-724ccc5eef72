"""Project Repository.

This module provides data access layer for Project entities, extending the base
repository with project-specific query methods and operations.
"""

from datetime import UTC, datetime
from typing import List, Optional

from sqlalchemy import and_, desc, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.project import Project
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository


class ProjectRepository(BaseRepository[Project]):
    """Repository for Project entity data access operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the Project repository."""
        super().__init__(db_session, Project)
        logger.debug("ProjectRepository initialized")

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    async def get_by_name(self, name: str) -> Optional[Project]:
        """Get project by name."""
        stmt = select(self.model).where(
            and_(self.model.name == name, self.model.is_deleted == False)
        )
        result = await self.db_session.execute(stmt)
        return result.scalar_one_or_none()

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    async def get_by_project_number(self, project_number: str) -> Optional[Project]:
        """Get project by project number."""
        stmt = select(self.model).where(
            and_(
                self.model.project_number == project_number,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        return result.scalar_one_or_none()

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    async def get_active_projects(
        self, skip: int = 0, limit: int = 100
    ) -> List[Project]:
        """Get list of active (non-deleted) projects."""
        stmt = (
            select(self.model)
            .where(self.model.is_deleted == False)
            .offset(skip)
            .limit(limit)
            .order_by(self.model.created_at.desc())
        )
        result = await self.db_session.execute(stmt)
        return list(result.scalars().all())

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    async def soft_delete_project(
        self, project_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """Soft delete a project."""
        update_data = {
            "is_deleted": True,
            "deleted_at": datetime.now(UTC),
            "deleted_by_user_id": deleted_by_user_id,
        }
        stmt = (
            update(self.model)
            .where(and_(self.model.id == project_id, self.model.is_deleted == False))
            .values(**update_data)
        )
        result = await self.db_session.execute(stmt)
        return result.rowcount > 0

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    async def update_project_status(self, project_id: int, status: str) -> Project:
        """Update project status."""
        project = await self.get_by_id(project_id)
        project.status = status
        await self.db_session.flush()
        return project

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    async def get_project_with_related_data(self, project_id: int) -> Optional[Project]:
        """Get project with eagerly loaded related data."""
        stmt = (
            select(self.model)
            .where(self.model.id == project_id)
            .options(selectinload(self.model.members))
        )
        result = await self.db_session.execute(stmt)
        return result.scalar_one_or_none()

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    async def get_recent_projects(self, limit: int = 10) -> List[Project]:
        """Get recently created projects ordered by creation date."""
        stmt = (
            select(self.model)
            .where(self.model.is_deleted == False)
            .order_by(self.model.created_at.desc())
            .limit(limit)
        )
        result = await self.db_session.execute(stmt)
        return list(result.scalars().all())

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    async def get_projects_by_client(
        self, client_name: str, skip: int = 0, limit: int = 100
    ) -> List[Project]:
        """Get projects by client name."""
        from src.core.errors.exceptions import InvalidInputError

        if not client_name or not client_name.strip():
            raise InvalidInputError("Client name cannot be empty")

        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.client == client_name.strip(),
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.created_at.desc())
        )
        result = await self.db_session.execute(stmt)
        return list(result.scalars().all())

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    async def count_active_projects(self) -> int:
        """Count total number of active projects."""
        from sqlalchemy import func

        stmt = select(func.count(self.model.id)).where(self.model.is_deleted == False)
        result = await self.db_session.execute(stmt)
        return result.scalar_one() or 0

    @handle_repository_errors("project")
    @monitor_repository_performance("project")
    async def get_projects_by_status(
        self, status: str, skip: int = 0, limit: int = 100
    ) -> List[Project]:
        """Get projects filtered by status."""
        stmt = (
            select(self.model)
            .where(and_(self.model.status == status, self.model.is_deleted == False))
            .offset(skip)
            .limit(limit)
            .order_by(self.model.created_at.desc())
        )
        result = await self.db_session.execute(stmt)
        return list(result.scalars().all())
