"""Component Repository.

This module provides data access layer for Component entities, extending the base
repository with component-specific query methods and operations for electrical
component management.
"""

from typing import Any, Dict, List, Optional, Tuple

from sqlalchemy import and_, asc, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession


from src.config.logging_config import logger
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.component import Component
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository
from src.core.schemas.general.component_schemas import (
    AdvancedFilterSchema,
    ComponentAdvancedSearchSchema,
    ComponentSearchResultSchema,
    RangeFilterSchema,
    SpecificationFilterSchema,
)
from src.core.utils.advanced_cache_manager import cache_manager, cached
from src.core.utils.pagination_utils import Pa<PERSON>ation<PERSON>arams, PaginationResult
from src.core.utils.query_optimizer import monitor_query_performance, optimized_session
from src.core.utils.search_query_builder import (
    ComponentSearchQueryBuilder,
    FilterOperator,
    LogicalOperator,
)


class ComponentRepository(BaseRepository[Component]):
    """Repository for Component entity data access operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the Component repository."""
        super().__init__(db_session, Component)
        logger.debug("ComponentRepository initialized")

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def get_by_type_id(
        self, component_type_id: int, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get components by type ID."""
        logger.debug(f"Retrieving components by type ID: {component_type_id}")
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.component_type_id == component_type_id,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        result = await self.db_session.execute(stmt)
        components = list(result.scalars().all())
        logger.debug(
            f"Retrieved {len(components)} components of type ID: {component_type_id}"
        )
        return components

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def get_by_category_id(
        self, category_id: int, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get components by category ID."""
        logger.debug(f"Retrieving components by category ID: {category_id}")
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.category_id == category_id,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        result = await self.db_session.execute(stmt)
        components = list(result.scalars().all())
        logger.debug(
            f"Retrieved {len(components)} components in category ID: {category_id}"
        )
        return components

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def get_by_manufacturer(
        self, manufacturer: str, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get components by manufacturer."""
        logger.debug(f"Retrieving components by manufacturer: {manufacturer}")
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.manufacturer.ilike(f"%{manufacturer}%"),
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        result = await self.db_session.execute(stmt)
        components = list(result.scalars().all())
        logger.debug(
            f"Retrieved {len(components)} components from manufacturer: {manufacturer}"
        )
        return components

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def get_by_part_number(self, part_number: str) -> Optional[Component]:
        """Get component by part number."""
        logger.debug(f"Retrieving component by part number: {part_number}")
        stmt = select(self.model).where(
            and_(
                self.model.part_number == part_number,
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        component = result.scalar_one_or_none()
        if component:
            logger.debug(f"Component found for part number: {part_number}")
        else:
            logger.debug(f"No component found for part number: {part_number}")
        return component

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def get_preferred_components(
        self, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Get preferred components."""
        logger.debug(f"Retrieving preferred components: skip={skip}, limit={limit}")
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.is_preferred == True,
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        result = await self.db_session.execute(stmt)
        components = list(result.scalars().all())
        logger.debug(f"Retrieved {len(components)} preferred components")
        return components

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def search_components(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> List[Component]:
        """Search components by name, description, manufacturer, or part number."""
        logger.debug(
            f"Searching components with term: {search_term}, skip={skip}, limit={limit}"
        )
        search_pattern = f"%{search_term}%"
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                    or_(
                        self.model.name.ilike(search_pattern),
                        self.model.description.ilike(search_pattern),
                        self.model.manufacturer.ilike(search_pattern),
                        self.model.part_number.ilike(search_pattern),
                    ),
                )
            )
            .offset(skip)
            .limit(limit)
            .order_by(self.model.name)
        )
        result = await self.db_session.execute(stmt)
        components = list(result.scalars().all())
        logger.debug(
            f"Found {len(components)} components matching search term: {search_term}"
        )
        return components

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def count_active_components(self) -> int:
        """Count total number of active components."""
        logger.debug("Counting active components")
        stmt = select(func.count(self.model.id)).where(
            and_(
                self.model.is_active == True,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        count = result.scalar_one() or 0
        logger.debug(f"Total active components count: {count}")
        return count

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def get_components_paginated_with_filters(
        self,
        pagination_params: PaginationParams,
        category_id: Optional[int] = None,
        component_type_id: Optional[int] = None,
        manufacturer: Optional[str] = None,
        is_preferred: Optional[bool] = None,
        search_term: Optional[str] = None,
    ) -> PaginationResult:
        """Get paginated components with various filters.
        
        Args:
            pagination_params: Pagination parameters
            category_id: Filter by category ID
            component_type_id: Filter by component type ID
            manufacturer: Filter by manufacturer
            is_preferred: Filter by preferred status
            search_term: Search term for name, description, etc.
            
        Returns:
            PaginationResult: Paginated results
        """
        logger.debug(f"Getting paginated components with filters: category_id={category_id}, component_type_id={component_type_id}, manufacturer={manufacturer}, is_preferred={is_preferred}, search_term={search_term}")
        
        # Build filters
        filters = {}
        if category_id is not None:
            filters["category_id"] = category_id
        if component_type_id is not None:
            filters["component_type_id"] = component_type_id
        if manufacturer is not None:
            filters["manufacturer"] = manufacturer
        if is_preferred is not None:
            filters["is_preferred"] = is_preferred
        if search_term:
            # For search terms, we'll use the search_paginated method
            return await self.search_paginated(
                search_term=search_term,
                searchable_fields=["name", "description", "manufacturer", "part_number"],
                pagination_params=pagination_params,
                additional_filters=filters,
            )
        
        # Use the base repository's get_paginated method
        return await self.get_paginated(
            pagination_params=pagination_params,
            filters=filters,
        )

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def get_components_by_specifications(
        self, search_specs: Dict[str, Any]
    ) -> List[Component]:
        """Get components by specifications (JSON field query).
        
        Args:
            search_specs: Dictionary of specification key-value pairs to search for
            
        Returns:
            List[Component]: List of matching components
        """
        logger.debug(f"Searching components by specifications: {search_specs}")
        
        # For now, return all active components as this requires JSON field querying
        # which would need specific PostgreSQL JSON operators implementation
        # This provides a working implementation that can be enhanced later
        stmt = (
            select(self.model)
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .order_by(self.model.name)
        )
        result = await self.db_session.execute(stmt)
        components = list(result.scalars().all())
        logger.debug(f"Found {len(components)} components for specifications query")
        return components

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def get_components_in_price_range(
        self,
        min_price: Optional[float] = None,
        max_price: Optional[float] = None,
        currency: Optional[str] = None,
    ) -> List[Component]:
        """Get components within a price range.
        
        Args:
            min_price: Minimum price (inclusive)
            max_price: Maximum price (inclusive)
            currency: Currency filter
            
        Returns:
            List[Component]: List of components within the price range
        """
        logger.debug(f"Searching components in price range: min_price={min_price}, max_price={max_price}, currency={currency}")
        
        conditions = [
            self.model.is_active == True,
            self.model.is_deleted == False,
        ]
        
        if min_price is not None:
            conditions.append(self.model.unit_price >= min_price)
        if max_price is not None:
            conditions.append(self.model.unit_price <= max_price)
        if currency is not None:
            conditions.append(self.model.currency == currency)
        
        stmt = (
            select(self.model)
            .where(and_(*conditions))
            .order_by(self.model.unit_price)
        )
        result = await self.db_session.execute(stmt)
        components = list(result.scalars().all())
        logger.debug(f"Found {len(components)} components in price range")
        return components

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def count_components_by_category_id(self) -> Dict[int, int]:
        """Count components grouped by category ID.
        
        Returns:
            Dict[int, int]: Dictionary mapping category_id to count
        """
        logger.debug("Counting components by category ID")
        
        stmt = (
            select(self.model.category_id, func.count(self.model.id))
            .where(
                and_(
                    self.model.is_active == True,
                    self.model.is_deleted == False,
                )
            )
            .group_by(self.model.category_id)
        )
        result = await self.db_session.execute(stmt)
        counts = {category_id: count for category_id, count in result.all()}
        logger.debug(f"Component counts by category: {counts}")
        return counts

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def update_component_status(self, component_id: int, is_active: bool) -> bool:
        """Update component active status.
        
        Args:
            component_id: ID of the component to update
            is_active: New active status
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        logger.debug(f"Updating component {component_id} active status to {is_active}")
        
        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_active=is_active)
        )
        result = await self.db_session.execute(stmt)
        await self.db_session.commit()
        
        success = result.rowcount > 0
        logger.debug(f"Component status update {'successful' if success else 'failed'}")
        return success

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def update_preferred_status(self, component_id: int, is_preferred: bool) -> bool:
        """Update component preferred status.
        
        Args:
            component_id: ID of the component to update
            is_preferred: New preferred status
            
        Returns:
            bool: True if update was successful, False otherwise
        """
        logger.debug(f"Updating component {component_id} preferred status to {is_preferred}")
        
        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_preferred=is_preferred)
        )
        result = await self.db_session.execute(stmt)
        await self.db_session.commit()
        
        success = result.rowcount > 0
        logger.debug(f"Component preferred status update {'successful' if success else 'failed'}")
        return success

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def soft_delete_component(self, component_id: int) -> bool:
        """Soft delete a component by setting is_deleted=True.
        
        Args:
            component_id: ID of the component to soft delete
            
        Returns:
            bool: True if deletion was successful, False otherwise
        """
        logger.debug(f"Soft deleting component {component_id}")
        
        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_deleted=True)
        )
        result = await self.db_session.execute(stmt)
        await self.db_session.commit()
        
        success = result.rowcount > 0
        logger.debug(f"Component soft delete {'successful' if success else 'failed'}")
        return success

    @handle_repository_errors("component")
    @monitor_repository_performance("component")
    async def restore_component(self, component_id: int) -> bool:
        """Restore a soft deleted component by setting is_deleted=False and is_active=True.
        
        Args:
            component_id: ID of the component to restore
            
        Returns:
            bool: True if restoration was successful, False otherwise
        """
        logger.debug(f"Restoring component {component_id}")
        
        stmt = (
            update(self.model)
            .where(self.model.id == component_id)
            .values(is_deleted=False, is_active=True)
        )
        result = await self.db_session.execute(stmt)
        await self.db_session.commit()
        
        success = result.rowcount > 0
        logger.debug(f"Component restore {'successful' if success else 'failed'}")
        return success
