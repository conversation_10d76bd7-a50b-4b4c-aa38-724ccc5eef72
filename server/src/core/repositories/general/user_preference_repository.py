"""User Preference Repository.

This module provides data access layer for UserPreference entities,
extending the base repository with user preference-specific query methods
and operations.
"""

from datetime import UTC
from typing import Any, Dict, Optional

from sqlalchemy import and_, select, update
from sqlalchemy.ext.asyncio import AsyncSession


from src.config.logging_config import logger

# Unified systems imports
from src.core.errors.unified_error_handler import handle_repository_errors
from src.core.models.general.user import UserPreference
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.repositories.base_repository import BaseRepository


class UserPreferenceRepository(BaseRepository[UserPreference]):
    """Repository for UserPreference entity data access operations."""

    def __init__(self, db_session: AsyncSession):
        """Initialize the UserPreference repository."""
        super().__init__(db_session, UserPreference)
        logger.debug("UserPreferenceRepository initialized")

    @handle_repository_errors("user_preference")
    @monitor_repository_performance("user_preference")
    async def get_by_user_id(self, user_id: int) -> Optional[UserPreference]:
        """Get user preferences by user ID."""
        logger.debug(f"Retrieving preferences for user {user_id}")

        stmt = select(self.model).where(
            and_(
                self.model.user_id == user_id,
                self.model.is_deleted == False,
            )
        )
        result = await self.db_session.execute(stmt)
        preferences = result.scalar_one_or_none()

        if preferences:
            logger.debug(f"Preferences found for user {user_id}")
        else:
            logger.debug(f"No preferences found for user {user_id}")

        return preferences

    @handle_repository_errors("user_preference")
    @monitor_repository_performance("user_preference")
    async def soft_delete_preferences(
        self, user_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """Soft delete user preferences."""
        logger.debug(f"Soft deleting preferences for user {user_id}")

        from datetime import datetime

        update_data = {
            "is_deleted": True,
            "deleted_at": datetime.now(UTC),
            "deleted_by_user_id": deleted_by_user_id,
        }

        stmt = (
            update(self.model)
            .where(and_(self.model.user_id == user_id, self.model.is_deleted == False))
            .values(**update_data)
        )

        result = await self.db_session.execute(stmt)

        if result.rowcount > 0:
            logger.debug(f"Preferences soft deleted for user {user_id}")
            return True
        logger.debug(f"No preferences found for user {user_id} or already deleted")
        return False

    @handle_repository_errors("user_preference")
    @monitor_repository_performance("user_preference")
    async def create_or_update_preferences(
        self, user_id: int, preferences_data: Dict[str, Any]
    ) -> UserPreference:
        """Create or update user preferences."""
        logger.debug(f"Creating or updating preferences for user {user_id}")

        existing_preferences = await self.get_by_user_id(user_id)

        if existing_preferences:
            logger.debug(f"Updating existing preferences for user {user_id}")
            updated_preferences = await self.update(
                existing_preferences.id, preferences_data
            )
            await self.db_session.flush()
            logger.debug(f"Updated preferences for user {user_id}")
            return updated_preferences
        else:
            logger.debug(f"Creating new preferences for user {user_id}")
            preferences_data["user_id"] = user_id
            new_preferences = await self.create(preferences_data)
            await self.db_session.flush()
            logger.debug(f"Created new preferences for user {user_id}")
            return new_preferences
