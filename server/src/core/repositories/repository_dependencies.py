"""Repository Dependencies.

This module provides repository dependency injection providers for FastAPI.
Each repository receives a database session and is properly instantiated
for use in the service layer.
"""

from typing import TYPE_CHECKING

from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.database.connection_manager import get_contextual_db_session

if TYPE_CHECKING:
    from src.core.repositories.general.component_category_repository import (
        ComponentCategoryRepository,
    )
    from src.core.repositories.general.component_repository import ComponentRepository
    from src.core.repositories.general.component_type_repository import (
        ComponentTypeRepository,
    )
    from src.core.repositories.general.project_member_repository import (
        ProjectMemberRepository,
    )
    from src.core.repositories.general.project_repository import ProjectRepository
    from src.core.repositories.general.user_preference_repository import (
        UserPreferenceRepository,
    )
    from src.core.repositories.general.user_repository import UserRepository


def get_project_repository(
    db: AsyncSession = Depends(get_contextual_db_session),
) -> "ProjectRepository":
    """Dependency provider for ProjectRepository."""
    from src.core.repositories.general.project_repository import ProjectRepository

    return ProjectRepository(db)


def get_user_repository(
    db: AsyncSession = Depends(get_contextual_db_session),
) -> "UserRepository":
    """Dependency provider for UserRepository."""
    from src.core.repositories.general.user_repository import UserRepository

    return UserRepository(db)


def get_component_repository(
    db: AsyncSession = Depends(get_contextual_db_session),
) -> "ComponentRepository":
    """Dependency provider for ComponentRepository."""
    from src.core.repositories.general.component_repository import ComponentRepository

    return ComponentRepository(db)


def get_component_category_repository(
    db: AsyncSession = Depends(get_contextual_db_session),
) -> "ComponentCategoryRepository":
    """Dependency provider for ComponentCategoryRepository."""
    from src.core.repositories.general.component_category_repository import (
        ComponentCategoryRepository,
    )

    return ComponentCategoryRepository(db)


def get_component_type_repository(
    db: AsyncSession = Depends(get_contextual_db_session),
) -> "ComponentTypeRepository":
    """Dependency provider for ComponentTypeRepository."""
    from src.core.repositories.general.component_type_repository import (
        ComponentTypeRepository,
    )

    return ComponentTypeRepository(db)


def get_project_member_repository(
    db: AsyncSession = Depends(get_contextual_db_session),
) -> "ProjectMemberRepository":
    """Dependency provider for ProjectMemberRepository."""
    from src.core.repositories.general.project_member_repository import (
        ProjectMemberRepository,
    )

    return ProjectMemberRepository(db)


def get_task_repository(
    db: AsyncSession = Depends(get_contextual_db_session),
) -> "TaskRepository":
    """Dependency provider for TaskRepository."""
    from src.core.repositories.general.task_repository import TaskRepository

    return TaskRepository(db)


def get_user_preference_repository(
    db: AsyncSession = Depends(get_contextual_db_session),
) -> "UserPreferenceRepository":
    """Dependency provider for UserPreferenceRepository."""
    from src.core.repositories.general.user_preference_repository import (
        UserPreferenceRepository,
    )

    return UserPreferenceRepository(db)
