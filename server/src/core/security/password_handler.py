"""Password handling with professional security practices.

This module provides password handling utilities including:
- Password hashing and verification with Argon2
- Password strength validation
- Hash rehashing checks
"""

import re
from typing import Dict

from argon2 import PasswordHasher
from argon2.exceptions import VerifyMismatchError


class PasswordHandler:
    """Password handling with professional security practices."""

    ph = PasswordHasher(
        time_cost=4,  # t parameter
        memory_cost=65536,  # m parameter
        parallelism=1,  # p parameter
        hash_len=32,  # Length of the hash in bytes
        salt_len=16,  # Length of the salt in bytes
    )

    # Password complexity requirements
    MIN_LENGTH = 8
    REQUIRE_UPPERCASE = True
    REQUIRE_LOWERCASE = True
    REQUIRE_DIGITS = True
    REQUIRE_SPECIAL = True

    @classmethod
    def hash_password(cls, password: str) -> str:
        """Hash password using Argon2."""
        return cls.ph.hash(password)

    @classmethod
    def verify_password(cls, password: str, hashed_password_from_db: str) -> bool:
        """Verify password using Argon2."""
        try:
            cls.ph.verify(hashed_password_from_db, password)
            return True
        except VerifyMismatchError:
            return False
        except Exception as e:
            # Handle other potential exceptions, e.g., InvalidHashError
            print(f"An error occurred during verification: {e}")
            return False

    @classmethod
    def needs_rehash(cls, hashed_password: str) -> bool:
        """Check if the hash needs to be rehashed."""
        return cls.ph.check_needs_rehash(hashed_password)

    @classmethod
    def validate_password_strength(cls, password: str) -> Dict[str, bool]:
        """Validate password strength."""
        validations = {
            "length": len(password) >= cls.MIN_LENGTH,
            "uppercase": bool(re.search(r"[A-Z]", password))
            if cls.REQUIRE_UPPERCASE
            else True,
            "lowercase": bool(re.search(r"[a-z]", password))
            if cls.REQUIRE_LOWERCASE
            else True,
            "digits": bool(re.search(r"\d", password)) if cls.REQUIRE_DIGITS else True,
            "special": bool(re.search(r'[!@#$%^&*(),.?":{}|<>]', password))
            if cls.REQUIRE_SPECIAL
            else True,
        }
        return validations

    @classmethod
    def is_password_valid(cls, password: str) -> bool:
        """Check if password meets all requirements."""
        validations = cls.validate_password_strength(password)
        return all(validations.values())
