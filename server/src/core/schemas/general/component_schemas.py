"""Component Schemas.

This module provides comprehensive Pydantic schemas for component management
operations, supporting CRUD operations, validation, and serialization for
electrical component catalog management.

Key Features:
- Comprehensive field validation with custom validators
- Nested schema validation for component relationships and dependencies
- Input sanitization and data transformation
- Output formatting and serialization rules
- Schema versioning support and backward compatibility
- Custom validation rules for electrical component standards
"""

import json
from decimal import Decimal
from enum import Enum
from typing import Any, Dict, List, Optional, Union

from pydantic import BaseModel, Field, field_validator, model_validator

from src.core.schemas.base_schemas import (
    BaseSchema,
    PaginatedResponseSchema,
    TimestampMixin,
)


class ComponentSpecificationsSchema(BaseModel):
    """Schema for component electrical specifications."""

    electrical: Optional[Dict[str, Any]] = Field(
        None, description="Electrical specifications (voltage, current, power, etc.)"
    )
    thermal: Optional[Dict[str, Any]] = Field(
        None, description="Thermal specifications (operating temperature, etc.)"
    )
    mechanical: Optional[Dict[str, Any]] = Field(
        None, description="Mechanical specifications (mounting, enclosure, etc.)"
    )
    standards_compliance: Optional[List[str]] = Field(
        None, description="List of applicable standards (IEC, IEEE, EN, etc.)"
    )
    environmental: Optional[Dict[str, Any]] = Field(
        None, description="Environmental specifications (IP rating, humidity, etc.)"
    )

    @field_validator("standards_compliance")
    @classmethod
    def validate_standards_format(cls, v: Optional[List[str]]) -> Optional[List[str]]:
        """Validate standards compliance format."""
        if v is None:
            return v

        # Validate each standard follows expected format (e.g., IEC-60947-1, IEEE-802.11)
        valid_prefixes = ["IEC", "IEEE", "EN"]
        validated_standards = []

        for standard in v:
            if not isinstance(standard, str):
                continue  # type: ignore[unreachable]

            standard = standard.strip().upper()
            if any(standard.startswith(prefix) for prefix in valid_prefixes):
                validated_standards.append(standard)

        return validated_standards if validated_standards else None


class ComponentDimensionsSchema(BaseModel):
    """Schema for component physical dimensions."""

    length: Optional[float] = Field(None, ge=0, description="Length in mm")
    width: Optional[float] = Field(None, ge=0, description="Width in mm")
    height: Optional[float] = Field(None, ge=0, description="Height in mm")
    diameter: Optional[float] = Field(
        None, ge=0, description="Diameter in mm (for cylindrical components)"
    )
    unit: str = Field("mm", description="Unit of measurement")

    @field_validator("unit")
    @classmethod
    def validate_unit(cls, v: str) -> str:
        """Validate dimension unit."""
        allowed_units = ["mm", "cm", "m", "in", "ft"]
        if v.lower() not in allowed_units:
            raise ValueError(f"Unit must be one of: {allowed_units}")
        return v.lower()


class ComponentBaseSchema(BaseSchema):
    """Base schema for component operations."""

    name: str = Field(..., min_length=1, max_length=200, description="Component name")
    manufacturer: str = Field(
        ..., min_length=1, max_length=100, description="Component manufacturer"
    )
    model_number: str = Field(
        ..., min_length=1, max_length=100, description="Manufacturer model/part number"
    )
    description: Optional[str] = Field(
        None, max_length=1000, description="Detailed component description"
    )
    component_type_id: int = Field(..., ge=1, description="Component type ID")
    category_id: int = Field(..., ge=1, description="Component category ID")

    @field_validator("name", "manufacturer", "model_number", "description")
    @classmethod
    def sanitize_text_fields(cls, v: Optional[str]) -> Optional[str]:
        """Sanitize text fields for security and consistency."""
        if v is None:
            return v

        # Strip whitespace and normalize
        v = v.strip()
        if not v:
            return None

        # Remove potentially harmful characters
        import re

        v = re.sub(r'[<>"\']', "", v)

        # Normalize multiple spaces to single space
        v = re.sub(r"\s+", " ", v)

        return v


class ComponentCreateSchema(ComponentBaseSchema):
    """Schema for creating components with comprehensive validation."""

    # Specifications (flexible JSON structure)
    specifications: Optional[Union[Dict[str, Any], ComponentSpecificationsSchema]] = (
        Field(None, description="Component technical specifications")
    )

    # Pricing & Procurement
    unit_price: Optional[Decimal] = Field(
        None,
        ge=0,
        decimal_places=2,
        description="Component unit price",
        json_schema_extra={"example": 125.99},
    )
    currency: str = Field(
        "EUR", min_length=3, max_length=3, description="Price currency (ISO 4217)"
    )
    supplier: Optional[str] = Field(
        None, max_length=100, description="Primary supplier name"
    )
    part_number: Optional[str] = Field(
        None, max_length=100, description="Supplier part number"
    )

    # Physical Properties
    weight_kg: Optional[float] = Field(
        None, ge=0, description="Component weight in kilograms"
    )
    dimensions: Optional[ComponentDimensionsSchema] = Field(
        None, description="Physical dimensions"
    )

    # Status & Metadata
    is_active: bool = Field(True, description="Whether component is active")
    is_preferred: bool = Field(False, description="Whether component is preferred")
    stock_status: str = Field("available", description="Stock availability status")
    version: str = Field("1.0", description="Component data version")
    metadata: Optional[Dict[str, Any]] = Field(
        None,
        description="Additional metadata",
        json_schema_extra={
            "example": {"supplier_code": "XYZ-123", "internal_ref": "REF-456"}
        },
    )

    @model_validator(mode="after")
    def validate_component_classification(self) -> "ComponentCreateSchema":
        """Validate that relational IDs are provided."""
        has_relational = (
            self.component_type_id is not None and self.category_id is not None
        )

        if not has_relational:
            raise ValueError("Component_type_id and category_id must be provided")

        return self

    @field_validator("currency")
    @classmethod
    def validate_currency_code(cls, v: str) -> str:
        """Validate ISO 4217 currency code."""
        # Common currency codes validation
        valid_currencies = [
            "USD",
            "EUR",
            "GBP",
            "JPY",
            "CAD",
            "AUD",
            "CHF",
            "CNY",
            "SEK",
            "NOK",
            "DKK",
            "PLN",
            "CZK",
            "HUF",
            "RUB",
            "BRL",
            "INR",
            "KRW",
            "SGD",
            "HKD",
            "NZD",
            "MXN",
            "ZAR",
            "TRY",
        ]

        v = v.upper()
        if v not in valid_currencies:
            raise ValueError(
                f"Currency must be a valid ISO 4217 code. Common codes: {valid_currencies[:10]}..."
            )

        return v

    @field_validator("stock_status")
    @classmethod
    def validate_stock_status(cls, v: str) -> str:
        """Validate stock status values."""
        valid_statuses = [
            "available",
            "limited",
            "out_of_stock",
            "discontinued",
            "obsolete",
            "special_order",
            "pre_order",
        ]

        v = v.lower()
        if v not in valid_statuses:
            raise ValueError(f"Stock status must be one of: {valid_statuses}")

        return v

    @field_validator("version")
    @classmethod
    def validate_version_format(cls, v: str) -> str:
        """Validate version format (semantic versioning)."""
        import re

        # Allow semantic versioning format (e.g., 1.0, 1.0.0, 2.1.3)
        version_pattern = r"^\d+\.\d+(\.\d+)?$"
        if not re.match(version_pattern, v):
            raise ValueError(
                "Version must follow semantic versioning format (e.g., 1.0, 1.0.0)"
            )

        return v


class ComponentUpdateSchema(BaseModel):
    """Schema for updating components with partial validation."""

    name: Optional[str] = Field(
        None, min_length=1, max_length=200, description="Component name"
    )
    manufacturer: Optional[str] = Field(
        None, min_length=1, max_length=100, description="Component manufacturer"
    )
    model_number: Optional[str] = Field(
        None, min_length=1, max_length=100, description="Manufacturer model/part number"
    )
    description: Optional[str] = Field(
        None, max_length=1000, description="Detailed component description"
    )

    # New relational fields
    component_type_id: Optional[int] = Field(
        None, ge=1, description="Component type ID (new relational approach)"
    )
    category_id: Optional[int] = Field(
        None, ge=1, description="Component category ID (new relational approach)"
    )
    specifications: Optional[Union[Dict[str, Any], ComponentSpecificationsSchema]] = (
        Field(None, description="Component technical specifications")
    )
    unit_price: Optional[Decimal] = Field(
        None, ge=0, decimal_places=2, description="Component unit price"
    )
    currency: Optional[str] = Field(
        None, min_length=3, max_length=3, description="Price currency (ISO 4217)"
    )
    supplier: Optional[str] = Field(
        None, max_length=100, description="Primary supplier name"
    )
    part_number: Optional[str] = Field(
        None, max_length=100, description="Supplier part number"
    )
    weight_kg: Optional[float] = Field(
        None, ge=0, description="Component weight in kilograms"
    )
    dimensions: Optional[ComponentDimensionsSchema] = Field(
        None, description="Physical dimensions"
    )
    is_active: Optional[bool] = Field(None, description="Whether component is active")
    is_preferred: Optional[bool] = Field(
        None, description="Whether component is preferred"
    )
    stock_status: Optional[str] = Field(None, description="Stock availability status")
    version: Optional[str] = Field(None, description="Component data version")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    # Apply same validators as create schema
    @field_validator("name", "manufacturer", "model_number", "description")
    @classmethod
    def sanitize_text_fields(cls, v: Optional[str]) -> Optional[str]:
        """Sanitize text fields for security and consistency."""
        return ComponentBaseSchema.sanitize_text_fields(v)

    @field_validator("currency")
    @classmethod
    def validate_currency_code(cls, v: Optional[str]) -> Optional[str]:
        """Validate ISO 4217 currency code."""
        if v is None:
            return v
        return ComponentCreateSchema.validate_currency_code(v)

    @field_validator("stock_status")
    @classmethod
    def validate_stock_status(cls, v: Optional[str]) -> Optional[str]:
        """Validate stock status values."""
        if v is None:
            return v
        return ComponentCreateSchema.validate_stock_status(v)

    @field_validator("version")
    @classmethod
    def validate_version_format(cls, v: Optional[str]) -> Optional[str]:
        """Validate version format (semantic versioning)."""
        if v is None:
            return v
        return ComponentCreateSchema.validate_version_format(v)


class ComponentReadSchema(ComponentBaseSchema, TimestampMixin):
    """Schema for reading components with full data."""

    id: int = Field(..., description="Unique component identifier")
    specifications: Optional[Dict[str, Any]] = Field(
        None, description="Component technical specifications"
    )
    unit_price: Optional[Decimal] = Field(None, description="Component unit price")
    currency: str = Field("EUR", description="Price currency")
    supplier: Optional[str] = Field(None, description="Primary supplier name")
    part_number: Optional[str] = Field(None, description="Supplier part number")
    weight_kg: Optional[float] = Field(
        None, description="Component weight in kilograms"
    )
    dimensions: Optional[ComponentDimensionsSchema] = Field(
        None, description="Physical dimensions"
    )
    is_active: bool = Field(True, description="Whether component is active")
    is_preferred: bool = Field(False, description="Whether component is preferred")
    stock_status: str = Field("available", description="Stock availability status")
    version: str = Field("1.0", description="Component data version")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    # Computed fields
    full_name: Optional[str] = Field(
        None, description="Full component name (manufacturer + model)"
    )
    display_name: Optional[str] = Field(None, description="Display name for UI")

    class Config:
        """Pydantic configuration."""

        from_attributes = True

    @model_validator(mode="after")
    def compute_derived_fields(self) -> "ComponentReadSchema":
        """Compute derived fields for display purposes."""
        # Compute full name
        if self.manufacturer and self.model_number and not self.full_name:
            object.__setattr__(
                self, "full_name", f"{self.manufacturer} {self.model_number}"
            )

        # Compute display name
        if not self.display_name:
            if self.name and self.name != self.model_number:
                object.__setattr__(
                    self,
                    "display_name",
                    f"{self.name} ({self.manufacturer} {self.model_number})",
                )
            else:
                object.__setattr__(
                    self, "display_name", f"{self.manufacturer} {self.model_number}"
                )

        return self


class ComponentSummarySchema(BaseSchema):
    """Schema for component summaries in lists."""

    id: int = Field(..., description="Unique component identifier")
    name: str = Field(..., description="Component name")
    manufacturer: str = Field(..., description="Component manufacturer")
    model_number: str = Field(..., description="Manufacturer model/part number")
    component_type_id: int = Field(..., description="Component type ID")
    category_id: int = Field(..., description="Component category ID")
    unit_price: Optional[Decimal] = Field(None, description="Component unit price")
    currency: str = Field("EUR", description="Price currency")
    is_active: bool = Field(True, description="Whether component is active")
    is_preferred: bool = Field(False, description="Whether component is preferred")
    stock_status: str = Field("available", description="Stock availability status")

    class Config:
        """Pydantic configuration."""

        from_attributes = True


class ComponentSearchSchema(BaseModel):
    """Schema for component search parameters."""

    search_term: Optional[str] = Field(
        None,
        max_length=200,
        description="Search term for name, description, manufacturer, or part number",
    )
    category_id: Optional[int] = Field(None, description="Filter by component category")
    component_type_id: Optional[int] = Field(
        None, description="Filter by component type"
    )
    manufacturer: Optional[str] = Field(
        None, max_length=100, description="Filter by manufacturer"
    )
    is_preferred: Optional[bool] = Field(None, description="Filter by preferred status")
    is_active: Optional[bool] = Field(True, description="Filter by active status")
    min_price: Optional[Decimal] = Field(None, ge=0, description="Minimum price filter")
    max_price: Optional[Decimal] = Field(None, ge=0, description="Maximum price filter")
    currency: Optional[str] = Field(
        None, min_length=3, max_length=3, description="Currency for price filters"
    )
    stock_status: Optional[str] = Field(None, description="Filter by stock status")
    specifications: Optional[Dict[str, Any]] = Field(
        None, description="Filter by technical specifications"
    )

    @field_validator("search_term")
    @classmethod
    def sanitize_search_term(cls, v: Optional[str]) -> Optional[str]:
        """Sanitize search term."""
        if v is None:
            return v

        v = v.strip()
        if not v:
            return None

        # Remove potentially harmful characters for search
        import re

        v = re.sub(r'[<>"\';]', "", v)

        return v

    @model_validator(mode="after")
    def validate_price_range(self) -> "ComponentSearchSchema":
        """Validate price range consistency."""
        if self.min_price is not None and self.max_price is not None:
            if self.min_price > self.max_price:
                raise ValueError("Minimum price cannot be greater than maximum price")

        return self


class FilterOperatorEnum(str, Enum):
    """Enumeration of supported filter operators for advanced search."""

    EQUALS = "eq"
    NOT_EQUALS = "ne"
    GREATER_THAN = "gt"
    GREATER_THAN_OR_EQUAL = "gte"
    LESS_THAN = "lt"
    LESS_THAN_OR_EQUAL = "lte"
    CONTAINS = "contains"
    STARTS_WITH = "starts_with"
    ENDS_WITH = "ends_with"
    IN = "in"
    NOT_IN = "not_in"
    BETWEEN = "between"
    FUZZY = "fuzzy"
    REGEX = "regex"
    IS_NULL = "is_null"
    IS_NOT_NULL = "is_not_null"


class LogicalOperatorEnum(str, Enum):
    """Enumeration of logical operators for combining filters."""

    AND = "and"
    OR = "or"
    NOT = "not"


class AdvancedFilterSchema(BaseModel):
    """Schema for individual advanced search filter."""

    field: str = Field(..., description="Field name to filter on")
    operator: FilterOperatorEnum = Field(..., description="Filter operator")
    value: Any = Field(..., description="Filter value")
    logical_operator: LogicalOperatorEnum = Field(
        default=LogicalOperatorEnum.AND, description="How to combine with other filters"
    )


class SpecificationFilterSchema(BaseModel):
    """Schema for specification-based filter."""

    path: str = Field(..., description="Dot notation path in specifications JSON")
    operator: FilterOperatorEnum = Field(..., description="Filter operator")
    value: Any = Field(..., description="Filter value")
    data_type: str = Field("string", description="Data type for proper casting")
    unit: Optional[str] = Field(None, description="Unit for conversion (if applicable)")
    logical_operator: LogicalOperatorEnum = Field(
        default=LogicalOperatorEnum.AND, description="How to combine with other filters"
    )


class RangeFilterSchema(BaseModel):
    """Schema for range filter."""

    field: str = Field(..., description="Field name to filter on")
    min_value: Optional[Union[int, float, Decimal]] = Field(
        None, description="Minimum value (inclusive by default)"
    )
    max_value: Optional[Union[int, float, Decimal]] = Field(
        None, description="Maximum value (inclusive by default)"
    )
    include_min: bool = Field(True, description="Whether to include minimum value")
    include_max: bool = Field(True, description="Whether to include maximum value")

    @model_validator(mode="after")
    def validate_range(self) -> "RangeFilterSchema":
        """Validate range consistency."""
        if (
            self.min_value is not None
            and self.max_value is not None
            and self.min_value > self.max_value
        ):
            raise ValueError("Minimum value cannot be greater than maximum value")
        return self


class ComponentAdvancedSearchSchema(BaseModel):
    """Schema for advanced component search with complex filtering."""

    # Basic search parameters
    search_term: Optional[str] = Field(
        None, max_length=200, description="Text search across multiple fields"
    )
    search_fields: Optional[List[str]] = Field(
        None,
        description="Specific fields to search in (defaults to name, description, manufacturer, part_number)",
    )
    fuzzy_search: bool = Field(False, description="Enable fuzzy text matching")

    # Basic filters
    basic_filters: Optional[List[AdvancedFilterSchema]] = Field(
        None, description="List of basic field filters"
    )

    # Specification filters
    specification_filters: Optional[List[SpecificationFilterSchema]] = Field(
        None, description="List of specification-based filters"
    )

    # Range filters
    range_filters: Optional[List[RangeFilterSchema]] = Field(
        None, description="List of range filters"
    )

    # Price range (convenience filter)
    price_range: Optional[Dict[str, Any]] = Field(
        None, description="Price range filter with currency support"
    )

    # Sorting
    sort_by: Optional[str] = Field("name", description="Field to sort by")
    sort_order: str = Field("asc", description="Sort order (asc/desc)")

    # Search options
    include_inactive: bool = Field(False, description="Include inactive components")
    include_deleted: bool = Field(False, description="Include soft-deleted components")

    @field_validator("search_term")
    @classmethod
    def sanitize_search_term(cls, v: Optional[str]) -> Optional[str]:
        """Sanitize search term."""
        if v is None:
            return v

        v = v.strip()
        if not v:
            return None

        # Remove potentially harmful characters for search
        import re

        v = re.sub(r'[<>"\';]', "", v)

        return v

    @field_validator("sort_order")
    @classmethod
    def validate_sort_order(cls, v: str) -> str:
        """Validate sort order."""
        if v.lower() not in ["asc", "desc"]:
            raise ValueError("Sort order must be 'asc' or 'desc'")
        return v.lower()


class ComponentSearchResultSchema(BaseModel):
    """Schema for individual search result with relevance scoring."""

    component: ComponentReadSchema = Field(..., description="Component data")
    relevance_score: Optional[float] = Field(
        None, description="Search relevance score (0.0 to 1.0)"
    )
    matched_fields: Optional[List[str]] = Field(
        None, description="Fields that matched the search criteria"
    )


class ComponentAdvancedSearchResponseSchema(PaginatedResponseSchema):
    """Response schema for advanced component search."""

    items: List[ComponentSearchResultSchema] = Field(..., description="Search results")
    search_metadata: Optional[Dict[str, Any]] = Field(
        None, description="Search metadata (query time, filters applied, etc.)"
    )
    suggestions: Optional[List[str]] = Field(
        None, description="Search suggestions for refinement"
    )


class ComponentPaginatedResponseSchema(PaginatedResponseSchema):
    """Paginated response schema for components."""

    items: List[ComponentReadSchema] = Field(..., description="List of components")


class ComponentListResponseSchema(PaginatedResponseSchema):
    """Paginated response schema for component summaries."""

    items: List[ComponentSummarySchema] = Field(
        ..., description="List of component summaries"
    )


class ComponentBulkCreateSchema(BaseModel):
    """Schema for bulk component creation."""

    components: List[ComponentCreateSchema] = Field(
        ..., min_length=1, max_length=100, description="List of components to create"
    )
    validate_duplicates: bool = Field(
        True, description="Whether to validate for duplicate components"
    )
    skip_invalid: bool = Field(
        False, description="Whether to skip invalid components and continue"
    )

    @field_validator("components")
    @classmethod
    def validate_components_list(
        cls, v: List[ComponentCreateSchema]
    ) -> List[ComponentCreateSchema]:
        """Validate components list for bulk operations."""
        if len(v) > 100:
            raise ValueError("Maximum 100 components allowed per bulk operation")

        return v


class ComponentBulkUpdateSchema(BaseModel):
    """Schema for bulk component updates."""

    component_ids: List[int] = Field(
        ..., min_length=1, max_length=100, description="List of component IDs to update"
    )
    update_data: ComponentUpdateSchema = Field(
        ..., description="Update data to apply to all components"
    )
    skip_missing: bool = Field(
        True, description="Whether to skip missing components and continue"
    )


class ComponentValidationResultSchema(BaseModel):
    """Schema for component validation results."""

    is_valid: bool = Field(..., description="Whether component is valid")
    errors: List[str] = Field(
        default_factory=list, description="List of validation errors"
    )
    warnings: List[str] = Field(
        default_factory=list, description="List of validation warnings"
    )
    component_id: Optional[int] = Field(None, description="Component ID if applicable")


class ComponentStatsSchema(BaseModel):
    """Schema for component statistics."""

    total_components: int = Field(..., description="Total number of components")
    active_components: int = Field(..., description="Number of active components")
    preferred_components: int = Field(..., description="Number of preferred components")
    components_by_category: Dict[str, int] = Field(
        ..., description="Component count by category"
    )
    components_by_manufacturer: Dict[str, int] = Field(
        ..., description="Component count by manufacturer"
    )
    average_price: Optional[Decimal] = Field(
        None, description="Average component price"
    )
    price_range: Optional[Dict[str, Decimal]] = Field(
        None, description="Price range (min/max)"
    )


__all__ = [
    "ComponentSpecificationsSchema",
    "ComponentDimensionsSchema",
    "ComponentBaseSchema",
    "ComponentCreateSchema",
    "ComponentUpdateSchema",
    "ComponentReadSchema",
    "ComponentSummarySchema",
    "ComponentSearchSchema",
    "FilterOperatorEnum",
    "LogicalOperatorEnum",
    "AdvancedFilterSchema",
    "SpecificationFilterSchema",
    "RangeFilterSchema",
    "ComponentAdvancedSearchSchema",
    "ComponentSearchResultSchema",
    "ComponentAdvancedSearchResponseSchema",
    "ComponentPaginatedResponseSchema",
    "ComponentListResponseSchema",
    "ComponentBulkCreateSchema",
    "ComponentBulkUpdateSchema",
    "ComponentValidationResultSchema",
    "ComponentStatsSchema",
    "ComponentBulkUpdateSchema",
    "ComponentValidationResultSchema",
    "ComponentStatsSchema",
]
