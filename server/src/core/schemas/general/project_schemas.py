"""Project schemas.

This module provides Pydantic schemas for project management operations,
supporting comprehensive project lifecycle management.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator
from src.core.enums import ProjectStatus
from src.core.schemas.base_schemas import (
    BaseSchema,
    PaginatedResponseSchema,
    TimestampMixin,
)
from src.core.schemas.general.project_member_schemas import ProjectMemberReadSchema


class ProjectBaseSchema(BaseSchema):
    """Base schema for projects."""

    name: str = Field(..., min_length=1, max_length=255, description="Project name")
    description: Optional[str] = Field(
        None, max_length=2000, description="Project description"
    )
    status: ProjectStatus = Field(ProjectStatus.DRAFT, description="Project status")
    client: Optional[str] = Field(None, max_length=255, description="Client name")
    location: Optional[str] = Field(
        None, max_length=255, description="Project location"
    )
    is_offline: bool = Field(False, description="Whether the project is offline-only")
    database_url: Optional[str] = Field(
        None,
        description="Optional local database URL for project-specific data storage",
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: str) -> str:
        """Validate and normalize project name."""
        if not v or not v.strip():
            raise ValueError("Project name cannot be empty or just whitespace")
        name = v.strip()
        if len(name) > 255:
            raise ValueError("Project name cannot exceed 255 characters")
        return name

    @field_validator("description")
    @classmethod
    def validate_description(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize project description."""
        if v is not None:
            description = v.strip()
            if len(description) > 2000:
                raise ValueError("Project description cannot exceed 2000 characters")
            return description if description else None
        return v

    @field_validator("client")
    @classmethod
    def validate_client(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize client name."""
        if v is not None:
            client = v.strip()
            if len(client) > 255:
                raise ValueError("Client name cannot exceed 255 characters")
            return client if client else None
        return v

    @field_validator("location")
    @classmethod
    def validate_location(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize location."""
        if v is not None:
            location = v.strip()
            if len(location) > 255:
                raise ValueError("Location cannot exceed 255 characters")
            return location if location else None
        return v

    @field_validator("database_url")
    @classmethod
    def validate_database_url(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize database URL."""
        if v is not None:
            database_url = v.strip()
            if not database_url:
                return None
            # Basic URL validation - must start with supported database schemes
            if not any(
                database_url.startswith(scheme)
                for scheme in ["postgresql://", "postgresql+asyncpg://"]
            ):
                raise ValueError(
                    "Database URL must use supported schemes: postgresql://, postgresql+asyncpg://"
                )
            return database_url
        return v


class ProjectCreateSchema(ProjectBaseSchema):
    """Schema for creating projects."""

    project_number: Optional[str] = Field(
        None, max_length=100, description="Project number/identifier"
    )

    @field_validator("project_number")
    @classmethod
    def validate_project_number(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize project number."""
        if v is not None:
            project_number = v.strip()
            if len(project_number) > 100:
                raise ValueError("Project number cannot exceed 100 characters")
            return project_number if project_number else None
        return v


class ProjectUpdateSchema(BaseModel):
    """Schema for updating projects."""

    name: Optional[str] = Field(
        None, min_length=1, max_length=255, description="Project name"
    )
    description: Optional[str] = Field(
        None, max_length=2000, description="Project description"
    )
    status: Optional[ProjectStatus] = Field(None, description="Project status")
    client_name: Optional[str] = Field(None, max_length=255, description="Client name")
    client_contact: Optional[str] = Field(
        None, max_length=500, description="Client contact information"
    )
    location: Optional[str] = Field(
        None, max_length=255, description="Project location"
    )
    start_date: Optional[datetime] = Field(None, description="Project start date")
    end_date: Optional[datetime] = Field(None, description="Project end date")
    budget: Optional[float] = Field(None, ge=0, description="Project budget")
    currency: Optional[str] = Field(None, max_length=10, description="Budget currency")
    is_offline: Optional[bool] = Field(
        None, description="Whether the project is offline-only"
    )
    database_url: Optional[str] = Field(
        None,
        description="Optional local database URL for project-specific data storage",
    )

    @field_validator("name")
    @classmethod
    def validate_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize project name."""
        if v is not None:
            if not v.strip():
                raise ValueError("Project name cannot be empty or just whitespace")
            name = v.strip()
            if len(name) > 255:
                raise ValueError("Project name cannot exceed 255 characters")
            return name
        return v

    @field_validator("description")
    @classmethod
    def validate_description(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize project description."""
        if v is not None:
            description = v.strip()
            if len(description) > 2000:
                raise ValueError("Project description cannot exceed 2000 characters")
            return description if description else None
        return v

    @field_validator("client_name")
    @classmethod
    def validate_client_name(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize client name."""
        if v is not None:
            client_name = v.strip()
            if len(client_name) > 255:
                raise ValueError("Client name cannot exceed 255 characters")
            return client_name if client_name else None
        return v

    @field_validator("client_contact")
    @classmethod
    def validate_client_contact(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize client contact."""
        if v is not None:
            contact = v.strip()
            if len(contact) > 500:
                raise ValueError(
                    "Client contact information cannot exceed 500 characters"
                )
            return contact if contact else None
        return v

    @field_validator("location")
    @classmethod
    def validate_location(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize location."""
        if v is not None:
            location = v.strip()
            if len(location) > 255:
                raise ValueError("Location cannot exceed 255 characters")
            return location if location else None
        return v

    @field_validator("currency")
    @classmethod
    def validate_currency(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize currency."""
        if v is not None:
            currency = v.strip().upper()
            if len(currency) > 10:
                raise ValueError("Currency code cannot exceed 10 characters")
            return currency if currency else None
        return v

    @field_validator("database_url")
    @classmethod
    def validate_database_url(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize database URL."""
        if v is not None:
            database_url = v.strip()
            if not database_url:
                return None
            # Modified to only accept PostgreSQL URLs
            if not any(
                database_url.startswith(scheme)
                for scheme in ["postgresql://", "postgresql+asyncpg://"]
            ):
                raise ValueError(
                    "Database URL must use supported schemes: postgresql://, postgresql+asyncpg://"
                )
            return database_url
        return v


class ProjectReadSchema(ProjectBaseSchema, TimestampMixin):
    """Schema for reading projects."""

    id: int = Field(..., description="Unique identifier")
    project_number: str = Field(..., description="Project number/identifier")
    members: List["ProjectMemberReadSchema"] = Field([], description="Project members")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class ProjectListResponseSchema(PaginatedResponseSchema):
    """Paginated response schema for projects."""

    items: List[ProjectReadSchema] = Field(..., description="List of projects")


class ProjectSummarySchema(BaseSchema):
    """Schema for project summaries."""

    id: int = Field(..., description="Unique identifier")
    name: str = Field(..., description="Project name")
    status: ProjectStatus = Field(..., description="Project status")
    client_name: Optional[str] = Field(None, description="Client name")
    location: Optional[str] = Field(None, description="Project location")
    start_date: Optional[datetime] = Field(None, description="Project start date")
    end_date: Optional[datetime] = Field(None, description="Project end date")
    budget: Optional[float] = Field(None, description="Project budget")
    currency: Optional[str] = Field(None, description="Budget currency")
    owner_id: int = Field(..., description="Project owner user ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: datetime = Field(..., description="Last update timestamp")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


__all__ = [
    "ProjectBaseSchema",
    "ProjectCreateSchema",
    "ProjectUpdateSchema",
    "ProjectReadSchema",
    "ProjectListResponseSchema",
    "ProjectSummarySchema",
]

ProjectReadSchema.model_rebuild()
