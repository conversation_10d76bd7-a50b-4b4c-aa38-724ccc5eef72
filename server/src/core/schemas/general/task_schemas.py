"""Task Management Schemas.

Pydantic schemas for task management API endpoints, providing request and
response validation with strict type safety and enum handling.
"""

from datetime import datetime
from typing import List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, field_validator

from src.core.enums import TaskPriority, TaskStatus
from src.core.schemas.base_schemas import BaseSchema, TimestampMixin, PaginatedResponseSchema


class TaskAssignmentBaseSchema(BaseSchema):
    """Base schema for task assignments."""

    user_id: int = Field(..., ge=1, description="ID of the assigned user")
    is_active: bool = Field(True, description="Whether the assignment is active")


class TaskAssignmentCreateSchema(TaskAssignmentBaseSchema):
    """Schema for creating task assignments."""

    assigned_by_user_id: Optional[int] = Field(
        None, ge=1, description="ID of the user who made the assignment"
    )


class TaskAssignmentReadSchema(TaskAssignmentBaseSchema, TimestampMixin):
    """Schema for reading task assignments."""

    id: int = Field(..., description="Unique assignment identifier")
    task_id: int = Field(..., description="ID of the assigned task")
    assigned_at: datetime = Field(..., description="When the assignment was made")
    assigned_by_user_id: Optional[int] = Field(
        None, description="ID of the user who made the assignment"
    )

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class TaskBaseSchema(BaseSchema):
    """Base schema for tasks."""

    title: str = Field(..., min_length=1, max_length=255, description="Task title")
    description: Optional[str] = Field(
        None, max_length=2000, description="Detailed task description"
    )
    due_date: Optional[datetime] = Field(None, description="Optional due date for the task")
    priority: TaskPriority = Field(TaskPriority.MEDIUM, description="Task priority level")
    status: TaskStatus = Field(TaskStatus.NOT_STARTED, description="Current task status")

    @field_validator("title")
    @classmethod
    def validate_title(cls, v: str) -> str:
        """Validate and normalize task title."""
        title = v.strip()
        if not title:
            raise ValueError("Task title cannot be empty")
        return title

    @field_validator("description")
    @classmethod
    def validate_description(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize task description."""
        if v is not None:
            description = v.strip()
            return description if description else None
        return v


class TaskCreateSchema(TaskBaseSchema):
    """Schema for creating tasks."""

    project_id: int = Field(..., ge=1, description="ID of the project this task belongs to")
    assigned_user_ids: Optional[List[int]] = Field(
        None, description="List of user IDs to assign to this task"
    )

    @field_validator("assigned_user_ids")
    @classmethod
    def validate_assigned_user_ids(cls, v: Optional[List[int]]) -> Optional[List[int]]:
        """Validate assigned user IDs."""
        if v is not None:
            # Remove duplicates while preserving order
            seen = set()
            unique_ids = []
            for user_id in v:
                if user_id not in seen:
                    if user_id <= 0:
                        raise ValueError("User IDs must be positive integers")
                    seen.add(user_id)
                    unique_ids.append(user_id)
            return unique_ids if unique_ids else None
        return v


class TaskUpdateSchema(BaseModel):
    """Schema for updating tasks."""

    title: Optional[str] = Field(
        None, min_length=1, max_length=255, description="Task title"
    )
    description: Optional[str] = Field(
        None, max_length=2000, description="Detailed task description"
    )
    due_date: Optional[datetime] = Field(None, description="Optional due date for the task")
    priority: Optional[TaskPriority] = Field(None, description="Task priority level")
    status: Optional[TaskStatus] = Field(None, description="Current task status")

    @field_validator("title")
    @classmethod
    def validate_title(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize task title."""
        if v is not None:
            title = v.strip()
            if not title:
                raise ValueError("Task title cannot be empty")
            return title
        return v

    @field_validator("description")
    @classmethod
    def validate_description(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize task description."""
        if v is not None:
            description = v.strip()
            return description if description else None
        return v


class TaskReadSchema(TaskBaseSchema, TimestampMixin):
    """Schema for reading tasks."""

    id: int = Field(..., description="Unique task identifier")
    task_id: str = Field(..., description="Unique UUID identifier for the task")
    project_id: int = Field(..., description="ID of the project this task belongs to")
    assignments: List[TaskAssignmentReadSchema] = Field(
        [], description="List of user assignments for this task"
    )

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class TaskSummarySchema(BaseSchema):
    """Schema for task summaries."""

    id: int = Field(..., description="Unique task identifier")
    task_id: str = Field(..., description="Unique UUID identifier for the task")
    title: str = Field(..., description="Task title")
    status: TaskStatus = Field(..., description="Current task status")
    priority: TaskPriority = Field(..., description="Task priority level")
    due_date: Optional[datetime] = Field(None, description="Optional due date for the task")
    project_id: int = Field(..., description="ID of the project this task belongs to")
    assigned_user_count: int = Field(0, description="Number of users assigned to this task")
    created_at: datetime = Field(..., description="Creation timestamp")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class TaskAssignmentRequestSchema(BaseSchema):
    """Schema for task assignment requests."""

    user_ids: List[int] = Field(..., min_items=1, description="List of user IDs to assign")

    @field_validator("user_ids")
    @classmethod
    def validate_user_ids(cls, v: List[int]) -> List[int]:
        """Validate user IDs."""
        # Remove duplicates while preserving order
        seen = set()
        unique_ids = []
        for user_id in v:
            if user_id not in seen:
                if user_id <= 0:
                    raise ValueError("User IDs must be positive integers")
                seen.add(user_id)
                unique_ids.append(user_id)
        
        if not unique_ids:
            raise ValueError("At least one valid user ID is required")
        
        return unique_ids


class TaskUnassignmentRequestSchema(BaseSchema):
    """Schema for task unassignment requests."""

    user_id: int = Field(..., ge=1, description="ID of the user to unassign")


class TaskStatusUpdateSchema(BaseSchema):
    """Schema for updating task status."""

    status: TaskStatus = Field(..., description="New task status")


class TaskStatisticsSchema(BaseSchema):
    """Schema for task statistics."""

    total_tasks: int = Field(..., ge=0, description="Total number of tasks")
    status_counts: dict = Field(..., description="Count of tasks by status")
    priority_counts: dict = Field(..., description="Count of tasks by priority")
    overdue_count: int = Field(..., ge=0, description="Number of overdue tasks")

    class Config:
        """Configuration for the schema."""

        from_attributes = True


class TaskListResponseSchema(PaginatedResponseSchema):
    """Paginated response schema for tasks."""

    items: List[TaskReadSchema] = Field(..., description="List of tasks")


class TaskSearchRequestSchema(BaseSchema):
    """Schema for task search requests."""

    search_term: str = Field(..., min_length=1, max_length=255, description="Search term")
    project_id: Optional[int] = Field(None, ge=1, description="Optional project ID filter")

    @field_validator("search_term")
    @classmethod
    def validate_search_term(cls, v: str) -> str:
        """Validate and normalize search term."""
        term = v.strip()
        if not term:
            raise ValueError("Search term cannot be empty")
        return term


__all__ = [
    "TaskAssignmentBaseSchema",
    "TaskAssignmentCreateSchema",
    "TaskAssignmentReadSchema",
    "TaskBaseSchema",
    "TaskCreateSchema",
    "TaskUpdateSchema",
    "TaskReadSchema",
    "TaskSummarySchema",
    "TaskAssignmentRequestSchema",
    "TaskUnassignmentRequestSchema",
    "TaskStatusUpdateSchema",
    "TaskStatisticsSchema",
    "TaskListResponseSchema",
    "TaskSearchRequestSchema",
]
