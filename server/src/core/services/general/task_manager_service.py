"""Task Manager Service for business logic operations.

This module provides the TaskManagerService class for handling all business
logic related to task management, orchestrating calls to repositories and
other services.
"""

from typing import List, Optional, Dict, Any
from datetime import datetime

from src.config.logging_config import logger
from src.core.errors.exceptions import NotFoundError, ValidationError
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.models.general.task import Task, TaskAssignment
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.repositories.general.task_repository import TaskRepository
from src.core.repositories.general.user_repository import UserRepository
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.utils.pagination_utils import PaginationParams, SortParams, PaginationResult


class TaskManagerService:
    """Service for task management business logic."""

    def __init__(
        self,
        task_repository: TaskRepository,
        user_repository: UserRepository,
        project_repository: ProjectRepository,
    ):
        """Initialize the TaskManagerService."""
        self.task_repository = task_repository
        self.user_repository = user_repository
        self.project_repository = project_repository
        logger.debug("TaskManagerService initialized")

    @handle_service_errors("create_task_with_assignments")
    @monitor_service_performance("create_task_with_assignments")
    async def create_task_with_assignments(
        self,
        project_id: int,
        title: str,
        description: Optional[str] = None,
        due_date: Optional[datetime] = None,
        priority: str = "Medium",
        status: str = "Not Started",
        assigned_user_ids: Optional[List[int]] = None,
        created_by_user_id: Optional[int] = None,
    ) -> Task:
        """Create a new task with optional user assignments."""
        logger.info(f"Creating task '{title}' for project {project_id}")

        # Validate project exists
        project = await self.project_repository.get_by_id(project_id)
        if not project:
            raise NotFoundError(code="PROJECT_NOT_FOUND", detail=f"Project with ID {project_id} not found")

        # Validate assigned users exist
        if assigned_user_ids:
            for user_id in assigned_user_ids:
                user = await self.user_repository.get_by_id(user_id)
                if not user:
                    raise NotFoundError(code="USER_NOT_FOUND", detail=f"User with ID {user_id} not found")

        # Create task data
        from src.core.enums import TaskPriority, TaskStatus

        task_data = {
            "project_id": project_id,
            "title": title,
            "description": description,
            "due_date": due_date,
            "priority": TaskPriority(priority),
            "status": TaskStatus(status),
        }

        # Create the task
        task = await self.task_repository.create(task_data)

        # Create task assignments if users are specified
        if assigned_user_ids:
            for user_id in assigned_user_ids:
                assignment_data = {
                    "task_id": task.id,
                    "user_id": user_id,
                    "assigned_by_user_id": created_by_user_id,
                    "name": f"Assignment for task {task.title}",
                }

                # Create TaskAssignment through the session
                assignment = TaskAssignment(**assignment_data)
                self.task_repository.db_session.add(assignment)

        await self.task_repository.db_session.flush()

        # Reload task with assignments
        created_task = await self.task_repository.get_by_task_id(task.task_id)
        logger.info(f"Successfully created task {task.task_id} with {len(assigned_user_ids or [])} assignments")

        return created_task

    @handle_service_errors("get_tasks_for_project")
    @monitor_service_performance("get_tasks_for_project")
    async def get_tasks_for_project(
        self,
        project_id: int,
        pagination_params: Optional[PaginationParams] = None,
        sort_params: Optional[SortParams] = None,
        filters: Optional[Dict[str, Any]] = None,
        include_deleted: bool = False,
    ) -> List[Task]:
        """Get all tasks for a specific project with optional pagination and filters."""
        logger.debug(f"Retrieving tasks for project {project_id}")

        # Validate project exists
        project = await self.project_repository.get_by_id(project_id)
        if not project:
            raise NotFoundError(code="PROJECT_NOT_FOUND", detail=f"Project with ID {project_id} not found")

        if pagination_params:
            result = await self.task_repository.get_paginated_by_project_id(
                project_id=project_id,
                pagination_params=pagination_params,
                sort_params=sort_params,
                filters=filters,
                include_deleted=include_deleted,
            )
            return result.items
        else:
            return await self.task_repository.get_all_by_project_id(
                project_id=project_id, include_deleted=include_deleted
            )

    @handle_service_errors("get_task_by_id")
    @monitor_service_performance("get_task_by_id")
    async def get_task_by_id(self, task_id: str) -> Task:
        """Get a task by its task_id."""
        logger.debug(f"Retrieving task {task_id}")

        task = await self.task_repository.get_by_task_id(task_id)
        if not task:
            raise NotFoundError(code="TASK_NOT_FOUND", detail=f"Task with ID {task_id} not found")

        return task

    @handle_service_errors("update_task")
    @monitor_service_performance("update_task")
    async def update_task(
        self,
        task_id: str,
        title: Optional[str] = None,
        description: Optional[str] = None,
        due_date: Optional[datetime] = None,
        priority: Optional[str] = None,
        status: Optional[str] = None,
        updated_by_user_id: Optional[int] = None,
    ) -> Task:
        """Update an existing task."""
        logger.info(f"Updating task {task_id}")

        # Get existing task
        task = await self.task_repository.get_by_task_id(task_id)
        if not task:
            raise NotFoundError(code="TASK_NOT_FOUND", detail=f"Task with ID {task_id} not found")

        # Prepare update data
        update_data = {}
        if title is not None:
            update_data["title"] = title
        if description is not None:
            update_data["description"] = description
        if due_date is not None:
            update_data["due_date"] = due_date
        if priority is not None:
            from src.core.enums import TaskPriority

            update_data["priority"] = TaskPriority(priority)
        if status is not None:
            from src.core.enums import TaskStatus

            update_data["status"] = TaskStatus(status)

        if not update_data:
            logger.debug(f"No fields to update for task {task_id}")
            return task

        # Update the task
        updated_task = await self.task_repository.update(task.id, update_data)
        logger.info(f"Successfully updated task {task_id}")

        return updated_task

    @handle_service_errors("delete_task")
    @monitor_service_performance("delete_task")
    async def delete_task(self, task_id: str, deleted_by_user_id: int) -> bool:
        """Soft delete a task."""
        logger.info(f"Deleting task {task_id}")

        success = await self.task_repository.soft_delete_by_task_id(
            task_id=task_id, deleted_by_user_id=deleted_by_user_id
        )

        if not success:
            raise NotFoundError(code="TASK_NOT_FOUND", detail=f"Task with ID {task_id} not found")

        logger.info(f"Successfully deleted task {task_id}")
        return success

    @handle_service_errors("assign_users_to_task")
    @monitor_service_performance("assign_users_to_task")
    async def assign_users_to_task(
        self,
        task_id: str,
        user_ids: List[int],
        assigned_by_user_id: Optional[int] = None,
    ) -> Task:
        """Assign users to a task."""
        logger.info(f"Assigning {len(user_ids)} users to task {task_id}")

        # Get existing task
        task = await self.task_repository.get_by_task_id(task_id)
        if not task:
            raise NotFoundError(code="TASK_NOT_FOUND", detail=f"Task with ID {task_id} not found")

        # Validate users exist
        for user_id in user_ids:
            user = await self.user_repository.get_by_id(user_id)
            if not user:
                raise NotFoundError(code="USER_NOT_FOUND", detail=f"User with ID {user_id} not found")

        # Get existing active assignments
        existing_user_ids = {
            assignment.user_id for assignment in task.assignments if assignment.is_active and not assignment.is_deleted
        }

        # Create new assignments for users not already assigned
        new_assignments = 0
        for user_id in user_ids:
            if user_id not in existing_user_ids:
                assignment_data = {
                    "task_id": task.id,
                    "user_id": user_id,
                    "assigned_by_user_id": assigned_by_user_id,
                    "name": f"Assignment for task {task.title}",
                }

                assignment = TaskAssignment(**assignment_data)
                self.task_repository.db_session.add(assignment)
                new_assignments += 1

        await self.task_repository.db_session.flush()

        # Reload task with updated assignments
        updated_task = await self.task_repository.get_by_task_id(task_id)
        logger.info(f"Successfully created {new_assignments} new assignments for task {task_id}")

        return updated_task

    @handle_service_errors("unassign_user_from_task")
    @monitor_service_performance("unassign_user_from_task")
    async def unassign_user_from_task(
        self, task_id: str, user_id: int, unassigned_by_user_id: Optional[int] = None
    ) -> Task:
        """Unassign a user from a task."""
        logger.info(f"Unassigning user {user_id} from task {task_id}")

        # Get existing task
        task = await self.task_repository.get_by_task_id(task_id)
        if not task:
            raise NotFoundError(code="TASK_NOT_FOUND", detail=f"Task with ID {task_id} not found")

        # Find and deactivate the assignment
        assignment_found = False
        for assignment in task.assignments:
            if assignment.user_id == user_id and assignment.is_active and not assignment.is_deleted:
                assignment.is_active = False
                assignment.deleted_at = datetime.utcnow()
                assignment.deleted_by_user_id = unassigned_by_user_id
                assignment_found = True
                break

        if not assignment_found:
            raise NotFoundError(detail=f"Active assignment for user {user_id} on task {task_id} not found")

        await self.task_repository.db_session.flush()

        # Reload task with updated assignments
        updated_task = await self.task_repository.get_by_task_id(task_id)
        logger.info(f"Successfully unassigned user {user_id} from task {task_id}")

        return updated_task

    @handle_service_errors("get_user_tasks")
    @monitor_service_performance("get_user_tasks")
    async def get_user_tasks(
        self,
        user_id: int,
        include_completed: bool = True,
        include_deleted: bool = False,
    ) -> List[Task]:
        """Get all tasks assigned to a specific user."""
        logger.debug(f"Retrieving tasks for user {user_id}")

        # Validate user exists
        user = await self.user_repository.get_by_id(user_id)
        if not user:
            raise NotFoundError(code="USER_NOT_FOUND", detail=f"User with ID {user_id} not found")

        return await self.task_repository.get_by_assigned_user(
            user_id=user_id,
            include_completed=include_completed,
            include_deleted=include_deleted,
        )

    @handle_service_errors("get_task_statistics")
    @monitor_service_performance("get_task_statistics")
    async def get_task_statistics(self, project_id: int) -> Dict[str, Any]:
        """Get task statistics for a project."""
        logger.debug(f"Retrieving task statistics for project {project_id}")

        # Validate project exists
        project = await self.project_repository.get_by_id(project_id)
        if not project:
            raise NotFoundError(code="PROJECT_NOT_FOUND", detail=f"Project with ID {project_id} not found")

        return await self.task_repository.get_task_statistics(project_id)
