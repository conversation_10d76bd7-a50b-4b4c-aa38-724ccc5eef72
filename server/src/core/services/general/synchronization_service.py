"""Synchronization Service for Unified Local Database Management.

This module provides the SynchronizationService class responsible for ensuring
data consistency between local PostgreSQL instances and the central PostgreSQL
database. It implements bi-directional synchronization with conflict resolution
and change detection capabilities.

Key Features:
- Bi-directional data synchronization between local and central databases
- Change Data Capture (CDC) for efficient incremental synchronization
- Conflict resolution strategies for concurrent modifications
- Transaction-safe synchronization operations
- Comprehensive logging and monitoring integration
- Support for selective entity synchronization
"""

from typing import Any, Dict, List, Optional, Set, Union, TYPE_CHECKING
from datetime import datetime
from enum import Enum

from src.config.logging_config import logger
from src.core.database.connection_manager import DynamicConnectionManager
from src.core.errors.exceptions import (
    BusinessLogicError,
    DatabaseError,
    SynchronizationError,
)
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.monitoring.unified_performance_monitor import monitor_service_performance

if TYPE_CHECKING:
    from src.core.repositories.general.project_repository import ProjectRepository


class SyncOperation(Enum):
    """Enumeration of synchronization operations."""

    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"


class SyncDirection(Enum):
    """Enumeration of synchronization directions."""

    LOCAL_TO_CENTRAL = "local_to_central"
    CENTRAL_TO_LOCAL = "central_to_local"
    BIDIRECTIONAL = "bidirectional"


class ChangeRecord:
    """Represents a single change record for synchronization.

    This class encapsulates all information needed to track and apply
    changes between local and central databases.
    """

    def __init__(
        self,
        entity_type: str,
        entity_id: Union[int, str],
        operation: SyncOperation,
        changed_fields: Optional[Dict[str, Any]] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        timestamp: Optional[datetime] = None,
        source: Optional[str] = None,
    ):
        """Initialize a change record.

        Args:
            entity_type: Type of entity (e.g., 'project', 'component')
            entity_id: Unique identifier of the entity
            operation: Type of operation (CREATE, UPDATE, DELETE)
            changed_fields: Dict of field names to new values
            old_values: Previous values before change
            new_values: New values after change
            timestamp: When the change occurred
            source: Source database ('local' or 'central')
        """
        self.entity_type = entity_type
        self.entity_id = entity_id
        self.operation = operation
        self.changed_fields = changed_fields or {}
        self.old_values = old_values or {}
        self.new_values = new_values or {}
        self.timestamp = timestamp or datetime.utcnow()
        self.source = source

    def __repr__(self) -> str:
        """String representation of the change record."""
        return (
            f"ChangeRecord(entity_type='{self.entity_type}', "
            f"entity_id={self.entity_id}, operation={self.operation.value}, "
            f"timestamp={self.timestamp})"
        )


class SynchronizationService:
    """Service class for database synchronization operations.

    This service manages bi-directional synchronization between local PostgreSQL
    instances and the central PostgreSQL database, ensuring data consistency
    while handling conflicts and maintaining transaction integrity.
    """

    def __init__(
        self,
        connection_manager: DynamicConnectionManager,
        project_repository: "ProjectRepository",
        sync_config: Optional[Dict[str, Any]] = None,
    ):
        """Initialize the SynchronizationService.

        Args:
            connection_manager: Dynamic connection manager for database access
            project_repository: Repository for project operations
            sync_config: Optional configuration for synchronization behavior
        """
        self.connection_manager = connection_manager
        self.project_repository = project_repository
        self.sync_config = sync_config or self._get_default_sync_config()

        logger.info(
            f"SynchronizationService initialized with config: {self.sync_config}"
        )

    def _get_default_sync_config(self) -> Dict[str, Any]:
        """Get default synchronization configuration.

        Returns:
            Default configuration dictionary
        """
        return {
            "batch_size": 100,
            "timeout_seconds": 300,
            "retry_attempts": 3,
            "conflict_resolution": "last_write_wins",
            "sync_interval_minutes": 15,
            "enable_incremental_sync": True,
            "monitored_entities": ["project", "component", "user", "project_member"],
        }

    @handle_service_errors("get_last_sync_timestamp")
    @monitor_service_performance("get_last_sync_timestamp")
    async def _get_last_sync_timestamp(self, project_id: int) -> Optional[datetime]:
        """Retrieve the last successful synchronization timestamp for a project.

        This method queries the SynchronizationLog table to determine when the
        last successful sync occurred for the given project.

        Args:
            project_id: Unique identifier of the project

        Returns:
            DateTime of last successful sync, or None if never synchronized
        """
        from sqlalchemy import select
        from src.core.models.general.synchronization_log import SynchronizationLog
        from src.core.enums.system_enums import SyncStatus

        logger.debug(f"Retrieving last sync timestamp for project: {project_id}")

        try:
            async with self.connection_manager.get_session(
                self.project_repository,
                None,  # Use central database for sync logs
            ) as session:
                # Query for the most recent successful synchronization
                stmt = (
                    select(SynchronizationLog.completed_at)
                    .where(
                        SynchronizationLog.project_id == project_id,
                        SynchronizationLog.status == SyncStatus.COMPLETED,
                    )
                    .order_by(SynchronizationLog.completed_at.desc())
                    .limit(1)
                )

                result = await session.execute(stmt)
                last_sync_timestamp = result.scalar_one_or_none()

                if last_sync_timestamp:
                    logger.debug(
                        f"Found last sync timestamp for project {project_id}: {last_sync_timestamp}"
                    )
                else:
                    logger.debug(
                        f"No previous successful sync found for project: {project_id}"
                    )

                return last_sync_timestamp

        except Exception as e:
            logger.error(
                f"Error retrieving last sync timestamp for project {project_id}: {e}"
            )
            # Return None to indicate no previous synchronization in case of error
            return None

    @handle_service_errors("compare_entity_data")
    @monitor_service_performance("compare_entity_data")
    def _compare_entity_data(
        self, local_data: Dict[str, Any], central_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Compare two entity data dictionaries and identify differences.

        This utility function performs a deep comparison of two data dictionaries
        and returns information about what has changed between them.

        Args:
            local_data: Data from the local database
            central_data: Data from the central database

        Returns:
            Dictionary containing comparison results with keys:
            - 'has_differences': bool indicating if there are any differences
            - 'changed_fields': dict of field names to {'local': value, 'central': value}
            - 'local_only': dict of fields only present in local data
            - 'central_only': dict of fields only present in central data
            - 'identical_fields': list of field names that are identical
        """
        logger.debug("Comparing entity data between local and central databases")

        if not isinstance(local_data, dict) or not isinstance(central_data, dict):
            raise ValueError("Both local_data and central_data must be dictionaries")

        result = {
            "has_differences": False,
            "changed_fields": {},
            "local_only": {},
            "central_only": {},
            "identical_fields": [],
        }

        # Get all unique field names from both datasets
        all_fields = set(local_data.keys()) | set(central_data.keys())

        for field in all_fields:
            local_value = local_data.get(field)
            central_value = central_data.get(field)

            if field in local_data and field not in central_data:
                result["local_only"][field] = local_value
                result["has_differences"] = True

            elif field in central_data and field not in local_data:
                result["central_only"][field] = central_value
                result["has_differences"] = True

            elif local_value != central_value:
                result["changed_fields"][field] = {
                    "local": local_value,
                    "central": central_value,
                }
                result["has_differences"] = True

            else:
                result["identical_fields"].append(field)

        logger.debug(
            f"Entity comparison complete. Differences found: {result['has_differences']}, "
            f"Changed fields: {len(result['changed_fields'])}, "
            f"Local only: {len(result['local_only'])}, "
            f"Central only: {len(result['central_only'])}"
        )

        return result

    @handle_service_errors("get_local_changes")
    @monitor_service_performance("get_local_changes")
    async def _get_local_changes(
        self, project_id: int, last_sync_timestamp: datetime
    ) -> List[Dict[str, Any]]:
        """Retrieve changes from the local database since the last synchronization.

        This method implements timestamp-based Change Data Capture (CDC) by querying
        monitored entities for changes since the last synchronization timestamp.

        Args:
            project_id: Unique identifier of the project
            last_sync_timestamp: Timestamp of the last successful synchronization

        Returns:
            List of change dictionaries, each containing:
            - entity_type: Type of entity that changed
            - entity_id: Unique identifier of the changed entity
            - operation: Type of operation (create, update, delete)
            - changed_fields: Dictionary of changed field names to new values
            - old_values: Previous values before the change
            - new_values: New values after the change
            - timestamp: When the change occurred
            - source: Always 'local' for this method
        """
        from sqlalchemy import select, and_, or_
        from src.core.models.general.project import Project
        from src.core.models.general.component import Component
        from src.core.models.general.user import User

        logger.debug(
            f"Retrieving local changes for project {project_id} since {last_sync_timestamp}"
        )

        changes = []

        try:
            async with self.connection_manager.get_session(
                self.project_repository, project_id
            ) as session:
                # Map entity types to their corresponding models
                entity_models = {
                    "project": Project,
                    "component": Component,
                    "user": User,
                }

                for entity_type in self.sync_config["monitored_entities"]:
                    if entity_type in entity_models:
                        model_class = entity_models[entity_type]
                        entity_changes = await self._get_entity_changes_by_timestamp(
                            session,
                            model_class,
                            entity_type,
                            last_sync_timestamp,
                            project_id,
                        )
                        changes.extend(entity_changes)

                logger.debug(
                    f"Found {len(changes)} local changes for project {project_id}"
                )
                return changes

        except Exception as e:
            logger.error(
                f"Error retrieving local changes for project {project_id}: {e}"
            )
            return []

    async def _get_entity_changes_by_timestamp(
        self,
        session,
        model_class,
        entity_type: str,
        last_sync_timestamp: datetime,
        project_id: int,
    ) -> List[Dict[str, Any]]:
        """Get changes for a specific entity type using timestamp-based detection.

        Args:
            session: Database session
            model_class: SQLAlchemy model class
            entity_type: Type of entity (e.g., 'project', 'component')
            last_sync_timestamp: Timestamp of last successful sync
            project_id: Project identifier for filtering

        Returns:
            List of change records for the entity type
        """
        from sqlalchemy import select, and_, or_

        changes = []

        try:
            # Build query based on entity type
            if entity_type == "project":
                # For projects, only get the specific project
                stmt = select(model_class).where(
                    and_(
                        model_class.id == project_id,
                        or_(
                            model_class.created_at > last_sync_timestamp,
                            model_class.updated_at > last_sync_timestamp,
                        ),
                    )
                )
            elif entity_type == "component":
                # For components, get all components related to the project
                # Note: This assumes components have a project_id field
                if hasattr(model_class, "project_id"):
                    stmt = select(model_class).where(
                        and_(
                            model_class.project_id == project_id,
                            or_(
                                model_class.created_at > last_sync_timestamp,
                                model_class.updated_at > last_sync_timestamp,
                            ),
                        )
                    )
                else:
                    # Skip if no project relationship
                    return changes
            else:
                # For other entities (like users), check for global changes
                # Users don't belong to specific projects but may affect sync
                stmt = select(model_class).where(
                    or_(
                        model_class.created_at > last_sync_timestamp,
                        model_class.updated_at > last_sync_timestamp,
                    )
                )

            result = await session.execute(stmt)
            entities = result.scalars().all()

            for entity in entities:
                # Determine operation type based on timestamps
                operation = self._determine_operation_type(entity, last_sync_timestamp)

                # Create change record
                change_record = self._format_change_record(
                    entity_type=entity_type,
                    entity_id=entity.id,
                    operation=operation,
                    old_values=None,  # Timestamp-based CDC doesn't capture old values
                    new_values=self._entity_to_dict(entity),
                    timestamp=max(entity.created_at, entity.updated_at),
                    source="local",
                )
                changes.append(change_record)

        except Exception as e:
            logger.error(f"Error getting {entity_type} changes: {e}")

        return changes

    def _determine_operation_type(self, entity, last_sync_timestamp: datetime) -> str:
        """Determine the operation type for an entity based on timestamps.

        Args:
            entity: Database entity object
            last_sync_timestamp: Timestamp of last successful sync

        Returns:
            Operation type: 'create', 'update', or 'delete'
        """
        # Check if entity was created after last sync
        if entity.created_at > last_sync_timestamp:
            return "create"

        # Check if entity was updated after last sync
        if entity.updated_at > last_sync_timestamp:
            # Check for soft deletion if supported
            if hasattr(entity, "is_deleted") and entity.is_deleted:
                return "delete"
            return "update"

        # Fallback
        return "update"

    def _entity_to_dict(self, entity) -> Dict[str, Any]:
        """Convert a SQLAlchemy entity to a dictionary.

        Args:
            entity: SQLAlchemy model instance

        Returns:
            Dictionary representation of the entity
        """
        result = {}

        # Get all columns from the entity's table
        for column in entity.__table__.columns:
            value = getattr(entity, column.name, None)

            # Handle special types that need serialization
            if isinstance(value, datetime):
                result[column.name] = value.isoformat()
            elif hasattr(value, "value"):  # Enum types
                result[column.name] = value.value
            else:
                result[column.name] = value

        return result

    @handle_service_errors("get_central_changes")
    @monitor_service_performance("get_central_changes")
    async def _get_central_changes(
        self, project_id: int, last_sync_timestamp: datetime
    ) -> List[Dict[str, Any]]:
        """Retrieve changes from the central database since the last synchronization.

        This method implements timestamp-based CDC by querying the central database
        for changes that need to be synchronized to the local database.

        Args:
            project_id: Unique identifier of the project
            last_sync_timestamp: Timestamp of the last successful synchronization

        Returns:
            List of change dictionaries with the same structure as _get_local_changes,
            but with source set to 'central'
        """
        from sqlalchemy import select, and_, or_
        from src.core.models.general.project import Project
        from src.core.models.general.component import Component
        from src.core.models.general.user import User

        logger.debug(
            f"Retrieving central changes for project {project_id} since {last_sync_timestamp}"
        )

        changes = []

        try:
            async with self.connection_manager.get_session(
                self.project_repository,
                None,  # Central database session
            ) as session:
                # Map entity types to their corresponding models
                entity_models = {
                    "project": Project,
                    "component": Component,
                    "user": User,
                }

                for entity_type in self.sync_config["monitored_entities"]:
                    if entity_type in entity_models:
                        model_class = entity_models[entity_type]
                        entity_changes = (
                            await self._get_central_entity_changes_by_timestamp(
                                session,
                                model_class,
                                entity_type,
                                last_sync_timestamp,
                                project_id,
                            )
                        )
                        changes.extend(entity_changes)

                # Sort by timestamp for consistent processing order
                changes.sort(key=lambda x: x.get("timestamp", datetime.min))

                logger.debug(
                    f"Found {len(changes)} central changes for project {project_id}"
                )
                return changes

        except Exception as e:
            logger.error(
                f"Error retrieving central changes for project {project_id}: {e}"
            )
            return []

    async def _get_central_entity_changes_by_timestamp(
        self,
        session,
        model_class,
        entity_type: str,
        last_sync_timestamp: datetime,
        project_id: int,
    ) -> List[Dict[str, Any]]:
        """Get changes for a specific entity type from central database.

        Args:
            session: Database session (central database)
            model_class: SQLAlchemy model class
            entity_type: Type of entity (e.g., 'project', 'component')
            last_sync_timestamp: Timestamp of last successful sync
            project_id: Project identifier for filtering

        Returns:
            List of change records for the entity type from central database
        """
        from sqlalchemy import select, and_, or_

        changes = []

        try:
            # Build query based on entity type for central database
            if entity_type == "project":
                # For projects, only get the specific project
                stmt = select(model_class).where(
                    and_(
                        model_class.id == project_id,
                        or_(
                            model_class.created_at > last_sync_timestamp,
                            model_class.updated_at > last_sync_timestamp,
                        ),
                    )
                )
            elif entity_type == "component":
                # For components, get all components related to the project
                if hasattr(model_class, "project_id"):
                    stmt = select(model_class).where(
                        and_(
                            model_class.project_id == project_id,
                            or_(
                                model_class.created_at > last_sync_timestamp,
                                model_class.updated_at > last_sync_timestamp,
                            ),
                        )
                    )
                else:
                    # Skip if no project relationship
                    return changes
            elif entity_type == "user":
                # For users, get users that are members of the project
                # This requires joining with project members
                from src.core.models.general.project import ProjectMember

                stmt = (
                    select(model_class)
                    .join(ProjectMember, model_class.id == ProjectMember.user_id)
                    .where(
                        and_(
                            ProjectMember.project_id == project_id,
                            or_(
                                model_class.created_at > last_sync_timestamp,
                                model_class.updated_at > last_sync_timestamp,
                            ),
                        )
                    )
                )
            else:
                # For other entities, check for global changes
                stmt = select(model_class).where(
                    or_(
                        model_class.created_at > last_sync_timestamp,
                        model_class.updated_at > last_sync_timestamp,
                    )
                )

            result = await session.execute(stmt)
            entities = result.scalars().all()

            for entity in entities:
                # Determine operation type based on timestamps
                operation = self._determine_operation_type(entity, last_sync_timestamp)

                # Create change record with central source
                change_record = self._format_change_record(
                    entity_type=entity_type,
                    entity_id=entity.id,
                    operation=operation,
                    old_values=None,  # Timestamp-based CDC doesn't capture old values
                    new_values=self._entity_to_dict(entity),
                    timestamp=max(entity.created_at, entity.updated_at),
                    source="central",
                )
                changes.append(change_record)

        except Exception as e:
            logger.error(f"Error getting central {entity_type} changes: {e}")

        return changes

    @handle_service_errors("detect_change_conflicts")
    @monitor_service_performance("detect_change_conflicts")
    def _detect_change_conflicts(
        self, local_changes: List[Dict[str, Any]], central_changes: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Detect conflicts between local and central changes.

        This method analyzes local and central changes to identify conflicts that
        need resolution before synchronization can proceed. Conflicts occur when
        the same entity has been modified in both databases.

        Args:
            local_changes: List of changes from the local database
            central_changes: List of changes from the central database

        Returns:
            List of conflict dictionaries, each containing:
            - entity_type: Type of entity with conflict
            - entity_id: Unique identifier of the conflicted entity
            - local_change: The local change record
            - central_change: The central change record
            - conflict_type: Type of conflict (field_conflict, delete_conflict, etc.)
            - resolution_strategy: Recommended resolution strategy
        """
        logger.debug(
            f"Detecting conflicts between {len(local_changes)} local and "
            f"{len(central_changes)} central changes"
        )

        conflicts = []

        # Group changes by entity for conflict detection
        local_by_entity = {}
        for change in local_changes:
            key = (change.get("entity_type"), change.get("entity_id"))
            local_by_entity[key] = change

        central_by_entity = {}
        for change in central_changes:
            key = (change.get("entity_type"), change.get("entity_id"))
            central_by_entity[key] = change

        # Detect conflicts where same entity was modified in both databases
        common_entities = set(local_by_entity.keys()) & set(central_by_entity.keys())

        for entity_key in common_entities:
            local_change = local_by_entity[entity_key]
            central_change = central_by_entity[entity_key]

            conflict = {
                "entity_type": entity_key[0],
                "entity_id": entity_key[1],
                "local_change": local_change,
                "central_change": central_change,
                "conflict_type": self._classify_conflict_type(
                    local_change, central_change
                ),
                "resolution_strategy": self.sync_config.get(
                    "conflict_resolution", "last_write_wins"
                ),
            }
            conflicts.append(conflict)

        logger.debug(f"Detected {len(conflicts)} conflicts requiring resolution")
        return conflicts

    @handle_service_errors("resolve_conflicts_and_filter_changes")
    @monitor_service_performance("resolve_conflicts_and_filter_changes")
    def _resolve_conflicts_and_filter_changes(
        self,
        local_changes: List[Dict[str, Any]],
        central_changes: List[Dict[str, Any]],
        conflicts: List[Dict[str, Any]],
    ) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        Resolve conflicts and filter changes based on resolution outcomes.

        This method takes the detected conflicts, resolves each one using the configured
        strategy, and then filters the original change lists to only include the
        winning changes.

        Args:
            local_changes: List of local database changes
            central_changes: List of central database changes
            conflicts: List of detected conflicts to resolve

        Returns:
            Tuple of (local_changes_to_apply, central_changes_to_apply) after conflict resolution
        """
        logger.debug(f"Resolving {len(conflicts)} conflicts using configured strategy")

        # Create lookup dictionaries for efficient filtering
        local_by_entity = {}
        for change in local_changes:
            key = (change.get("entity_type"), change.get("entity_id"))
            local_by_entity[key] = change

        central_by_entity = {}
        for change in central_changes:
            key = (change.get("entity_type"), change.get("entity_id"))
            central_by_entity[key] = change

        # Track entities to exclude from application
        exclude_local_entities = set()
        exclude_central_entities = set()

        # Process each conflict
        for conflict in conflicts:
            entity_key = (conflict["entity_type"], conflict["entity_id"])
            conflict_type = conflict["conflict_type"]
            local_change = conflict["local_change"]
            central_change = conflict["central_change"]

            logger.debug(
                f"Resolving {conflict_type} conflict for {conflict['entity_type']} {conflict['entity_id']}"
            )

            try:
                # Resolve the conflict using the configured strategy
                winning_change = self._resolve_conflict(
                    conflict_type=conflict_type,
                    local_change=local_change,
                    central_change=central_change,
                    sync_config=self.sync_config,
                )

                # Determine which change won and exclude the losing change
                if winning_change == local_change:
                    logger.debug(
                        f"Local change wins for {conflict['entity_type']} {conflict['entity_id']} - excluding central change"
                    )
                    exclude_central_entities.add(entity_key)
                elif winning_change == central_change:
                    logger.debug(
                        f"Central change wins for {conflict['entity_type']} {conflict['entity_id']} - excluding local change"
                    )
                    exclude_local_entities.add(entity_key)
                else:
                    # This shouldn't happen with current resolution strategies, but handle gracefully
                    logger.warning(
                        f"Unexpected resolution result for {conflict['entity_type']} {conflict['entity_id']} - keeping both changes"
                    )

            except Exception as e:
                logger.error(
                    f"Failed to resolve conflict for {conflict['entity_type']} {conflict['entity_id']}: {e}"
                )
                # On resolution failure, default to excluding both changes to prevent data corruption
                exclude_local_entities.add(entity_key)
                exclude_central_entities.add(entity_key)

        # Filter changes based on conflict resolution results
        local_changes_to_apply = [
            change
            for change in local_changes
            if (change.get("entity_type"), change.get("entity_id"))
            not in exclude_local_entities
        ]

        central_changes_to_apply = [
            change
            for change in central_changes
            if (change.get("entity_type"), change.get("entity_id"))
            not in exclude_central_entities
        ]

        logger.info(
            f"Conflict resolution complete: "
            f"local changes {len(local_changes)} → {len(local_changes_to_apply)}, "
            f"central changes {len(central_changes)} → {len(central_changes_to_apply)}"
        )

        return local_changes_to_apply, central_changes_to_apply

    def _classify_conflict_type(
        self, local_change: Dict[str, Any], central_change: Dict[str, Any]
    ) -> str:
        """Classify the type of conflict between two changes.

        Args:
            local_change: Change record from local database
            central_change: Change record from central database

        Returns:
            String describing the conflict type
        """
        local_op = local_change.get("operation")
        central_op = central_change.get("operation")

        if local_op == "delete" and central_op == "delete":
            return "double_delete"
        elif local_op == "delete" and central_op in ["create", "update"]:
            return "delete_vs_modify"
        elif local_op in ["create", "update"] and central_op == "delete":
            return "modify_vs_delete"
        elif local_op == "create" and central_op == "create":
            return "double_create"
        elif local_op == "update" and central_op == "update":
            return "field_conflict"
        else:
            return "unknown_conflict"

    @handle_service_errors("format_change_record")
    @monitor_service_performance("format_change_record")
    def _format_change_record(
        self,
        entity_type: str,
        entity_id: Union[int, str],
        operation: str,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        timestamp: Optional[datetime] = None,
        source: str = "unknown",
    ) -> Dict[str, Any]:
        """Format a standardized change record dictionary.

        This utility method creates a standardized change record format that
        can be used consistently across the synchronization system.

        Args:
            entity_type: Type of entity that changed
            entity_id: Unique identifier of the entity
            operation: Type of operation (create, update, delete)
            old_values: Previous values before the change
            new_values: New values after the change
            timestamp: When the change occurred
            source: Source database ('local' or 'central')

        Returns:
            Standardized change record dictionary
        """
        # Calculate changed fields by comparing old and new values
        changed_fields = {}
        if old_values and new_values:
            comparison = self._compare_entity_data(old_values, new_values)
            changed_fields = comparison.get("changed_fields", {})

        return {
            "entity_type": entity_type,
            "entity_id": entity_id,
            "operation": operation,
            "changed_fields": changed_fields,
            "old_values": old_values or {},
            "new_values": new_values or {},
            "timestamp": timestamp or datetime.utcnow(),
            "source": source,
        }

    @handle_service_errors("create_sync_log_entry")
    @monitor_service_performance("create_sync_log_entry")
    async def _create_sync_log_entry(self, project_id: int) -> int:
        """
        Create a new synchronization log entry in the central database.

        Args:
            project_id: The project ID being synchronized

        Returns:
            The ID of the created synchronization log entry
        """
        from sqlalchemy import select
        from src.core.models.general.synchronization_log import SynchronizationLog
        from src.core.enums.system_enums import SyncOperation, SyncDirection, SyncStatus
        from src.core.utils.datetime_utils import utcnow_aware

        logger.debug(f"Creating sync log entry for project {project_id}")

        try:
            async with self.connection_manager.get_session(
                self.project_repository, None
            ) as session:
                # Create new sync log entry
                sync_log = SynchronizationLog(
                    project_id=project_id,
                    operation_type=SyncOperation.MANUAL_SYNC,  # Default to manual sync
                    sync_direction=SyncDirection.BIDIRECTIONAL,
                    status=SyncStatus.IN_PROGRESS,
                    started_at=utcnow_aware(),
                )

                session.add(sync_log)
                await session.flush()  # Get the ID without committing
                sync_log_id = sync_log.id
                await session.commit()

                logger.debug(
                    f"Created sync log entry {sync_log_id} for project {project_id}"
                )
                return sync_log_id

        except Exception as e:
            logger.error(
                f"Failed to create sync log entry for project {project_id}: {e}"
            )
            # Return a dummy ID to prevent breaking the sync process
            return -1

    @handle_service_errors("complete_sync_log_entry")
    @monitor_service_performance("complete_sync_log_entry")
    async def _complete_sync_log_entry(
        self,
        sync_log_id: int,
        status: str,
        records_processed: int,
        conflicts_detected: int,
        local_to_central_counts: Dict[str, int] = None,
        central_to_local_counts: Dict[str, int] = None,
        error_message: str = None,
    ) -> None:
        """
        Update synchronization log entry with completion details.

        Args:
            sync_log_id: ID of the sync log entry to update
            status: Final status of the sync operation
            records_processed: Number of records processed
            conflicts_detected: Number of conflicts detected
            local_to_central_counts: Counts for local-to-central operations
            central_to_local_counts: Counts for central-to-local operations
            error_message: Error message if the sync failed
        """
        from sqlalchemy import select
        from src.core.models.general.synchronization_log import SynchronizationLog
        from src.core.enums.system_enums import SyncStatus
        from src.core.utils.datetime_utils import utcnow_aware

        # Skip if dummy ID was used due to creation failure
        if sync_log_id == -1:
            return

        logger.debug(f"Updating sync log entry {sync_log_id} with status: {status}")

        try:
            async with self.connection_manager.get_session(
                self.project_repository, None
            ) as session:
                # Find the sync log entry
                stmt = select(SynchronizationLog).where(
                    SynchronizationLog.id == sync_log_id
                )
                result = await session.execute(stmt)
                sync_log = result.scalar_one_or_none()

                if not sync_log:
                    logger.warning(
                        f"Sync log entry {sync_log_id} not found for completion update"
                    )
                    return

                # Map string status to enum
                status_mapping = {
                    "completed": SyncStatus.COMPLETED,
                    "completed_with_errors": SyncStatus.COMPLETED,
                    "failed": SyncStatus.FAILED,
                }

                # Update sync log fields
                sync_log.status = status_mapping.get(status, SyncStatus.FAILED)
                sync_log.completed_at = utcnow_aware()
                sync_log.records_processed = records_processed
                sync_log.conflicts_detected = conflicts_detected

                if error_message:
                    sync_log.error_message = error_message

                # Calculate performance metrics
                sync_log.update_completion_metrics()

                # Add operation details as JSON
                if local_to_central_counts or central_to_local_counts:
                    operation_details = {}
                    if local_to_central_counts:
                        operation_details["local_to_central"] = local_to_central_counts
                    if central_to_local_counts:
                        operation_details["central_to_local"] = central_to_local_counts

                    # Store as metadata (assuming the model supports this)
                    if hasattr(sync_log, "metadata"):
                        sync_log.metadata = operation_details

                await session.commit()

                logger.debug(f"Updated sync log entry {sync_log_id} successfully")

        except Exception as e:
            logger.error(f"Failed to update sync log entry {sync_log_id}: {e}")
            # Don't re-raise as this is a secondary operation

    @handle_service_errors("synchronize_project")
    @monitor_service_performance("synchronize_project")
    async def synchronize_project(self, project_id: int) -> Dict[str, Any]:
        """
        Orchestrates the bi-directional synchronization of a project's data.

        This is the main entry point for the synchronization process. It implements
        bi-directional synchronization flow:
        1. Retrieve the last sync timestamp for the project
        2. Get local changes since that timestamp (local-to-central)
        3. Get central changes since that timestamp (central-to-local)
        4. Apply local changes to the central database
        5. Apply central changes to the local database

        Args:
            project_id: The ID of the project to synchronize

        Returns:
            Dictionary containing synchronization summary with operation counts

        Raises:
            SynchronizationError: If synchronization process fails
        """
        logger.info(
            f"Starting bi-directional synchronization for project ID: {project_id}"
        )

        # Create synchronization log entry
        sync_log_id = await self._create_sync_log_entry(project_id)

        try:
            # Step 1: Retrieve the last sync timestamp for this project
            last_sync_timestamp = await self._get_last_sync_timestamp(project_id)

            if last_sync_timestamp:
                logger.debug(
                    f"Last sync timestamp for project {project_id}: {last_sync_timestamp}"
                )
            else:
                logger.info(
                    f"No previous sync found for project {project_id}, performing full sync"
                )
                # Use a very old timestamp to capture all changes
                last_sync_timestamp = datetime(2000, 1, 1)

            # Step 2: Get local changes since last sync (local-to-central)
            logger.debug(
                f"Retrieving local changes for project {project_id} since {last_sync_timestamp}"
            )
            local_changes = await self._get_local_changes(
                project_id, last_sync_timestamp
            )

            # Step 3: Get central changes since last sync (central-to-local)
            logger.debug(
                f"Retrieving central changes for project {project_id} since {last_sync_timestamp}"
            )
            central_changes = await self._get_central_changes(
                project_id, last_sync_timestamp
            )

            # Initialize summary counters
            local_to_central_counts = {
                "created": 0,
                "updated": 0,
                "deleted": 0,
                "errors": 0,
            }
            central_to_local_counts = {
                "created": 0,
                "updated": 0,
                "deleted": 0,
                "errors": 0,
            }
            conflicts_detected = 0
            conflicts_resolved = 0

            # Check if any changes exist
            if not local_changes and not central_changes:
                logger.info(f"No changes found for project {project_id}")
                return {
                    "project_id": project_id,
                    "status": "completed",
                    "local_to_central": local_to_central_counts,
                    "central_to_local": central_to_local_counts,
                    "conflicts_detected": conflicts_detected,
                    "conflicts_resolved": conflicts_resolved,
                    "sync_direction": "bidirectional",
                    "timestamp": datetime.utcnow().isoformat(),
                    "message": "No changes to synchronize",
                }

            # Step 3.5: Detect and resolve conflicts
            logger.debug(
                f"Detecting conflicts between {len(local_changes)} local and {len(central_changes)} central changes"
            )
            conflicts = self._detect_change_conflicts(local_changes, central_changes)
            conflicts_detected = len(conflicts)

            if conflicts_detected > 0:
                logger.info(
                    f"Detected {conflicts_detected} conflicts requiring resolution"
                )

                # Resolve conflicts and filter changes accordingly
                local_changes_to_apply, central_changes_to_apply = (
                    self._resolve_conflicts_and_filter_changes(
                        local_changes, central_changes, conflicts
                    )
                )
                conflicts_resolved = conflicts_detected  # All conflicts get resolved

                logger.info(
                    f"Resolved {conflicts_resolved} conflicts - applying {len(local_changes_to_apply)} local and {len(central_changes_to_apply)} central changes"
                )
            else:
                logger.debug("No conflicts detected - applying all changes as-is")
                local_changes_to_apply = local_changes
                central_changes_to_apply = central_changes

            logger.info(
                f"Processing synchronization with conflict resolution: {len(local_changes_to_apply)} local changes, {len(central_changes_to_apply)} central changes"
            )

            # Step 4: Apply local changes to central database (local-to-central)
            if local_changes_to_apply:
                logger.debug(
                    f"Applying {len(local_changes_to_apply)} local changes to central database"
                )

                try:
                    async with self.connection_manager.get_session(
                        self.project_repository, None
                    ) as central_session:
                        local_to_central_counts = await self._apply_changes(
                            central_session,
                            local_changes_to_apply,
                            target_database="central",
                        )

                        # Only commit if there were no errors
                        if local_to_central_counts["errors"] == 0:
                            await central_session.commit()
                            logger.info(
                                f"Successfully committed local changes to central database for project {project_id}: "
                                f"created={local_to_central_counts['created']}, updated={local_to_central_counts['updated']}, "
                                f"deleted={local_to_central_counts['deleted']}"
                            )
                        else:
                            # Partial success - commit successful changes, log errors
                            await central_session.commit()
                            logger.warning(
                                f"Committed local changes to central database with errors for project {project_id}: "
                                f"created={local_to_central_counts['created']}, updated={local_to_central_counts['updated']}, "
                                f"deleted={local_to_central_counts['deleted']}, errors={local_to_central_counts['errors']}"
                            )

                except Exception as e:
                    logger.error(
                        f"Failed to apply local changes to central database for project {project_id}: {e}"
                    )
                    # Re-raise with context for proper error handling
                    raise SynchronizationError(
                        f"Central database synchronization failed: {e}"
                    ) from e
            else:
                logger.debug(
                    f"No local changes to apply to central database for project {project_id}"
                )

            # Step 5: Apply central changes to local database (central-to-local)
            if central_changes_to_apply:
                logger.debug(
                    f"Applying {len(central_changes_to_apply)} central changes to local database"
                )

                try:
                    async with self.connection_manager.get_session(
                        self.project_repository, project_id
                    ) as local_session:
                        central_to_local_counts = await self._apply_changes(
                            local_session,
                            central_changes_to_apply,
                            target_database="local",
                        )

                        # Only commit if there were no errors
                        if central_to_local_counts["errors"] == 0:
                            await local_session.commit()
                            logger.info(
                                f"Successfully committed central changes to local database for project {project_id}: "
                                f"created={central_to_local_counts['created']}, updated={central_to_local_counts['updated']}, "
                                f"deleted={central_to_local_counts['deleted']}"
                            )
                        else:
                            # Partial success - commit successful changes, log errors
                            await local_session.commit()
                            logger.warning(
                                f"Committed central changes to local database with errors for project {project_id}: "
                                f"created={central_to_local_counts['created']}, updated={central_to_local_counts['updated']}, "
                                f"deleted={central_to_local_counts['deleted']}, errors={central_to_local_counts['errors']}"
                            )

                except Exception as e:
                    logger.error(
                        f"Failed to apply central changes to local database for project {project_id}: {e}"
                    )
                    # Re-raise with context for proper error handling
                    raise SynchronizationError(
                        f"Local database synchronization failed: {e}"
                    ) from e
            else:
                logger.debug(
                    f"No central changes to apply to local database for project {project_id}"
                )

            # Calculate overall status
            total_errors = (
                local_to_central_counts["errors"] + central_to_local_counts["errors"]
            )
            overall_status = (
                "completed" if total_errors == 0 else "completed_with_errors"
            )

            # Update synchronization log with completion status
            await self._complete_sync_log_entry(
                sync_log_id=sync_log_id,
                status=overall_status,
                records_processed=len(local_changes_to_apply)
                + len(central_changes_to_apply),
                conflicts_detected=conflicts_detected,
                local_to_central_counts=local_to_central_counts,
                central_to_local_counts=central_to_local_counts,
            )

            logger.info(
                f"Synchronization completed for project {project_id} with status: {overall_status}"
            )

            # Return comprehensive synchronization summary with conflict metrics
            return {
                "project_id": project_id,
                "status": overall_status,
                "local_to_central": local_to_central_counts,
                "central_to_local": central_to_local_counts,
                "conflicts_detected": conflicts_detected,
                "conflicts_resolved": conflicts_resolved,
                "sync_direction": "bidirectional",
                "timestamp": datetime.utcnow().isoformat(),
                "last_sync_timestamp": last_sync_timestamp.isoformat()
                if last_sync_timestamp
                else None,
                "total_changes_processed": len(local_changes) + len(central_changes),
                "changes_applied_after_resolution": len(local_changes_to_apply)
                + len(central_changes_to_apply),
            }

        except Exception as e:
            logger.error(f"Synchronization failed for project {project_id}: {e}")

            # Update synchronization log with failure status
            try:
                await self._complete_sync_log_entry(
                    sync_log_id=sync_log_id,
                    status="failed",
                    records_processed=0,
                    conflicts_detected=0,
                    error_message=str(e),
                )
            except Exception as log_error:
                logger.error(f"Failed to update sync log on error: {log_error}")

            raise SynchronizationError(
                f"Failed to synchronize project {project_id}: {e}"
            ) from e

    @handle_service_errors("resolve_conflict")
    @monitor_service_performance("resolve_conflict")
    def _resolve_conflict(
        self,
        conflict_type: str,
        local_change: Dict[str, Any],
        central_change: Dict[str, Any],
        sync_config: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Resolves conflicts between local and central changes using configurable strategies.

        This method implements conflict resolution logic to determine which change should
        be applied when the same entity has been modified in both databases.

        Args:
            conflict_type: Type of conflict (e.g., 'field_conflict', 'delete_vs_modify')
            local_change: Change record from the local database
            central_change: Change record from the central database
            sync_config: Optional configuration overriding default resolution strategy

        Returns:
            The winning change record that should be applied

        Raises:
            SynchronizationError: If conflict cannot be resolved or data is invalid
        """
        if not local_change or not central_change:
            raise SynchronizationError(
                "Cannot resolve conflict: missing local or central change record"
            )

        # Get resolution strategy from config or use default
        config = sync_config or self.sync_config
        resolution_strategy = config.get("conflict_resolution", "last_write_wins")

        logger.debug(
            f"Resolving {conflict_type} conflict between entity {local_change.get('entity_id')} "
            f"using {resolution_strategy} strategy"
        )

        try:
            if resolution_strategy == "last_write_wins":
                return self._resolve_conflict_last_write_wins(
                    conflict_type, local_change, central_change
                )
            elif resolution_strategy == "local_wins":
                logger.debug(
                    f"Local change wins for entity {local_change.get('entity_id')}"
                )
                return local_change
            elif resolution_strategy == "central_wins":
                logger.debug(
                    f"Central change wins for entity {central_change.get('entity_id')}"
                )
                return central_change
            elif resolution_strategy == "manual":
                return self._resolve_conflict_manual(
                    conflict_type, local_change, central_change
                )
            else:
                raise SynchronizationError(
                    f"Unknown conflict resolution strategy: {resolution_strategy}"
                )

        except Exception as e:
            logger.error(
                f"Error resolving conflict for entity {local_change.get('entity_id')}: {e}"
            )
            raise SynchronizationError(f"Conflict resolution failed: {e}") from e

    def _resolve_conflict_last_write_wins(
        self,
        conflict_type: str,
        local_change: Dict[str, Any],
        central_change: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Resolves conflicts using last-write-wins strategy.

        Args:
            conflict_type: Type of conflict
            local_change: Local change record
            central_change: Central change record

        Returns:
            The change record with the most recent timestamp
        """
        # Handle special conflict types first
        if conflict_type == "double_delete":
            # Both deleted - prefer the one with later timestamp
            logger.debug(
                "Double delete conflict - using last write wins on deletion timestamp"
            )
            return self._compare_timestamps_and_select(local_change, central_change)

        elif conflict_type == "delete_vs_modify":
            # Local delete vs central modify - use timestamps to decide
            if local_change.get("operation") == "delete":
                logger.debug("Local delete vs central modify - comparing timestamps")
                return self._compare_timestamps_and_select(local_change, central_change)
            else:
                # Central delete vs local modify
                logger.debug("Central delete vs local modify - comparing timestamps")
                return self._compare_timestamps_and_select(local_change, central_change)

        elif conflict_type == "modify_vs_delete":
            # Local modify vs central delete - use timestamps to decide
            logger.debug("Modify vs delete conflict - comparing timestamps")
            return self._compare_timestamps_and_select(local_change, central_change)

        elif conflict_type == "double_create":
            # Both created - prefer the one with later timestamp
            logger.debug("Double create conflict - using last write wins")
            return self._compare_timestamps_and_select(local_change, central_change)

        elif conflict_type == "field_conflict":
            # Field-level conflicts - use last write wins
            logger.debug("Field conflict - using last write wins")
            return self._compare_timestamps_and_select(local_change, central_change)

        else:
            # Unknown conflict type - default to timestamp comparison
            logger.warning(
                f"Unknown conflict type {conflict_type} - defaulting to timestamp comparison"
            )
            return self._compare_timestamps_and_select(local_change, central_change)

    def _compare_timestamps_and_select(
        self, local_change: Dict[str, Any], central_change: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Compares timestamps of two change records and selects the more recent one.

        Args:
            local_change: Local change record
            central_change: Central change record

        Returns:
            The change record with the more recent timestamp
        """
        local_timestamp = local_change.get("timestamp")
        central_timestamp = central_change.get("timestamp")

        if not local_timestamp:
            logger.warning("Local change missing timestamp - defaulting to central")
            return central_change

        if not central_timestamp:
            logger.warning("Central change missing timestamp - defaulting to local")
            return local_change

        # Handle string timestamps by parsing them
        if isinstance(local_timestamp, str):
            local_timestamp = datetime.fromisoformat(
                local_timestamp.replace("Z", "+00:00")
            )
        if isinstance(central_timestamp, str):
            central_timestamp = datetime.fromisoformat(
                central_timestamp.replace("Z", "+00:00")
            )

        if local_timestamp >= central_timestamp:
            logger.debug(
                f"Local change wins (timestamp: {local_timestamp} >= {central_timestamp}) "
                f"for entity {local_change.get('entity_id')}"
            )
            return local_change
        else:
            logger.debug(
                f"Central change wins (timestamp: {central_timestamp} > {local_timestamp}) "
                f"for entity {central_change.get('entity_id')}"
            )
            return central_change

    def _resolve_conflict_manual(
        self,
        conflict_type: str,
        local_change: Dict[str, Any],
        central_change: Dict[str, Any],
    ) -> Dict[str, Any]:
        """
        Handles manual conflict resolution by logging conflict details for later review.

        Args:
            conflict_type: Type of conflict
            local_change: Local change record
            central_change: Central change record

        Returns:
            Placeholder change record indicating manual resolution required

        Note:
            In a production system, this would typically:
            1. Store the conflict in a review queue
            2. Send notifications to administrators
            3. Return a special marker indicating manual resolution needed
        """
        entity_id = local_change.get("entity_id")
        entity_type = local_change.get("entity_type")

        logger.warning(
            f"Manual conflict resolution required for {entity_type} {entity_id}: "
            f"conflict_type={conflict_type}, "
            f"local_op={local_change.get('operation')}, "
            f"central_op={central_change.get('operation')}"
        )

        # Create a conflict record for tracking
        conflict_record = {
            "entity_type": entity_type,
            "entity_id": entity_id,
            "conflict_type": conflict_type,
            "local_change": local_change,
            "central_change": central_change,
            "resolution_required": True,
            "timestamp": datetime.utcnow(),
        }

        # For now, default to local change as placeholder
        # In production, this would be handled by a conflict management system
        logger.info(
            f"Defaulting to local change for manual conflict resolution: {entity_id}"
        )
        return local_change

    @handle_service_errors("apply_changes")
    @monitor_service_performance("apply_changes")
    async def _apply_changes(
        self,
        db_session,
        changes: List[Dict[str, Any]],
        target_database: str = "unknown",
    ) -> Dict[str, int]:
        """
        Apply a list of change records to the given database session.

        This method takes a list of ChangeRecord dictionaries and applies them to the
        target database using SQLAlchemy ORM operations. It handles CREATE, UPDATE,
        and DELETE operations while maintaining transactional integrity.

        Args:
            db_session: SQLAlchemy async session for database operations
            changes: List of change record dictionaries to apply
            target_database: Description of target database for logging (e.g., "local", "central")

        Returns:
            Dictionary with counts of operations performed:
            - 'created': Number of entities created
            - 'updated': Number of entities updated
            - 'deleted': Number of entities deleted
            - 'errors': Number of operations that failed

        Raises:
            SynchronizationError: If critical errors occur during change application
        """
        if not changes:
            logger.debug(f"No changes to apply to {target_database} database")
            return {"created": 0, "updated": 0, "deleted": 0, "errors": 0}

        logger.info(f"Applying {len(changes)} changes to {target_database} database")

        # Initialize operation counters
        counters = {"created": 0, "updated": 0, "deleted": 0, "errors": 0}

        # Map entity types to their model classes
        entity_models = {
            "project": "src.core.models.general.project:Project",
            "component": "src.core.models.general.component:Component",
            "user": "src.core.models.general.user:User",
        }

        try:
            for change in changes:
                try:
                    await self._apply_single_change(
                        db_session, change, entity_models, counters
                    )
                except Exception as e:
                    logger.error(
                        f"Error applying change for {change.get('entity_type')} "
                        f"{change.get('entity_id')}: {e}"
                    )
                    counters["errors"] += 1

                    # Continue with other changes rather than failing entire batch
                    continue

            logger.info(
                f"Successfully applied changes to {target_database}: "
                f"created={counters['created']}, updated={counters['updated']}, "
                f"deleted={counters['deleted']}, errors={counters['errors']}"
            )

            return counters

        except Exception as e:
            logger.error(
                f"Critical error during change application to {target_database}: {e}"
            )
            raise SynchronizationError(
                f"Failed to apply changes to {target_database}: {e}"
            ) from e

    async def _apply_single_change(
        self,
        db_session,
        change: Dict[str, Any],
        entity_models: Dict[str, str],
        counters: Dict[str, int],
    ) -> None:
        """
        Apply a single change record to the database session.

        Args:
            db_session: SQLAlchemy async session
            change: Individual change record to apply
            entity_models: Mapping of entity types to model class paths
            counters: Operation counters to update

        Raises:
            ValueError: If change record is malformed
            Exception: If database operation fails
        """
        entity_type = change.get("entity_type")
        entity_id = change.get("entity_id")
        operation = change.get("operation")
        new_values = change.get("new_values", {})

        if not entity_type or entity_id is None or not operation:
            raise ValueError(f"Malformed change record: missing required fields")

        # Get the model class for this entity type
        model_class = await self._get_model_class(entity_type, entity_models)
        if not model_class:
            logger.warning(f"Unknown entity type {entity_type}, skipping change")
            return

        logger.debug(f"Applying {operation} operation for {entity_type} {entity_id}")

        if operation == "create":
            await self._apply_create_operation(
                db_session, model_class, entity_id, new_values, counters
            )
        elif operation == "update":
            await self._apply_update_operation(
                db_session, model_class, entity_id, new_values, counters
            )
        elif operation == "delete":
            await self._apply_delete_operation(
                db_session, model_class, entity_id, counters
            )
        else:
            logger.warning(
                f"Unknown operation type {operation} for {entity_type} {entity_id}"
            )

    async def _get_model_class(self, entity_type: str, entity_models: Dict[str, str]):
        """
        Dynamically import and return the model class for the given entity type.

        Args:
            entity_type: Type of entity (e.g., 'project', 'component')
            entity_models: Mapping of entity types to model class paths

        Returns:
            Model class or None if not found
        """
        if entity_type not in entity_models:
            return None

        model_path = entity_models[entity_type]
        module_path, class_name = model_path.rsplit(":", 1)

        try:
            # Dynamic import of the model class
            import importlib

            module = importlib.import_module(module_path.replace(":", "."))
            return getattr(module, class_name)
        except (ImportError, AttributeError) as e:
            logger.error(f"Failed to import model class {model_path}: {e}")
            return None

    async def _apply_create_operation(
        self,
        db_session,
        model_class,
        entity_id: Any,
        new_values: Dict[str, Any],
        counters: Dict[str, int],
    ) -> None:
        """
        Apply a CREATE operation to the database.

        Args:
            db_session: SQLAlchemy async session
            model_class: SQLAlchemy model class
            entity_id: ID of the entity to create
            new_values: Field values for the new entity
            counters: Operation counters to update
        """
        from sqlalchemy import select

        # Check if entity already exists
        stmt = select(model_class).where(model_class.id == entity_id)
        result = await db_session.execute(stmt)
        existing_entity = result.scalar_one_or_none()

        if existing_entity:
            logger.warning(
                f"Entity {model_class.__name__} {entity_id} already exists, treating as update"
            )
            await self._apply_update_operation(
                db_session, model_class, entity_id, new_values, counters
            )
            return

        # Create new entity
        # Filter out None values and ensure id is set correctly
        filtered_values = {k: v for k, v in new_values.items() if v is not None}
        filtered_values["id"] = entity_id

        try:
            new_entity = model_class(**filtered_values)
            db_session.add(new_entity)
            counters["created"] += 1

            logger.debug(f"Created {model_class.__name__} {entity_id}")

        except Exception as e:
            logger.error(f"Failed to create {model_class.__name__} {entity_id}: {e}")
            raise

    async def _apply_update_operation(
        self,
        db_session,
        model_class,
        entity_id: Any,
        new_values: Dict[str, Any],
        counters: Dict[str, int],
    ) -> None:
        """
        Apply an UPDATE operation to the database.

        Args:
            db_session: SQLAlchemy async session
            model_class: SQLAlchemy model class
            entity_id: ID of the entity to update
            new_values: New field values for the entity
            counters: Operation counters to update
        """
        from sqlalchemy import select

        # Find existing entity
        stmt = select(model_class).where(model_class.id == entity_id)
        result = await db_session.execute(stmt)
        existing_entity = result.scalar_one_or_none()

        if not existing_entity:
            logger.warning(
                f"Entity {model_class.__name__} {entity_id} not found for update, treating as create"
            )
            await self._apply_create_operation(
                db_session, model_class, entity_id, new_values, counters
            )
            return

        # Update entity fields
        updated_fields = 0
        for field_name, new_value in new_values.items():
            if field_name == "id":
                continue  # Don't update ID field

            if hasattr(existing_entity, field_name):
                current_value = getattr(existing_entity, field_name)

                # Only update if value actually changed
                if current_value != new_value:
                    setattr(existing_entity, field_name, new_value)
                    updated_fields += 1

        if updated_fields > 0:
            # Update the updated_at timestamp if the model supports it
            if hasattr(existing_entity, "updated_at"):
                from src.core.utils.datetime_utils import utcnow_aware

                existing_entity.updated_at = utcnow_aware()

            counters["updated"] += 1
            logger.debug(
                f"Updated {model_class.__name__} {entity_id} ({updated_fields} fields)"
            )
        else:
            logger.debug(f"No changes needed for {model_class.__name__} {entity_id}")

    async def _apply_delete_operation(
        self, db_session, model_class, entity_id: Any, counters: Dict[str, int]
    ) -> None:
        """
        Apply a DELETE operation to the database.

        Args:
            db_session: SQLAlchemy async session
            model_class: SQLAlchemy model class
            entity_id: ID of the entity to delete
            counters: Operation counters to update
        """
        from sqlalchemy import select

        # Find existing entity
        stmt = select(model_class).where(model_class.id == entity_id)
        result = await db_session.execute(stmt)
        existing_entity = result.scalar_one_or_none()

        if not existing_entity:
            logger.warning(
                f"Entity {model_class.__name__} {entity_id} not found for deletion"
            )
            return

        # Check if model supports soft deletion
        if hasattr(existing_entity, "is_deleted") and hasattr(
            existing_entity, "deleted_at"
        ):
            # Soft delete
            existing_entity.is_deleted = True
            if hasattr(existing_entity, "deleted_at"):
                from src.core.utils.datetime_utils import utcnow_aware

                existing_entity.deleted_at = utcnow_aware()

            logger.debug(f"Soft deleted {model_class.__name__} {entity_id}")
        else:
            # Hard delete
            await db_session.delete(existing_entity)
            logger.debug(f"Hard deleted {model_class.__name__} {entity_id}")

        counters["deleted"] += 1


"""
Detailed Specification Notes:

1.  Change Data Capture (CDC):
    - The actual implementation will require a robust CDC mechanism.
    - Options to consider:
        a. PostgreSQL Logical Replication: A powerful native feature.
        b. Custom Triggers: Writing custom database triggers to log changes to a dedicated 'outbox' table.
        c. External CDC Tools: Tools like Debezium can stream changes from the database logs.
    - Each synchronized table must have a `last_modified_at` timestamp column that is reliably updated.

2.  State Management:
    - The service needs a mechanism to track the `last_successful_sync_timestamp` for each project. This could be stored in a dedicated table in the local database.

3.  Transaction Management:
    - The synchronization process must be transactional. If any part of the sync fails, the entire operation for that batch should be rolled back to ensure data consistency.
    - The final commit should only happen after both push and pull operations are successfully prepared.

4.  Error Handling and Retries:
    - The `@handle_service_errors` decorator will catch generic errors, but specific synchronization errors (e.g., network issues, data transformation failures) should be handled gracefully.
    - A retry mechanism with exponential backoff should be implemented for transient errors.

5.  Batch Processing:
    - For performance, changes should be processed in batches rather than one by one. The size of the batch should be configurable.

6.  Data Transformation:
    - This specification assumes that the schemas of the local and central databases are identical. If they differ, a data transformation layer will be required within this service.

7.  Dependencies:
    - The service will require two configured database sessions to be injected: one for the local database and one for the central database. The dependency injection system will need to be configured to provide these.
"""
