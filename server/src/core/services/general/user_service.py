"""User Service.

This module provides business logic for user operations, including
authentication, user management, and security operations.
"""

from typing import Dict, List, Optional

from sqlalchemy.ext.asyncio import AsyncSession
from src.config.logging_config import logger
from src.config.settings import settings
from src.core.errors.exceptions import (
    BaseApplicationException,
    InvalidInputError,
    NotFoundError,
)

# Unified systems imports
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.repositories.general.user_preference_repository import (
    UserPreferenceRepository,
)
from src.core.repositories.general.user_repository import UserRepository
from src.core.schemas.base_schemas import PaginationSchema
from src.core.schemas.general.user_schemas import (
    LoginRequestSchema,
    LoginResponseSchema,
    LogoutResponseSchema,
    PasswordChangeRequestSchema,
    UserCreateSchema,
    UserPaginatedResponseSchema,
    UserPreferenceReadSchema,
    UserPreferenceUpdateSchema,
    UserReadSchema,
    UserSummarySchema,
    UserUpdateSchema,
)
from src.core.security.password_handler import PasswordHandler
from src.core.utils.datetime_utils import utcnow_aware
from src.core.utils.pagination_utils import (
    PaginationParams,
    SortParams,
    paginate_query_async,
)
from src.core.utils.query_utils import QueryBuilder

# Import utilities for enhanced functionality
from src.core.utils.string_utils import sanitize_text


class UserService:
    """Service for user operations and authentication management."""

    def __init__(
        self,
        user_repository: UserRepository,
        preference_repository: UserPreferenceRepository,
    ):
        """Initialize the user service."""
        self.user_repo = user_repository
        self.preference_repo = preference_repository
        logger.debug("UserService initialized")

    async def _convert_user_to_schema(self, user) -> UserReadSchema:
        """Convert User model to UserReadSchema with async role loading."""
        # Load role relationships asynchronously
        await self.user_repo.db_session.refresh(user, ["role_assignments"])
        role = None
        if user.role_assignments:
            active_assignments = [
                assignment for assignment in user.role_assignments
                if assignment.is_active and not assignment.is_deleted
            ]
            if active_assignments:
                for assignment in active_assignments:
                    await self.user_repo.db_session.refresh(assignment, ["role"])
                active_assignments.sort(
                    key=lambda x: x.role.priority if x.role else 0, reverse=True
                )
                role = active_assignments[0].role.name if active_assignments[0].role else None
        
        user_data = {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "role": role,
            "last_login": user.last_login,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
        }
        return UserReadSchema.model_validate(user_data)

    @handle_service_errors("create_user")
    @monitor_service_performance("create_user")
    async def create_user(self, user_data: UserCreateSchema) -> UserReadSchema:
        """Create a new user with secure password hashing and data sanitization."""
        logger.info(f"Creating user: {user_data.name}")

        if user_data.email:
            if await self.user_repo.check_email_exists(user_data.email):
                raise InvalidInputError(
                    f"Email address '{user_data.email}' is already registered."
                )

        password_hash = PasswordHandler.hash_password(user_data.password)
        user_dict = user_data.model_dump(exclude={"password", "role"})
        user_dict["password_hash"] = password_hash

        if user_dict.get("name"):
            user_dict["name"] = sanitize_text(user_dict["name"])
        if user_dict.get("bio"):
            user_dict["bio"] = sanitize_text(user_dict["bio"])
        if user_dict.get("company"):
            user_dict["company"] = sanitize_text(user_dict["company"])
        if user_dict.get("department"):
            user_dict["department"] = sanitize_text(user_dict["department"])

        user = await self.user_repo.create(user_dict)
        await self.user_repo.db_session.flush()
        await self.user_repo.db_session.commit()
        await self.user_repo.db_session.refresh(user)

        logger.info(f"User created successfully: {user.id}")
        
        # Create UserReadSchema manually to avoid SQLAlchemy lazy loading issues with role property
        user_data = {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "role": None,  # New users have no role assignments yet
            "last_login": None,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
        }
        return UserReadSchema.model_validate(user_data)

    @handle_service_errors("create_superuser")
    @monitor_service_performance("create_superuser")
    async def create_superuser(
        self, username: str, password: str, email: str
    ) -> UserReadSchema:
        """Create a superuser with administrative privileges."""
        logger.info(f"Creating superuser: {username}")

        if await self.user_repo.check_email_exists(email):
            raise InvalidInputError(f"Email address '{email}' is already registered.")

        existing_user_by_name = await self.user_repo.get_by_name(username)
        if existing_user_by_name:
            raise InvalidInputError(f"User with name {username} already exists")

        user_data = UserCreateSchema(
            name=username,
            email=email,
            password=password,
            is_superuser=True,
        )

        superuser = await self.create_user(user_data)

        logger.info(f"Superuser created successfully: {superuser.id}")
        return superuser

    @handle_service_errors("get_user")
    @monitor_service_performance("get_user")
    async def get_user(self, user_id: int) -> UserReadSchema:
        """Get user by ID."""
        logger.debug(f"Retrieving user: {user_id}")
        user = await self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )
        return await self._convert_user_to_schema(user)

    @handle_service_errors("get_user_by_email")
    @monitor_service_performance("get_user_by_email")
    async def get_user_by_email(self, email: str) -> UserReadSchema:
        """Get user by email address."""
        logger.debug(f"Retrieving user by email: {email}")
        user = await self.user_repo.get_by_email(email)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User with email {email} not found or inactive",
            )
        return await self._convert_user_to_schema(user)

    @handle_service_errors("update_user")
    @monitor_service_performance("update_user")
    async def update_user(
        self, user_id: int, update_data: UserUpdateSchema
    ) -> UserReadSchema:
        """Update user information."""
        logger.info(f"Updating user: {user_id}")
        existing_user = await self.user_repo.get_by_id(user_id)
        if not existing_user or not existing_user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )

        if (
            update_data.email
            and update_data.email.lower() != existing_user.email.lower()
        ):
            if await self.user_repo.check_email_exists(
                update_data.email, exclude_user_id=user_id
            ):
                raise InvalidInputError(
                    f"Email address '{update_data.email}' is already registered."
                )

        update_dict = update_data.model_dump(exclude_unset=True)

        if update_dict.get("name"):
            update_dict["name"] = sanitize_text(update_dict["name"])
        if update_dict.get("bio"):
            update_dict["bio"] = sanitize_text(update_dict["bio"])
        if update_dict.get("company"):
            update_dict["company"] = sanitize_text(update_dict["company"])
        if update_dict.get("department"):
            update_dict["department"] = sanitize_text(update_dict["department"])

        updated_user = await self.user_repo.update(user_id, update_dict)
        await self.user_repo.db_session.flush()
        await self.user_repo.db_session.commit()
        await self.user_repo.db_session.refresh(updated_user)

        logger.info(f"User updated successfully: {user_id}")
        return UserReadSchema.model_validate(updated_user)

    @handle_service_errors("deactivate_user")
    @monitor_service_performance("deactivate_user")
    async def deactivate_user(self, user_id: int) -> bool:
        """Deactivate a user account."""
        logger.info(f"Deactivating user: {user_id}")
        success = await self.user_repo.deactivate_user(user_id)
        if not success:
            raise NotFoundError(
                code="USER_NOT_FOUND", detail=f"User {user_id} not found"
            )
        await self.user_repo.db_session.flush()
        await self.user_repo.db_session.commit()
        logger.info(f"User deactivated successfully: {user_id}")
        return success

    @handle_service_errors("activate_user")
    @monitor_service_performance("activate_user")
    async def activate_user(self, user_id: int) -> bool:
        """Activate a user account."""
        logger.info(f"Activating user: {user_id}")
        existing_user = await self.user_repo.get_by_id(user_id)
        if not existing_user:
            raise NotFoundError(
                code="USER_NOT_FOUND", detail=f"User {user_id} not found"
            )

        success = await self.user_repo.activate_user(user_id)
        if not success:
            raise NotFoundError(
                code="USER_NOT_FOUND", detail=f"User {user_id} not found"
            )
        await self.user_repo.db_session.flush()
        await self.user_repo.db_session.commit()
        logger.info(f"User activated successfully: {user_id}")
        return success

    @handle_service_errors("get_users")
    @monitor_service_performance("get_users")
    async def get_users(self, skip: int = 0, limit: int = 100) -> List[UserReadSchema]:
        """Get list of active users."""
        logger.debug(f"Retrieving users: skip={skip}, limit={limit}")
        users = await self.user_repo.get_active_users(skip, limit)
        return [await self._convert_user_to_schema(user) for user in users]

    @handle_service_errors("get_users_paginated")
    @monitor_service_performance("get_users_paginated")
    async def get_users_paginated(
        self,
        pagination_params: PaginationParams,
        sort_params: Optional[SortParams] = None,
        filters: Optional[Dict] = None,
    ) -> UserPaginatedResponseSchema:
        """Get paginated list of users with enhanced search and sorting."""
        logger.debug(
            f"Retrieving paginated users: page={pagination_params.page}, "
            f"per_page={pagination_params.per_page}, sort={sort_params}, filters={filters}"
        )
        from src.core.models.general.user import User

        query_filters = {}
        if filters:
            include_inactive = filters.get("include_inactive", False)
            if not include_inactive:
                query_filters["is_active"] = True
            if filters.get("role"):
                query_filters["role"] = filters.get("role")

        search_term = filters.get("search") if filters else None

        result = await self.user_repo.search_paginated(
            search_term=search_term,
            searchable_fields=["name", "email", "company", "department"],
            pagination_params=pagination_params,
            sort_params=sort_params,
            additional_filters=query_filters,
        )

        user_schemas = [await self._convert_user_to_schema(u) for u in result.items]
        pagination = PaginationSchema(
            page=result.page,
            size=result.per_page,
            total=result.total,
            pages=result.total_pages,
        )
        response = UserPaginatedResponseSchema(
            items=user_schemas, pagination=pagination
        )
        logger.debug(f"Retrieved {len(user_schemas)} users (total: {result.total})")
        return response

    @handle_service_errors("authenticate_user")
    @monitor_service_performance("authenticate_user")
    async def authenticate_user(self, login_data: LoginRequestSchema) -> UserReadSchema:
        """Authenticate user with email and password."""
        logger.debug(f"Authenticating user: {login_data.username}")
        user = await self.user_repo.get_by_email(login_data.username)
        if not user or not user.is_active or not user.password_hash:
            logger.warning(
                f"Authentication failed for {login_data.username}: User not found, inactive, or no password hash."
            )
            raise InvalidInputError("Invalid email or password")

        if not PasswordHandler.verify_password(login_data.password, user.password_hash):
            logger.warning(
                f"Authentication failed for {login_data.username}: Password verification failed."
            )
            raise InvalidInputError("Invalid email or password")

        if PasswordHandler.needs_rehash(user.password_hash):
            logger.info(f"Password for user {user.id} needs rehashing.")
            new_hash = PasswordHandler.hash_password(login_data.password)
            await self.user_repo.update_password(user.id, new_hash)
            await self.user_repo.db_session.flush()
            await self.user_repo.db_session.commit()
            logger.info(f"Password for user {user.id} has been rehashed.")

        logger.info(f"User authenticated successfully: {user.id}")
        
        # Create UserReadSchema manually to avoid SQLAlchemy lazy loading issues with role property
        # We need to load the role separately using async methods
        await self.user_repo.db_session.refresh(user, ["role_assignments"])
        role = None
        if user.role_assignments:
            # Get active role assignments sorted by priority (highest first)
            active_assignments = [
                assignment for assignment in user.role_assignments
                if assignment.is_active and not assignment.is_deleted
            ]
            if active_assignments:
                # Sort by role priority (highest first) - need to load role relationship
                for assignment in active_assignments:
                    await self.user_repo.db_session.refresh(assignment, ["role"])
                active_assignments.sort(
                    key=lambda x: x.role.priority if x.role else 0, reverse=True
                )
                role = active_assignments[0].role.name if active_assignments[0].role else None
        
        user_data = {
            "id": user.id,
            "name": user.name,
            "email": user.email,
            "is_active": user.is_active,
            "is_superuser": user.is_superuser,
            "role": role,
            "last_login": user.last_login,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
        }
        return UserReadSchema.model_validate(user_data)

    @handle_service_errors("generate_access_token")
    @monitor_service_performance("generate_access_token")
    def generate_access_token(self, user: UserReadSchema) -> str:
        """Generate JWT access token for user."""
        logger.debug(f"Generating access token for user: {user.id}")
        from datetime import datetime, timedelta, timezone

        from jose import jwt
        from src.config.settings import settings

        now = datetime.now(timezone.utc)
        expire = now + timedelta(minutes=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        payload = {
            "sub": user.email,
            "email": user.email,
            "name": user.name,
            "iat": now,
            "exp": expire,
            "type": "access_token",
        }
        token = jwt.encode(
            payload, settings.SECRET_KEY, algorithm=settings.JWT_ALGORITHM
        )
        logger.debug(f"JWT access token generated for user: {user.id}")
        return token

    @handle_service_errors("login")
    @monitor_service_performance("login")
    async def login(self, login_data: LoginRequestSchema) -> LoginResponseSchema:
        """Perform user login and return authentication response."""
        logger.info(f"Login attempt for: {login_data.username}")
        user = await self.authenticate_user(login_data)
        access_token = self.generate_access_token(user)
        response = LoginResponseSchema(
            access_token=access_token,
            token_type="bearer",
            expires_in=settings.JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            user=user,
        )
        logger.info(f"Login successful for user: {user.id}")
        return response

    @handle_service_errors("logout")
    @monitor_service_performance("logout")
    async def logout(self, user_id: int) -> LogoutResponseSchema:
        """Perform user logout."""
        logger.info(f"Logout for user: {user_id}")
        from datetime import datetime, timezone

        logger.info(f"Logout successful for user: {user_id}")
        return LogoutResponseSchema(
            message="Successfully logged out", logged_out_at=datetime.now(timezone.utc)
        )

    @handle_service_errors("change_password")
    @monitor_service_performance("change_password")
    async def change_password(
        self, user_id: int, password_data: PasswordChangeRequestSchema
    ) -> bool:
        """Change user password."""
        logger.info(f"Password change request for user: {user_id}")
        user = await self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )
        if not PasswordHandler.verify_password(
            password_data.current_password, user.password_hash
        ):
            raise InvalidInputError("Current password is incorrect")

        new_password_hash = PasswordHandler.hash_password(password_data.new_password)
        success = await self.user_repo.update_password(user_id, new_password_hash)
        if not success:
            raise NotFoundError(
                code="USER_NOT_FOUND", detail=f"User {user_id} not found"
            )
        await self.user_repo.db_session.flush()
        await self.user_repo.db_session.commit()
        logger.info(f"Password changed successfully for user: {user_id}")
        return success

    @handle_service_errors("get_user_preferences")
    @monitor_service_performance("get_user_preferences")
    async def get_user_preferences(
        self, user_id: int
    ) -> Optional[UserPreferenceReadSchema]:
        """Get user preferences."""
        logger.debug(f"Retrieving preferences for user: {user_id}")
        user = await self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )
        preferences = await self.preference_repo.get_by_user_id(user_id)
        if preferences:
            return UserPreferenceReadSchema.model_validate(preferences)

        logger.debug(
            f"No preferences found for user {user_id}, returning default structure"
        )
        default_preferences = {
            "user_id": user_id,
            "theme": "light",
            "language": "en",
            "timezone": "UTC",
            "units": "metric",
            "notifications_enabled": True,
            "email_notifications": True,
            "dashboard_layout": "default",
            "calculation_precision": 2,
            "auto_save_enabled": True,
            "preferences_status": "default_applied",
            "message": "No custom preferences found, using system defaults",
        }
        return UserPreferenceReadSchema.model_validate(default_preferences)

    @handle_service_errors("create_or_update_user_preferences")
    @monitor_service_performance("create_or_update_user_preferences")
    async def create_or_update_user_preferences(
        self, user_id: int, preferences_data: UserPreferenceUpdateSchema
    ) -> UserPreferenceReadSchema:
        """Create or update user preferences."""
        logger.info(f"Creating/updating preferences for user: {user_id}")
        user = await self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )
        preferences_dict = preferences_data.model_dump(exclude_unset=True)
        preferences = await self.preference_repo.create_or_update_preferences(
            user_id, preferences_dict
        )
        await self.preference_repo.db_session.flush()
        await self.preference_repo.db_session.commit()
        await self.preference_repo.db_session.refresh(preferences)
        logger.info(f"Preferences updated successfully for user: {user_id}")
        return UserPreferenceReadSchema.model_validate(preferences)

    @handle_service_errors("delete_user_preferences")
    @monitor_service_performance("delete_user_preferences")
    async def delete_user_preferences(
        self, user_id: int, deleted_by_user_id: Optional[int] = None
    ) -> bool:
        """Soft delete user preferences."""
        logger.info(f"Deleting preferences for user: {user_id}")
        user = await self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )
        success = await self.preference_repo.soft_delete_preferences(
            user_id, deleted_by_user_id
        )
        if success:
            await self.preference_repo.db_session.flush()
            await self.preference_repo.db_session.commit()
            logger.info(f"Preferences deleted successfully for user: {user_id}")
        else:
            logger.debug(f"No preferences found for user: {user_id}")
        return success

    @handle_service_errors("search_users")
    @monitor_service_performance("search_users")
    async def search_users(
        self, search_term: str, skip: int = 0, limit: int = 100
    ) -> List[UserReadSchema]:
        """Search users by name or email."""
        logger.debug(f"Searching users with term: {search_term}")
        users = await self.user_repo.search_users(search_term, skip, limit)
        return [await self._convert_user_to_schema(user) for user in users]

    @handle_service_errors("count_active_users")
    @monitor_service_performance("count_active_users")
    async def count_active_users(self) -> int:
        """Count total number of active users."""
        logger.debug("Counting active users")
        return await self.user_repo.count_active_users()

    @handle_service_errors("update_user_last_login")
    @monitor_service_performance("update_user_last_login")
    async def update_user_last_login(self, user_id: int) -> bool:
        """Update user's last login timestamp with timezone-aware datetime."""
        logger.debug(f"Updating last login for user: {user_id}")
        user = await self.user_repo.get_by_id(user_id)
        if not user or not user.is_active:
            raise NotFoundError(
                code="USER_NOT_FOUND",
                detail=f"User {user_id} not found or inactive",
            )
        user.last_login = utcnow_aware()
        await self.user_repo.db_session.flush()
        await self.user_repo.db_session.commit()
        logger.info(f"Last login updated for user: {user_id}")
        return True
