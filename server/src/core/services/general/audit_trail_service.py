"""Generic Audit Trail Service for System-Wide Activity Logging.

This service provides comprehensive audit trail functionality that can be used
by any module to log user activities, system events, and data changes.
It supports both high-level activity logging and detailed change tracking.
"""

import datetime
from typing import Any, Dict, List, Optional, Union

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError

from src.core.models.general.activity_log import ActivityLog, AuditTrail
from src.core.models.general.user import User
from src.core.enums.system_enums import ErrorSeverity
from src.core.errors.unified_error_handler import unified_error_handler
from src.core.repositories.base_repository import BaseRepository
from src.core.utils.datetime_utils import utcnow_aware


class AuditTrailService:
    """Service for managing audit trails and activity logging.

    This service provides methods to:
    - Log user activities and system events
    - Track data changes with before/after values
    - Query audit logs for compliance and debugging
    - Support system-wide auditing requirements
    """

    def __init__(self, db: Session):
        """Initialize the audit trail service.

        Args:
            db: Database session for persistence operations
        """
        self.db = db
        self.activity_log_repo = BaseRepository(ActivityLog, db)
        self.audit_trail_repo = BaseRepository(AuditTrail, db)

    @unified_error_handler("activity_log_creation")
    def log_activity(
        self,
        action_type: str,
        action_description: str,
        user_id: Optional[int] = None,
        target_type: Optional[str] = None,
        target_id: Optional[int] = None,
        target_name: Optional[str] = None,
        session_id: Optional[str] = None,
        request_method: Optional[str] = None,
        request_path: Optional[str] = None,
        request_ip: Optional[str] = None,
        user_agent: Optional[str] = None,
        status: str = "SUCCESS",
        severity: ErrorSeverity = ErrorSeverity.INFO,
        metadata: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        execution_time_ms: Optional[int] = None,
        category: Optional[str] = None,
        tags: Optional[List[str]] = None,
        is_security_related: bool = False,
        is_data_change: bool = False,
        is_system_event: bool = False,
    ) -> ActivityLog:
        """Log a user activity or system event.

        Args:
            action_type: Type of action performed (e.g., "CREATE", "UPDATE", "DELETE")
            action_description: Human-readable description of the action
            user_id: ID of the user who performed the action
            target_type: Type of target entity (e.g., "Component", "Project")
            target_id: ID of the target entity
            target_name: Name of the target entity
            session_id: Session identifier
            request_method: HTTP request method
            request_path: Request path/endpoint
            request_ip: IP address of the request
            user_agent: User agent string
            status: Status of the action (SUCCESS, FAILED, WARNING)
            severity: Severity level of the event
            metadata: Additional metadata as JSON
            error_message: Error message if action failed
            execution_time_ms: Execution time in milliseconds
            category: Category for grouping related actions
            tags: List of tags for categorization
            is_security_related: Whether this is a security-related event
            is_data_change: Whether this involves data changes
            is_system_event: Whether this is a system-generated event

        Returns:
            ActivityLog: The created activity log entry
        """
        # Convert tags list to comma-separated string
        tags_str = None
        if tags:
            tags_str = ",".join(tags)

        # Convert metadata to JSON string if provided
        metadata_json = None
        if metadata:
            import json

            metadata_json = json.dumps(metadata)

        activity_log = ActivityLog(
            name=f"{action_type}_{utcnow_aware().strftime('%Y%m%d_%H%M%S')}",
            user_id=user_id,
            session_id=session_id,
            action_type=action_type,
            action_description=action_description,
            target_type=target_type,
            target_id=target_id,
            target_name=target_name,
            request_method=request_method,
            request_path=request_path,
            request_ip=request_ip,
            user_agent=user_agent,
            status=status,
            severity=severity,
            metadata=metadata_json,
            error_message=error_message,
            execution_time_ms=execution_time_ms,
            category=category,
            tags=tags_str,
            is_security_related=is_security_related,
            is_data_change=is_data_change,
            is_system_event=is_system_event,
        )

        return self.activity_log_repo.create(activity_log)

    @unified_error_handler("audit_trail_creation")
    def log_data_change(
        self,
        table_name: str,
        record_id: int,
        operation: str,
        user_id: Optional[int] = None,
        activity_log_id: Optional[int] = None,
        field_name: Optional[str] = None,
        old_value: Optional[str] = None,
        new_value: Optional[str] = None,
        change_reason: Optional[str] = None,
        change_context: Optional[Dict[str, Any]] = None,
        is_sensitive: bool = False,
        is_system_change: bool = False,
    ) -> AuditTrail:
        """Log a specific data change with before/after values.

        Args:
            table_name: Name of the table/model that was changed
            record_id: ID of the record that was changed
            operation: Type of operation (INSERT, UPDATE, DELETE)
            user_id: ID of the user who made the change
            activity_log_id: ID of the related activity log entry
            field_name: Name of the field that was changed
            old_value: Previous value of the field
            new_value: New value of the field
            change_reason: Reason for the change
            change_context: Additional context as JSON
            is_sensitive: Whether the change involves sensitive data
            is_system_change: Whether this is a system-generated change

        Returns:
            AuditTrail: The created audit trail entry
        """
        # Convert change context to JSON string if provided
        context_json = None
        if change_context:
            import json

            context_json = json.dumps(change_context)

        audit_trail = AuditTrail(
            name=f"{table_name}_{record_id}_{operation}_{utcnow_aware().strftime('%Y%m%d_%H%M%S')}",
            activity_log_id=activity_log_id,
            user_id=user_id,
            table_name=table_name,
            record_id=record_id,
            operation=operation,
            field_name=field_name,
            old_value=old_value,
            new_value=new_value,
            change_reason=change_reason,
            change_context=context_json,
            is_sensitive=is_sensitive,
            is_system_change=is_system_change,
        )

        return self.audit_trail_repo.create(audit_trail)

    @unified_error_handler("activity_log_batch_creation")
    def log_batch_activity(
        self,
        activities: List[Dict[str, Any]],
    ) -> List[ActivityLog]:
        """Log multiple activities in a batch operation.

        Args:
            activities: List of activity dictionaries with log parameters

        Returns:
            List[ActivityLog]: List of created activity log entries
        """
        activity_logs = []

        for activity_data in activities:
            activity_log = self.log_activity(**activity_data)
            activity_logs.append(activity_log)

        return activity_logs

    @unified_error_handler("audit_trail_batch_creation")
    def log_batch_data_changes(
        self,
        changes: List[Dict[str, Any]],
    ) -> List[AuditTrail]:
        """Log multiple data changes in a batch operation.

        Args:
            changes: List of change dictionaries with audit parameters

        Returns:
            List[AuditTrail]: List of created audit trail entries
        """
        audit_trails = []

        for change_data in changes:
            audit_trail = self.log_data_change(**change_data)
            audit_trails.append(audit_trail)

        return audit_trails

    @unified_error_handler("activity_log_query")
    def get_user_activities(
        self,
        user_id: int,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        action_types: Optional[List[str]] = None,
        categories: Optional[List[str]] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[ActivityLog]:
        """Get activities for a specific user.

        Args:
            user_id: ID of the user
            start_date: Start date for filtering
            end_date: End date for filtering
            action_types: List of action types to filter by
            categories: List of categories to filter by
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List[ActivityLog]: List of activity log entries
        """
        query = self.db.query(ActivityLog).filter(ActivityLog.user_id == user_id)

        if start_date:
            query = query.filter(ActivityLog.created_at >= start_date)

        if end_date:
            query = query.filter(ActivityLog.created_at <= end_date)

        if action_types:
            query = query.filter(ActivityLog.action_type.in_(action_types))

        if categories:
            query = query.filter(ActivityLog.category.in_(categories))

        return (
            query.order_by(ActivityLog.created_at.desc())
            .offset(offset)
            .limit(limit)
            .all()
        )

    @unified_error_handler("audit_trail_query")
    def get_record_history(
        self,
        table_name: str,
        record_id: int,
        field_name: Optional[str] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[AuditTrail]:
        """Get change history for a specific record.

        Args:
            table_name: Name of the table/model
            record_id: ID of the record
            field_name: Optional field name to filter by
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List[AuditTrail]: List of audit trail entries
        """
        query = self.db.query(AuditTrail).filter(
            AuditTrail.table_name == table_name,
            AuditTrail.record_id == record_id,
        )

        if field_name:
            query = query.filter(AuditTrail.field_name == field_name)

        return (
            query.order_by(AuditTrail.changed_at.desc())
            .offset(offset)
            .limit(limit)
            .all()
        )

    @unified_error_handler("security_event_query")
    def get_security_events(
        self,
        start_date: Optional[datetime.datetime] = None,
        end_date: Optional[datetime.datetime] = None,
        severity: Optional[ErrorSeverity] = None,
        limit: int = 100,
        offset: int = 0,
    ) -> List[ActivityLog]:
        """Get security-related events.

        Args:
            start_date: Start date for filtering
            end_date: End date for filtering
            severity: Severity level to filter by
            limit: Maximum number of records to return
            offset: Number of records to skip

        Returns:
            List[ActivityLog]: List of security-related activity log entries
        """
        query = self.db.query(ActivityLog).filter(
            ActivityLog.is_security_related == True
        )

        if start_date:
            query = query.filter(ActivityLog.created_at >= start_date)

        if end_date:
            query = query.filter(ActivityLog.created_at <= end_date)

        if severity:
            query = query.filter(ActivityLog.severity == severity)

        return (
            query.order_by(ActivityLog.created_at.desc())
            .offset(offset)
            .limit(limit)
            .all()
        )

    @unified_error_handler("audit_trail_cleanup")
    def cleanup_old_logs(
        self,
        days_to_keep: int = 365,
        batch_size: int = 1000,
    ) -> int:
        """Clean up old audit logs to manage database size.

        Args:
            days_to_keep: Number of days to keep logs
            batch_size: Number of records to delete in each batch

        Returns:
            int: Number of records deleted
        """
        cutoff_date = utcnow_aware() - datetime.timedelta(days=days_to_keep)

        # Delete old activity logs
        activity_deleted = 0
        while True:
            old_activities = (
                self.db.query(ActivityLog)
                .filter(ActivityLog.created_at < cutoff_date)
                .limit(batch_size)
                .all()
            )

            if not old_activities:
                break

            for activity in old_activities:
                self.db.delete(activity)

            activity_deleted += len(old_activities)
            self.db.commit()

        # Delete old audit trails
        audit_deleted = 0
        while True:
            old_audits = (
                self.db.query(AuditTrail)
                .filter(AuditTrail.changed_at < cutoff_date)
                .limit(batch_size)
                .all()
            )

            if not old_audits:
                break

            for audit in old_audits:
                self.db.delete(audit)

            audit_deleted += len(old_audits)
            self.db.commit()

        return activity_deleted + audit_deleted
