"""Project Member Service.

This module provides the business logic for managing project members,
including adding, updating, and removing members from projects.
"""

from typing import List

from sqlalchemy.ext.asyncio import AsyncSession

from src.config.logging_config import logger
from src.core.errors.exceptions import (
    DuplicateEntryError,
    NotFoundError,
    ProjectNotFoundError,
    UserNotFoundError,
)
from src.core.errors.unified_error_handler import handle_service_errors
from src.core.monitoring.unified_performance_monitor import monitor_service_performance
from src.core.repositories.general.project_member_repository import (
    ProjectMemberRepository,
)
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.repositories.general.user_repository import UserRepository
from src.core.schemas.general.project_member_schemas import (
    ProjectMemberCreateSchema,
    ProjectMemberReadSchema,
    ProjectMemberUpdateSchema,
)


class ProjectMemberService:
    """Service for managing project members."""

    def __init__(
        self,
        project_member_repo: ProjectMemberRepository,
        user_repo: UserRepository,
        project_repo: ProjectRepository,
    ):
        """Initialize the service."""
        self.project_member_repo = project_member_repo
        self.user_repo = user_repo
        self.project_repo = project_repo
        logger.debug("ProjectMemberService initialized")

    @handle_service_errors("add_member_to_project")
    @monitor_service_performance("add_member_to_project")
    async def add_member_to_project(
        self, project_id: int, member_data: ProjectMemberCreateSchema
    ) -> ProjectMemberReadSchema:
        """Add a member to a project."""
        # Validate that project and user exist
        project = await self.project_repo.get_by_id(project_id)
        if not project:
            raise ProjectNotFoundError(project_id=str(project_id))
        # Handle both model and schema objects
        is_deleted = getattr(project, "is_deleted", False)
        if is_deleted:
            raise ProjectNotFoundError(project_id=str(project_id))

        user = await self.user_repo.get_by_id(member_data.user_id)
        if not user:
            raise UserNotFoundError(user_id=member_data.user_id)
        # Handle both model and schema objects
        is_active = getattr(user, "is_active", True)
        if not is_active:
            raise UserNotFoundError(user_id=member_data.user_id)

        # Check if the user is already a member of the project
        existing_member = await self.project_member_repo.get_member_by_user_and_project(
            user_id=member_data.user_id, project_id=project_id
        )
        if existing_member:
            raise DuplicateEntryError("User is already a member of this project.")

        member_dict = member_data.model_dump()
        member_dict["project_id"] = project_id
        
        # Auto-generate name if not provided
        if "name" not in member_dict or not member_dict["name"]:
            user_name = getattr(user, "username", f"user_{member_data.user_id}")
            project_name = getattr(project, "name", f"project_{project_id}")
            member_dict["name"] = f"{user_name} - {project_name}"

        new_member = await self.project_member_repo.create(member_dict)
        await self.project_member_repo.db_session.flush()
        await self.project_member_repo.db_session.refresh(new_member)

        # Eagerly load relationships for the response
        member_with_details = await self.project_member_repo.get_member_with_details(
            new_member.id
        )

        return ProjectMemberReadSchema.model_validate(member_with_details)

    @handle_service_errors("remove_member_from_project")
    @monitor_service_performance("remove_member_from_project")
    async def remove_member_from_project(self, project_id: int, user_id: int) -> None:
        """Remove a member from a project."""
        member = await self.project_member_repo.get_member_by_user_and_project(
            user_id=user_id, project_id=project_id
        )
        if not member:
            raise NotFoundError(
                code="PROJECT_MEMBER_NOT_FOUND", detail="Project member not found."
            )

        await self.project_member_repo.delete(member.id)
        await self.project_member_repo.db_session.flush()

    @handle_service_errors("update_project_member")
    @monitor_service_performance("update_project_member")
    async def update_project_member(
        self, member_id: int, update_data: ProjectMemberUpdateSchema
    ) -> ProjectMemberReadSchema:
        """Update a project member's role or status."""
        member = await self.project_member_repo.get_by_id(member_id)
        if not member:
            raise NotFoundError(
                code="PROJECT_MEMBER_NOT_FOUND", detail="Project member not found."
            )

        update_dict = update_data.model_dump(exclude_unset=True)
        updated_member = await self.project_member_repo.update(member_id, update_dict)
        await self.project_member_repo.db_session.flush()
        # Only refresh if it's a model object, not a schema
        if hasattr(updated_member, "_sa_instance_state"):
            await self.project_member_repo.db_session.refresh(updated_member)

        # Eagerly load relationships for the response
        member_with_details = await self.project_member_repo.get_member_with_details(
            updated_member.id
        )

        return ProjectMemberReadSchema.model_validate(member_with_details)

    @handle_service_errors("list_project_members")
    @monitor_service_performance("list_project_members")
    async def list_project_members(
        self, project_id: int, skip: int = 0, limit: int = 100
    ) -> List[ProjectMemberReadSchema]:
        """List all members of a project."""
        project = await self.project_repo.get_by_id(project_id)
        if not project:
            raise ProjectNotFoundError(project_id=str(project_id))
        # Handle both model and schema objects
        is_deleted = getattr(project, "is_deleted", False)
        if is_deleted:
            raise ProjectNotFoundError(project_id=str(project_id))

        members = await self.project_member_repo.list_members_by_project(
            project_id, skip, limit
        )
        return [ProjectMemberReadSchema.model_validate(m) for m in members]
