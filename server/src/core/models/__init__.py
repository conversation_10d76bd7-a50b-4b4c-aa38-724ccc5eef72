"""Models Module.

This module imports all database models to ensure they are registered
with SQLAlchemy's metadata for Alembic migrations and other operations.
"""

# Import base classes
from .base import Base, CommonColumns, EnumType, SoftDeleteColumns

# Import all model classes to register them with SQLAlchemy metadata
from .general.activity_log import ActivityLog, AuditTrail
from .general.component import Component
from .general.component_category import ComponentCategory
from .general.component_type import ComponentType
from .general.project import Project, ProjectMember
from .general.synchronization_log import SynchronizationLog, SynchronizationConflict
from .general.task import Task, TaskAssignment
from .general.user import User, UserPreference
from .general.user_role import UserRole, UserRoleAssignment

# Export commonly used classes
__all__ = [
    "Base",
    "CommonColumns",
    "SoftDeleteColumns",
    "EnumType",
    "User",
    "UserPreference",
    "UserRole",
    "UserRoleAssignment",
    "ActivityLog",
    "AuditTrail",
    "Project",
    "ProjectMember",
    "SynchronizationLog",
    "SynchronizationConflict",
    "Component",
    "ComponentCategory",
    "ComponentType",
    "Task",
    "TaskAssignment",
]
