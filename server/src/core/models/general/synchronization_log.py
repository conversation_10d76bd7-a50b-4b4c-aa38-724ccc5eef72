"""Synchronization Log Database Models.

SQLAlchemy models for tracking synchronization operations
between local PostgreSQL instances and the central PostgreSQL database.

Key models:
- SynchronizationLog: Records synchronization attempts, status, and metadata
- SynchronizationConflict: Tracks conflicts detected during synchronization
"""

import datetime
from typing import TYPE_CHECKING, Optional
from uuid import UUID

from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, Integer, String, Text
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from src.core.enums.system_enums import (
    ErrorSeverity,
    SyncDirection,
    SyncOperation,
    SyncStatus,
)
from src.core.models.base import Base, CommonColumns, EnumType
from src.core.utils.datetime_utils import utcnow_aware
from src.core.utils.json_validation import FlexibleJSON

if TYPE_CHECKING:
    from .project import Project
    from .user import User


class SynchronizationLog(Base):
    """Model for logging synchronization operations and their outcomes.

    This model provides comprehensive tracking of synchronization attempts,
    their status, timing, and any conflicts or errors encountered.
    """

    __tablename__ = "synchronization_logs"

    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    # Project and session information
    project_id: Mapped[int] = mapped_column(ForeignKey("projects.id"), nullable=False)
    session_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    user_id: Mapped[Optional[int]] = mapped_column(ForeignKey("users.id"), nullable=True)

    # Synchronization operation details
    operation_type: Mapped[SyncOperation] = mapped_column(
        EnumType(SyncOperation), nullable=False
    )
    sync_direction: Mapped[SyncDirection] = mapped_column(
        EnumType(SyncDirection), nullable=False
    )
    status: Mapped[SyncStatus] = mapped_column(
        EnumType(SyncStatus), default=SyncStatus.PENDING, nullable=False
    )

    # Timing information
    started_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=utcnow_aware, nullable=False
    )
    completed_at: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True
    )
    duration_ms: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)

    # Source and target database information
    source_database_url: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    target_database_url: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Synchronization results
    records_processed: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    records_created: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    records_updated: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    records_deleted: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    conflicts_detected: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    conflicts_resolved: Mapped[int] = mapped_column(Integer, default=0, nullable=False)

    # Error and metadata information
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    error_details: Mapped[Optional[str]] = mapped_column(FlexibleJSON, nullable=True)
    sync_metadata: Mapped[Optional[str]] = mapped_column(FlexibleJSON, nullable=True)

    # Configuration used for this sync
    sync_config: Mapped[Optional[str]] = mapped_column(FlexibleJSON, nullable=True)

    # Performance and quality metrics
    throughput_records_per_second: Mapped[Optional[float]] = mapped_column(
        nullable=True
    )
    memory_usage_peak_mb: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    network_bytes_transferred: Mapped[Optional[int]] = mapped_column(
        Integer, nullable=True
    )

    # Flags for categorization
    is_automatic: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_retry: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    is_critical: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)

    # Retry information
    retry_count: Mapped[int] = mapped_column(Integer, default=0, nullable=False)
    max_retries: Mapped[int] = mapped_column(Integer, default=3, nullable=False)
    next_retry_at: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True
    )

    # Audit and tracking
    created_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=utcnow_aware, nullable=False
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=utcnow_aware, onupdate=utcnow_aware, nullable=False
    )

    # Relationships
    project: Mapped["Project"] = relationship(
        "Project",
        foreign_keys=[project_id],
        back_populates="synchronization_logs",
    )

    user: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[user_id],
        back_populates="synchronization_logs",
    )

    conflicts: Mapped[list["SynchronizationConflict"]] = relationship(
        "SynchronizationConflict",
        back_populates="sync_log",
        cascade="all, delete-orphan",
    )

    def __repr__(self) -> str:
        return (
            f"<SynchronizationLog(id={self.id}, project_id={self.project_id}, "
            f"operation_type='{self.operation_type.value}', status='{self.status.value}')>"
        )

    @property
    def is_completed(self) -> bool:
        """Check if the synchronization operation is completed."""
        return self.status in (
            SyncStatus.COMPLETED,
            SyncStatus.FAILED,
            SyncStatus.CANCELLED,
        )

    @property
    def is_successful(self) -> bool:
        """Check if the synchronization operation was successful."""
        return self.status == SyncStatus.COMPLETED

    @property
    def has_conflicts(self) -> bool:
        """Check if the synchronization operation had conflicts."""
        return self.conflicts_detected > 0

    def calculate_duration(self) -> Optional[int]:
        """Calculate the duration of the synchronization in milliseconds."""
        if self.started_at and self.completed_at:
            delta = self.completed_at - self.started_at
            return int(delta.total_seconds() * 1000)
        return None

    def update_completion_metrics(self) -> None:
        """Update completion metrics when synchronization finishes."""
        self.completed_at = utcnow_aware()
        self.duration_ms = self.calculate_duration()

        if self.duration_ms and self.duration_ms > 0:
            self.throughput_records_per_second = (
                self.records_processed / (self.duration_ms / 1000.0)
                if self.records_processed > 0
                else 0.0
            )


class SynchronizationConflict(Base):
    """Model for tracking synchronization conflicts and their resolution.

    This model stores detailed information about conflicts detected during
    synchronization operations and how they were resolved.
    """

    __tablename__ = "synchronization_conflicts"

    # Primary key
    id: Mapped[int] = mapped_column(primary_key=True, autoincrement=True)

    # Link to synchronization log
    sync_log_id: Mapped[int] = mapped_column(
        ForeignKey("synchronization_logs.id"), nullable=False
    )

    # Entity information
    entity_type: Mapped[str] = mapped_column(String(100), nullable=False)
    entity_id: Mapped[str] = mapped_column(String(255), nullable=False)
    table_name: Mapped[str] = mapped_column(String(100), nullable=False)

    # Conflict details
    conflict_type: Mapped[str] = mapped_column(String(100), nullable=False)
    field_name: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)

    # Conflicting values
    local_value: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    central_value: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    local_timestamp: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True
    )
    central_timestamp: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True
    )

    # Resolution information
    is_resolved: Mapped[bool] = mapped_column(Boolean, default=False, nullable=False)
    resolution_strategy: Mapped[Optional[str]] = mapped_column(
        String(100), nullable=True
    )
    resolved_value: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    resolved_at: Mapped[Optional[datetime.datetime]] = mapped_column(
        DateTime, nullable=True
    )
    resolved_by_user_id: Mapped[Optional[int]] = mapped_column(
        ForeignKey("users.id"), nullable=True
    )

    # Severity and impact
    severity: Mapped[ErrorSeverity] = mapped_column(
        EnumType(ErrorSeverity), default=ErrorSeverity.MEDIUM, nullable=False
    )
    requires_manual_intervention: Mapped[bool] = mapped_column(
        Boolean, default=False, nullable=False
    )

    # Additional metadata
    conflict_metadata: Mapped[Optional[str]] = mapped_column(
        FlexibleJSON, nullable=True
    )
    resolution_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Audit timestamps
    detected_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=utcnow_aware, nullable=False
    )
    updated_at: Mapped[datetime.datetime] = mapped_column(
        DateTime, default=utcnow_aware, onupdate=utcnow_aware, nullable=False
    )

    # Relationships
    sync_log: Mapped["SynchronizationLog"] = relationship(
        "SynchronizationLog",
        foreign_keys=[sync_log_id],
        back_populates="conflicts",
    )

    resolved_by_user: Mapped[Optional["User"]] = relationship(
        "User",
        foreign_keys=[resolved_by_user_id],
        back_populates="resolved_sync_conflicts",
    )

    def __repr__(self) -> str:
        return (
            f"<SynchronizationConflict(id={self.id}, entity_type='{self.entity_type}', "
            f"entity_id='{self.entity_id}', conflict_type='{self.conflict_type}', "
            f"is_resolved={self.is_resolved})>"
        )

    @property
    def is_critical(self) -> bool:
        """Check if this conflict is critical."""
        return self.severity in (ErrorSeverity.CRITICAL, ErrorSeverity.HIGH)

    @property
    def age_hours(self) -> float:
        """Calculate the age of the conflict in hours."""
        now = utcnow_aware()
        delta = now - self.detected_at
        return delta.total_seconds() / 3600

    def resolve_conflict(
        self,
        resolution_strategy: str,
        resolved_value: Optional[str] = None,
        resolved_by_user_id: Optional[int] = None,
        resolution_notes: Optional[str] = None,
    ) -> None:
        """Mark the conflict as resolved with the given strategy and value."""
        self.is_resolved = True
        self.resolution_strategy = resolution_strategy
        self.resolved_value = resolved_value
        self.resolved_by_user_id = resolved_by_user_id
        self.resolution_notes = resolution_notes
        self.resolved_at = utcnow_aware()
