"""Base Model Classes and Common Database Patterns.

base model classes and common database patterns used throughout
the Ultimate Electrical Designer backend application. It provides mixins for common
functionality like timestamps, soft delete, and audit trails.

Key components:
- CommonColumns: Provides id, name, notes, created_at, updated_at fields
- SoftDeleteColumns: Provides soft delete functionality with deleted_at, deleted_by
- EnumType: Custom SQLAlchemy type for handling Python enums
- Base: SQLAlchemy declarative base for all models

All models in the application should inherit from these base classes to ensure
consistent database patterns and functionality.
"""

import datetime
from typing import TYPE_CHECKING, Any, Optional

from sqlalchemy import <PERSON>olean, DateTime, ForeignKey, String, Text, TypeDecorator
from sqlalchemy.ext.declarative import declared_attr
from sqlalchemy.orm import Mapped, declarative_base, mapped_column, relationship

# Import datetime utilities for timezone-naive timestamps (PostgreSQL TIMESTAMP WITHOUT TIME ZONE compatibility)
from src.core.utils.datetime_utils import utcnow_naive

# Lazy import to avoid circular dependencies - handle_database_errors imported at end of file

if TYPE_CHECKING:
    from .general.user import User

# Define the declarative base
Base = declarative_base()


# --- Mixin for Common Columns ---
class CommonColumns:
    """Mixin for common database table columns.
    
    Attributes:
        id: Auto-incrementing primary key identifier
        name: Required name column for the entity
        notes: Optional notes about the entity
        created_at: Timestamp when the entity was created
        updated_at: Timestamp when the entity was last updated
    """

    @declared_attr
    def id(cls) -> Mapped[int]:
        """Primary key identifier for the entity.

        Returns:
            Mapped[int]: Auto-incrementing primary key column.

        """
        return mapped_column(primary_key=True, autoincrement=True)

    @declared_attr
    def name(cls) -> Mapped[str]:
        """Name of the entity.

        Returns:
            Mapped[str]: Required name column for the entity.

        """
        return mapped_column(String(255), nullable=False)

    @declared_attr
    def notes(cls) -> Mapped[Optional[str]]:
        """Optional notes about the entity.

        Returns:
            Mapped[Optional[str]]: Optional notes column.

        """
        return mapped_column(Text, nullable=True)

    @declared_attr
    def created_at(cls) -> Mapped[datetime.datetime]:
        """Timestamp when the entity was created.

        Returns:
            Mapped[datetime.datetime]: Creation timestamp (naive UTC for PostgreSQL compatibility).

        """
        return mapped_column(DateTime, default=utcnow_naive)

    @declared_attr
    def updated_at(cls) -> Mapped[datetime.datetime]:
        """Timestamp when the entity was last updated.

        Returns:
            Mapped[datetime.datetime]: Last update timestamp (naive UTC for PostgreSQL compatibility).

        """
        return mapped_column(DateTime, default=utcnow_naive, onupdate=utcnow_naive)


# --- Mixin for Soft Delete ---
class SoftDeleteColumns:
    """Mixin for soft deletion columns.
    
    Attributes:
        is_deleted: Flag indicating if the entity has been soft deleted
        deleted_at: Timestamp when the entity was soft deleted
        deleted_by_user_id: ID of the user who soft deleted the entity
        deleted_by_user: Relationship to the user who soft deleted the entity
    """

    @declared_attr
    def is_deleted(cls) -> Mapped[bool]:
        """Flag indicating if the entity has been soft deleted.

        Returns:
            Mapped[bool]: Soft delete flag column.

        """
        return mapped_column(Boolean, default=False, nullable=False)

    @declared_attr
    def deleted_at(cls) -> Mapped[Optional[datetime.datetime]]:
        """Timestamp when the entity was soft deleted.

        Returns:
            Mapped[Optional[datetime.datetime]]: Deletion timestamp column.

        """
        return mapped_column(DateTime, nullable=True)

    @declared_attr
    def deleted_by_user_id(cls) -> Mapped[Optional[int]]:
        """ID of the user who soft deleted the entity.

        Returns:
            Mapped[Optional[int]]: User ID column for soft delete tracking.

        """
        return mapped_column(ForeignKey("users.id"), nullable=True)

    @declared_attr
    def deleted_by_user(cls) -> Mapped[Optional["User"]]:
        """Relationship to the user who soft deleted the entity.

        Returns:
            Mapped[Optional["User"]]: User relationship for soft delete tracking.

        """
        # Use a forward reference for 'User' as it's defined in another file
        return relationship(
            "User",
            foreign_keys=lambda: [cls.__table__.c.deleted_by_user_id],  # type: ignore
        )


# --- Custom TypeDecorator for Enums ---
class EnumType(TypeDecorator):
    """Custom SQLAlchemy TypeDecorator for handling Python Enum types.

    This decorator automatically converts Python Enum instances to their string
    values when storing in the database, and converts string values back to
    Enum instances when loading from the database.

    Attributes:
        impl: Base SQLAlchemy type (String)
        cache_ok: Enables SQLAlchemy caching for performance
        enum_class: The Python Enum class to use for conversion
    
    Methods:
        process_bind_param: Converts Enum instance to string for database storage
        process_result_value: Converts string from database back to Enum instance
        copy: Creates a copy of the EnumType instance with the same enum_class
    """

    impl = String
    cache_ok = True

    def __init__(self, enum_class: Optional[Any] = None, *arg: Any, **kw: Any) -> None:
        super(EnumType, self).__init__(*arg, **kw)
        self.enum_class = enum_class

    def process_bind_param(self, value: Any, dialect: Any) -> Any:
        """Convert Enum instance to string value for database storage."""
        if value is None:
            return value
        if self.enum_class and not isinstance(value, self.enum_class):
            raise TypeError(
                f"Value must be an instance of {self.enum_class}, got {type(value)}"
            )
        return value.value if hasattr(value, "value") else str(value)

    def process_result_value(self, value: Any, dialect: Any) -> Any:
        """Convert string value from database to Enum instance."""
        if value is None:
            return value
        if self.enum_class:
            return self.enum_class(value)
        return value

    def copy(self, **kw: Any) -> "EnumType":
        """Create a copy of the EnumType instance."""
        return EnumType(self.enum_class, **kw)


# Apply unified error handling to methods after class definition to avoid circular imports
def _apply_unified_error_handling_to_enum_type() -> None:
    """Apply unified error handling decorators to EnumType methods."""
    try:
        from src.core.errors.unified_error_handler import handle_database_errors

        # List of methods to apply error handling to
        methods_to_wrap = [
            ("process_bind_param", "enum_bind_param_processing"),
            ("process_result_value", "enum_result_value_processing"),
        ]

        for method_name, operation_name in methods_to_wrap:
            if hasattr(EnumType, method_name):
                original_method = getattr(EnumType, method_name)
                # Only wrap if not already wrapped
                if not hasattr(original_method, "_unified_error_wrapped"):
                    wrapped_method = handle_database_errors(operation_name)(
                        original_method
                    )
                    # Mark as wrapped using a different approach
                    try:
                        wrapped_method._unified_error_wrapped = True  # type: ignore
                    except AttributeError:
                        # If we can't set the attribute, just continue
                        pass
                    setattr(EnumType, method_name, wrapped_method)

    except ImportError:
        # Unified error handler not available, skip wrapping
        pass


# Apply the error handling
_apply_unified_error_handling_to_enum_type()
