"""Enumeration classes.

enumeration classes used for calculations,
data input/output, electrical design, standards compliance,
system operations, and user/project management within the
Ultimate Electrical Designer backend application. It provides
standardized choices for various parameters and statuses.
"""

from .calculation_enums import CalculationStatus, CalculationType, OptimizationObjective
from .common_enums import InstallationEnvironment, UnitOfMeasure
from .data_io_enums import (
    DomainDataType,
    FileFormat,
    ImportStatus,
    MappingDataType,
    MappingType,
    ParseStatus,
    RenderingEngine,
    ReportDocumentType,
    ReportPriority,
    ReportStatus,
    TemplateCategory,
    TrendPeriod,
)
from .electrical_enums import (
    CableInstallationMethod,
    CableSelectionCriteria,
    CircuitType,
    ComponentCategoryType,
    ComponentFunctionalCategory,
    ComponentType,
    ConductorMaterial,
    ElectricalCableType,
    ElectricalInsulationType,
    ElectricalNodeType,
    FeederType,
    LoadCriticality,
    LoadType,
    ProtectionDeviceType,
    SwitchboardFunction,
    VoltageLevel,
)
from .heat_tracing_enums import (
    HeatingMethodType,
    HeatTracingCableCategory,
    HTCircuitApplicationType,
    HTSensorType,
)
from .mechanical_enums import (
    PipeMaterialType,
    PipeSchedule,
    SoilType,
    SupportType,
    TankAccessoryType,
    TankType,
    ThermalInsulationType,
    ValveType,
)
from .project_management_enums import (
    BOMItemStatus,
    BOMStatus,
    EntityType,
    EventType,
    MilestoneStatus,
    ProcurementStatus,
    ProjectStatus,
    TaskPriority,
    TaskStatus,
    UserRole,
)
from .standards_enums import (
    ATEXGasGroup,
    ATEXProtectionConcept,
    ATEXZone,
    ComplianceLevel,
    EngineeringStandard,
    TemperatureClass,
)
from .system_enums import (
    ErrorContext,
    ErrorSeverity,
    MetricType,
    MonitoringContext,
    SecurityLevel,
    ValidationResult,
    ValidationSeverity,
    ValidationType,
)

__all__ = [
    # common_enums
    "InstallationEnvironment",
    "UnitOfMeasure",
    # electrical_enums
    "ComponentFunctionalCategory",
    "ElectricalNodeType",
    "CableInstallationMethod",
    "CircuitType",
    "LoadType",
    "LoadCriticality",
    "ElectricalCableType",
    "ElectricalInsulationType",
    "ConductorMaterial",
    "VoltageLevel",
    "CableSelectionCriteria",
    "ProtectionDeviceType",
    "SwitchboardFunction",
    "FeederType",
    # heat_tracing_enums
    "HeatingMethodType",
    "HTCircuitApplicationType",
    "HTSensorType",
    "HeatTracingCableCategory",
    # mechanical_enums
    "PipeMaterialType",
    "ThermalInsulationType",
    "TankType",
    "SupportType",
    "TankAccessoryType",
    "PipeSchedule",
    "ValveType",
    "SoilType",
    # calculations_enums
    "CalculationType",
    "CalculationStatus",
    "OptimizationObjective",
    # data_io_enums
    "FileFormat",
    "MappingDataType",
    "DomainDataType",
    "ImportStatus",
    "ParseStatus",
    "MappingType",
    "ReportDocumentType",
    "TemplateCategory",
    "RenderingEngine",
    "ReportStatus",
    "ReportPriority",
    "TrendPeriod",
    # standards_enums
    "TemperatureClass",
    "EngineeringStandard",
    "ComplianceLevel",
    "ATEXZone",
    "ATEXGasGroup",
    "ATEXProtectionConcept",
    # system_enums
    "ErrorSeverity",
    "ErrorContext",
    "MonitoringContext",
    "MetricType",
    "ValidationSeverity",
    "ValidationType",
    "ValidationResult",
    "SecurityLevel",
    # project_management_enums
    "EventType",
    "EntityType",
    "ProjectStatus",
    "UserRole",
    "BOMStatus",
    "BOMItemStatus",
    "ProcurementStatus",
    "TaskStatus",
    "MilestoneStatus",
]
