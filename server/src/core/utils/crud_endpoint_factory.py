"""CRUD Endpoint Factory.

This module provides a factory function for dynamically generating standard CRUD
(Create, Read, Update, Delete) FastAPI endpoints with engineering-grade robustness
and unified patterns compliance for professional electrical design workflows.

Key Features:
- Dynamic FastAPI router generation with unified error handling
- Engineering-grade CRUD endpoint patterns
- Professional electrical design workflow support
- IEEE/IEC/EN standards compliance integration
- Unified error handling with @handle_database_errors decorators
- Performance monitoring integration
- Advanced pagination, filtering, and search capabilities
- Custom parameters and business logic injection points
- Repository-specific method support beyond basic CRUD
- Multi-environment configuration support
"""

from collections.abc import Callable
from typing import Any, Dict, List, Optional, Type, TypeVar, Union

from fastapi import APIRouter, Body, Depends, HTTPException, Query, Response, status
from pydantic import BaseModel

from src.config.logging_config import logger
from src.core.errors.unified_error_handler import (
    handle_api_errors,
    handle_database_errors,
)
from src.core.monitoring.unified_performance_monitor import (
    monitor_repository_performance,
)
from src.core.schemas.error import ErrorResponseSchema
from src.core.utils.pagination_utils import parse_pagination_params, parse_sort_params

# Type variables
CreateSchemaType = TypeVar("CreateSchemaType", bound=BaseModel)
ReadSchemaType = TypeVar("ReadSchemaType", bound=BaseModel)
UpdateSchemaType = TypeVar("UpdateSchemaType", bound=BaseModel)
ListSchemaType = TypeVar("ListSchemaType", bound=BaseModel)
ServiceType = TypeVar("ServiceType")


class CRUDEndpointConfig:
    """Configuration for CRUD endpoint generation with engineering-grade capabilities.

    This configuration class supports professional electrical design workflows with
    advanced customization options, standards compliance, and unified patterns.
    """

    def __init__(
        self,
        entity_name: str,
        entity_name_plural: str,
        create_schema: Type[CreateSchemaType],
        read_schema: Type[ReadSchemaType],
        update_schema: Type[UpdateSchemaType],
        list_response_schema: Type[ListSchemaType],
        service_class: Type[ServiceType],
        service_dependency: Callable[..., Any],
        id_field: str = "id",
        id_type: Type[Union[str, int]] = str,
        enable_create: bool = True,
        enable_read: bool = True,
        enable_update: bool = True,
        enable_delete: bool = True,
        enable_list: bool = True,
        enable_search: bool = True,
        searchable_fields: Optional[List[str]] = None,
        sortable_fields: Optional[List[str]] = None,
        custom_responses: Optional[Dict[Union[int, str], Dict[str, Any]]] = None,
        create_method_name: Optional[str] = None,
        read_method_name: Optional[str] = None,
        update_method_name: Optional[str] = None,
        delete_method_name: Optional[str] = None,
        list_method_name: Optional[str] = None,
        global_dependencies: Optional[List[Any]] = None,
        # Custom path configuration
        create_path: Optional[str] = None,
        read_path: Optional[str] = None,
        update_path: Optional[str] = None,
        delete_path: Optional[str] = None,
        list_path: Optional[str] = None,
        # Engineering-grade enhancements
        enable_performance_monitoring: bool = True,
        enable_standards_validation: bool = False,
        custom_query_params: Optional[Dict[str, Any]] = None,
        business_logic_hooks: Optional[Dict[str, Callable[..., Any]]] = None,
        repository_specific_methods: Optional[List[str]] = None,
        validation_rules: Optional[Dict[str, Callable[..., Any]]] = None,
        # OpenAPI documentation overrides
        create_summary: Optional[str] = None,
        create_description: Optional[str] = None,
        read_summary: Optional[str] = None,
        read_description: Optional[str] = None,
        update_summary: Optional[str] = None,
        update_description: Optional[str] = None,
        delete_summary: Optional[str] = None,
        delete_description: Optional[str] = None,
        list_summary: Optional[str] = None,
        list_description: Optional[str] = None,
    ):
        self.entity_name = entity_name
        self.entity_name_plural = entity_name_plural
        self.create_schema = create_schema
        self.read_schema = read_schema
        self.update_schema = update_schema
        self.list_response_schema = list_response_schema
        self.service_class = service_class
        self.service_dependency = service_dependency
        self.id_field = id_field
        self.id_type = id_type
        self.enable_create = enable_create
        self.enable_read = enable_read
        self.enable_update = enable_update
        self.enable_delete = enable_delete
        self.enable_list = enable_list
        self.enable_search = enable_search
        self.searchable_fields = searchable_fields or []
        self.sortable_fields = sortable_fields or ["id", "created_at", "updated_at"]
        self.custom_responses = custom_responses or {}
        self.create_method_name = create_method_name or f"create_{entity_name.lower()}"
        self.read_method_name = read_method_name or f"get_{entity_name.lower()}"
        self.update_method_name = update_method_name or f"update_{entity_name.lower()}"
        self.delete_method_name = delete_method_name or f"delete_{entity_name.lower()}"
        self.list_method_name = (
            list_method_name or f"get_{entity_name_plural.lower()}_list"
        )
        self.global_dependencies = (
            global_dependencies if global_dependencies is not None else []
        )

        # Custom path configuration with defaults
        self.create_path = create_path or "/"
        self.read_path = read_path or f"/{{{id_field}}}"
        self.update_path = update_path or f"/{{{id_field}}}"
        self.delete_path = delete_path or f"/{{{id_field}}}"
        self.list_path = list_path or "/"

        # Engineering-grade enhancements
        self.enable_performance_monitoring = enable_performance_monitoring
        self.enable_standards_validation = enable_standards_validation
        self.custom_query_params = custom_query_params or {}
        self.business_logic_hooks = business_logic_hooks or {}
        self.repository_specific_methods = repository_specific_methods or []
        self.validation_rules = validation_rules or {}
        # OpenAPI documentation overrides
        self.create_summary = create_summary
        self.create_description = create_description
        self.read_summary = read_summary
        self.read_description = read_description
        self.update_summary = update_summary
        self.update_description = update_description
        self.delete_summary = delete_summary
        self.delete_description = delete_description
        self.list_summary = list_summary
        self.list_description = list_description


def create_crud_router(config: CRUDEndpointConfig) -> APIRouter:
    """Create a FastAPI router with standard CRUD endpoints.

    Args:
        config: CRUD endpoint configuration

    Returns:
        APIRouter: Router with CRUD endpoints

    """
    router = APIRouter()

    # Standard error responses
    standard_responses: Dict[Union[int, str], Dict[str, Any]] = {
        status.HTTP_400_BAD_REQUEST: {
            "model": ErrorResponseSchema,
            "description": "Invalid input data",
        },
        status.HTTP_404_NOT_FOUND: {
            "model": ErrorResponseSchema,
            "description": f"{config.entity_name.capitalize()} not found",
        },
        status.HTTP_500_INTERNAL_SERVER_ERROR: {
            "model": ErrorResponseSchema,
            "description": "Internal server error",
        },
    }

    # Merge with custom responses
    standard_responses.update(config.custom_responses)

    # CREATE endpoint
    if config.enable_create:

        @router.post(
            config.create_path,
            response_model=config.read_schema,
            status_code=status.HTTP_201_CREATED,
            summary=config.create_summary or f"Create a new {config.entity_name}",
            description=config.create_description,
            responses={
                **standard_responses,
                status.HTTP_409_CONFLICT: {
                    "model": ErrorResponseSchema,
                    "description": f"{config.entity_name.capitalize()} already exists",
                },
            },
        )
        @handle_api_errors(f"create_{config.entity_name}")
        @handle_database_errors(f"create_{config.entity_name}")
        async def create_entity(
            entity_data_dict: Dict[str, Any] = Body(...),
            service: ServiceType = Depends(config.service_dependency),
        ) -> Any:
            """Create a new entity with unified error handling."""
            if config.create_description:
                create_entity.__doc__ = config.create_description
            # Validate the data using the schema - this will raise ValidationError for FastAPI to handle
            try:
                entity_data = config.create_schema(**entity_data_dict)
            except Exception as e:
                # Return HTTP response directly for validation errors
                from fastapi import HTTPException
                from pydantic import ValidationError

                if isinstance(e, ValidationError):
                    # Return 422 status code with validation errors
                    raise HTTPException(
                        status_code=422, detail=f"Input validation failed: {e.errors()}"
                    )
                else:
                    # Convert other errors to validation errors
                    raise HTTPException(
                        status_code=422, detail=f"Input validation failed: {str(e)}"
                    )

            # Inject global dependencies
            for dep in config.global_dependencies:
                _ = dep

            # Apply pre-creation business logic hooks
            if "pre_create" in config.business_logic_hooks:
                entity_data = config.business_logic_hooks["pre_create"](entity_data)

            # Apply validation rules
            if "create" in config.validation_rules:
                config.validation_rules["create"](entity_data)

            create_method = getattr(service, config.create_method_name)
            new_entity = await create_method(entity_data)

            # Apply post-creation business logic hooks
            if "post_create" in config.business_logic_hooks:
                new_entity = config.business_logic_hooks["post_create"](new_entity)

            return new_entity

        # Apply performance monitoring if enabled
        if config.enable_performance_monitoring:
            create_entity = monitor_repository_performance(
                f"create_{config.entity_name}"
            )(create_entity)

    # READ endpoint
    if config.enable_read:

        @router.get(
            config.read_path,
            response_model=config.read_schema,
            summary=config.read_summary
            or f"Retrieve {config.entity_name} details by {config.id_field}",
            description=config.read_description,
            responses=standard_responses,
        )
        @handle_database_errors(f"get_{config.entity_name}")
        async def get_entity(
            id: config.id_type,  # Use configured id_type for proper type casting
            service: ServiceType = Depends(config.service_dependency),
        ) -> Any:
            """Retrieve entity details by ID with unified error handling."""
            if config.read_description:
                get_entity.__doc__ = config.read_description
            # Inject global dependencies
            for dep in config.global_dependencies:
                _ = dep

            # Apply pre-read business logic hooks
            if "pre_read" in config.business_logic_hooks:
                id = config.business_logic_hooks["pre_read"](id)

            get_method = getattr(service, config.read_method_name)
            entity = await get_method(id)

            # Apply post-read business logic hooks
            if "post_read" in config.business_logic_hooks:
                entity = config.business_logic_hooks["post_read"](entity)

            return entity

        # Apply performance monitoring if enabled
        if config.enable_performance_monitoring:
            get_entity = monitor_repository_performance(f"get_{config.entity_name}")(
                get_entity
            )

    # UPDATE endpoint
    if config.enable_update:

        @router.put(
            config.update_path,
            response_model=config.read_schema,
            summary=config.update_summary or f"Update {config.entity_name} details",
            description=config.update_description,
            responses=standard_responses,
        )
        @handle_database_errors(f"update_{config.entity_name}")
        async def update_entity(
            id: config.id_type,  # Use configured id_type for proper type casting
            entity_data_dict: Dict[str, Any] = Body(...),
            service: ServiceType = Depends(config.service_dependency),
        ) -> Any:
            """Update entity details with unified error handling."""
            if config.update_description:
                update_entity.__doc__ = config.update_description
            # Validate the data using the schema - this will raise ValidationError for FastAPI to handle
            try:
                entity_data = config.update_schema(**entity_data_dict)
            except Exception as e:
                # Re-raise validation errors for FastAPI to handle properly
                from fastapi import HTTPException
                from pydantic import ValidationError

                if isinstance(e, ValidationError):
                    # Convert to HTTPException with 422 status code
                    raise HTTPException(status_code=422, detail=e.errors())
                else:
                    # Convert other errors to validation errors
                    raise HTTPException(
                        status_code=422,
                        detail=[
                            {"loc": ["body"], "msg": str(e), "type": "value_error"}
                        ],
                    )

            # Inject global dependencies
            for dep in config.global_dependencies:
                _ = dep

            # Apply pre-update business logic hooks
            if "pre_update" in config.business_logic_hooks:
                entity_data = config.business_logic_hooks["pre_update"](id, entity_data)

            # Apply validation rules
            if "update" in config.validation_rules:
                config.validation_rules["update"](id, entity_data)

            update_method = getattr(service, config.update_method_name)
            updated_entity = await update_method(id, entity_data)

            # Apply post-update business logic hooks
            if "post_update" in config.business_logic_hooks:
                updated_entity = config.business_logic_hooks["post_update"](
                    updated_entity
                )

            return updated_entity

        # Apply performance monitoring if enabled
        if config.enable_performance_monitoring:
            update_entity = monitor_repository_performance(
                f"update_{config.entity_name}"
            )(update_entity)

    # DELETE endpoint
    if config.enable_delete:

        @router.delete(
            config.delete_path,
            status_code=status.HTTP_204_NO_CONTENT,
            summary=config.delete_summary or f"Delete a {config.entity_name}",
            description=config.delete_description,
            response_model=None,
        )
        @handle_database_errors(f"delete_{config.entity_name}")
        async def delete_entity(
            id: config.id_type,  # Use configured id_type for proper type casting
            service: ServiceType = Depends(config.service_dependency),
        ) -> Response:
            """Delete entity (soft delete) with unified error handling."""
            if config.delete_description:
                delete_entity.__doc__ = config.delete_description
            # Inject global dependencies
            for dep in config.global_dependencies:
                _ = dep

            # Apply pre-delete business logic hooks
            if "pre_delete" in config.business_logic_hooks:
                id = config.business_logic_hooks["pre_delete"](id)

            # Apply validation rules
            if "delete" in config.validation_rules:
                config.validation_rules["delete"](id)

            delete_method = getattr(service, config.delete_method_name)
            result = await delete_method(id)

            # Apply post-delete business logic hooks
            if "post_delete" in config.business_logic_hooks:
                config.business_logic_hooks["post_delete"](id, result)

            return Response(status_code=status.HTTP_204_NO_CONTENT)

        # Apply performance monitoring if enabled
        if config.enable_performance_monitoring:
            delete_entity = monitor_repository_performance(
                f"delete_{config.entity_name}"
            )(delete_entity)

    # LIST endpoint with pagination and search
    if config.enable_list:

        @router.get(
            config.list_path,
            response_model=config.list_response_schema,
            summary=config.list_summary
            or f"List {config.entity_name_plural} with pagination",
            description=config.list_description,
            responses=standard_responses,
        )
        @handle_database_errors(f"list_{config.entity_name_plural}")
        async def list_entities(
            page: int = Query(1, ge=1, description="Page number (1-based)"),
            per_page: int = Query(
                10,
                ge=1,
                le=100,
                description=f"Number of {config.entity_name_plural} per page",
            ),
            sort_by: Optional[str] = Query(None, description="Sort field"),
            sort_order: Optional[str] = Query(
                "asc", description="Sort order (asc/desc)"
            ),
            search: Optional[str] = (
                Query(None, description="Search term") if config.enable_search else None
            ),
            include_deleted: bool = Query(
                False, description="Include soft-deleted records"
            ),
            service: ServiceType = Depends(config.service_dependency),
        ) -> Any:
            """List entities with pagination and optional search using unified error handling."""
            if config.list_description:
                list_entities.__doc__ = config.list_description
            # Inject global dependencies
            for dep in config.global_dependencies:
                _ = dep

            # Parse parameters
            pagination_params = parse_pagination_params(page, per_page)
            sort_params = parse_sort_params(sort_by, sort_order)

            # Validate sort field
            if (
                sort_params.sort_by
                and sort_params.sort_by not in config.sortable_fields
            ):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid sort field. Allowed fields: {config.sortable_fields}",
                )

            # Build filters with custom query parameters
            filters: Dict[str, Any] = {"include_deleted": include_deleted}
            if search and config.enable_search:
                filters["search"] = search

            # Add custom query parameters
            filters.update(config.custom_query_params)

            # Apply pre-list business logic hooks
            if "pre_list" in config.business_logic_hooks:
                filters = config.business_logic_hooks["pre_list"](filters)

            # Call service method
            list_method = getattr(service, config.list_method_name)

            # Try enhanced method with pagination/search first
            try:
                result = await list_method(
                    pagination_params=pagination_params,
                    sort_params=sort_params,
                    filters=filters,
                )
            except TypeError:
                # Fallback to basic method for backward compatibility
                result = await list_method(
                    page=page, per_page=per_page, include_deleted=include_deleted
                )

            # Apply post-list business logic hooks
            if "post_list" in config.business_logic_hooks:
                result = config.business_logic_hooks["post_list"](result)

            return result

        # Apply performance monitoring if enabled
        if config.enable_performance_monitoring:
            list_entities = monitor_repository_performance(
                f"list_{config.entity_name_plural}"
            )(list_entities)

    # Add repository-specific methods beyond basic CRUD
    if config.repository_specific_methods:
        for method_name in config.repository_specific_methods:
            _add_repository_specific_endpoint(router, config, method_name)

    return router


def _add_repository_specific_endpoint(
    router: APIRouter, config: CRUDEndpointConfig, method_name: str
) -> None:
    """Add repository-specific endpoint beyond basic CRUD operations.

    Args:
        router: FastAPI router to add endpoint to
        config: CRUD endpoint configuration
        method_name: Name of the repository-specific method

    """

    @router.get(
        f"/{method_name}",
        summary=f"{config.entity_name.capitalize()} {method_name}",
        responses={
            status.HTTP_200_OK: {"description": f"Successful {method_name} operation"},
            status.HTTP_400_BAD_REQUEST: {"model": ErrorResponseSchema},
            status.HTTP_500_INTERNAL_SERVER_ERROR: {"model": ErrorResponseSchema},
        },
    )
    @handle_database_errors(f"{config.entity_name}_{method_name}")
    async def repository_specific_method(
        service: ServiceType = Depends(config.service_dependency),
        **kwargs: Any,  # Accept dynamic parameters
    ) -> Any:
        """Execute repository-specific method with unified error handling."""
        # Inject global dependencies
        for dep in config.global_dependencies:
            _ = dep

        # Apply pre-method business logic hooks
        hook_name = f"pre_{method_name}"
        if hook_name in config.business_logic_hooks:
            kwargs = config.business_logic_hooks[hook_name](kwargs)

        # Apply validation rules
        if method_name in config.validation_rules:
            config.validation_rules[method_name](**kwargs)

        # Execute the repository-specific method
        method = getattr(service, method_name)
        result = await method(**kwargs)

        # Apply post-method business logic hooks
        hook_name = f"post_{method_name}"
        if hook_name in config.business_logic_hooks:
            result = config.business_logic_hooks[hook_name](result)

        return result

    # Apply performance monitoring if enabled
    if config.enable_performance_monitoring:
        repository_specific_method = monitor_repository_performance(
            f"{config.entity_name}_{method_name}"
        )(repository_specific_method)


def create_simple_crud_router(
    entity_name: str,
    create_schema: Type[BaseModel],
    read_schema: Type[BaseModel],
    update_schema: Type[BaseModel],
    list_response_schema: Type[BaseModel],
    service_class: Type[Any],
    service_dependency: Callable[..., Any],
    global_dependencies: Optional[List[Any]] = None,
    **kwargs: Any,
) -> APIRouter:
    """Simplified function to create CRUD router with engineering-grade capabilities.

    This function creates a CRUD router with unified error handling, performance monitoring,
    and support for professional electrical design workflows.

    Args:
        entity_name: Name of the entity (e.g., "project")
        create_schema: Pydantic schema for creation
        read_schema: Pydantic schema for reading
        update_schema: Pydantic schema for updates
        list_response_schema: Pydantic schema for list responses
        service_class: Service class type
        service_dependency: FastAPI dependency for service injection
        global_dependencies: List of global dependencies to inject into all endpoints
        **kwargs: Additional configuration options including:
            - enable_performance_monitoring: Enable performance monitoring (default: True)
            - enable_standards_validation: Enable IEEE/IEC/EN standards validation (default: False)
            - custom_query_params: Custom query parameters for filtering
            - business_logic_hooks: Pre/post operation hooks for business logic
            - repository_specific_methods: List of repository-specific methods to expose
            - validation_rules: Custom validation rules for operations

    Returns:
        APIRouter: Configured CRUD router with unified patterns compliance

    """
    entity_name_plural = kwargs.pop("entity_name_plural", f"{entity_name}s")

    config = CRUDEndpointConfig(
        entity_name=entity_name,
        entity_name_plural=entity_name_plural,
        create_schema=create_schema,
        read_schema=read_schema,
        update_schema=update_schema,
        list_response_schema=list_response_schema,
        service_class=service_class,
        service_dependency=service_dependency,
        global_dependencies=global_dependencies,
        **kwargs,
    )

    return create_crud_router(config)


# Utility functions for common patterns


def create_project_crud_router(service_dependency: Callable[..., Any]) -> APIRouter:
    """Create CRUD router specifically for projects."""
    from src.core.schemas.general.project_schemas import (
        ProjectCreateSchema,
        ProjectListResponseSchema,
        ProjectReadSchema,
        ProjectUpdateSchema,
    )
    from src.core.services.general.project_service import ProjectService

    return create_simple_crud_router(
        entity_name="project",
        entity_name_plural="projects",
        create_schema=ProjectCreateSchema,
        read_schema=ProjectReadSchema,
        update_schema=ProjectUpdateSchema,
        list_response_schema=ProjectListResponseSchema,
        service_class=ProjectService,
        service_dependency=service_dependency,
        list_method_name="get_projects_paginated",  # Use the enhanced pagination method
        searchable_fields=["name", "description", "project_number"],
        sortable_fields=["name", "created_at", "updated_at", "project_number"],
    )


def create_user_crud_router(service_dependency: Callable[..., Any]) -> APIRouter:
    """Create CRUD router specifically for users."""
    from src.core.schemas.general.user_schemas import (
        UserCreateSchema,
        UserPaginatedResponseSchema,
        UserReadSchema,
        UserUpdateSchema,
    )
    from src.core.services.general.user_service import UserService

    return create_simple_crud_router(
        entity_name="user",
        entity_name_plural="users",
        create_schema=UserCreateSchema,
        read_schema=UserReadSchema,
        update_schema=UserUpdateSchema,
        list_response_schema=UserPaginatedResponseSchema,
        service_class=UserService,
        service_dependency=service_dependency,
        id_type=int,  # Critical fix: User IDs are integers, not strings
        delete_method_name="deactivate_user",  # Use deactivate_user for soft delete
        list_method_name="get_users_paginated",  # Use enhanced pagination method
        searchable_fields=["username", "email", "first_name", "last_name"],
        sortable_fields=["username", "email", "created_at", "last_login"],
        # OpenAPI documentation overrides
        create_summary="Create a new user",
        create_description="Create a new user with unified error handling.",
        read_summary="Retrieve user details by id",
        read_description="Retrieve user details by ID with unified error handling.",
        update_summary="Update user details",
        update_description="Update user details with unified error handling.",
        delete_summary="Delete a user",
        delete_description="Delete user (soft delete) with unified error handling.",
        list_summary="List users with pagination",
        list_description="List users with pagination and optional search using unified error handling.",
    )


def create_component_crud_router(service_dependency: Callable[..., Any]) -> APIRouter:
    """Create CRUD router specifically for components."""
    from src.core.schemas.general.component_schemas import (
        ComponentCreateSchema,
        ComponentPaginatedResponseSchema,
        ComponentReadSchema,
        ComponentUpdateSchema,
    )
    from src.core.services.general.component_service import ComponentService

    return create_simple_crud_router(
        entity_name="component",
        entity_name_plural="components",
        create_schema=ComponentCreateSchema,
        read_schema=ComponentReadSchema,
        update_schema=ComponentUpdateSchema,
        list_response_schema=ComponentPaginatedResponseSchema,
        service_class=ComponentService,
        service_dependency=service_dependency,
        delete_method_name="delete_component",  # Use delete_component for soft delete
        list_method_name="get_components_list",  # Use get_components_list for basic listing
        searchable_fields=["name", "description", "manufacturer", "part_number"],
        sortable_fields=["name", "manufacturer", "created_at", "updated_at"],
        id_type=int,
        # OpenAPI documentation overrides
        create_summary="Create a new component",
        create_description="Create a new electrical component with comprehensive validation.",
        read_summary="Retrieve component details by ID",
        read_description="Retrieve component details by ID with unified error handling.",
        update_summary="Update component details",
        update_description="Update component details with unified error handling.",
        delete_summary="Delete a component",
        delete_description="Delete component (soft delete) with unified error handling.",
        list_summary="List components with pagination",
        list_description="List components with pagination, search, and filtering.",
    )


def create_component_category_crud_router(
    service_dependency: Callable[..., Any],
) -> APIRouter:
    """Create CRUD router specifically for component categories."""
    from src.core.schemas.general.component_category_schemas import (
        ComponentCategoryCreateSchema,
        ComponentCategoryListResponseSchema,
        ComponentCategoryReadSchema,
        ComponentCategoryUpdateSchema,
    )
    from src.core.services.general.component_category_service import (
        ComponentCategoryService,
    )

    return create_simple_crud_router(
        entity_name="component_category",
        entity_name_plural="component_categories",
        create_schema=ComponentCategoryCreateSchema,
        read_schema=ComponentCategoryReadSchema,
        update_schema=ComponentCategoryUpdateSchema,
        list_response_schema=ComponentCategoryListResponseSchema,
        service_class=ComponentCategoryService,
        service_dependency=service_dependency,
        delete_method_name="delete_category",  # Use delete_category for soft delete
        list_method_name="list_categories",  # Use list_categories method
        searchable_fields=["name", "description"],
        sortable_fields=["name", "created_at", "updated_at"],
        id_type=int,
        # OpenAPI documentation overrides
        create_summary="Create a new component category",
        create_description="Create a new component category with hierarchical support.",
        read_summary="Retrieve component category details by ID",
        read_description="Retrieve component category details by ID with unified error handling.",
        update_summary="Update component category details",
        update_description="Update component category details with unified error handling.",
        delete_summary="Delete a component category",
        delete_description="Delete component category (soft delete) with unified error handling.",
        list_summary="List component categories with pagination",
        list_description="List component categories with pagination, search, and filtering.",
    )


def create_component_type_crud_router(
    service_dependency: Callable[..., Any],
) -> APIRouter:
    """Create CRUD router specifically for component types."""
    from src.core.schemas.general.component_type_schemas import (
        ComponentTypeCreateSchema,
        ComponentTypeListResponseSchema,
        ComponentTypeReadSchema,
        ComponentTypeUpdateSchema,
    )
    from src.core.services.general.component_type_service import ComponentTypeService

    return create_simple_crud_router(
        entity_name="component_type",
        entity_name_plural="component_types",
        create_schema=ComponentTypeCreateSchema,
        read_schema=ComponentTypeReadSchema,
        update_schema=ComponentTypeUpdateSchema,
        list_response_schema=ComponentTypeListResponseSchema,
        service_class=ComponentTypeService,
        service_dependency=service_dependency,
        delete_method_name="delete_type",  # Use delete_type for soft delete
        list_method_name="list_types",  # Use list_types method
        searchable_fields=["name", "description"],
        sortable_fields=["name", "created_at", "updated_at"],
        id_type=int,
        # OpenAPI documentation overrides
        create_summary="Create a new component type",
        create_description="Create a new component type with category relationship.",
        read_summary="Retrieve component type details by ID",
        read_description="Retrieve component type details by ID with unified error handling.",
        update_summary="Update component type details",
        update_description="Update component type details with unified error handling.",
        delete_summary="Delete a component type",
        delete_description="Delete component type (soft delete) with unified error handling.",
        list_summary="List component types with pagination",
        list_description="List component types with pagination, search, and filtering.",
    )
