============================= test session starts ==============================
platform linux -- Python 3.13.5, pytest-8.4.1, pluggy-1.6.0 -- /home/<USER>/dev/ued/server/.venv/bin/python
cachedir: .pytest_cache
metadata: {'Python': '3.13.5', 'Platform': 'Linux-5.15.167.4-microsoft-standard-WSL2-x86_64-with-glibc2.39', 'Packages': {'pytest': '8.4.1', 'pluggy': '1.6.0'}, 'Plugins': {'metadata': '3.1.1', 'timeout': '2.4.0', 'mock': '3.14.1', 'xdist': '3.8.0', 'benchmark': '5.1.0', 'Faker': '37.4.2', 'html': '4.1.1', 'env': '1.1.5', 'cov': '6.2.1', 'anyio': '4.9.0', 'hypothesis': '6.136.1', 'asyncio': '1.1.0'}}
benchmark: 5.1.0 (defaults: timer=time.perf_counter disable_gc=False min_rounds=5 min_time=0.000005 max_time=1.0 calibration_precision=10 warmup=False warmup_iterations=100000)
hypothesis profile 'default'
rootdir: /home/<USER>/dev/ued/server
configfile: pyproject.toml
plugins: metadata-3.1.1, timeout-2.4.0, mock-3.14.1, xdist-3.8.0, benchmark-5.1.0, Faker-37.4.2, html-4.1.1, env-1.1.5, cov-6.2.1, anyio-4.9.0, hypothesis-6.136.1, asyncio-1.1.0
timeout: 6000.0s
timeout method: thread
timeout func_only: True
asyncio: mode=Mode.AUTO, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function
collecting ... PROJECT_ROOT: /home/<USER>/dev/ued
PROJECT_ROOT: /home/<USER>/dev/ued
ENV_FILE: /home/<USER>/dev/ued/.env
collected 0 items

============================ no tests ran in 0.09s =============================
