fastapi[standard]~=0.116.1
uvicorn[standard]>=0.35.0
pydantic>=2.11.7
pydantic-settings>=2.10.1
sqlalchemy>=2.0.41
alembic>=1.16.4
psycopg2-binary>=2.9.10
asyncpg>=0.30.0
python-jose[cryptography]>=3.5.0
bcrypt>=4.3.0
python-multipart>=0.0.20
email-validator>=2.2.0
python-dotenv>=1.1.1
loguru>=0.7.3
structlog>=25.4.0
rich>=14.0.0
typer>=0.16.0
uuid6>=2025.0.1
psutil>=7.0.0
types-psutil>=7.0.0.20250601
numpy>=2.3.1
scipy>=1.16.0
pandas>=2.3.1
openpyxl>=3.1.5
xlsxwriter>=3.2.5
chardet>=5.2.0
jinja2>=3.1.6
weasyprint>=65.1
httpx>=0.28.1
requests>=2.32.4
jsonschema>=4.25.0
redis>=6.2.0
pandas-stubs>=2.3.0.250703
types-python-jose>=3.5.0.20250531
argon2-cffi>=25.1.0
networkx>=3.5
jsonpath-ng>=1.7.0

[dev]
pytest>=8.4.1
pytest-cov>=6.2.1
pytest-asyncio>=1.1.0
pytest-mock>=3.14.1
pytest-xdist>=3.8.0
pytest-benchmark>=5.1.0
pytest-timeout>=2.4.0
pytest-html>=4.1.1
pytest-env>=1.1.5
coverage[toml]>=7.9.2
factory-boy>=3.3.3
faker>=37.4.2
hypothesis<7.0.0,>=6.136.1
ruff>=0.12.4
mypy>=1.17.0
pre-commit>=4.2.0
bandit>=1.8.6
locust>=2.37.14
memory-profiler>=0.61.0
mkdocs>=1.6.1
mkdocs-material>=9.6.15
mkdocstrings[python]>=0.29.1
