"""Unit tests for compatibility matrix validation system.

This module contains comprehensive tests for the advanced compatibility matrix
validation including multi-dimensional compatibility analysis.
"""

import pytest
import asyncio
from datetime import datetime
from typing import Dict, List, Any

from src.core.validation.compatibility_matrix import (
    CompatibilityMatrix,
    CompatibilityLevel,
    CompatibilityDimension,
    CompatibilityScore,
    CompatibilityMatrixResult,
)


class TestCompatibilityMatrix:
    """Tests for compatibility matrix validation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.matrix = CompatibilityMatrix()

        # Test project data
        self.test_project = {
            "id": "project-1",
            "system_voltage": 480,
            "total_load_current": 100,
            "total_power": 48000,
            "system_frequency": 60,
            "environmental_conditions": {
                "ip_rating": "IP54",
                "min_temperature": -20,
                "max_temperature": 40,
                "max_humidity": 85,
            },
            "thermal_conditions": {
                "max_temperature_rise": 35,
                "heat_dissipation_capacity": 2000,
            },
            "safety_requirements": {
                "required_features": [
                    "overload_protection",
                    "short_circuit_protection",
                ],
                "required_certifications": ["UL", "CE"],
            },
            "applicable_standards": ["IEEE-1584", "NEC", "IEC-60947"],
            "mounting_requirements": {"type": "wall_mount", "max_weight": 50},
            "space_constraints": {
                "max_width": 400,
                "max_height": 300,
                "max_depth": 200,
            },
        }

        # Test component data
        self.test_component = {
            "id": "breaker-1",
            "voltage_rating": 480,
            "rated_current": 125,
            "rated_power": 60000,
            "frequency_rating": 60,
            "environmental_rating": {
                "ip_rating": "IP65",
                "min_operating_temp": -25,
                "max_operating_temp": 60,
                "max_humidity": 95,
            },
            "thermal_characteristics": {"temperature_rise": 30, "heat_generation": 800},
            "safety_features": {
                "safety_features": [
                    "overload_protection",
                    "short_circuit_protection",
                    "arc_fault_protection",
                ],
                "certifications": ["UL", "CE", "CSA"],
            },
            "compliance_standards": ["IEEE-1584", "NEC", "IEC-60947"],
            "mounting_type": "wall_mount",
            "dimensions": {"width": 350, "height": 250, "depth": 150},
            "weight": 25,
        }

    @pytest.mark.asyncio
    async def test_perfect_compatibility(self):
        """Test perfect compatibility scenario."""
        result = await self.matrix.calculate_compatibility_matrix(
            self.test_project, self.test_component
        )

        assert result.overall_score > 0.9
        assert result.overall_level == CompatibilityLevel.PERFECT
        assert len(result.critical_issues) == 0

    @pytest.mark.asyncio
    async def test_voltage_incompatibility(self):
        """Test voltage incompatibility."""
        component = dict(self.test_component)
        component["voltage_rating"] = 240  # Mismatch with 480V project

        result = await self.matrix.calculate_compatibility_matrix(
            self.test_project, component
        )

        electrical_score = next(
            s
            for s in result.dimension_scores
            if s.dimension == CompatibilityDimension.ELECTRICAL
        )

        assert electrical_score.score < 0.5
        assert electrical_score.level == CompatibilityLevel.INCOMPATIBLE
        assert any("480V" in rec for rec in electrical_score.recommendations)

    @pytest.mark.asyncio
    async def test_current_capacity_warning(self):
        """Test current capacity warning."""
        project = dict(self.test_project)
        project["total_load_current"] = 150  # Higher than component's 125A

        result = await self.matrix.calculate_compatibility_matrix(
            project, self.test_component
        )

        electrical_score = next(
            s
            for s in result.dimension_scores
            if s.dimension == CompatibilityDimension.ELECTRICAL
        )

        assert electrical_score.score < 0.8  # Should be marginal due to capacity
        assert any("Upgrade" in rec for rec in electrical_score.recommendations)

    @pytest.mark.asyncio
    async def test_environmental_rating_compatibility(self):
        """Test environmental rating compatibility."""
        component = dict(self.test_component)
        component["environmental_rating"] = {
            "ip_rating": "IP44",  # Lower than required IP54
            "min_operating_temp": -10,
            "max_operating_temp": 50,
            "max_humidity": 90,
        }

        result = await self.matrix.calculate_compatibility_matrix(
            self.test_project, component
        )

        environmental_score = next(
            s
            for s in result.dimension_scores
            if s.dimension == CompatibilityDimension.ENVIRONMENTAL
        )

        assert environmental_score.score < 0.8  # Should be marginal or incompatible
        assert any("IP54" in rec for rec in environmental_score.recommendations)

    @pytest.mark.asyncio
    async def test_mechanical_size_compatibility(self):
        """Test mechanical size compatibility."""
        component = dict(self.test_component)
        component["dimensions"] = {
            "width": 500,  # Exceeds 400mm limit
            "height": 250,
            "depth": 150,
        }

        result = await self.matrix.calculate_compatibility_matrix(
            self.test_project, component
        )

        mechanical_score = next(
            s
            for s in result.dimension_scores
            if s.dimension == CompatibilityDimension.MECHANICAL
        )

        assert mechanical_score.score < 0.8  # Should be marginal or incompatible
        assert any("dimensions" in rec for rec in mechanical_score.recommendations)

    @pytest.mark.asyncio
    async def test_standards_compliance(self):
        """Test standards compliance checking."""
        component = dict(self.test_component)
        component["compliance_standards"] = ["IEEE-1584"]  # Missing NEC and IEC

        result = await self.matrix.calculate_compatibility_matrix(
            self.test_project, component
        )

        standards_score = next(
            s
            for s in result.dimension_scores
            if s.dimension == CompatibilityDimension.STANDARDS
        )

        assert standards_score.score < 1.0  # Should be less than perfect
        assert "NEC" in str(standards_score.details.get("missing_standards", []))

    @pytest.mark.asyncio
    async def test_safety_feature_compatibility(self):
        """Test safety feature compatibility."""
        component = dict(self.test_component)
        component["safety_features"] = {
            "safety_features": [
                "overload_protection"
            ],  # Missing short_circuit_protection
            "certifications": ["UL", "CE"],
        }

        result = await self.matrix.calculate_compatibility_matrix(
            self.test_project, component
        )

        safety_score = next(
            s
            for s in result.dimension_scores
            if s.dimension == CompatibilityDimension.SAFETY
        )

        assert safety_score.score < 1.0  # Should be less than perfect
        assert any(
            "short_circuit_protection" in rec for rec in safety_score.recommendations
        )

    @pytest.mark.asyncio
    async def test_thermal_compatibility(self):
        """Test thermal compatibility."""
        component = dict(self.test_component)
        component["thermal_characteristics"] = {
            "temperature_rise": 40,  # Exceeds 35°C limit
            "heat_generation": 2500,  # Exceeds 2000W capacity
        }

        result = await self.matrix.calculate_compatibility_matrix(
            self.test_project, component
        )

        thermal_score = next(
            s
            for s in result.dimension_scores
            if s.dimension == CompatibilityDimension.THERMAL
        )

        assert thermal_score.score < 0.8  # Should be marginal or incompatible
        assert any("temperature" in rec for rec in thermal_score.recommendations)

    @pytest.mark.asyncio
    async def test_batch_compatibility_calculation(self):
        """Test batch compatibility calculation."""
        components = [
            self.test_component,
            {
                "id": "breaker-2",
                "voltage_rating": 240,  # Incompatible
                "rated_current": 100,
                "rated_power": 24000,
                "frequency_rating": 60,
                "environmental_rating": {
                    "ip_rating": "IP54",
                    "min_operating_temp": -20,
                    "max_operating_temp": 50,
                    "max_humidity": 80,
                },
                "thermal_characteristics": {
                    "temperature_rise": 35,
                    "heat_generation": 600,
                },
                "safety_features": {
                    "safety_features": ["overload_protection"],
                    "certifications": ["UL"],
                },
                "compliance_standards": ["IEEE-1584"],
                "mounting_type": "wall_mount",
                "dimensions": {"width": 300, "height": 200, "depth": 120},
                "weight": 20,
            },
        ]

        results = await self.matrix.batch_calculate_compatibility(
            self.test_project, components
        )

        assert len(results) == 2

        # First component should be highly compatible
        first_id, first_result = results[0]
        assert first_result.overall_score > 0.9
        assert first_result.overall_level == CompatibilityLevel.PERFECT

        # Second component should have issues
        second_id, second_result = results[1]
        assert second_result.overall_score < 0.8  # Should have compatibility issues
        assert any("voltage" in issue for issue in second_result.critical_issues)

    def test_compatibility_level_determination(self):
        """Test compatibility level determination."""
        assert (
            self.matrix._determine_compatibility_level(0.98)
            == CompatibilityLevel.PERFECT
        )
        assert (
            self.matrix._determine_compatibility_level(0.85)
            == CompatibilityLevel.COMPATIBLE
        )
        assert (
            self.matrix._determine_compatibility_level(0.70)
            == CompatibilityLevel.MARGINAL
        )
        assert (
            self.matrix._determine_compatibility_level(0.50)
            == CompatibilityLevel.INCOMPATIBLE
        )
        assert (
            self.matrix._determine_compatibility_level(0.30)
            == CompatibilityLevel.CONFLICT
        )

    def test_voltage_compatibility_calculation(self):
        """Test voltage compatibility calculation."""
        assert abs(self.matrix._calculate_voltage_compatibility(480, 480) - 1.0) < 0.01
        assert abs(self.matrix._calculate_voltage_compatibility(480, 500) - 0.96) < 0.01
        assert self.matrix._calculate_voltage_compatibility(480, 240) == 0.0

    def test_current_compatibility_calculation(self):
        """Test current compatibility calculation."""
        assert abs(self.matrix._calculate_current_compatibility(100, 150) - 1.0) < 0.01
        assert abs(self.matrix._calculate_current_compatibility(100, 125) - 1.0) < 0.01
        assert abs(self.matrix._calculate_current_compatibility(100, 110) - 0.88) < 0.01

    def test_ip_compatibility_calculation(self):
        """Test IP rating compatibility calculation."""
        assert self.matrix._calculate_ip_compatibility("IP54", "IP65") == 1.0
        assert (
            abs(self.matrix._calculate_ip_compatibility("IP54", "IP44") - 0.81) < 0.01
        )
        assert self.matrix._calculate_ip_compatibility("IP65", "IP54") == 0.0

    def test_temperature_compatibility_calculation(self):
        """Test temperature range compatibility calculation."""
        project_temp = {"min": -20, "max": 40}
        component_temp = {"min": -25, "max": 50}
        assert (
            abs(
                self.matrix._calculate_temperature_compatibility(
                    project_temp, component_temp
                )
                - 1.0
            )
            < 0.01
        )

        component_temp = {"min": -10, "max": 30}
        assert (
            abs(
                self.matrix._calculate_temperature_compatibility(
                    project_temp, component_temp
                )
                - 0.75
            )
            < 0.01
        )

        component_temp = {"min": 0, "max": 20}
        assert (
            self.matrix._calculate_temperature_compatibility(
                project_temp, component_temp
            )
            < 0.5
        )

    @pytest.mark.asyncio
    async def test_empty_project_data(self):
        """Test handling of empty project data."""
        result = await self.matrix.calculate_compatibility_matrix(
            {}, self.test_component
        )

        assert result.overall_score >= 0.0  # Should handle gracefully
        assert isinstance(result, CompatibilityMatrixResult)

    @pytest.mark.asyncio
    async def test_empty_component_data(self):
        """Test handling of empty component data."""
        result = await self.matrix.calculate_compatibility_matrix(self.test_project, {})

        assert result.overall_score == 0.0  # Should be incompatible
        assert result.overall_level == CompatibilityLevel.CONFLICT

    @pytest.mark.asyncio
    async def test_optimization_suggestions(self):
        """Test optimization suggestions generation."""
        component = dict(self.test_component)
        component["voltage_rating"] = 240  # Voltage issue
        component["environmental_rating"]["ip_rating"] = "IP44"  # IP issue

        result = await self.matrix.calculate_compatibility_matrix(
            self.test_project, component
        )

        assert len(result.optimization_suggestions) > 0
        assert any(
            "voltage" in suggestion.lower()
            for suggestion in result.optimization_suggestions
        )
        assert any(
            "ip" in suggestion.lower() for suggestion in result.optimization_suggestions
        )

    def test_compatibility_matrix_structure(self):
        """Test compatibility matrix structure."""
        assert len(self.matrix.dimension_weights) == 6
        assert sum(self.matrix.dimension_weights.values()) == 1.0

        assert len(self.matrix.compatibility_thresholds) == 4
        assert all(
            0.0 <= threshold <= 1.0
            for threshold in self.matrix.compatibility_thresholds.values()
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
