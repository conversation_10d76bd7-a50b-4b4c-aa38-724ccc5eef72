"""Unit tests for legacy data format migration validation system.

This module contains comprehensive tests for legacy electrical data format migration
including CSV, JSON, XML, and proprietary format validation.
"""

import pytest
import json
import csv
import io
from pathlib import Path
from typing import Dict, List, Any

from src.core.validation.legacy_migration_validator import (
    LegacyMigrationValidator,
    LegacyFormatType,
    DataFormat,
    ValidationLevel,
    MigrationStatus,
)


class TestLegacyMigrationValidator:
    """Tests for legacy data migration validation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.validator = LegacyMigrationValidator()

        # Test data
        self.old_csv_data = """VOLT,CURR,POW,FREQ,TYPE,CAT,DESC,MFG,MODEL,IP
480,100,48000,60,breaker,power,Main breaker,Schneider,QO120,IP65
240,50,12000,60,switch,control,Control switch,ABB,OT63F3,IP54"""

        self.pipe_delimited_data = """V_RATING|I_RATING|P_RATING|F_RATING|ENV_CLASS|TEMP_MIN|TEMP_MAX|STANDARDS
480|100|48000|60|industrial|-25|85|IEEE,IEC
240|50|12000|60|commercial|-20|70|NEC"""

        self.xml_v1_data = """<?xml version="1.0"?>
<components>
    <component>
        <voltage>480</voltage>
        <current>100</current>
        <power>48000</power>
        <frequency>60</frequency>
        <componentType>breaker</componentType>
        <category>power</category>
        <ipCode>IP65</ipCode>
        <temperatureRange>-25-85</temperatureRange>
    </component>
    <component>
        <voltage>240</voltage>
        <current>50</current>
        <power>12000</power>
        <frequency>60</frequency>
        <componentType>switch</componentType>
        <category>control</category>
        <ipCode>IP54</ipCode>
        <temperatureRange>-20-70</temperatureRange>
    </component>
</components>"""

        self.json_v1_data = """[
    {
        "v": 480,
        "i": 100,
        "p": 48000,
        "f": 60,
        "t": "breaker",
        "c": "power",
        "desc": "Main breaker",
        "ip": "IP65",
        "standards": ["IEEE", "IEC"]
    },
    {
        "v": 240,
        "i": 50,
        "p": 12000,
        "f": 60,
        "t": "switch",
        "c": "control",
        "desc": "Control switch",
        "ip": "IP54",
        "standards": ["NEC"]
    }
]"""

    @pytest.mark.asyncio
    async def test_old_csv_migration(self):
        """Test migration from old CSV format."""
        result = await self.validator.validate_legacy_migration(
            self.old_csv_data, LegacyFormatType.OLD_CSV, DataFormat.JSON
        )

        assert result.migration_status == MigrationStatus.COMPLETED
        assert result.total_records == 2
        assert result.migrated_records == 2
        assert result.data_quality_score > 0.8
        assert result.source_checksum != result.target_checksum

    @pytest.mark.asyncio
    async def test_pipe_delimited_migration(self):
        """Test migration from pipe delimited format."""
        result = await self.validator.validate_legacy_migration(
            self.pipe_delimited_data, LegacyFormatType.PIPE_DELIMITED, DataFormat.JSON
        )

        assert result.migration_status == MigrationStatus.COMPLETED
        assert result.total_records == 2
        assert result.migrated_records == 2
        assert result.data_quality_score > 0.8

    @pytest.mark.asyncio
    async def test_xml_v1_migration(self):
        """Test migration from XML v1 format."""
        result = await self.validator.validate_legacy_migration(
            self.xml_v1_data, LegacyFormatType.XML_V1, DataFormat.JSON
        )

        assert result.migration_status == MigrationStatus.COMPLETED
        assert result.total_records == 2
        assert result.migrated_records == 2
        assert result.data_quality_score > 0.8

    @pytest.mark.asyncio
    async def test_json_v1_migration(self):
        """Test migration from JSON v1 format."""
        result = await self.validator.validate_legacy_migration(
            self.json_v1_data, LegacyFormatType.JSON_V1, DataFormat.CSV
        )

        assert result.migration_status == MigrationStatus.COMPLETED
        assert result.total_records == 2
        assert result.migrated_records == 2
        assert result.data_quality_score > 0.8

    @pytest.mark.asyncio
    async def test_csv_to_csv_migration(self):
        """Test CSV to CSV migration."""
        result = await self.validator.validate_legacy_migration(
            self.old_csv_data, LegacyFormatType.OLD_CSV, DataFormat.CSV
        )

        assert result.migration_status == MigrationStatus.COMPLETED
        assert result.total_records == 2
        assert result.migrated_records == 2

    @pytest.mark.asyncio
    async def test_detection_of_legacy_format(self):
        """Test automatic detection of legacy format."""
        # Test pipe delimited detection
        result = await self.validator.validate_legacy_migration(
            self.pipe_delimited_data,
            LegacyFormatType.PROPRIETARY,  # Let it auto-detect
            DataFormat.JSON,
        )

        # Should auto-detect as pipe delimited
        assert result.migration_status == MigrationStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_invalid_legacy_data(self):
        """Test migration with invalid legacy data."""
        invalid_data = """VOLT,CURR,POW,FREQ
invalid,100,48000,60
480,invalid,12000,60"""

        result = await self.validator.validate_legacy_migration(
            invalid_data, LegacyFormatType.OLD_CSV, DataFormat.JSON
        )

        assert result.migration_status in [
            MigrationStatus.PARTIAL_SUCCESS,
            MigrationStatus.VALIDATION_ERROR,
        ]
        assert result.data_quality_score < 1.0
        assert len(result.validation_errors) > 0

    @pytest.mark.asyncio
    async def test_empty_legacy_data(self):
        """Test migration with empty legacy data."""
        empty_data = """VOLT,CURR,POW,FREQ"""

        result = await self.validator.validate_legacy_migration(
            empty_data, LegacyFormatType.OLD_CSV, DataFormat.JSON
        )

        assert result.migration_status == MigrationStatus.COMPLETED
        assert result.total_records == 0
        assert result.migrated_records == 0

    @pytest.mark.asyncio
    async def test_negative_values_in_legacy_data(self):
        """Test migration with negative electrical values."""
        negative_data = """VOLT,CURR,POW,FREQ,TYPE
-480,100,48000,60,breaker
480,-100,12000,60,switch"""

        result = await self.validator.validate_legacy_migration(
            negative_data, LegacyFormatType.OLD_CSV, DataFormat.JSON
        )

        assert result.migration_status == MigrationStatus.VALIDATION_ERROR
        assert any(
            "negative" in str(e.get("error", "")).lower()
            for e in result.validation_errors
        )

    @pytest.mark.asyncio
    async def test_custom_field_mapping(self):
        """Test custom field mapping functionality."""
        # Add custom field mapping
        self.validator.add_custom_field_mapping(
            LegacyFormatType.OLD_CSV,
            "CUSTOM_FIELD",
            "custom_attribute",
            "string",
            default_value="default",
        )

        custom_data = """VOLT,CURR,POW,FREQ,CUSTOM_FIELD
480,100,48000,60,custom_value"""

        result = await self.validator.validate_legacy_migration(
            custom_data, LegacyFormatType.OLD_CSV, DataFormat.JSON
        )

        assert result.migration_status == MigrationStatus.COMPLETED
        assert "custom_attribute" in result.field_mapping

    @pytest.mark.asyncio
    async def test_data_loss_identification(self):
        """Test identification of data loss during migration."""
        # Data with fields that won't be mapped
        extra_field_data = """VOLT,CURR,POW,FREQ,EXTRA_FIELD1,EXTRA_FIELD2
480,100,48000,60,extra1,extra2"""

        result = await self.validator.validate_legacy_migration(
            extra_field_data, LegacyFormatType.OLD_CSV, DataFormat.JSON
        )

        assert "Unmigrated fields" in str(result.data_loss)

    @pytest.mark.asyncio
    async def test_migration_history(self):
        """Test migration history tracking."""
        # Perform migration
        await self.validator.validate_legacy_migration(
            self.json_v1_data, LegacyFormatType.JSON_V1, DataFormat.CSV
        )

        history = self.validator.get_migration_history()
        assert len(history) > 0
        assert history[-1].source_format == LegacyFormatType.JSON_V1
        assert history[-1].target_format == DataFormat.CSV

    @pytest.mark.asyncio
    async def test_clear_migration_history(self):
        """Test clearing migration history."""
        # Add some history
        await self.validator.validate_legacy_migration(
            self.old_csv_data, LegacyFormatType.OLD_CSV, DataFormat.JSON
        )

        # Clear history
        self.validator.clear_migration_history()

        history = self.validator.get_migration_history()
        assert len(history) == 0

    @pytest.mark.asyncio
    async def test_unit_conversion_in_legacy_data(self):
        """Test unit conversion in legacy data."""
        unit_data = """VOLT,CURR,POW,FREQ
480V,100A,48kW,60Hz"""

        # Add custom transformation for units
        self.validator.add_custom_transformation_rule(
            "convert_voltage_unit", lambda x: float(str(x).replace("V", ""))
        )

        result = await self.validator.validate_legacy_migration(
            unit_data, LegacyFormatType.OLD_CSV, DataFormat.JSON
        )

        assert result.migration_status == MigrationStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_complex_xml_structure(self):
        """Test migration of complex XML structure."""
        complex_xml = """<?xml version="1.0"?>
<electrical_system>
    <components>
        <breaker>
            <specifications>
                <voltage>480</voltage>
                <current>100</current>
                <power>48000</power>
            </specifications>
            <environmental>
                <temperature>-25</temperature>
                <humidity>85</humidity>
            </environmental>
        </breaker>
    </components>
</electrical_system>"""

        result = await self.validator.validate_legacy_migration(
            complex_xml, LegacyFormatType.XML_V1, DataFormat.JSON
        )

        assert result.migration_status == MigrationStatus.COMPLETED

    @pytest.mark.asyncio
    async def test_recommendations_generation(self):
        """Test recommendations generation for migration."""
        # Data with issues
        problematic_data = """VOLT,CURR,POW,FREQ
,100,48000,60
480,,12000,60"""

        result = await self.validator.validate_legacy_migration(
            problematic_data, LegacyFormatType.OLD_CSV, DataFormat.JSON
        )

        assert len(result.recommendations) > 0
        assert any(
            "source data integrity" in str(rec) for rec in result.recommendations
        )


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
