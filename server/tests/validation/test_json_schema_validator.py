"""Unit tests for advanced JSON schema validation with JSON path queries.

This module contains comprehensive tests for the JSON schema validator including
JSON path queries, custom validators, and electrical-specific validation rules.
"""

import pytest
import asyncio
from datetime import datetime
from typing import Dict, List, Any

from src.core.validation.json_schema_validator import (
    AdvancedJsonSchemaValidator,
    ValidationComplexity,
    JsonPathQuery,
    json_schema_validator,
)


class TestJsonSchemaValidator:
    """Tests for advanced JSON schema validation."""

    def setup_method(self):
        """Set up test fixtures."""
        self.validator = AdvancedJsonSchemaValidator()

        # Test data
        self.valid_component = {
            "id": "breaker-001",
            "type": "breaker",
            "voltage_rating": 480,
            "current_rating": 100,
            "power_rating": 48000,
            "frequency_rating": 60,
            "environmental_rating": {
                "ip_rating": "IP65",
                "temperature_range": {"min": -25, "max": 60},
            },
            "compliance_standards": ["IEEE-1584", "NEC", "IEC-60947"],
        }

        self.valid_project = {
            "id": "project-001",
            "name": "Industrial Distribution System",
            "description": "480V industrial power distribution",
            "system_voltage": 480,
            "system_frequency": 60,
            "total_load_current": 500,
            "environmental_conditions": {
                "location_type": "indoor",
                "ambient_temperature": 25,
                "humidity": 75,
                "altitude": 100,
            },
            "applicable_standards": ["IEEE-1584", "NEC"],
        }

    @pytest.mark.asyncio
    async def test_valid_component_validation(self):
        """Test validation of valid component data."""
        result = await self.validator.validate_with_schema(
            self.valid_component, "component", ValidationComplexity.ADVANCED
        )

        assert result.is_valid is True
        assert len(result.errors) == 0
        assert result.data_quality_score > 0.9
        assert result.schema_version == "component"

    @pytest.mark.asyncio
    async def test_valid_project_validation(self):
        """Test validation of valid project data."""
        result = await self.validator.validate_with_schema(
            self.valid_project, "project", ValidationComplexity.ADVANCED
        )

        assert result.is_valid is True
        assert len(result.errors) == 0
        assert result.data_quality_score > 0.9
        assert result.schema_version == "project"

    @pytest.mark.asyncio
    async def test_missing_required_fields(self):
        """Test validation with missing required fields."""
        invalid_component = dict(self.valid_component)
        del invalid_component["voltage_rating"]

        result = await self.validator.validate_with_schema(
            invalid_component, "component", ValidationComplexity.BASIC
        )

        assert result.is_valid is False
        assert any("voltage_rating" in error.path for error in result.errors)
        assert result.data_quality_score < 0.5

    @pytest.mark.asyncio
    async def test_invalid_data_types(self):
        """Test validation with invalid data types."""
        invalid_component = dict(self.valid_component)
        invalid_component["voltage_rating"] = "high"  # Should be number

        result = await self.validator.validate_with_schema(
            invalid_component, "component", ValidationComplexity.BASIC
        )

        assert result.is_valid is False
        assert any(
            "voltage_rating" in error.path and "Type mismatch" in error.message
            for error in result.errors
        )

    @pytest.mark.asyncio
    async def test_out_of_range_values(self):
        """Test validation with out of range values."""
        invalid_component = dict(self.valid_component)
        invalid_component["voltage_rating"] = 2000000  # Above max

        result = await self.validator.validate_with_schema(
            invalid_component, "component", ValidationComplexity.BASIC
        )

        assert result.is_valid is False
        assert any(
            "voltage_rating" in error.path and "above maximum" in error.message.lower()
            for error in result.errors
        )

    @pytest.mark.asyncio
    async def test_invalid_enum_values(self):
        """Test validation with invalid enum values."""
        invalid_component = dict(self.valid_component)
        invalid_component["frequency_rating"] = 75  # Not 50 or 60

        result = await self.validator.validate_with_schema(
            invalid_component, "component", ValidationComplexity.BASIC
        )

        assert result.is_valid is False
        assert any(
            "frequency_rating" in error.path and "75" in str(error.value)
            for error in result.errors
        )

    @pytest.mark.asyncio
    async def test_invalid_ip_rating_format(self):
        """Test validation with invalid IP rating format."""
        invalid_component = dict(self.valid_component)
        invalid_component["environmental_rating"]["ip_rating"] = "IPX65"

        result = await self.validator.validate_with_schema(
            invalid_component, "component", ValidationComplexity.ADVANCED
        )

        assert result.is_valid is False
        assert any(
            "IP" in error.message and "format" in error.message.lower()
            for error in result.errors
        )

    @pytest.mark.asyncio
    async def test_temperature_range_validation(self):
        """Test temperature range validation."""
        invalid_component = dict(self.valid_component)
        invalid_component["environmental_rating"]["temperature_range"]["min"] = 50
        invalid_component["environmental_rating"]["temperature_range"]["max"] = 40

        result = await self.validator.validate_with_schema(
            invalid_component, "component", ValidationComplexity.ADVANCED
        )

        assert result.is_valid is False
        assert any(
            "temperature" in error.message and "minimum" in error.message.lower()
            for error in result.errors
        )

    @pytest.mark.asyncio
    async def test_custom_json_path_queries(self):
        """Test validation with custom JSON path queries."""
        queries = [
            JsonPathQuery(
                path="$.voltage_rating",
                query=None,  # Will be set by validator
                expected_type="number",
                constraints={"min": 100, "max": 1000},
                validation_function=lambda v, c: (
                    100 <= v <= 1000,
                    f"Voltage {v}V out of range",
                ),
            ),
            JsonPathQuery(
                path="$.current_rating",
                query=None,
                expected_type="number",
                constraints={"min": 1, "max": 500},
                validation_function=lambda v, c: (
                    1 <= v <= 500,
                    f"Current {v}A out of range",
                ),
            ),
        ]

        result = await self.validator.validate_with_json_path_queries(
            self.valid_component, queries
        )

        assert result.is_valid is True
        assert len(result.errors) == 0
        assert "$.voltage_rating" in result.processed_paths
        assert "$.current_rating" in result.processed_paths

    @pytest.mark.asyncio
    async def test_custom_json_path_validation_failure(self):
        """Test custom JSON path query validation failure."""
        invalid_data = dict(self.valid_component)
        invalid_data["voltage_rating"] = 50  # Below custom minimum

        queries = [
            JsonPathQuery(
                path="$.voltage_rating",
                query=None,
                expected_type="number",
                constraints={"min": 100, "max": 1000},
                validation_function=lambda v, c: (
                    100 <= v <= 1000,
                    f"Voltage {v}V out of range",
                ),
            )
        ]

        result = await self.validator.validate_with_json_path_queries(
            invalid_data, queries
        )

        assert result.is_valid is False
        assert any("50V" in error.message for error in result.errors)

    @pytest.mark.asyncio
    async def test_empty_data_validation(self):
        """Test validation with empty data."""
        result = await self.validator.validate_with_schema({}, "component")

        assert result.is_valid is False
        assert len(result.errors) > 0
        assert any("missing" in error.message.lower() for error in result.errors)

    @pytest.mark.asyncio
    async def test_invalid_schema_name(self):
        """Test validation with invalid schema name."""
        result = await self.validator.validate_with_schema(
            self.valid_component, "invalid_schema"
        )

        assert result.is_valid is False
        assert any("Unknown schema" in error.message for error in result.errors)

    @pytest.mark.asyncio
    async def test_basic_complexity_mode(self):
        """Test basic complexity validation mode."""
        result = await self.validator.validate_with_schema(
            self.valid_component, "component", ValidationComplexity.BASIC
        )

        assert result.is_valid is True
        assert len(result.errors) == 0

    @pytest.mark.asyncio
    async def test_nested_object_validation(self):
        """Test validation of nested objects."""
        invalid_data = dict(self.valid_component)
        del invalid_data["environmental_rating"]["temperature_range"]["max"]

        result = await self.validator.validate_with_schema(
            invalid_data, "component", ValidationComplexity.ADVANCED
        )

        assert result.is_valid is False
        assert any("temperature" in error.message.lower() for error in result.errors)

    @pytest.mark.asyncio
    async def test_array_validation(self):
        """Test validation of arrays."""
        invalid_component = dict(self.valid_component)
        invalid_component["compliance_standards"] = []  # Empty array

        result = await self.validator.validate_with_schema(
            invalid_component, "component", ValidationComplexity.BASIC
        )

        assert result.is_valid is False
        assert any(
            "compliance_standards" in error.path and "minItems" in str(error.constraint)
            for error in result.errors
        )

    @pytest.mark.asyncio
    async def test_data_quality_score_calculation(self):
        """Test data quality score calculation."""
        # Valid data should have high quality score
        result = await self.validator.validate_with_schema(
            self.valid_component, "component"
        )
        assert result.data_quality_score > 0.9

        # Data with warnings should have reduced score
        data_with_warnings = dict(self.valid_component)
        data_with_warnings["power_rating"] = 0  # Warning condition

        # Note: Basic schema doesn't generate warnings, so score remains high

    @pytest.mark.asyncio
    async def test_schema_extension(self):
        """Test adding custom schema."""
        custom_schema = {
            "$schema": "https://json-schema.org/draft/2020-12/schema",
            "type": "object",
            "required": ["name"],
            "properties": {"name": {"type": "string"}, "value": {"type": "number"}},
        }

        self.validator.add_custom_schema("custom", custom_schema)

        valid_data = {"name": "test", "value": 123}
        result = await self.validator.validate_with_schema(valid_data, "custom")

        assert result.is_valid is True
        assert "custom" in self.validator.get_available_schemas()

    def test_available_schemas(self):
        """Test getting available schemas."""
        schemas = self.validator.get_available_schemas()
        assert "component" in schemas
        assert "project" in schemas

    def test_schema_definition_retrieval(self):
        """Test schema definition retrieval."""
        schema = self.validator.get_schema_definition("component")
        assert schema is not None
        assert schema["$schema"] == "https://json-schema.org/draft/2020-12/schema"
        assert "properties" in schema
        assert "id" in schema["properties"]

    def test_global_validator_instance(self):
        """Test global validator instance."""
        assert json_schema_validator is not None
        assert hasattr(json_schema_validator, "validate_with_schema")
        assert hasattr(json_schema_validator, "validate_with_json_path_queries")

    @pytest.mark.asyncio
    async def test_validation_timing(self):
        """Test validation timing measurement."""
        result = await self.validator.validate_with_schema(
            self.valid_component, "component"
        )

        assert result.validation_time_ms >= 0
        assert isinstance(result.validation_time_ms, (int, float))

    @pytest.mark.asyncio
    async def test_complex_nested_validation(self):
        """Test complex nested object validation."""
        complex_data = {
            "id": "system-001",
            "name": "Complex System",
            "components": [
                {"id": "comp-001", "voltage_rating": 480, "current_rating": 100},
                {"id": "comp-002", "voltage_rating": 240, "current_rating": 50},
            ],
            "configuration": {
                "primary_voltage": 480,
                "secondary_voltage": 240,
                "protection": {
                    "main_breaker": "CB-001",
                    "branch_breakers": ["CB-002", "CB-003"],
                },
            },
        }

        # Add custom schema for complex data
        complex_schema = {
            "$schema": "https://json-schema.org/draft/2020-12/schema",
            "type": "object",
            "required": ["id", "name", "components"],
            "properties": {
                "id": {"type": "string"},
                "name": {"type": "string"},
                "components": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "required": ["id", "voltage_rating", "current_rating"],
                        "properties": {
                            "id": {"type": "string"},
                            "voltage_rating": {"type": "number", "minimum": 1},
                            "current_rating": {"type": "number", "minimum": 0.1},
                        },
                    },
                },
            },
        }

        self.validator.add_custom_schema("complex", complex_schema)
        result = await self.validator.validate_with_schema(complex_data, "complex")

        assert result.is_valid is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
