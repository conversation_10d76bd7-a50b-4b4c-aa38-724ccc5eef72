"""Global test configuration."""

import os
import sys
import pytest
import asyncio
import httpx
from typing import Generator, AsyncGenerator
from sqlalchemy import create_engine, text
from sqlalchemy.ext.asyncio import create_async_engine, async_sessionmaker, AsyncSession
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from fastapi.testclient import TestClient

# Set testing environment variables before importing application modules
os.environ["TESTING"] = "true"
os.environ["ENVIRONMENT"] = "testing"

# Add server and tests to path for imports# Add backend to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..")
tests_path = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, server_path)
sys.path.insert(0, tests_path)
from src.core.enums import ProjectStatus
from src.core.database.dependencies import get_db
from src.core.models.base import Base

# Test database configuration - Use PostgreSQL for consistency with production
# Note: For CI/CD, ensure PostgreSQL test database is available
# Local test database can be accessed with command:
# `psql -h localhost -p 5433 -U user -d ultimate_electrical_designer_test`
TEST_DATABASE_URL = None


@pytest.fixture(scope="session")
def test_settings():
    """Override settings for tests."""
    from src.config.settings import settings
    
    # Set the DATABASE_URL to the test database URL
    settings.DATABASE_URL = settings.TEST_DATABASE_URL
    settings.ENVIRONMENT = "testing"
    return settings


@pytest.fixture(scope="function")
def engine(test_settings):
    """Create a test database engine for each test function."""
    # Use PostgreSQL test database for consistency with production
    from sqlalchemy import create_engine
    import uuid

    # Get TEST_DATABASE_URL from test_settings
    TEST_DATABASE_URL = test_settings.TEST_DATABASE_URL
    
    # Use the configured test database directly (database isolation handled by test cleanup)
    test_db_url = TEST_DATABASE_URL
    
    # DEBUG: Print the actual URL being used
    print(f"DEBUG: Creating engine with URL: {test_db_url}")

    engine = create_engine(
        test_db_url,
        echo=False,  # Disable SQL logging for tests
        pool_pre_ping=True,
        pool_recycle=300,
    )

    # Create the pg_trgm extension for GIN indexes on text columns
    with engine.connect() as conn:
        conn.execute(text("CREATE EXTENSION IF NOT EXISTS pg_trgm"))
        conn.commit()

    Base.metadata.create_all(bind=engine)
    yield engine
    Base.metadata.drop_all(bind=engine)
    engine.dispose()


@pytest.fixture(scope="function")
def db_session(engine) -> Generator[Session, None, None]:
    """Create a new database session for each test."""
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestingSessionLocal()
    try:
        yield session
    finally:
        session.rollback()  # Rollback any uncommitted transactions
        session.close()
        # Since we're using in-memory databases per test, cleanup is automatic


@pytest.fixture(scope="function")
async def async_db_session(test_settings, engine) -> AsyncGenerator[AsyncSession, None]:
    """Create a new async database session for each test using the same database as the sync engine."""
    # Create async engine using the same database URL as the sync engine
    # The sync engine already creates the tables, so we just need to connect async to the same DB
    async_url = test_settings.TEST_DATABASE_URL.replace("postgresql://", "postgresql+asyncpg://")
    print(f"DEBUG: Async URL (reusing sync engine's database): {async_url}")
    async_engine = create_async_engine(async_url)
    
    TestingAsyncSessionLocal = async_sessionmaker(
        autocommit=False, 
        autoflush=False, 
        bind=async_engine,
        class_=AsyncSession,
        expire_on_commit=False  # Critical: Keep objects accessible after commit
    )
    async_session = TestingAsyncSessionLocal()
    try:
        yield async_session
    finally:
        # Don't rollback - let commits persist for the duration of the test
        # The engine/database cleanup will handle cleanup
        try:
            await async_session.close()
        except RuntimeError as e:
            if "attached to a different loop" in str(e):
                # Session cleanup will be handled by the engine disposal
                # This happens when pytest-xdist runs tests in parallel
                pass
            else:
                raise
        
        # Cleanup async engine with event loop safety
        try:
            await async_engine.dispose()
        except RuntimeError as e:
            if "attached to a different loop" in str(e):
                # Engine cleanup will be handled by process cleanup
                # This happens when pytest-xdist runs tests in parallel
                pass
            else:
                raise


@pytest.fixture(scope="function")
def client(db_session: Session) -> Generator[TestClient, None, None]:
    """Create a synchronous test client with database session override for backward compatibility."""
    from src.app import create_app
    from src.core.database.dependencies import get_db

    app = create_app()

    def override_get_db():
        yield db_session

    app.dependency_overrides[get_db] = override_get_db

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
async def async_http_client(async_db_session: AsyncSession, engine) -> AsyncGenerator[httpx.AsyncClient, None]:
    """Create an async test client with database session overrides for project-scoped routes."""
    from src.app import create_app
    from src.core.database.dependencies import get_project_db_session
    from src.core.database.connection_manager import get_contextual_db_session, get_project_contextual_db_session
    import httpx

    app = create_app()

    # Override all database session dependencies to use the shared test session
    # CRITICAL: These must yield the SAME session instance for persistence across HTTP requests
    async def override_get_project_db_session(project_id: int = None, project_repo=None):
        yield async_db_session

    async def override_get_contextual_db_session(project_id: int = None, project_repo=None):
        yield async_db_session

    async def override_get_project_contextual_db_session(project_id: int, project_repo=None):
        yield async_db_session

    app.dependency_overrides[get_project_db_session] = override_get_project_db_session
    app.dependency_overrides[get_contextual_db_session] = override_get_contextual_db_session
    app.dependency_overrides[get_project_contextual_db_session] = override_get_project_contextual_db_session

    # Use httpx.AsyncClient with transport that routes to the FastAPI app
    transport = httpx.ASGITransport(app=app)
    async with httpx.AsyncClient(transport=transport, base_url="http://testserver") as async_client:
        yield async_client

    app.dependency_overrides.clear()


@pytest.fixture(scope="function")
def async_client(async_db_session: AsyncSession) -> Generator[TestClient, None, None]:
    """Create a test client with async database session override."""
    from src.app import create_app
    from src.core.database.connection_manager import get_contextual_db_session

    app = create_app()

    async def override_get_contextual_db_session(project_id: int = None, project_repo=None):
        yield async_db_session

    app.dependency_overrides[get_contextual_db_session] = override_get_contextual_db_session

    with TestClient(app) as test_client:
        yield test_client

    app.dependency_overrides.clear()


# --------------------------------User Fixtures--------------------------------#

import uuid


@pytest.fixture
def admin_user_data():
    """Create admin user data."""
    unique_suffix = str(uuid.uuid4())[:8]
    return {
        "name": f"Test Admin {unique_suffix}",
        "email": f"admin.{unique_suffix}@example.com",
        "password": "SecurePass123",
        "is_superuser": True,
    }


@pytest.fixture
def user_repository(db_session):
    """Fixture for UserRepository."""
    from src.core.repositories.general.user_repository import UserRepository

    return UserRepository(db_session)


@pytest.fixture
async def async_user_repository(async_db_session):
    """Fixture for UserRepository with async session."""
    from src.core.repositories.general.user_repository import UserRepository

    return UserRepository(async_db_session)


@pytest.fixture
def user_preference_repository(db_session):
    """Fixture for UserPreferenceRepository."""
    from src.core.repositories.general.user_preference_repository import (
        UserPreferenceRepository,
    )

    return UserPreferenceRepository(db_session)


@pytest.fixture
async def async_user_preference_repository(async_db_session):
    """Fixture for UserPreferenceRepository with async session."""
    from src.core.repositories.general.user_preference_repository import (
        UserPreferenceRepository,
    )

    return UserPreferenceRepository(async_db_session)


@pytest.fixture
def user_service(user_repository, user_preference_repository):
    """Fixture for UserService."""
    from src.core.services.general.user_service import UserService

    return UserService(
        user_repository=user_repository,
        preference_repository=user_preference_repository,
    )


@pytest.fixture
async def async_user_service(async_user_repository, async_user_preference_repository):
    """Fixture for UserService with async repositories."""
    from src.core.services.general.user_service import UserService

    return UserService(
        user_repository=async_user_repository,
        preference_repository=async_user_preference_repository,
    )


@pytest.fixture
async def test_admin_user(async_user_service, admin_user_data):
    """Create an admin user in the database."""
    from src.core.schemas.general.user_schemas import UserCreateSchema

    user_create = UserCreateSchema(**admin_user_data)
    user = await async_user_service.create_user(user_create)
    return user


@pytest.fixture
def test_user_data():
    """Create test user data."""
    unique_suffix = str(uuid.uuid4())[:8]
    return {
        "name": f"Test Viewer {unique_suffix}",
        "email": f"viewer.{uuid.uuid4().hex}@example.com",
        "password": "SecurePass123",
        "is_superuser": False,
    }


@pytest.fixture
async def test_user(async_user_service, test_user_data):
    """Create a test user in the database."""
    from src.core.schemas.general.user_schemas import UserCreateSchema

    user_create = UserCreateSchema(**test_user_data)
    user = await async_user_service.create_user(user_create)
    return user


@pytest.fixture
async def admin_token(async_http_client: httpx.AsyncClient, test_admin_user):
    """Get admin authentication token."""
    login_data = {
        "username": test_admin_user.email,
        "password": "SecurePass123",
    }
    response = await async_http_client.post("/api/v1/auth/login", json=login_data)
    response.raise_for_status()
    return response.json()["access_token"]


@pytest.fixture
async def user_token(async_http_client: httpx.AsyncClient, test_user):
    """Get regular user authentication token."""
    login_data = {
        "username": test_user.email,
        "password": "SecurePass123",
    }
    response = await async_http_client.post("/api/v1/auth/login", json=login_data)
    response.raise_for_status()
    return response.json()["access_token"]


@pytest.fixture(scope="function")
async def authenticated_client(async_http_client: httpx.AsyncClient, user_token) -> httpx.AsyncClient:
    """Create authenticated test client."""

    # Set authorization header
    async_http_client.headers.update({"Authorization": f"Bearer {user_token}"})

    return async_http_client


@pytest.fixture(scope="function")
async def admin_client(async_http_client: httpx.AsyncClient, admin_token) -> httpx.AsyncClient:
    """Create authenticated test client with admin privileges."""

    # Set authorization header
    async_http_client.headers.update({"Authorization": f"Bearer {admin_token}"})

    return async_http_client


# Pytest markers for test categorization
pytest_plugins = [
    "pytest_asyncio",
]


def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line("markers", "unit: Unit tests for individual components")
    config.addinivalue_line(
        "markers", "integration: Integration tests for component interaction"
    )
    config.addinivalue_line("markers", "api: API endpoint tests")
    config.addinivalue_line("markers", "database: Database operation tests")
    config.addinivalue_line("markers", "calculations: Engineering calculation tests")
    config.addinivalue_line("markers", "standards: Standards compliance tests")
    config.addinivalue_line("markers", "security: Security validation tests")
    config.addinivalue_line("markers", "performance: Performance benchmarking tests")
    config.addinivalue_line("markers", "slow: Tests that take longer than 1 second")


def pytest_collection_modifyitems(config, items):
    """Automatically mark tests based on their location."""
    for item in items:
        # Mark database tests
        if "database" in str(item.fspath) or "db_session" in item.fixturenames:
            item.add_marker(pytest.mark.database)

        # Mark repository tests
        if "repositories" in str(item.fspath):
            item.add_marker(pytest.mark.repository)
            item.add_marker(pytest.mark.database)
            item.add_marker(pytest.mark.unit)

        # Mark API tests
        if "api" in str(item.fspath):
            item.add_marker(pytest.mark.api)

        # Mark calculation tests
        if "calculations" in str(item.fspath):
            item.add_marker(pytest.mark.calculations)

        # Mark service tests
        if "services" in str(item.fspath):
            item.add_marker(pytest.mark.service)

        # Mark standards tests
        if "standards" in str(item.fspath):
            item.add_marker(pytest.mark.standards)

        # Mark integration tests
        if "integration" in str(item.fspath):
            item.add_marker(pytest.mark.integration)

        # Mark security tests
        if "security" in str(item.fspath):
            item.add_marker(pytest.mark.security)

        # Mark performance tests
        if "performance" in str(item.fspath):
            item.add_marker(pytest.mark.performance)

        # Mark standards tests
        if "standards" in str(item.fspath):
            item.add_marker(pytest.mark.standards)
        else:
            # Default to unit tests
            item.add_marker(pytest.mark.unit)


# --------------------------------Project Fixtures--------------------------------#


@pytest.fixture
def project_repository(db_session):
    """Fixture for ProjectRepository."""
    from src.core.repositories.general.project_repository import ProjectRepository

    return ProjectRepository(db_session)


@pytest.fixture
def project_member_repository(db_session):
    """Fixture for ProjectMemberRepository."""
    from src.core.repositories.general.project_member_repository import ProjectMemberRepository

    return ProjectMemberRepository(db_session)


@pytest.fixture
async def async_project_repository(async_db_session):
    """Fixture for ProjectRepository with async session."""
    from src.core.repositories.general.project_repository import ProjectRepository

    return ProjectRepository(async_db_session)


@pytest.fixture
async def async_project_member_repository(async_db_session):
    """Fixture for ProjectMemberRepository with async session."""
    from src.core.repositories.general.project_member_repository import ProjectMemberRepository

    return ProjectMemberRepository(async_db_session)


@pytest.fixture
def test_project(db_session, test_user):
    """Create a test project in the database."""
    from src.core.models.general.project import Project

    unique_suffix = str(uuid.uuid4())[:8]
    project = Project(
        name=f"Test Project {unique_suffix}",
        description="A test project",
        project_number=f"PRJ-TEST-{unique_suffix}",
        status=ProjectStatus.ACTIVE.value,  # Use the enum value, not the enum
        client="Test Client",
        location="Test Location",
    )
    db_session.add(project)
    db_session.commit()
    db_session.refresh(project)
    return project



@pytest.fixture
def test_user_role(db_session):
    """Create a test user role in the database."""
    from src.core.models.general.user_role import UserRole

    unique_suffix = str(uuid.uuid4())[:8]
    role = UserRole()
    role.name = f"TEST_ROLE_{unique_suffix}"
    role.description = "Test role for unit tests"
    role.is_system_role = False
    role.is_active = True
    role.priority = 50
    db_session.add(role)
    db_session.commit()
    db_session.refresh(role)
    return role


@pytest.fixture
async def test_project_member(async_project_member_repository, async_user_repository, async_project_repository, test_project, test_user, test_user_role):
    """Create a test project member in the database."""
    from src.core.services.general.project_member_service import ProjectMemberService
    from src.core.schemas.general.project_member_schemas import (
        ProjectMemberCreateSchema,
    )

    member_service = ProjectMemberService(
        project_member_repo=async_project_member_repository,
        user_repo=async_user_repository,
        project_repo=async_project_repository,
    )
    member_create = ProjectMemberCreateSchema(
        name="Test Member",
        user_id=test_user.id,
        role_id=test_user_role.id,
        expires_at=None,
    )
    member = await member_service.add_member_to_project(test_project.id, member_create)
    return member
