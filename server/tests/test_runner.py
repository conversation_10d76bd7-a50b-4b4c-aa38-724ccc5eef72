"""Comprehensive Testing Script.

This script provides a unified interface for running tests across all layers of the
Ultimate Electrical Designer server with comprehensive coverage reporting, logging,
and integration with the project's unified patterns and error handling.

Features:
- Layer-specific testing with corrected coverage paths
- HTML and JSON coverage reports
- Timestamped test execution logs
- Real database connections (NO mocks) as per project standards
- Integration with unified patterns and error handling
- Command-line interface with layer selection
- Makefile integration support
- Exit codes reflecting test success/failure status

Usage:
    python scripts/testing/runner.py all --coverage
    python scripts/testing/runner.py api --verbose
    python scripts/testing/runner.py repositories --fail-fast
"""

import argparse
import json
import logging
import os
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple

# Add server to path for imports
server_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "..")
sys.path.insert(0, server_path)

from src.core.errors.unified_error_handler import handle_database_errors
from src.core.utils.datetime_utils import format_datetime, utcnow_aware


class TestRunnerError(Exception):
    """Custom exception for test runner errors."""

    pass


class TestLayerConfig:
    """Configuration for a test layer."""

    def __init__(
        self, name: str, test_path: str, coverage_path: str, description: str = ""
    ):
        self.name = name
        self.test_path = test_path
        self.coverage_path = coverage_path
        self.description = description


# Layer-specific testing configurations with corrected coverage paths
TEST_LAYERS = {
    "api": TestLayerConfig(
        name="api",
        test_path="tests/api/",
        coverage_path="src/api",
        description="API endpoint tests",
    ),
    "calculations": TestLayerConfig(
        name="calculations",
        test_path="tests/calculations/",
        coverage_path="src/core/calculations",
        description="Core calculation engine tests",
    ),
    "data_io": TestLayerConfig(
        name="data_io",
        test_path="tests/data_io/",
        coverage_path="src/core/data_io",
        description="Core data input/output tests",
    ),
    "database": TestLayerConfig(
        name="database",
        test_path="tests/database/",
        coverage_path="src/core/database",
        description="Core database infrastructure tests",
    ),
    "errors": TestLayerConfig(
        name="errors",
        test_path="tests/errors/",
        coverage_path="src/core/errors",
        description="Core error handling tests",
    ),
    "models": TestLayerConfig(
        name="models",
        test_path="tests/models/",
        coverage_path="src/core/models",
        description="Core data model tests",
    ),
    "monitoring": TestLayerConfig(
        name="monitoring",
        test_path="tests/monitoring/",
        coverage_path="src/core/monitoring",
        description="Core monitoring tests",
    ),
    "repositories": TestLayerConfig(
        name="repositories",
        test_path="tests/repositories/",
        coverage_path="src/core/repositories",
        description="Core repository layer tests",
    ),
    "schemas": TestLayerConfig(
        name="schemas",
        test_path="tests/schemas/",
        coverage_path="src/core/schemas",
        description="Core schema validation tests",
    ),
    "security": TestLayerConfig(
        name="security",
        test_path="tests/security/",
        coverage_path="src/core/security",
        description="Core security tests",
    ),
    "validation": TestLayerConfig(
        name="validation",
        test_path="tests/validation/",
        coverage_path="src/core/validation",
        description="Core validation tests",
    ),
    "services": TestLayerConfig(
        name="services",
        test_path="tests/services/",
        coverage_path="src/core/services",
        description="Core service layer tests",
    ),
    "standards": TestLayerConfig(
        name="standards",
        test_path="tests/standards/",
        coverage_path="src/core/standards",
        description="Engineering standards compliance tests",
    ),
    "utils": TestLayerConfig(
        name="utils",
        test_path="tests/utils/",
        coverage_path="src/core/utils",
        description="Core utility tests",
    ),
    "middleware": TestLayerConfig(
        name="middleware",
        test_path="tests/middleware/",
        coverage_path="middleware",
        description="Middleware tests",
    ),
    "security": TestLayerConfig(
        name="security",
        test_path="tests/security/",
        coverage_path="security",
        description="Security validation tests",
    ),
    "performance": TestLayerConfig(
        name="performance",
        test_path="tests/performance/",
        coverage_path="",
        description="Performance tests",
    ),
    "integration": TestLayerConfig(
        name="integration",
        test_path="tests/integration/",
        coverage_path="",
        description="Integration tests",
    ),
}


class UltimateElectricalDesignerTestRunner:
    """
    Comprehensive test runner for Ultimate Electrical Designer server.

    Provides layer-specific testing with coverage reporting, logging,
    and integration with unified patterns and error handling.
    """

    def __init__(self, server_path: Path):
        self.server_path = server_path
        self.test_reports_dir = server_path / "test_reports"
        self.test_logs_dir = server_path / "test_logs"
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # Setup logging
        self._setup_logging()

        # Ensure directories exist
        self._ensure_directories()

    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s [%(levelname)8s] %(name)s: %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        self.logger = logging.getLogger(__name__)

    @handle_database_errors("test_directory_creation")
    def _ensure_directories(self) -> None:
        """Ensure test report and log directories exist."""
        try:
            # Create main directories
            self.test_reports_dir.mkdir(exist_ok=True)
            self.test_logs_dir.mkdir(exist_ok=True)

            # Create layer-specific directories
            for layer_name in TEST_LAYERS.keys():
                (self.test_reports_dir / layer_name).mkdir(exist_ok=True)
                (self.test_logs_dir / layer_name).mkdir(exist_ok=True)

            # Create full test suite directory
            (self.test_reports_dir / "full").mkdir(exist_ok=True)
            (self.test_logs_dir / "full").mkdir(exist_ok=True)

            self.logger.info("✅ Test directories created successfully")

        except Exception as e:
            self.logger.error(f"❌ Failed to create test directories: {e}")
            raise TestRunnerError(f"Directory creation failed: {e}")

    def _build_pytest_command(
        self, layer_config: TestLayerConfig, options: Dict[str, any]
    ) -> List[str]:
        """Build pytest command for a specific layer."""
        cmd = ["python", "-m", "pytest", layer_config.test_path]

        # Coverage options
        if options.get("coverage", False):
            cmd.extend(
                [
                    f"--cov={layer_config.coverage_path}",
                    "--cov-report=term-missing",
                    f"--cov-report=html:{self.test_reports_dir}/{layer_config.name}/html",
                    f"--cov-report=json:{self.test_reports_dir}/{layer_config.name}/coverage.json",
                ]
            )

            # Coverage threshold
            if options.get("coverage_threshold"):
                cmd.append(f"--cov-fail-under={options['coverage_threshold']}")

        # Verbosity options
        if options.get("verbose", False):
            cmd.extend(["-vv"])
        elif options.get("quiet", False):
            cmd.extend(["-q", "-v"])
        else:
            cmd.extend(["-q", "-v"])

        # Traceback options
        if options.get("detailed_traceback", False):
            cmd.append("--tb=long")
        else:
            cmd.append("--tb=short")

        # Fail-fast option
        if options.get("fail_fast", False):
            cmd.append("--maxfail=1")

        # Parallel execution
        if options.get("parallel", False):
            cmd.extend(["-n", "auto"])

        return cmd

    def _build_full_suite_command(self, options: Dict[str, any]) -> List[str]:
        """Build pytest command for full test suite."""
        cmd = [
            "python",
            "-m",
            "pytest",
            "tests/",
            "--cov",
            "--cov-report=term-missing",
            f"--cov-report=html:{self.test_reports_dir}/full/html",
            f"--cov-report=json:{self.test_reports_dir}/full/coverage.json",
        ]

        # Verbosity options
        if options.get("verbose", False):
            cmd.extend(["-vv"])
        elif options.get("quiet", False):
            cmd.extend(["-q", "-v"])
        else:
            cmd.extend(["-q", "-v"])

        # Traceback options
        if options.get("detailed_traceback", False):
            cmd.append("--tb=long")
        else:
            cmd.append("--tb=short")

        # Fail-fast option
        if options.get("fail_fast", False):
            cmd.append("--maxfail=1")

        # Parallel execution
        if options.get("parallel", False):
            cmd.extend(["-n", "auto"])

        # Coverage threshold
        if options.get("coverage_threshold"):
            cmd.append(f"--cov-fail-under={options['coverage_threshold']}")

        return cmd

    @handle_database_errors("test_execution")
    def _execute_test_command(
        self, cmd: List[str], log_file: Path, layer_name: str
    ) -> Tuple[int, str, str]:
        """Execute a test command and capture output."""
        try:
            self.logger.info(f"🧪 Running {layer_name} tests...")
            self.logger.debug(f"Command: {' '.join(cmd)}")

            # Execute command
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                cwd=self.server_path,
            )

            # Capture output
            stdout, stderr = process.communicate()

            # Write to log file
            with open(log_file, "w", encoding="utf-8") as f:
                f.write(f"# {layer_name.upper()} Test Execution Log\n")
                f.write(f"# Timestamp: {format_datetime(utcnow_aware())}\n")
                f.write(f"# Command: {' '.join(cmd)}\n")
                f.write(f"# Exit Code: {process.returncode}\n")
                f.write("=" * 80 + "\n\n")
                f.write(stdout or "")
                if stderr:
                    f.write("\n" + "=" * 80 + "\n")
                    f.write("STDERR:\n")
                    f.write(stderr)

            return process.returncode, stdout or "", stderr or ""

        except Exception as e:
            error_msg = f"Failed to execute {layer_name} tests: {e}"
            self.logger.error(f"❌ {error_msg}")
            raise TestRunnerError(error_msg)

    def _parse_coverage_report(self, coverage_file: Path) -> Optional[Dict]:
        """Parse JSON coverage report."""
        try:
            if coverage_file.exists():
                with open(coverage_file, "r") as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(
                f"⚠️ Failed to parse coverage report {coverage_file}: {e}"
            )
        return None

    def run_layer_tests(
        self, layer_name: str, options: Dict[str, any]
    ) -> Tuple[int, Dict]:
        """Run tests for a specific layer."""
        if layer_name not in TEST_LAYERS:
            raise TestRunnerError(f"Unknown test layer: {layer_name}")

        layer_config = TEST_LAYERS[layer_name]

        # Check if test directory exists
        test_path = self.server_path / layer_config.test_path
        if not test_path.exists():
            self.logger.warning(f"⚠️ Test directory not found: {test_path}")
            return 0, {"skipped": True, "reason": "Test directory not found"}

        # Build command
        cmd = self._build_pytest_command(layer_config, options)

        # Setup log file
        log_file = (
            self.test_logs_dir / layer_name / f"test_{layer_name}_{self.timestamp}.log"
        )

        # Execute tests
        exit_code, stdout, stderr = self._execute_test_command(
            cmd, log_file, layer_name
        )

        # Parse coverage if available
        coverage_data = None
        if options.get("coverage", False):
            coverage_file = self.test_reports_dir / layer_name / "coverage.json"
            coverage_data = self._parse_coverage_report(coverage_file)

        # Prepare result
        result = {
            "layer": layer_name,
            "exit_code": exit_code,
            "success": exit_code == 0,
            "log_file": str(log_file),
            "coverage": coverage_data,
            "timestamp": format_datetime(utcnow_aware()),
        }

        # Log result
        if exit_code == 0:
            self.logger.info(f"✅ {layer_name} tests passed")
        else:
            self.logger.error(f"❌ {layer_name} tests failed (exit code: {exit_code})")

        return exit_code, result

    def run_full_test_suite(self, options: Dict[str, any]) -> Tuple[int, Dict]:
        """Run the complete test suite."""
        self.logger.info("🚀 Starting full test suite execution...")

        # Build command
        cmd = self._build_full_suite_command(options)

        # Setup log file
        log_file = self.test_logs_dir / "full" / f"test_full_{self.timestamp}.log"

        # Execute tests
        exit_code, stdout, stderr = self._execute_test_command(cmd, log_file, "full")

        # Parse coverage
        coverage_data = None
        coverage_file = self.test_reports_dir / "full" / "coverage.json"
        coverage_data = self._parse_coverage_report(coverage_file)

        # Prepare result
        result = {
            "suite": "full",
            "exit_code": exit_code,
            "success": exit_code == 0,
            "log_file": str(log_file),
            "coverage": coverage_data,
            "timestamp": format_datetime(utcnow_aware()),
        }

        # Log result
        if exit_code == 0:
            self.logger.info("✅ Full test suite passed")
        else:
            self.logger.error(f"❌ Full test suite failed (exit code: {exit_code})")

        return exit_code, result

    def run_multiple_layers(
        self, layer_names: List[str], options: Dict[str, any]
    ) -> Tuple[int, List[Dict]]:
        """Run tests for multiple layers."""
        results = []
        overall_exit_code = 0

        for layer_name in layer_names:
            try:
                exit_code, result = self.run_layer_tests(layer_name, options)
                results.append(result)

                # Track overall failure
                if exit_code != 0:
                    overall_exit_code = exit_code

                    # Stop on first failure if fail-fast is enabled
                    if options.get("fail_fast", False):
                        self.logger.info("🛑 Stopping execution due to fail-fast mode")
                        break

            except Exception as e:
                self.logger.error(f"❌ Failed to run {layer_name} tests: {e}")
                results.append(
                    {
                        "layer": layer_name,
                        "exit_code": 1,
                        "success": False,
                        "error": str(e),
                        "timestamp": format_datetime(utcnow_aware()),
                    }
                )
                overall_exit_code = 1

                if options.get("fail_fast", False):
                    break

        return overall_exit_code, results

    def generate_summary_report(
        self, results: List[Dict], output_file: Optional[Path] = None
    ) -> Dict:
        """Generate consolidated summary report."""
        summary = {
            "timestamp": format_datetime(utcnow_aware()),
            "total_layers": len(results),
            "passed_layers": sum(1 for r in results if r.get("success", False)),
            "failed_layers": sum(1 for r in results if not r.get("success", False)),
            "skipped_layers": sum(1 for r in results if r.get("skipped", False)),
            "overall_success": all(
                r.get("success", False) for r in results if not r.get("skipped", False)
            ),
            "coverage_summary": {},
            "layer_results": results,
        }

        # Aggregate coverage data
        total_coverage = []
        for result in results:
            if result.get("coverage") and "totals" in result["coverage"]:
                coverage = result["coverage"]["totals"]
                total_coverage.append(
                    {
                        "layer": result.get("layer", "unknown"),
                        "percent_covered": coverage.get("percent_covered", 0),
                        "num_statements": coverage.get("num_statements", 0),
                        "missing_lines": coverage.get("missing_lines", 0),
                    }
                )

        if total_coverage:
            summary["coverage_summary"] = {
                "layers_with_coverage": len(total_coverage),
                "average_coverage": sum(c["percent_covered"] for c in total_coverage)
                / len(total_coverage),
                "total_statements": sum(c["num_statements"] for c in total_coverage),
                "total_missing": sum(c["missing_lines"] for c in total_coverage),
                "layer_coverage": total_coverage,
            }

        # Save to file if requested
        if output_file:
            try:
                with open(output_file, "w") as f:
                    json.dump(summary, f, indent=2, default=str)
                self.logger.info(f"📊 Summary report saved to: {output_file}")
            except Exception as e:
                self.logger.warning(f"⚠️ Failed to save summary report: {e}")

        return summary

    def print_summary(self, summary: Dict) -> None:
        """Print formatted summary to console."""
        print("\n" + "=" * 80)
        print("🧪 ULTIMATE ELECTRICAL DESIGNER TEST EXECUTION SUMMARY")
        print("=" * 80)
        print(f"📅 Timestamp: {summary['timestamp']}")
        print(f"📊 Total Layers: {summary['total_layers']}")
        print(f"✅ Passed: {summary['passed_layers']}")
        print(f"❌ Failed: {summary['failed_layers']}")
        print(f"⏭️ Skipped: {summary['skipped_layers']}")
        print(
            f"🎯 Overall Success: {'✅ YES' if summary['overall_success'] else '❌ NO'}"
        )

        # Coverage summary
        if summary.get("coverage_summary"):
            cov = summary["coverage_summary"]
            print(f"\n📈 COVERAGE SUMMARY:")
            print(f"   Average Coverage: {cov['average_coverage']:.1f}%")
            print(f"   Total Statements: {cov['total_statements']}")
            print(f"   Missing Lines: {cov['total_missing']}")

            # Layer-by-layer coverage
            if cov.get("layer_coverage"):
                print(f"\n📋 LAYER COVERAGE:")
                for layer_cov in sorted(
                    cov["layer_coverage"],
                    key=lambda x: x["percent_covered"],
                    reverse=True,
                ):
                    print(
                        f"   {layer_cov['layer']:<20}: {layer_cov['percent_covered']:>6.1f}%"
                    )

        # Failed layers details
        failed_layers = [
            r
            for r in summary["layer_results"]
            if not r.get("success", False) and not r.get("skipped", False)
        ]
        if failed_layers:
            print(f"\n❌ FAILED LAYERS:")
            for result in failed_layers:
                print(
                    f"   {result.get('layer', 'unknown')}: Exit code {result.get('exit_code', 'unknown')}"
                )
                if result.get("log_file"):
                    print(f"      Log: {result['log_file']}")

        print("=" * 80)


def create_argument_parser() -> argparse.ArgumentParser:
    """Create command-line argument parser."""
    parser = argparse.ArgumentParser(
        description="Ultimate Electrical Designer server Test Runner",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python scripts/testing/runner.py all --coverage
  python scripts/testing/runner.py api --verbose
  python scripts/testing/runner.py repositories --fail-fast
  python scripts/testing/runner.py calculations services --coverage --parallel
        """,
    )

    # Layer selection
    parser.add_argument(
        "layers",
        nargs="+",
        choices=list(TEST_LAYERS.keys()) + ["all"],
        help="Test layer(s) to run or 'all' for complete suite",
    )

    # Coverage options
    parser.add_argument(
        "--coverage", action="store_true", help="Generate coverage reports"
    )

    parser.add_argument(
        "--coverage-threshold",
        type=int,
        metavar="PERCENT",
        help="Minimum coverage percentage (e.g., 85)",
    )

    # Execution options
    parser.add_argument(
        "--fail-fast", action="store_true", help="Stop on first test failure"
    )

    parser.add_argument(
        "--parallel",
        action="store_true",
        help="Run tests in parallel (requires pytest-xdist)",
    )

    # Output options
    parser.add_argument("--verbose", action="store_true", help="Extra verbose output")

    parser.add_argument("--quiet", action="store_true", help="Minimal output")

    parser.add_argument(
        "--detailed-traceback",
        action="store_true",
        help="Show detailed tracebacks on failures",
    )

    # Report options
    parser.add_argument(
        "--summary-file",
        type=Path,
        metavar="FILE",
        help="Save summary report to JSON file",
    )

    return parser


def main() -> int:
    """Main entry point for the test runner."""
    try:
        # Parse arguments
        parser = create_argument_parser()
        args = parser.parse_args()

        # Setup server root
        server_path = Path(__file__).parent.parent

        # Create test runner
        runner = UltimateElectricalDesignerTestRunner(server_path)

        # Prepare options
        options = {
            "coverage": args.coverage,
            "coverage_threshold": args.coverage_threshold,
            "fail_fast": args.fail_fast,
            "parallel": args.parallel,
            "verbose": args.verbose,
            "quiet": args.quiet,
            "detailed_traceback": args.detailed_traceback,
        }

        # Execute tests
        if "all" in args.layers:
            # Run full test suite
            exit_code, result = runner.run_full_test_suite(options)
            results = [result]
        else:
            # Run specific layers
            exit_code, results = runner.run_multiple_layers(args.layers, options)

        # Generate and display summary
        summary = runner.generate_summary_report(results, args.summary_file)
        runner.print_summary(summary)

        # Print final status
        if exit_code == 0:
            print("\n🎉 All tests completed successfully!")
        else:
            print(f"\n💥 Tests failed with exit code: {exit_code}")
            print("📋 Check the logs and coverage reports for details.")

        return exit_code

    except KeyboardInterrupt:
        print("\n🛑 Test execution interrupted by user")
        return 130
    except TestRunnerError as e:
        print(f"\n❌ Test runner error: {e}")
        return 1
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
