"""Integration tests for DynamicConnectionManager with database connectivity."""

import pytest
from unittest.mock import patch, AsyncMock, MagicMock
from sqlalchemy.ext.asyncio import AsyncEngine

from src.core.database.connection_manager import (
    DynamicConnectionManager,
    initialize_connection_manager,
    shutdown_connection_manager,
)
from src.core.errors.exceptions import DatabaseError


class TestDynamicConnectionManagerIntegration:
    """Integration tests for DynamicConnectionManager."""

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_connection_manager_lifecycle(self) -> None:
        """Test complete connection manager lifecycle with mocked database."""
        # This test verifies the complete lifecycle without requiring a real database
        manager = DynamicConnectionManager()

        # Mock SQLAlchemy components
        mock_engine = AsyncMock(spec=AsyncEngine)
        mock_session_factory = MagicMock()

        with patch("src.core.database.connection_manager.settings") as mock_settings:
            mock_settings.DATABASE_URL = (
                "postgresql+asyncpg://user:password@localhost:5433/ultimate_electrical_designer_test"
            )
            mock_settings.DB_ECHO = False

            with patch("src.core.database.connection_manager.create_async_engine") as mock_create_engine:
                mock_create_engine.return_value = mock_engine

                with patch("src.core.database.connection_manager.async_sessionmaker") as mock_sessionmaker:
                    mock_sessionmaker.return_value = mock_session_factory

                    # Test initialization
                    await manager.initialize()

                    # Verify engine was created with correct parameters
                    mock_create_engine.assert_called_once_with(
                        "postgresql+asyncpg://user:password@localhost:5433/ultimate_electrical_designer_test",
                        pool_pre_ping=True,
                        echo=False,
                    )

                    # Verify session factory was created
                    assert manager._central_engine is mock_engine
                    assert manager._central_session_factory is mock_session_factory

                    # Test shutdown
                    await manager.shutdown()

                    # Verify engine disposal
                    mock_engine.dispose.assert_called_once()

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_global_connection_manager_lifecycle(self) -> None:
        """Test global connection manager initialization and shutdown."""
        # Mock the connection manager instance
        with patch("src.core.database.connection_manager._connection_manager") as mock_manager:
            mock_manager.initialize = AsyncMock()
            mock_manager.shutdown = AsyncMock()

            # Test initialization
            await initialize_connection_manager()
            mock_manager.initialize.assert_called_once()

            # Test shutdown
            await shutdown_connection_manager()
            mock_manager.shutdown.assert_called_once()

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_multiple_local_database_engines(self) -> None:
        """Test managing multiple local database connections."""
        manager = DynamicConnectionManager()

        # Mock engine creation for different URLs
        mock_engine1 = AsyncMock(spec=AsyncEngine)
        mock_engine2 = AsyncMock(spec=AsyncEngine)
        mock_session_factory1 = MagicMock()
        mock_session_factory2 = MagicMock()

        with patch("src.core.database.connection_manager.create_async_engine") as mock_create_engine:
            with patch("src.core.database.connection_manager.async_sessionmaker") as mock_sessionmaker:
                # Setup different returns for different calls
                mock_create_engine.side_effect = [mock_engine1, mock_engine2]
                mock_sessionmaker.side_effect = [
                    mock_session_factory1,
                    mock_session_factory2,
                ]

                # Test creating session factories for different local databases
                url1 = "postgresql+asyncpg://local1:pwd@localhost/db1"
                url2 = "postgresql+asyncpg://local2:pwd@localhost/db2"

                factory1 = manager._get_local_session_factory(url1)
                factory2 = manager._get_local_session_factory(url2)

                # Verify engines were created for both URLs
                assert mock_create_engine.call_count == 2
                mock_create_engine.assert_any_call(url1, pool_pre_ping=True)
                mock_create_engine.assert_any_call(url2, pool_pre_ping=True)

                # Verify session factories were created
                assert factory1 is mock_session_factory1
                assert factory2 is mock_session_factory2

                # Verify caching - calling again should return cached factories
                cached_factory1 = manager._get_local_session_factory(url1)
                cached_factory2 = manager._get_local_session_factory(url2)

                assert cached_factory1 is mock_session_factory1
                assert cached_factory2 is mock_session_factory2

                # No additional engine creation should occur
                assert mock_create_engine.call_count == 2

                # Test shutdown clears all engines
                await manager.shutdown()

                mock_engine1.dispose.assert_called_once()
                mock_engine2.dispose.assert_called_once()

                # Verify cleanup
                assert manager._local_engines == {}
                assert manager._local_session_factories == {}

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_engine_creation_error_handling(self) -> None:
        """Test error handling during engine creation."""
        manager = DynamicConnectionManager()

        with patch("src.core.database.connection_manager.settings") as mock_settings:
            mock_settings.DATABASE_URL = "invalid://connection/string"

            with patch("src.core.database.connection_manager.create_async_engine") as mock_create_engine:
                # Simulate SQLAlchemy engine creation failure
                mock_create_engine.side_effect = Exception("Database connection failed")

                # Test that the exception propagates properly
                with pytest.raises(Exception) as exc_info:
                    await manager.initialize()

                assert "Database connection failed" in str(exc_info.value)

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_session_management_error_recovery(self) -> None:
        """Test session management handles errors gracefully."""
        manager = DynamicConnectionManager()

        # Test that get_session properly raises DatabaseError when no valid factory is available
        mock_project_repo = MagicMock()

        # Manager is not initialized, so no central session factory exists
        assert manager._central_session_factory is None

        with pytest.raises(DatabaseError) as exc_info:
            async for _ in manager.get_session(mock_project_repo, None):
                pass

        assert "Could not determine a valid session factory" in str(exc_info.value)

    @pytest.mark.integration
    @pytest.mark.asyncio
    async def test_concurrent_local_database_access(self) -> None:
        """Test concurrent access to local databases doesn't create duplicate engines."""
        manager = DynamicConnectionManager()

        mock_engine = AsyncMock(spec=AsyncEngine)
        mock_session_factory = MagicMock()

        with patch("src.core.database.connection_manager.create_async_engine") as mock_create_engine:
            mock_create_engine.return_value = mock_engine

            with patch("src.core.database.connection_manager.async_sessionmaker") as mock_sessionmaker:
                mock_sessionmaker.return_value = mock_session_factory

                url = "postgresql+asyncpg://concurrent:test@localhost/concurrent_db"

                # Simulate concurrent calls to get_local_session_factory
                # In reality, this would need proper asyncio.gather, but for this test
                # we simulate the caching behavior
                factory1 = manager._get_local_session_factory(url)
                factory2 = manager._get_local_session_factory(url)
                factory3 = manager._get_local_session_factory(url)

                # Should return the same factory instance
                assert factory1 is factory2 is factory3 is mock_session_factory

                # Engine should only be created once
                mock_create_engine.assert_called_once_with(url, pool_pre_ping=True)
                mock_sessionmaker.assert_called_once()

                # Verify only one engine is stored
                assert len(manager._local_engines) == 1
                assert manager._local_engines[url] is mock_engine
