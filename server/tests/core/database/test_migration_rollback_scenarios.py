"""Test suite for Alembic migration rollback scenarios.

This module provides comprehensive testing for Alembic migration rollback scenarios,
ensuring that migrations can be safely reverted without data loss and that rollback
operations maintain database integrity across different scenarios.

Test categories:
1. Single migration rollback testing
2. Multi-step rollback scenario validation
3. Data preservation during rollback operations
4. Rollback performance and safety validation
5. Constraint and index rollback behavior
"""

import pytest
import tempfile
import shutil
import time
import uuid
import json
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy import (
    create_engine,
    MetaData,
    Table,
    Column,
    Integer,
    String,
    text,
    inspect,
)
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from alembic import command
from alembic.config import Config
from alembic.script import ScriptDirectory
from alembic.runtime.migration import MigrationContext

from src.core.models.base import Base
from src.core.models.general.user import User
from src.core.models.general.project import Project
from src.core.enums import ProjectStatus

# Mark all tests in this module as database and integration tests
pytestmark = [pytest.mark.database, pytest.mark.integration, pytest.mark.slow]


class MigrationRollbackTestSuite:
    """Comprehensive migration rollback testing suite."""

    def __init__(self):
        self.rollback_results: Dict[str, Dict[str, Any]] = {}
        self.data_snapshots: Dict[str, Dict[str, Any]] = {}
        self.schema_states: Dict[str, Dict[str, Any]] = {}

    def create_test_migration_file(self, versions_dir: Path, revision_id: str, description: str) -> Path:
        """Create a test migration file for rollback testing."""
        migration_content = f'''"""
{description}

Revision ID: {revision_id}
Revises: 
Create Date: {time.strftime("%Y-%m-%d %H:%M:%S")}
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '{revision_id}'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Test migration - add a temporary table
    op.create_table('test_rollback_table',
        sa.Column('id', sa.Integer, primary_key=True),
        sa.Column('test_data', sa.String(255), nullable=False),
        sa.Column('created_at', sa.DateTime, server_default=sa.text('CURRENT_TIMESTAMP'))
    )

def downgrade():
    # Rollback - drop the temporary table
    op.drop_table('test_rollback_table')
'''

        migration_file = versions_dir / f"{revision_id}_{description.lower().replace(' ', '_')}.py"
        migration_file.write_text(migration_content)
        return migration_file

    def create_data_modification_migration(self, versions_dir: Path, revision_id: str) -> Path:
        """Create a migration that modifies existing data for rollback testing."""
        migration_content = f'''"""
Data modification migration for rollback testing

Revision ID: {revision_id}
Revises: 
Create Date: {time.strftime("%Y-%m-%d %H:%M:%S")}
"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = '{revision_id}'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Add a new column to User table
    op.add_column('User', sa.Column('migration_test_field', sa.String(100), nullable=True))
    
    # Update existing records with test data
    connection = op.get_bind()
    connection.execute(
        sa.text("UPDATE \"User\" SET migration_test_field = 'migrated_' || name WHERE migration_test_field IS NULL")
    )

def downgrade():
    # Remove the added column
    op.drop_column('User', 'migration_test_field')
'''

        migration_file = versions_dir / f"{revision_id}_data_modification_test.py"
        migration_file.write_text(migration_content)
        return migration_file

    def capture_data_snapshot(self, session: Session, label: str) -> Dict[str, Any]:
        """Capture detailed data snapshot for rollback validation."""
        snapshot = {"label": label, "timestamp": time.time(), "tables": {}}

        # Capture User data
        users = session.query(User).all()
        snapshot["tables"]["User"] = [
            {
                "id": user.id,
                "name": user.name,
                "email": user.email,
                "is_active": user.is_active,
                "is_superuser": user.is_superuser,
                "created_at": user.created_at.isoformat() if user.created_at else None,
                "updated_at": user.updated_at.isoformat() if user.updated_at else None,
            }
            for user in users
        ]

        # Capture Project data
        projects = session.query(Project).all()
        snapshot["tables"]["Project"] = [
            {
                "id": project.id,
                "name": project.name,
                "project_number": project.project_number,
                "status": project.status,
                "client": project.client,
                "location": project.location,
                "created_at": project.created_at.isoformat() if project.created_at else None,
                "updated_at": project.updated_at.isoformat() if project.updated_at else None,
            }
            for project in projects
        ]

        self.data_snapshots[label] = snapshot
        return snapshot

    def validate_data_snapshot_match(
        self,
        before_snapshot: Dict[str, Any],
        after_snapshot: Dict[str, Any],
        ignore_fields: List[str] = None,
    ) -> Dict[str, Any]:
        """Validate that data snapshots match after rollback."""
        if ignore_fields is None:
            ignore_fields = ["updated_at"]  # These may change during migration

        validation_results = {
            "tables_compared": 0,
            "records_compared": 0,
            "mismatches": [],
            "summary": {},
        }

        for table_name in before_snapshot["tables"]:
            if table_name not in after_snapshot["tables"]:
                validation_results["mismatches"].append(f"Table {table_name} missing in after snapshot")
                continue

            before_records = before_snapshot["tables"][table_name]
            after_records = after_snapshot["tables"][table_name]

            # Compare record counts
            before_count = len(before_records)
            after_count = len(after_records)

            validation_results["summary"][table_name] = {
                "before_count": before_count,
                "after_count": after_count,
                "count_match": before_count == after_count,
            }

            if before_count != after_count:
                validation_results["mismatches"].append(
                    f"Table {table_name}: record count mismatch {before_count} -> {after_count}"
                )

            # Compare individual records (by ID)
            before_by_id = {record["id"]: record for record in before_records}
            after_by_id = {record["id"]: record for record in after_records}

            for record_id, before_record in before_by_id.items():
                if record_id not in after_by_id:
                    validation_results["mismatches"].append(
                        f"Table {table_name}: record ID {record_id} missing after rollback"
                    )
                    continue

                after_record = after_by_id[record_id]

                # Compare fields (excluding ignored fields)
                for field, before_value in before_record.items():
                    if field in ignore_fields:
                        continue

                    after_value = after_record.get(field)
                    if before_value != after_value:
                        validation_results["mismatches"].append(
                            f"Table {table_name}, ID {record_id}, field {field}: {before_value} -> {after_value}"
                        )

                validation_results["records_compared"] += 1

            validation_results["tables_compared"] += 1

        return validation_results

    def get_current_migration_head(self, alembic_cfg: Config) -> Optional[str]:
        """Get the current migration head revision."""
        script = ScriptDirectory.from_config(alembic_cfg)
        try:
            return script.get_current_head()
        except:
            return None


class TestMigrationRollbackScenarios:
    """Migration rollback scenario testing with comprehensive validation."""

    @pytest.fixture(autouse=True)
    def setup_rollback_suite(self):
        """Set up rollback test suite for each test."""
        self.rollback_suite = MigrationRollbackTestSuite()
        self.temp_dirs: List[Path] = []
        yield
        # Cleanup temporary directories
        for temp_dir in self.temp_dirs:
            if temp_dir.exists():
                shutil.rmtree(temp_dir, ignore_errors=True)

    def create_test_environment_with_migration(self, temp_dir: Path) -> Tuple[str, Config, Path]:
        """Create test environment with a custom test migration."""
        # Set up basic Alembic environment
        alembic_dir = temp_dir / "alembic"
        alembic_dir.mkdir()
        versions_dir = alembic_dir / "versions"
        versions_dir.mkdir()

        # Copy alembic.ini
        source_ini = Path(__file__).parent.parent.parent.parent / "src" / "alembic.ini"
        target_ini = temp_dir / "alembic.ini"
        shutil.copy2(source_ini, target_ini)

        # Copy env.py
        source_env = Path(__file__).parent.parent.parent.parent / "src" / "alembic" / "env.py"
        target_env = alembic_dir / "env.py"
        shutil.copy2(source_env, target_env)

        # Create test database
        test_db_url = str(engine.url)

        # Configure Alembic
        alembic_cfg = Config(str(target_ini))
        alembic_cfg.set_main_option("script_location", str(alembic_dir))
        alembic_cfg.set_main_option("sqlalchemy.url", test_db_url)

        return test_db_url, alembic_cfg, versions_dir

    def test_single_migration_rollback_with_data_preservation(self):
        """Test single migration rollback ensuring data is preserved.

        This test:
        1. Creates a database with test data
        2. Applies a test migration
        3. Rolls back the migration
        4. Validates that original data is preserved
        """
        print(f"\n↩️ Testing single migration rollback with data preservation")

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            self.temp_dirs.append(temp_path)

            test_db_url, alembic_cfg, versions_dir = self.create_test_environment_with_migration(temp_path)

            # Create database engine
            engine = create_engine(
                test_db_url,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
            )

            # Create initial schema
            Base.metadata.create_all(engine)

            Session = sessionmaker(bind=engine)
            session = Session()

            try:
                # Create test data
                unique_suffix = str(uuid.uuid4())[:8]

                test_users = []
                for i in range(20):
                    user = User(
                        name=f"Rollback Test User {i} {unique_suffix}",
                        email=f"rollback.{i}.{unique_suffix}@test.com",
                        password_hash=f"hash_{i}",
                        is_active=i % 5 != 0,
                        is_superuser=i % 10 == 0,
                    )
                    test_users.append(user)
                    session.add(user)

                test_projects = []
                for i in range(10):
                    project = Project(
                        name=f"Rollback Test Project {i} {unique_suffix}",
                        project_number=f"RTP-{unique_suffix}-{i:03d}",
                        description=f"Test project for rollback validation {i}",
                        status=ProjectStatus.DRAFT.value if i % 2 == 0 else ProjectStatus.ACTIVE.value,
                        default_safety_margin_percent=10.0 + i,
                    )
                    test_projects.append(project)
                    session.add(project)

                session.commit()

                # Capture pre-migration data snapshot
                pre_migration_snapshot = self.rollback_suite.capture_data_snapshot(session, "pre_migration")
                print(f"   Pre-migration data: {len(test_users)} users, {len(test_projects)} projects")

                # Initialize Alembic and stamp initial state
                command.stamp(alembic_cfg, "head")

                # Create and apply a test migration
                test_revision_id = "test_001"
                migration_file = self.rollback_suite.create_test_migration_file(
                    versions_dir, test_revision_id, "Test Migration for Rollback"
                )

                print(f"   Created test migration: {migration_file.name}")

                # Apply the migration
                print(f"   Applying test migration...")
                upgrade_start = time.time()
                command.upgrade(alembic_cfg, test_revision_id)
                upgrade_time = time.time() - upgrade_start

                # Verify migration was applied
                inspector = inspect(engine)
                assert inspector.has_table("test_rollback_table"), "Test migration table not created"

                # Capture post-migration data snapshot
                post_migration_snapshot = self.rollback_suite.capture_data_snapshot(session, "post_migration")

                print(f"   Migration applied in {upgrade_time:.2f}s")
                print(f"   Test table created successfully")

                # Perform rollback
                print(f"   Rolling back migration...")
                rollback_start = time.time()
                command.downgrade(alembic_cfg, "-1")  # Go back one revision
                rollback_time = time.time() - rollback_start

                # Verify rollback was successful
                inspector = inspect(engine)
                assert not inspector.has_table("test_rollback_table"), (
                    "Test migration table still exists after rollback"
                )

                # Capture post-rollback data snapshot
                post_rollback_snapshot = self.rollback_suite.capture_data_snapshot(session, "post_rollback")

                print(f"   Rollback completed in {rollback_time:.2f}s")
                print(f"   Test table removed successfully")

                # Validate data preservation
                validation_results = self.rollback_suite.validate_data_snapshot_match(
                    pre_migration_snapshot, post_rollback_snapshot
                )

                print(
                    f"   Data validation: {validation_results['tables_compared']} tables, {validation_results['records_compared']} records compared"
                )

                # Assertions
                assert len(validation_results["mismatches"]) == 0, (
                    f"Data mismatches after rollback: {validation_results['mismatches']}"
                )
                assert rollback_time < 10.0, f"Rollback took too long: {rollback_time:.2f}s"
                assert upgrade_time < 10.0, f"Migration took too long: {upgrade_time:.2f}s"

                # Validate record counts match
                for table_name, summary in validation_results["summary"].items():
                    assert summary["count_match"], f"Record count mismatch for {table_name}"

                print(f"   ✅ Rollback validation successful - all data preserved")

            finally:
                session.close()
                engine.dispose()

    def test_data_modification_migration_rollback(self):
        """Test rollback of migrations that modify existing data.

        This test validates that migrations which modify data can be properly
        rolled back without losing the original data state.
        """
        print(f"\n🔄 Testing data modification migration rollback")

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            self.temp_dirs.append(temp_path)

            test_db_url, alembic_cfg, versions_dir = self.create_test_environment_with_migration(temp_path)

            engine = create_engine(
                test_db_url,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
            )

            Base.metadata.create_all(engine)

            Session = sessionmaker(bind=engine)
            session = Session()

            try:
                # Create test data
                unique_suffix = str(uuid.uuid4())[:8]

                original_users = []
                for i in range(15):
                    user = User(
                        name=f"Data Mod Test User {i} {unique_suffix}",
                        email=f"datamod.{i}.{unique_suffix}@test.com",
                        password_hash=f"hash_{i}",
                        is_active=True,
                        is_superuser=False,
                    )
                    original_users.append(user)
                    session.add(user)

                session.commit()

                # Capture original data state
                original_snapshot = self.rollback_suite.capture_data_snapshot(session, "original_data")

                # Verify users don't have the test field yet
                inspector = inspect(engine)
                user_columns = [col["name"] for col in inspector.get_columns("User")]
                assert "migration_test_field" not in user_columns, "Test field already exists"

                print(f"   Original data: {len(original_users)} users without test field")

                # Initialize Alembic
                command.stamp(alembic_cfg, "head")

                # Create and apply data modification migration
                test_revision_id = "data_mod_001"
                migration_file = self.rollback_suite.create_data_modification_migration(versions_dir, test_revision_id)

                print(f"   Applying data modification migration...")
                command.upgrade(alembic_cfg, test_revision_id)

                # Verify migration effects
                inspector = inspect(engine)
                user_columns_after = [col["name"] for col in inspector.get_columns("User")]
                assert "migration_test_field" in user_columns_after, "Test field not added by migration"

                # Check that data was modified
                session.expire_all()  # Clear session cache
                modified_users = session.query(User).all()

                users_with_test_data = sum(
                    1
                    for user in modified_users
                    if hasattr(user, "migration_test_field") and getattr(user, "migration_test_field", None) is not None
                )

                print(f"   Migration applied: test field added to {users_with_test_data} users")

                # Capture post-migration data state
                post_migration_snapshot = self.rollback_suite.capture_data_snapshot(session, "post_migration_data")

                # Perform rollback
                print(f"   Rolling back data modification migration...")
                rollback_start = time.time()
                command.downgrade(alembic_cfg, "-1")
                rollback_time = time.time() - rollback_start

                # Verify rollback
                session.expire_all()
                inspector = inspect(engine)
                user_columns_after_rollback = [col["name"] for col in inspector.get_columns("User")]
                assert "migration_test_field" not in user_columns_after_rollback, (
                    "Test field still exists after rollback"
                )

                # Capture post-rollback data state
                post_rollback_snapshot = self.rollback_suite.capture_data_snapshot(session, "post_rollback_data")

                print(f"   Rollback completed in {rollback_time:.2f}s")
                print(f"   Test field removed successfully")

                # Validate data integrity after rollback
                validation_results = self.rollback_suite.validate_data_snapshot_match(
                    original_snapshot, post_rollback_snapshot
                )

                print(f"   Data validation: {validation_results['records_compared']} records compared")

                # Assertions
                assert len(validation_results["mismatches"]) == 0, (
                    f"Data mismatches after rollback: {validation_results['mismatches']}"
                )
                assert rollback_time < 15.0, f"Data modification rollback took too long: {rollback_time:.2f}s"

                # Verify all users are still present and unchanged
                final_user_count = session.query(User).count()
                assert final_user_count == len(original_users), (
                    f"User count changed: {len(original_users)} -> {final_user_count}"
                )

                print(f"   ✅ Data modification rollback successful - original data state restored")

            finally:
                session.close()
                engine.dispose()

    def test_rollback_performance_and_safety_validation(self):
        """Test rollback performance and safety under various conditions.

        This test validates:
        1. Rollback performance with larger datasets
        2. Rollback safety with concurrent operations
        3. Transaction integrity during rollback
        4. Error handling in rollback scenarios
        """
        print(f"\n⚡ Testing rollback performance and safety validation")

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            self.temp_dirs.append(temp_path)

            test_db_url, alembic_cfg, versions_dir = self.create_test_environment_with_migration(temp_path)

            engine = create_engine(
                test_db_url,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
            )

            Base.metadata.create_all(engine)

            Session = sessionmaker(bind=engine)
            session = Session()

            try:
                # Create larger dataset for performance testing
                unique_suffix = str(uuid.uuid4())[:8]
                dataset_size = 500  # 500 users, 250 projects

                print(f"   Creating large dataset ({dataset_size} users, {dataset_size // 2} projects)...")
                dataset_start = time.time()

                # Bulk create users
                users_to_add = []
                for i in range(dataset_size):
                    user = User(
                        name=f"Perf Rollback User {i:03d} {unique_suffix}",
                        email=f"perf.rollback.{i:03d}.{unique_suffix}@test.com",
                        password_hash=f"hash_{i:03d}",
                        is_active=i % 8 != 0,
                        is_superuser=i % 50 == 0,
                    )
                    users_to_add.append(user)

                session.add_all(users_to_add)

                # Bulk create projects
                projects_to_add = []
                for i in range(dataset_size // 2):
                    project = Project(
                        name=f"Perf Rollback Project {i:03d} {unique_suffix}",
                        project_number=f"PRP-{unique_suffix}-{i:03d}",
                        description=f"Performance rollback test project {i}",
                        status=ProjectStatus.DRAFT.value if i % 3 == 0 else ProjectStatus.ACTIVE.value,
                        default_safety_margin_percent=10.0 + (i % 15),
                    )
                    projects_to_add.append(project)

                session.add_all(projects_to_add)
                session.commit()

                dataset_creation_time = time.time() - dataset_start
                print(f"   Dataset created in {dataset_creation_time:.2f}s")

                # Capture baseline data
                baseline_snapshot = self.rollback_suite.capture_data_snapshot(session, "performance_baseline")

                user_count = session.query(User).count()
                project_count = session.query(Project).count()
                print(f"   Baseline: {user_count} users, {project_count} projects")

                # Initialize Alembic and create performance test migration
                command.stamp(alembic_cfg, "head")

                perf_revision_id = "perf_001"
                migration_file = self.rollback_suite.create_test_migration_file(
                    versions_dir, perf_revision_id, "Performance Test Migration"
                )

                # Apply migration
                print(f"   Applying performance test migration...")
                migration_start = time.time()
                command.upgrade(alembic_cfg, perf_revision_id)
                migration_time = time.time() - migration_start

                # Verify migration
                inspector = inspect(engine)
                assert inspector.has_table("test_rollback_table"), "Performance test table not created"

                print(f"   Migration applied in {migration_time:.2f}s")

                # Test rollback performance
                print(f"   Testing rollback performance...")
                rollback_start = time.time()
                command.downgrade(alembic_cfg, "-1")
                rollback_time = time.time() - rollback_start

                # Verify rollback
                inspector = inspect(engine)
                assert not inspector.has_table("test_rollback_table"), "Performance test table still exists"

                print(f"   Rollback completed in {rollback_time:.2f}s")

                # Capture post-rollback data
                post_rollback_snapshot = self.rollback_suite.capture_data_snapshot(session, "performance_post_rollback")

                # Validate data integrity
                validation_results = self.rollback_suite.validate_data_snapshot_match(
                    baseline_snapshot, post_rollback_snapshot
                )

                # Calculate performance metrics
                total_records = user_count + project_count
                rollback_throughput = total_records / rollback_time if rollback_time > 0 else 0

                print(f"   Performance metrics:")
                print(f"     Migration time: {migration_time:.2f}s")
                print(f"     Rollback time: {rollback_time:.2f}s")
                print(f"     Rollback throughput: {rollback_throughput:.1f} records/sec")
                print(f"     Data validation: {validation_results['records_compared']} records verified")

                # Performance assertions
                max_acceptable_rollback_time = 30.0  # 30 seconds for 750 records
                assert rollback_time < max_acceptable_rollback_time, f"Rollback too slow: {rollback_time:.2f}s"

                min_acceptable_throughput = 25.0  # records per second
                assert rollback_throughput > min_acceptable_throughput, (
                    f"Rollback throughput too low: {rollback_throughput:.1f} records/sec"
                )

                # Data integrity assertions
                assert len(validation_results["mismatches"]) == 0, (
                    f"Data integrity issues: {validation_results['mismatches']}"
                )

                final_user_count = session.query(User).count()
                final_project_count = session.query(Project).count()

                assert final_user_count == user_count, f"User count changed: {user_count} -> {final_user_count}"
                assert final_project_count == project_count, (
                    f"Project count changed: {project_count} -> {final_project_count}"
                )

                print(f"   ✅ Performance and safety validation successful")

            finally:
                session.close()
                engine.dispose()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-s"])
