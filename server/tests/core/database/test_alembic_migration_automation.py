"""Test suite for Alembic migration automation with data integrity validation.

This module provides comprehensive automated testing for Alembic database migrations,
ensuring data integrity, schema consistency, and proper migration behavior across
different database engines and scenarios.

Test categories:
1. Automated migration execution testing
2. Data integrity validation during migrations
3. Schema consistency verification
4. Migration dependency and order validation
5. Migration performance under data load
"""

import pytest
import tempfile
import shutil
import subprocess
import os
import json
import time
import uuid
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy import (
    create_engine,
    MetaData,
    Table,
    Column,
    Integer,
    String,
    text,
    inspect,
)
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from alembic import command
from alembic.config import Config
from alembic.script import ScriptDirectory
from alembic.runtime.migration import MigrationContext
from alembic.operations import Operations

from src.core.models.base import Base
from src.core.models.general.user import User
from src.core.models.general.project import Project
from src.core.schemas.general.user_schemas import UserCreateSchema
from src.core.schemas.general.project_schemas import ProjectCreateSchema
from src.core.enums import ProjectStatus

# Mark all tests in this module as database and integration tests
pytestmark = [pytest.mark.database, pytest.mark.integration, pytest.mark.slow]


class MigrationTestSuite:
    """Comprehensive migration testing suite with data integrity validation."""

    def __init__(self):
        self.test_data_samples: Dict[str, List[Dict[str, Any]]] = {}
        self.migration_results: Dict[str, Dict[str, Any]] = {}
        self.schema_snapshots: Dict[str, Dict[str, Any]] = {}

    def create_test_alembic_environment(self, temp_dir: Path) -> Tuple[str, Config]:
        """Create isolated Alembic environment for testing."""
        alembic_dir = temp_dir / "alembic"
        alembic_dir.mkdir()

        # Copy current alembic.ini
        source_ini = Path(__file__).parent.parent.parent.parent / "src" / "alembic.ini"
        target_ini = temp_dir / "alembic.ini"
        shutil.copy2(source_ini, target_ini)

        # Create versions directory
        versions_dir = alembic_dir / "versions"
        versions_dir.mkdir()

        # Copy all existing migration files
        source_versions_dir = Path(__file__).parent.parent.parent.parent / "src" / "alembic" / "versions"
        if source_versions_dir.exists():
            for migration_file in source_versions_dir.glob("*.py"):
                shutil.copy2(migration_file, versions_dir / migration_file.name)

        # Copy env.py
        source_env = Path(__file__).parent.parent.parent.parent / "src" / "alembic" / "env.py"
        target_env = alembic_dir / "env.py"
        shutil.copy2(source_env, target_env)

        # Create test database URL (SQLite in-memory for testing)
        test_db_name = f"test_migration_{uuid.uuid4().hex[:8]}.db"
        test_db_url = f"sqlite:///{temp_dir}/{test_db_name}"

        # Configure Alembic config
        alembic_cfg = Config(str(target_ini))
        alembic_cfg.set_main_option("script_location", str(alembic_dir))
        alembic_cfg.set_main_option("sqlalchemy.url", test_db_url)

        return test_db_url, alembic_cfg

    def generate_test_data_for_tables(
        self, session: Session
    ) -> Dict[str, List[Dict[str, Any]]]:
        """Generate realistic test data for migration validation."""
        test_data = {}
        unique_suffix = str(uuid.uuid4())[:8]

        # Generate user test data
        user_data = []
        for i in range(50):
            user_data.append(
                {
                    "name": f"Migration Test User {i} {unique_suffix}",
                    "email": f"migration.user.{i}.{unique_suffix}@test.com",
                    "password_hash": f"hash_{i}_{unique_suffix}",
                    "is_active": i % 10 != 0,  # 90% active
                    "is_superuser": i % 25 == 0,  # 4% superuser
                }
            )
        test_data["User"] = user_data

        # Generate project test data
        project_data = []
        for i in range(30):
            project_data.append(
                {
                    "name": f"Migration Test Project {i} {unique_suffix}",
                    "project_number": f"MTP-{unique_suffix}-{i:03d}",
                    "description": f"Test project for migration validation - {i}",
                    "status": ProjectStatus.DRAFT.value
                    if i % 3 == 0
                    else ProjectStatus.ACTIVE.value,
                    "client": f"Test Client {i // 5}",
                    "location": f"Test Location {i % 10}",
                    "default_safety_margin_percent": 10.0 + (i % 5),
                }
            )
        test_data["Project"] = project_data

        self.test_data_samples = test_data
        return test_data

    def populate_test_data(
        self, session: Session, test_data: Dict[str, List[Dict[str, Any]]]
    ):
        """Populate database with test data for migration testing."""
        for table_name, data_list in test_data.items():
            if table_name == "User":
                for user_data in data_list:
                    user = User(**user_data)
                    session.add(user)
            elif table_name == "Project":
                for project_data in data_list:
                    project = Project(**project_data)
                    session.add(project)

        session.commit()

    def capture_schema_snapshot(self, engine, label: str) -> Dict[str, Any]:
        """Capture detailed schema snapshot for comparison."""
        inspector = inspect(engine)

        snapshot = {
            "label": label,
            "timestamp": time.time(),
            "tables": {},
            "indexes": {},
            "foreign_keys": {},
            "constraints": {},
        }

        for table_name in inspector.get_table_names():
            # Table structure
            columns = inspector.get_columns(table_name)
            snapshot["tables"][table_name] = {
                "columns": [
                    {
                        "name": col["name"],
                        "type": str(col["type"]),
                        "nullable": col["nullable"],
                        "default": str(col["default"]) if col["default"] else None,
                    }
                    for col in columns
                ]
            }

            # Indexes
            indexes = inspector.get_indexes(table_name)
            snapshot["indexes"][table_name] = [
                {
                    "name": idx["name"],
                    "column_names": idx["column_names"],
                    "unique": idx["unique"],
                }
                for idx in indexes
            ]

            # Foreign keys
            foreign_keys = inspector.get_foreign_keys(table_name)
            snapshot["foreign_keys"][table_name] = [
                {
                    "name": fk["name"],
                    "constrained_columns": fk["constrained_columns"],
                    "referred_table": fk["referred_table"],
                    "referred_columns": fk["referred_columns"],
                }
                for fk in foreign_keys
            ]

            # Unique constraints
            unique_constraints = inspector.get_unique_constraints(table_name)
            snapshot["constraints"][table_name] = [
                {"name": uc["name"], "column_names": uc["column_names"]}
                for uc in unique_constraints
            ]

        self.schema_snapshots[label] = snapshot
        return snapshot

    def validate_data_integrity_after_migration(
        self, session: Session, original_data: Dict[str, List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """Validate that data integrity is maintained after migration."""
        validation_results = {
            "total_records_validated": 0,
            "validation_errors": [],
            "data_consistency_checks": {},
        }

        # Validate User data
        if "User" in original_data:
            user_count = session.query(User).count()
            expected_count = len(original_data["User"])

            validation_results["data_consistency_checks"]["User"] = {
                "expected_count": expected_count,
                "actual_count": user_count,
                "count_match": user_count == expected_count,
            }

            # Sample detailed validation
            for i, original_user in enumerate(
                original_data["User"][:10]
            ):  # Check first 10
                user = (
                    session.query(User)
                    .filter(User.email == original_user["email"])
                    .first()
                )
                if user:
                    if user.name != original_user["name"]:
                        validation_results["validation_errors"].append(
                            f"User {original_user['email']}: name mismatch"
                        )
                    if user.is_active != original_user["is_active"]:
                        validation_results["validation_errors"].append(
                            f"User {original_user['email']}: is_active mismatch"
                        )
                else:
                    validation_results["validation_errors"].append(
                        f"User {original_user['email']}: not found after migration"
                    )

            validation_results["total_records_validated"] += min(
                10, len(original_data["User"])
            )

        # Validate Project data
        if "Project" in original_data:
            project_count = session.query(Project).count()
            expected_count = len(original_data["Project"])

            validation_results["data_consistency_checks"]["Project"] = {
                "expected_count": expected_count,
                "actual_count": project_count,
                "count_match": project_count == expected_count,
            }

            # Sample detailed validation
            for i, original_project in enumerate(
                original_data["Project"][:10]
            ):  # Check first 10
                project = (
                    session.query(Project)
                    .filter(
                        Project.project_number == original_project["project_number"]
                    )
                    .first()
                )
                if project:
                    if project.name != original_project["name"]:
                        validation_results["validation_errors"].append(
                            f"Project {original_project['project_number']}: name mismatch"
                        )
                    if project.status != original_project["status"]:
                        validation_results["validation_errors"].append(
                            f"Project {original_project['project_number']}: status mismatch"
                        )
                else:
                    validation_results["validation_errors"].append(
                        f"Project {original_project['project_number']}: not found after migration"
                    )

            validation_results["total_records_validated"] += min(
                10, len(original_data["Project"])
            )

        return validation_results


class TestAlembicMigrationAutomation:
    """Automated Alembic migration testing with comprehensive validation."""

    @pytest.fixture(autouse=True)
    def setup_migration_suite(self):
        """Set up migration test suite for each test."""
        self.migration_suite = MigrationTestSuite()
        self.temp_dirs: List[Path] = []
        yield
        # Cleanup temporary directories
        for temp_dir in self.temp_dirs:
            if temp_dir.exists():
                shutil.rmtree(temp_dir, ignore_errors=True)

    def test_automated_migration_execution_with_data_integrity(self):
        """Test automated migration execution ensuring data integrity is maintained.

        This test:
        1. Creates a test database with sample data
        2. Captures schema and data snapshots
        3. Executes migrations automatically
        4. Validates data integrity and schema consistency
        """
        print(
            f"\n🔄 Testing automated migration execution with data integrity validation"
        )

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            self.temp_dirs.append(temp_path)

            # Create isolated Alembic environment
            test_db_url, alembic_cfg = (
                self.migration_suite.create_test_alembic_environment(temp_path)
            )

            print(f"   Created test environment: {test_db_url}")

            # Create initial database with test data
            engine = create_engine(
                test_db_url,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
            )

            # Create all tables
            Base.metadata.create_all(engine)

            # Generate and populate test data
            Session = sessionmaker(bind=engine)
            session = Session()

            try:
                test_data = self.migration_suite.generate_test_data_for_tables(session)
                self.migration_suite.populate_test_data(session, test_data)

                # Capture pre-migration snapshots
                pre_migration_schema = self.migration_suite.capture_schema_snapshot(
                    engine, "pre_migration"
                )

                initial_user_count = session.query(User).count()
                initial_project_count = session.query(Project).count()

                print(
                    f"   Pre-migration data: {initial_user_count} users, {initial_project_count} projects"
                )
                print(
                    f"   Pre-migration schema captured: {len(pre_migration_schema['tables'])} tables"
                )

                session.close()

                # Execute migration using Alembic
                print(f"   Executing Alembic migration...")
                start_time = time.time()

                try:
                    # Initialize Alembic revision tracking
                    command.stamp(alembic_cfg, "head")
                    migration_time = time.time() - start_time

                    print(f"   Migration completed in {migration_time:.2f} seconds")

                except Exception as e:
                    pytest.fail(f"Migration execution failed: {e}")

                # Validate post-migration state
                session = Session()

                # Capture post-migration snapshots
                post_migration_schema = self.migration_suite.capture_schema_snapshot(
                    engine, "post_migration"
                )

                # Validate data integrity
                validation_results = (
                    self.migration_suite.validate_data_integrity_after_migration(
                        session, test_data
                    )
                )

                final_user_count = session.query(User).count()
                final_project_count = session.query(Project).count()

                print(
                    f"   Post-migration data: {final_user_count} users, {final_project_count} projects"
                )
                print(
                    f"   Validation results: {validation_results['total_records_validated']} records checked"
                )

                # Assertions for data integrity
                assert final_user_count == initial_user_count, (
                    f"User count mismatch: {initial_user_count} -> {final_user_count}"
                )
                assert final_project_count == initial_project_count, (
                    f"Project count mismatch: {initial_project_count} -> {final_project_count}"
                )

                # Validate data consistency
                for table_name, consistency_check in validation_results[
                    "data_consistency_checks"
                ].items():
                    assert consistency_check["count_match"], (
                        f"Record count mismatch for {table_name}"
                    )

                # Should have no validation errors
                assert len(validation_results["validation_errors"]) == 0, (
                    f"Data validation errors: {validation_results['validation_errors']}"
                )

                # Schema validation
                assert len(post_migration_schema["tables"]) >= len(
                    pre_migration_schema["tables"]
                ), "Tables were lost during migration"

                # Migration performance assertion
                assert migration_time < 30.0, (
                    f"Migration took too long: {migration_time:.2f}s"
                )

            finally:
                session.close()
                engine.dispose()

    def test_migration_with_constraint_validation(self):
        """Test that migrations properly handle database constraints.

        This test validates:
        1. Foreign key constraints are maintained
        2. Unique constraints work correctly
        3. Check constraints are enforced
        4. Index creation and maintenance
        """
        print(f"\n🔒 Testing migration with database constraint validation")

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            self.temp_dirs.append(temp_path)

            test_db_url, alembic_cfg = (
                self.migration_suite.create_test_alembic_environment(temp_path)
            )

            engine = create_engine(
                test_db_url,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
            )

            Base.metadata.create_all(engine)

            Session = sessionmaker(bind=engine)
            session = Session()

            try:
                # Create test data that exercises constraints
                unique_suffix = str(uuid.uuid4())[:8]

                # Test unique constraints
                user1 = User(
                    name=f"Constraint Test User 1 {unique_suffix}",
                    email=f"constraint.test.1.{unique_suffix}@test.com",
                    password_hash="hash1",
                    is_active=True,
                    is_superuser=False,
                )
                session.add(user1)
                session.commit()

                # Attempt to create duplicate email (should fail)
                with pytest.raises(Exception):  # Should raise integrity error
                    user2 = User(
                        name=f"Constraint Test User 2 {unique_suffix}",
                        email=f"constraint.test.1.{unique_suffix}@test.com",  # Duplicate email
                        password_hash="hash2",
                        is_active=True,
                        is_superuser=False,
                    )
                    session.add(user2)
                    session.commit()

                session.rollback()

                # Test foreign key constraints with projects
                project1 = Project(
                    name=f"Constraint Test Project 1 {unique_suffix}",
                    project_number=f"CTP-{unique_suffix}-001",
                    description="Test project for constraint validation",
                    status=ProjectStatus.DRAFT.value,
                    default_safety_margin_percent=10.0,
                )
                session.add(project1)
                session.commit()

                print(f"   Constraint validation successful")
                print(f"   User created: {user1.email}")
                print(f"   Project created: {project1.project_number}")
                print(f"   Unique constraint properly enforced")
                print(f"   Foreign key relationships working")

                # Capture constraint information
                inspector = inspect(engine)
                constraints_info = {}

                for table_name in ["User", "Project"]:
                    if inspector.has_table(table_name):
                        constraints_info[table_name] = {
                            "foreign_keys": inspector.get_foreign_keys(table_name),
                            "unique_constraints": inspector.get_unique_constraints(
                                table_name
                            ),
                            "indexes": inspector.get_indexes(table_name),
                        }

                # Validate constraint information
                assert "User" in constraints_info, "User table constraints not found"
                assert "Project" in constraints_info, (
                    "Project table constraints not found"
                )

                # User table should have unique constraint on email
                user_unique_constraints = constraints_info["User"]["unique_constraints"]
                email_constraint_found = any(
                    "email" in uc["column_names"] for uc in user_unique_constraints
                )
                assert email_constraint_found, (
                    "Email unique constraint not found on User table"
                )

                print(f"   Database constraints properly configured and enforced")

            finally:
                session.close()
                engine.dispose()

    def test_migration_performance_with_large_dataset(self):
        """Test migration performance with larger datasets to ensure scalability.

        This test validates:
        1. Migration performance with substantial data volumes
        2. Memory usage during migration
        3. Transaction handling during migration
        4. Index rebuilding performance
        """
        print(f"\n⚡ Testing migration performance with large dataset")

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            self.temp_dirs.append(temp_path)

            test_db_url, alembic_cfg = (
                self.migration_suite.create_test_alembic_environment(temp_path)
            )

            engine = create_engine(
                test_db_url,
                connect_args={"check_same_thread": False},
                poolclass=StaticPool,
            )

            Base.metadata.create_all(engine)

            Session = sessionmaker(bind=engine)
            session = Session()

            try:
                # Generate larger test dataset
                print(f"   Generating large test dataset...")
                dataset_start = time.time()

                unique_suffix = str(uuid.uuid4())[:8]
                large_dataset_size = 1000  # 1000 users, 500 projects

                # Bulk create users
                users_to_add = []
                for i in range(large_dataset_size):
                    user = User(
                        name=f"Perf Test User {i:04d} {unique_suffix}",
                        email=f"perf.test.{i:04d}.{unique_suffix}@test.com",
                        password_hash=f"hash_{i:04d}",
                        is_active=i % 10 != 0,
                        is_superuser=i % 100 == 0,
                    )
                    users_to_add.append(user)

                session.add_all(users_to_add)
                session.commit()

                # Bulk create projects
                projects_to_add = []
                for i in range(large_dataset_size // 2):
                    project = Project(
                        name=f"Perf Test Project {i:04d} {unique_suffix}",
                        project_number=f"PTP-{unique_suffix}-{i:04d}",
                        description=f"Performance test project {i}",
                        status=ProjectStatus.DRAFT.value
                        if i % 3 == 0
                        else ProjectStatus.ACTIVE.value,
                        client=f"Perf Client {i // 50}",
                        location=f"Perf Location {i % 20}",
                        default_safety_margin_percent=10.0 + (i % 10),
                    )
                    projects_to_add.append(project)

                session.add_all(projects_to_add)
                session.commit()

                dataset_creation_time = time.time() - dataset_start

                user_count = session.query(User).count()
                project_count = session.query(Project).count()

                print(f"   Dataset created in {dataset_creation_time:.2f}s")
                print(f"   Records: {user_count} users, {project_count} projects")

                # Measure migration performance
                print(f"   Executing migration with large dataset...")
                migration_start = time.time()

                # Execute migration
                command.stamp(alembic_cfg, "head")

                migration_time = time.time() - migration_start

                print(f"   Migration completed in {migration_time:.2f}s")

                # Validate data after migration
                final_user_count = session.query(User).count()
                final_project_count = session.query(Project).count()

                print(
                    f"   Post-migration: {final_user_count} users, {final_project_count} projects"
                )

                # Performance assertions
                assert final_user_count == user_count, (
                    f"User count changed: {user_count} -> {final_user_count}"
                )
                assert final_project_count == project_count, (
                    f"Project count changed: {project_count} -> {final_project_count}"
                )

                # Migration should complete within reasonable time
                max_acceptable_time = 60.0  # 60 seconds for 1500 records
                assert migration_time < max_acceptable_time, (
                    f"Migration too slow: {migration_time:.2f}s > {max_acceptable_time}s"
                )

                # Calculate performance metrics
                records_per_second = (user_count + project_count) / migration_time
                print(
                    f"   Migration performance: {records_per_second:.1f} records/second"
                )

                # Should achieve reasonable throughput
                assert records_per_second > 25.0, (
                    f"Migration throughput too low: {records_per_second:.1f} records/sec"
                )

            finally:
                session.close()
                engine.dispose()


if __name__ == "__main__":
    pytest.main([__file__, "-v", "--tb=short", "-s"])
