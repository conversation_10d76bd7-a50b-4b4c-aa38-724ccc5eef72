"""Unit tests for Advanced Component Search functionality.

This module tests the enhanced search capabilities including:
- Advanced query builder functionality
- Specification-based filtering
- Range queries and fuzzy matching
- Relevance scoring
- Complex boolean logic
"""

import pytest
from decimal import Decimal
from typing import Dict, Any

from src.core.utils.search_query_builder import (
    ComponentSearchQueryBuilder,
    FilterOperator,
    LogicalOperator,
    SearchFilter,
    SpecificationFilter,
    RangeFilter,
)
from src.core.schemas.general.component_schemas import (
    ComponentAdvancedSearchSchema,
    AdvancedFilterSchema,
    SpecificationFilterSchema,
    RangeFilterSchema,
    FilterOperatorEnum,
    LogicalOperatorEnum,
)
from src.core.models.general.component import Component


class TestComponentSearchQueryBuilder:
    """Test cases for the ComponentSearchQueryBuilder."""

    def test_query_builder_initialization(self):
        """Test query builder initialization."""
        builder = ComponentSearchQueryBuilder()
        assert builder.model == Component
        assert len(builder.filters) == 0
        assert len(builder.specification_filters) == 0
        assert len(builder.range_filters) == 0

    def test_add_basic_filter(self):
        """Test adding basic field filters."""
        builder = ComponentSearchQueryBuilder()

        # Add a basic filter
        builder.add_basic_filter(
            field="manufacturer",
            operator=FilterOperator.EQUALS,
            value="Schneider Electric",
        )

        assert len(builder.filters) == 1
        filter_item = builder.filters[0]
        assert filter_item.field == "manufacturer"
        assert filter_item.operator == FilterOperator.EQUALS
        assert filter_item.value == "Schneider Electric"
        assert filter_item.logical_operator == LogicalOperator.AND

    def test_add_specification_filter(self):
        """Test adding specification-based filters."""
        builder = ComponentSearchQueryBuilder()

        # Add a specification filter
        builder.add_specification_filter(
            path="electrical.voltage_rating",
            operator=FilterOperator.GREATER_THAN_OR_EQUAL,
            value=480,
            data_type="number",
        )

        assert len(builder.specification_filters) == 1
        spec_filter = builder.specification_filters[0]
        assert spec_filter.path == "electrical.voltage_rating"
        assert spec_filter.operator == FilterOperator.GREATER_THAN_OR_EQUAL
        assert spec_filter.value == 480
        assert spec_filter.data_type == "number"

    def test_add_range_filter(self):
        """Test adding range filters."""
        builder = ComponentSearchQueryBuilder()

        # Add a range filter
        builder.add_range_filter(
            field="unit_price", min_value=Decimal("100.00"), max_value=Decimal("500.00")
        )

        assert len(builder.range_filters) == 1
        range_filter = builder.range_filters[0]
        assert range_filter.field == "unit_price"
        assert range_filter.min_value == Decimal("100.00")
        assert range_filter.max_value == Decimal("500.00")
        assert range_filter.include_min == True
        assert range_filter.include_max == True

    def test_add_text_search(self):
        """Test adding text search filters."""
        builder = ComponentSearchQueryBuilder()

        # Add text search
        builder.add_text_search(
            search_term="circuit breaker", fields=["name", "description"], fuzzy=False
        )

        # Should add filters for each field with OR logic
        assert len(builder.filters) == 2
        assert all(f.logical_operator == LogicalOperator.OR for f in builder.filters)
        assert all(f.operator == FilterOperator.CONTAINS for f in builder.filters)

    def test_add_price_range(self):
        """Test adding price range filters."""
        builder = ComponentSearchQueryBuilder()

        # Add price range
        builder.add_price_range(
            min_price=Decimal("50.00"), max_price=Decimal("200.00"), currency="EUR"
        )

        # Should add range filter for unit_price
        assert len(builder.range_filters) == 1
        range_filter = builder.range_filters[0]
        assert range_filter.field == "unit_price"
        assert range_filter.min_value == Decimal("50.00")
        assert range_filter.max_value == Decimal("200.00")

    def test_clear_filters(self):
        """Test clearing all filters."""
        builder = ComponentSearchQueryBuilder()

        # Add various filters
        builder.add_basic_filter("manufacturer", FilterOperator.EQUALS, "Test")
        builder.add_specification_filter(
            "electrical.voltage", FilterOperator.EQUALS, 120
        )
        builder.add_range_filter("unit_price", min_value=10, max_value=100)

        # Verify filters were added
        assert len(builder.filters) > 0
        assert len(builder.specification_filters) > 0
        assert len(builder.range_filters) > 0

        # Clear filters
        builder.clear_filters()

        # Verify all filters are cleared
        assert len(builder.filters) == 0
        assert len(builder.specification_filters) == 0
        assert len(builder.range_filters) == 0

    def test_get_filter_summary(self):
        """Test getting filter summary."""
        builder = ComponentSearchQueryBuilder()

        # Add various filters
        builder.add_basic_filter("manufacturer", FilterOperator.EQUALS, "Test")
        builder.add_specification_filter(
            "electrical.voltage", FilterOperator.EQUALS, 120
        )
        builder.add_range_filter("unit_price", min_value=10, max_value=100)

        summary = builder.get_filter_summary()

        assert summary["basic_filters"] == 1
        assert summary["specification_filters"] == 1
        assert summary["range_filters"] == 1
        assert len(summary["filters_detail"]["basic"]) == 1
        assert len(summary["filters_detail"]["specifications"]) == 1
        assert len(summary["filters_detail"]["ranges"]) == 1


class TestAdvancedSearchSchemas:
    """Test cases for advanced search schemas."""

    def test_filter_operator_enum(self):
        """Test FilterOperatorEnum values."""
        assert FilterOperatorEnum.EQUALS == "eq"
        assert FilterOperatorEnum.CONTAINS == "contains"
        assert FilterOperatorEnum.GREATER_THAN == "gt"
        assert FilterOperatorEnum.FUZZY == "fuzzy"

    def test_logical_operator_enum(self):
        """Test LogicalOperatorEnum values."""
        assert LogicalOperatorEnum.AND == "and"
        assert LogicalOperatorEnum.OR == "or"
        assert LogicalOperatorEnum.NOT == "not"

    def test_advanced_filter_schema(self):
        """Test AdvancedFilterSchema validation."""
        filter_data = {
            "field": "manufacturer",
            "operator": "eq",
            "value": "Schneider Electric",
            "logical_operator": "and",
        }

        filter_schema = AdvancedFilterSchema(**filter_data)
        assert filter_schema.field == "manufacturer"
        assert filter_schema.operator == FilterOperatorEnum.EQUALS
        assert filter_schema.value == "Schneider Electric"
        assert filter_schema.logical_operator == LogicalOperatorEnum.AND

    def test_specification_filter_schema(self):
        """Test SpecificationFilterSchema validation."""
        spec_filter_data = {
            "path": "electrical.voltage_rating",
            "operator": "gte",
            "value": 480,
            "data_type": "number",
            "unit": "V",
        }

        spec_filter = SpecificationFilterSchema(**spec_filter_data)
        assert spec_filter.path == "electrical.voltage_rating"
        assert spec_filter.operator == FilterOperatorEnum.GREATER_THAN_OR_EQUAL
        assert spec_filter.value == 480
        assert spec_filter.data_type == "number"
        assert spec_filter.unit == "V"

    def test_range_filter_schema(self):
        """Test RangeFilterSchema validation."""
        range_data = {
            "field": "unit_price",
            "min_value": 100.0,
            "max_value": 500.0,
            "include_min": True,
            "include_max": False,
        }

        range_filter = RangeFilterSchema(**range_data)
        assert range_filter.field == "unit_price"
        assert range_filter.min_value == 100.0
        assert range_filter.max_value == 500.0
        assert range_filter.include_min == True
        assert range_filter.include_max == False

    def test_range_filter_validation_error(self):
        """Test RangeFilterSchema validation error for invalid range."""
        with pytest.raises(
            ValueError, match="Minimum value cannot be greater than maximum value"
        ):
            RangeFilterSchema(field="unit_price", min_value=500.0, max_value=100.0)

    def test_component_advanced_search_schema(self):
        """Test ComponentAdvancedSearchSchema validation."""
        search_data = {
            "search_term": "circuit breaker",
            "fuzzy_search": True,
            "basic_filters": [
                {
                    "field": "manufacturer",
                    "operator": "eq",
                    "value": "Schneider Electric",
                }
            ],
            "specification_filters": [
                {
                    "path": "electrical.voltage_rating",
                    "operator": "gte",
                    "value": 480,
                    "data_type": "number",
                }
            ],
            "range_filters": [
                {"field": "unit_price", "min_value": 100.0, "max_value": 500.0}
            ],
            "sort_by": "name",
            "sort_order": "asc",
        }

        search_schema = ComponentAdvancedSearchSchema(**search_data)
        assert search_schema.search_term == "circuit breaker"
        assert search_schema.fuzzy_search == True
        assert len(search_schema.basic_filters) == 1
        assert len(search_schema.specification_filters) == 1
        assert len(search_schema.range_filters) == 1
        assert search_schema.sort_by == "name"
        assert search_schema.sort_order == "asc"


if __name__ == "__main__":
    pytest.main([__file__])
