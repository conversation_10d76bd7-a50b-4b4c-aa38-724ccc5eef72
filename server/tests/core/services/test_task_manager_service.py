"""Unit and integration tests for TaskManagerService.

This module contains comprehensive tests for the TaskManagerService class,
testing both business logic in isolation and integration with repositories.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

from src.core.enums import TaskPriority, TaskStatus
from src.core.errors.exceptions import NotFoundError, ValidationError
from src.core.models.general.task import Task, TaskAssignment
from src.core.models.general.project import Project
from src.core.models.general.user import User
from src.core.repositories.general.task_repository import TaskRepository
from src.core.repositories.general.user_repository import UserRepository
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.services.general.task_manager_service import TaskManagerService


@pytest.mark.asyncio
class TestTaskManagerService:
    """Test suite for TaskManagerService functionality."""

    @pytest.fixture
    def mock_task_repository(self):
        """Create a mock TaskRepository."""
        return AsyncMock(spec=TaskRepository)

    @pytest.fixture
    def mock_user_repository(self):
        """Create a mock UserRepository."""
        return AsyncMock(spec=UserRepository)

    @pytest.fixture
    def mock_project_repository(self):
        """Create a mock ProjectRepository."""
        return AsyncMock(spec=ProjectRepository)

    @pytest.fixture
    def task_manager_service(self, mock_task_repository, mock_user_repository, mock_project_repository):
        """Create a TaskManagerService instance with mocked repositories."""
        return TaskManagerService(mock_task_repository, mock_user_repository, mock_project_repository)

    @pytest.fixture
    def sample_project(self):
        """Create a sample project for testing."""
        return Project(
            id=1,
            name="Test Project",
            project_number="TEST-001",
            description="Test project",
            status="active",
        )

    @pytest.fixture
    def sample_user(self):
        """Create a sample user for testing."""
        return User(
            id=1,
            name="Test User",
            email="<EMAIL>",
            is_active=True,
        )

    @pytest.fixture
    def sample_task(self):
        """Create a sample task for testing."""
        task = Task(
            id=1,
            task_id="test-task-uuid",
            project_id=1,
            name="Test Task",
            description="Test task description",
            priority=TaskPriority.MEDIUM,
            status=TaskStatus.NOT_STARTED,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
            is_deleted=False,
        )
        task.assignments = []
        return task

    async def test_create_task_with_assignments_success(
        self, task_manager_service, mock_task_repository, mock_user_repository, 
        mock_project_repository, sample_project, sample_user, sample_task
    ):
        """Test successful task creation with user assignments."""
        # Setup
        mock_project_repository.get_by_id.return_value = sample_project
        mock_user_repository.get_by_id.return_value = sample_user
        mock_task_repository.create.return_value = sample_task
        mock_task_repository.get_by_task_id.return_value = sample_task
        mock_task_repository.db_session = AsyncMock()

        # Execute
        result = await task_manager_service.create_task_with_assignments(
            project_id=1,
            title="Test Task",
            description="Test description",
            priority="Medium",
            status="Not Started",
            assigned_user_ids=[1],
            created_by_user_id=1
        )

        # Assert
        assert result == sample_task
        mock_project_repository.get_by_id.assert_called_once_with(1)
        mock_user_repository.get_by_id.assert_called_once_with(1)
        mock_task_repository.create.assert_called_once()
        mock_task_repository.get_by_task_id.assert_called_once()

    async def test_create_task_with_assignments_project_not_found(
        self, task_manager_service, mock_project_repository
    ):
        """Test task creation with non-existent project."""
        # Setup
        mock_project_repository.get_by_id.return_value = None

        # Execute & Assert
        with pytest.raises(NotFoundError, match="Project with ID 999 not found"):
            await task_manager_service.create_task_with_assignments(
                project_id=999,
                title="Test Task"
            )

    async def test_create_task_with_assignments_user_not_found(
        self, task_manager_service, mock_task_repository, mock_user_repository, 
        mock_project_repository, sample_project
    ):
        """Test task creation with non-existent user assignment."""
        # Setup
        mock_project_repository.get_by_id.return_value = sample_project
        mock_user_repository.get_by_id.return_value = None

        # Execute & Assert
        with pytest.raises(NotFoundError, match="User with ID 999 not found"):
            await task_manager_service.create_task_with_assignments(
                project_id=1,
                title="Test Task",
                assigned_user_ids=[999]
            )

    async def test_get_tasks_for_project_success(
        self, task_manager_service, mock_task_repository, mock_project_repository, 
        sample_project, sample_task
    ):
        """Test successful retrieval of tasks for a project."""
        # Setup
        mock_project_repository.get_by_id.return_value = sample_project
        mock_task_repository.get_all_by_project_id.return_value = [sample_task]

        # Execute
        result = await task_manager_service.get_tasks_for_project(project_id=1)

        # Assert
        assert result == [sample_task]
        mock_project_repository.get_by_id.assert_called_once_with(1)
        mock_task_repository.get_all_by_project_id.assert_called_once_with(
            project_id=1, include_deleted=False
        )

    async def test_get_tasks_for_project_not_found(
        self, task_manager_service, mock_project_repository
    ):
        """Test retrieval of tasks for non-existent project."""
        # Setup
        mock_project_repository.get_by_id.return_value = None

        # Execute & Assert
        with pytest.raises(NotFoundError, match="Project with ID 999 not found"):
            await task_manager_service.get_tasks_for_project(project_id=999)

    async def test_get_task_by_id_success(
        self, task_manager_service, mock_task_repository, sample_task
    ):
        """Test successful retrieval of task by ID."""
        # Setup
        mock_task_repository.get_by_task_id.return_value = sample_task

        # Execute
        result = await task_manager_service.get_task_by_id("test-task-uuid")

        # Assert
        assert result == sample_task
        mock_task_repository.get_by_task_id.assert_called_once_with("test-task-uuid")

    async def test_get_task_by_id_not_found(
        self, task_manager_service, mock_task_repository
    ):
        """Test retrieval of non-existent task."""
        # Setup
        mock_task_repository.get_by_task_id.return_value = None

        # Execute & Assert
        with pytest.raises(NotFoundError, match="Task with ID test-task-uuid not found"):
            await task_manager_service.get_task_by_id("test-task-uuid")

    async def test_update_task_success(
        self, task_manager_service, mock_task_repository, sample_task
    ):
        """Test successful task update."""
        # Setup
        mock_task_repository.get_by_task_id.return_value = sample_task
        mock_task_repository.update.return_value = sample_task

        # Execute
        result = await task_manager_service.update_task(
            task_id="test-task-uuid",
            title="Updated Task",
            priority="High",
            status="In Progress"
        )

        # Assert
        assert result == sample_task
        mock_task_repository.get_by_task_id.assert_called_once_with("test-task-uuid")
        mock_task_repository.update.assert_called_once()

    async def test_update_task_not_found(
        self, task_manager_service, mock_task_repository
    ):
        """Test update of non-existent task."""
        # Setup
        mock_task_repository.get_by_task_id.return_value = None

        # Execute & Assert
        with pytest.raises(NotFoundError, match="Task with ID test-task-uuid not found"):
            await task_manager_service.update_task(
                task_id="test-task-uuid",
                title="Updated Task"
            )

    async def test_update_task_no_changes(
        self, task_manager_service, mock_task_repository, sample_task
    ):
        """Test task update with no changes."""
        # Setup
        mock_task_repository.get_by_task_id.return_value = sample_task

        # Execute
        result = await task_manager_service.update_task(task_id="test-task-uuid")

        # Assert
        assert result == sample_task
        mock_task_repository.update.assert_not_called()

    async def test_delete_task_success(
        self, task_manager_service, mock_task_repository
    ):
        """Test successful task deletion."""
        # Setup
        mock_task_repository.soft_delete_by_task_id.return_value = True

        # Execute
        result = await task_manager_service.delete_task("test-task-uuid", 1)

        # Assert
        assert result is True
        mock_task_repository.soft_delete_by_task_id.assert_called_once_with(
            task_id="test-task-uuid", deleted_by_user_id=1
        )

    async def test_delete_task_not_found(
        self, task_manager_service, mock_task_repository
    ):
        """Test deletion of non-existent task."""
        # Setup
        mock_task_repository.soft_delete_by_task_id.return_value = False

        # Execute & Assert
        with pytest.raises(NotFoundError, match="Task with ID test-task-uuid not found"):
            await task_manager_service.delete_task("test-task-uuid", 1)

    async def test_assign_users_to_task_success(
        self, task_manager_service, mock_task_repository, mock_user_repository, 
        sample_task, sample_user
    ):
        """Test successful user assignment to task."""
        # Setup
        mock_task_repository.get_by_task_id.return_value = sample_task
        mock_user_repository.get_by_id.return_value = sample_user
        mock_task_repository.db_session = AsyncMock()

        # Execute
        result = await task_manager_service.assign_users_to_task(
            task_id="test-task-uuid",
            user_ids=[1],
            assigned_by_user_id=1
        )

        # Assert
        assert result == sample_task
        mock_task_repository.get_by_task_id.assert_called()
        mock_user_repository.get_by_id.assert_called_once_with(1)

    async def test_assign_users_to_task_task_not_found(
        self, task_manager_service, mock_task_repository
    ):
        """Test user assignment to non-existent task."""
        # Setup
        mock_task_repository.get_by_task_id.return_value = None

        # Execute & Assert
        with pytest.raises(NotFoundError, match="Task with ID test-task-uuid not found"):
            await task_manager_service.assign_users_to_task(
                task_id="test-task-uuid",
                user_ids=[1]
            )

    async def test_assign_users_to_task_user_not_found(
        self, task_manager_service, mock_task_repository, mock_user_repository, sample_task
    ):
        """Test assignment of non-existent user to task."""
        # Setup
        mock_task_repository.get_by_task_id.return_value = sample_task
        mock_user_repository.get_by_id.return_value = None

        # Execute & Assert
        with pytest.raises(NotFoundError, match="User with ID 999 not found"):
            await task_manager_service.assign_users_to_task(
                task_id="test-task-uuid",
                user_ids=[999]
            )

    async def test_get_user_tasks_success(
        self, task_manager_service, mock_task_repository, mock_user_repository, 
        sample_user, sample_task
    ):
        """Test successful retrieval of user tasks."""
        # Setup
        mock_user_repository.get_by_id.return_value = sample_user
        mock_task_repository.get_by_assigned_user.return_value = [sample_task]

        # Execute
        result = await task_manager_service.get_user_tasks(user_id=1)

        # Assert
        assert result == [sample_task]
        mock_user_repository.get_by_id.assert_called_once_with(1)
        mock_task_repository.get_by_assigned_user.assert_called_once_with(
            user_id=1, include_completed=True, include_deleted=False
        )

    async def test_get_user_tasks_user_not_found(
        self, task_manager_service, mock_user_repository
    ):
        """Test retrieval of tasks for non-existent user."""
        # Setup
        mock_user_repository.get_by_id.return_value = None

        # Execute & Assert
        with pytest.raises(NotFoundError, match="User with ID 999 not found"):
            await task_manager_service.get_user_tasks(user_id=999)

    async def test_get_task_statistics_success(
        self, task_manager_service, mock_task_repository, mock_project_repository, sample_project
    ):
        """Test successful retrieval of task statistics."""
        # Setup
        mock_project_repository.get_by_id.return_value = sample_project
        mock_task_repository.get_task_statistics.return_value = {
            "total_tasks": 10,
            "status_counts": {"Not Started": 5, "In Progress": 3, "Completed": 2},
            "priority_counts": {"Low": 2, "Medium": 5, "High": 3},
            "overdue_count": 1
        }

        # Execute
        result = await task_manager_service.get_task_statistics(project_id=1)

        # Assert
        assert result["total_tasks"] == 10
        assert result["overdue_count"] == 1
        mock_project_repository.get_by_id.assert_called_once_with(1)
        mock_task_repository.get_task_statistics.assert_called_once_with(1)

    async def test_get_task_statistics_project_not_found(
        self, task_manager_service, mock_project_repository
    ):
        """Test retrieval of statistics for non-existent project."""
        # Setup
        mock_project_repository.get_by_id.return_value = None

        # Execute & Assert
        with pytest.raises(NotFoundError, match="Project with ID 999 not found"):
            await task_manager_service.get_task_statistics(project_id=999)
