"""Unit tests for Component Category functionality.

This module provides comprehensive tests for component category management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentCategory model validation and business logic
- ComponentCategoryRepository data access operations
- ComponentCategoryService business logic and validation
- Component Category API endpoints and error handling
- Hierarchical operations and tree management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_category_repository import (
    ComponentCategoryRepository,
)
from src.core.services.general.component_category_service import (
    ComponentCategoryService,
)
from src.core.schemas.general.component_category_schemas import (
    ComponentCategoryCreateSchema,
    ComponentCategoryUpdateSchema,
    ComponentCategorySearchSchema,
)
from src.core.errors.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)
from src.core.utils.pagination_utils import PaginationParams


class TestComponentCategoryService:
    """Test ComponentCategoryService functionality."""

    async def test_create_category_success(
        self,
        category_service: ComponentCategoryService,
        mock_category_repository: Mock,
        sample_category_data: ComponentCategoryCreateSchema,
    ):
        """Test successful category creation."""
        # Setup mock
        mock_category = ComponentCategory(
            id=1,
            name=sample_category_data.name,
            description=sample_category_data.description,
            is_active=sample_category_data.is_active,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        from unittest.mock import AsyncMock, Mock
        mock_category_repository.get_by_name = AsyncMock(return_value=None)
        mock_category_repository.create = AsyncMock(return_value=mock_category)
        mock_category_repository.db_session = Mock()
        mock_category_repository.db_session.flush = AsyncMock()
        mock_category_repository.db_session.refresh = AsyncMock()

        # Test creation
        result = await category_service.create_category(sample_category_data)

        assert result.id == 1
        assert result.name == "Test Category"
        assert result.description == "Test description"
        mock_category_repository.create.assert_called_once()

    async def test_create_category_duplicate_name(
        self,
        category_service: ComponentCategoryService,
        mock_category_repository: Mock,
        sample_category_data: ComponentCategoryCreateSchema,
    ):
        """Test category creation with duplicate name."""
        # Setup mock to return existing category
        existing_category = ComponentCategory(
            id=1,
            name=sample_category_data.name,
            description="Existing description",
            is_active=True,
        )
        from unittest.mock import AsyncMock
        mock_category_repository.get_by_name = AsyncMock(return_value=existing_category)

        # Test creation should fail
        with pytest.raises(BusinessLogicError, match="already exists"):
            await category_service.create_category(sample_category_data)

    async def test_get_category_success(
        self, category_service: ComponentCategoryService, mock_category_repository: Mock
    ):
        """Test successful category retrieval."""
        # Setup mock
        mock_category = ComponentCategory(
            id=1,
            name="Test Category",
            description="Test description",
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        from unittest.mock import AsyncMock
        mock_category_repository.get_by_id = AsyncMock(return_value=mock_category)

        # Test retrieval
        result = await category_service.get_category(1)

        assert result.id == 1
        assert result.name == "Test Category"
        mock_category_repository.get_by_id.assert_called_once_with(1)

    async def test_get_category_not_found(
        self, category_service: ComponentCategoryService, mock_category_repository: Mock
    ):
        """Test category retrieval when not found."""
        # Setup mock to return None
        from unittest.mock import AsyncMock
        mock_category_repository.get_by_id = AsyncMock(return_value=None)

        # Test retrieval should fail
        with pytest.raises(NotFoundError, match="not found"):
            await category_service.get_category(999)

    async def test_update_category_success(
        self, category_service: ComponentCategoryService, mock_category_repository: Mock
    ):
        """Test successful category update."""
        # Setup mock
        existing_category = ComponentCategory(
            id=1,
            name="Old Name",
            description="Old description",
            is_active=True,
        )
        updated_category = ComponentCategory(
            id=1,
            name="New Name",
            description="New description",
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        from unittest.mock import AsyncMock, Mock
        mock_category_repository.get_by_id = AsyncMock(return_value=existing_category)
        mock_category_repository.validate_hierarchy = AsyncMock(return_value=True)
        mock_category_repository.get_by_name = AsyncMock(return_value=None)
        mock_category_repository.update = AsyncMock(return_value=updated_category)
        mock_category_repository.db_session = Mock()
        mock_category_repository.db_session.flush = AsyncMock()
        mock_category_repository.db_session.commit = AsyncMock()
        mock_category_repository.db_session.refresh = AsyncMock()

        update_data = ComponentCategoryUpdateSchema(
            name="New Name",
            description="New description",
            parent_category_id=None,
            is_active=True,
        )

        # Test update
        result = await category_service.update_category(1, update_data)

        assert result.name == "New Name"
        assert result.description == "New description"
        mock_category_repository.update.assert_called_once()

    async def test_delete_category_success(
        self, category_service: ComponentCategoryService, mock_category_repository: Mock
    ):
        """Test successful category deletion."""
        # Setup mock
        mock_category = ComponentCategory(
            id=1,
            name="Test Category",
            description="Test description",
            is_active=True,
        )
        mock_category.can_delete = Mock(return_value=(True, None))
        mock_category.soft_delete = Mock(return_value=True)
        from unittest.mock import AsyncMock
        mock_category_repository.get_by_id_with_relationships = AsyncMock(return_value=mock_category)
        mock_category_repository.soft_delete = AsyncMock(return_value=True)
        mock_category_repository.db_session = Mock()
        mock_category_repository.db_session.commit = AsyncMock()
        mock_category_repository.db_session.flush = AsyncMock()

        # Test deletion
        result = await category_service.delete_category(1, deleted_by_user_id=1)

        assert result is True
        mock_category_repository.soft_delete.assert_called_once_with(1, 1)

    async def test_delete_category_with_dependencies(
        self, category_service: ComponentCategoryService, mock_category_repository: Mock
    ):
        """Test category deletion when it has dependencies."""
        # Setup mock
        mock_category = ComponentCategory(
            id=1,
            name="Test Category",
            description="Test description",
            is_active=True,
        )
        mock_category.can_delete = Mock(
            return_value=(False, "Has active component types")
        )
        from unittest.mock import AsyncMock
        mock_category_repository.get_by_id_with_relationships = AsyncMock(return_value=mock_category)

        # Test deletion should fail
        with pytest.raises(BusinessLogicError, match="Cannot delete category"):
            await category_service.delete_category(1, deleted_by_user_id=1)

    async def test_list_categories(
        self, category_service: ComponentCategoryService, mock_category_repository: Mock
    ):
        """Test listing categories with pagination."""
        # Setup mock
        mock_categories = [
            ComponentCategory(id=1, name="Category 1", is_active=True),
            ComponentCategory(id=2, name="Category 2", is_active=True),
        ]
        from unittest.mock import AsyncMock
        mock_category_repository.search_categories = AsyncMock(return_value=(mock_categories, 2))

        # Test listing
        result = await category_service.list_categories()

        assert result.total_count == 2
        assert len(result.categories) == 2
        assert result.categories[0].name == "Category 1"
        assert result.categories[1].name == "Category 2"
