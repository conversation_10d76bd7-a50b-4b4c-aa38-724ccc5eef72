"""Unit tests for Component Type functionality.

This module provides comprehensive tests for component type management,
including models, repositories, services, and API endpoints.

Key Test Areas:
- ComponentType model validation and business logic
- ComponentTypeRepository data access operations
- ComponentTypeService business logic and validation
- Component Type API endpoints and error handling
- Category relationships and validation
- Specifications template management
- Performance and edge case testing
"""

import pytest
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch, AsyncMock

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from src.core.models.general.component_type import ComponentType
from src.core.models.general.component_category import ComponentCategory
from src.core.repositories.general.component_type_repository import (
    ComponentTypeRepository,
)
from src.core.services.general.component_type_service import ComponentTypeService
from src.core.schemas.general.component_type_schemas import (
    ComponentTypeCreateSchema,
    ComponentTypeUpdateSchema,
    ComponentTypeSearchSchema,
)
from src.core.errors.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)
from src.core.utils.pagination_utils import PaginationParams


class TestComponentTypeService:
    """Test ComponentTypeService functionality."""

    async def test_create_type_success(
        self,
        type_service: ComponentTypeService,
        mock_type_repository: Mock,
        sample_type_data: ComponentTypeCreateSchema,
    ):
        """Test successful component type creation."""
        # Setup mock
        mock_type = ComponentType(
            id=1,
            name=sample_type_data.name,
            description=sample_type_data.description,
            category_id=sample_type_data.category_id,
            is_active=sample_type_data.is_active,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        mock_type_repository.get_by_name = AsyncMock(return_value=None)
        mock_type_repository.validate_category_exists = AsyncMock(return_value=True)
        mock_type_repository.create = AsyncMock(return_value=mock_type)
        mock_type_repository.db_session = Mock()
        mock_type_repository.db_session.flush = AsyncMock()
        mock_type_repository.db_session.refresh = AsyncMock()

        # Test creation
        result = await type_service.create_type(sample_type_data)

        assert result.id == 1
        assert result.name == "Test Type"
        assert result.description == "Test description"
        mock_type_repository.create.assert_called_once()

    async def test_create_type_duplicate_name(
        self,
        type_service: ComponentTypeService,
        mock_type_repository: Mock,
        sample_type_data: ComponentTypeCreateSchema,
    ):
        """Test component type creation with duplicate name."""
        # Setup mock to return existing type
        existing_type = ComponentType(
            id=1,
            name=sample_type_data.name,
            description="Existing description",
            category_id=sample_type_data.category_id,
            is_active=True,
        )
        mock_type_repository.get_by_name = AsyncMock(return_value=existing_type)
        mock_type_repository.validate_category_exists = AsyncMock(return_value=True)

        # Test creation should fail
        with pytest.raises(BusinessLogicError, match="already exists"):
            await type_service.create_type(sample_type_data)

    async def test_create_type_invalid_category(
        self,
        type_service: ComponentTypeService,
        mock_type_repository: Mock,
        sample_type_data: ComponentTypeCreateSchema,
    ):
        """Test component type creation with invalid category."""
        # Setup mock
        mock_type_repository.get_by_name = AsyncMock(return_value=None)
        mock_type_repository.validate_category_exists = AsyncMock(return_value=False)

        # Test creation should fail
        with pytest.raises(ValidationError, match="Category does not exist"):
            await type_service.create_type(sample_type_data)

    async def test_get_type_success(
        self, type_service: ComponentTypeService, mock_type_repository: Mock
    ):
        """Test successful component type retrieval."""
        # Setup mock
        mock_type = ComponentType(
            id=1,
            name="Test Type",
            description="Test description",
            category_id=1,
            is_active=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )
        mock_type_repository.get_by_id = AsyncMock(return_value=mock_type)

        # Test retrieval
        result = await type_service.get_type(1)

        assert result.id == 1
        assert result.name == "Test Type"
        mock_type_repository.get_by_id.assert_called_once_with(1)

    async def test_get_type_not_found(
        self, type_service: ComponentTypeService, mock_type_repository: Mock
    ):
        """Test component type retrieval when not found."""
        # Setup mock to return None
        mock_type_repository.get_by_id = AsyncMock(return_value=None)

        # Test retrieval should fail
        with pytest.raises(NotFoundError, match="not found"):
            await type_service.get_type(999)

    async def test_update_specifications_template(
        self, type_service: ComponentTypeService, mock_type_repository: Mock
    ):
        """Test updating specifications template."""
        # Setup mock
        existing_type = ComponentType(
            id=1,
            name="Test Type",
            description="Test description",
            category_id=1,
            is_active=True,
        )
        updated_type = ComponentType(
            id=1,
            name="Test Type",
            description="Test description",
            category_id=1,
            is_active=True,
            specifications_template={"electrical": {"voltage": {"type": "number"}}},
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow(),
        )

        mock_type_repository.get_by_id = AsyncMock(return_value=existing_type)
        mock_type_repository.update_specifications_template = AsyncMock(return_value=updated_type)
        mock_type_repository.db_session = Mock()
        mock_type_repository.db_session.commit = AsyncMock()

        template = {"electrical": {"voltage": {"type": "number"}}}

        # Test update
        result = await type_service.update_specifications_template(1, template)

        assert result.specifications_template == template
        mock_type_repository.update_specifications_template.assert_called_once_with(
            1, template
        )
