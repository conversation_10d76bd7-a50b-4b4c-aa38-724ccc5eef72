"""Unit tests for SynchronizationService utility methods.

This module contains comprehensive unit tests for the utility methods
in the SynchronizationService class, focusing on data comparison and
basic synchronization functionality.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any, Optional
from datetime import datetime
from uuid import UUID, uuid4

from src.core.services.general.synchronization_service import (
    SynchronizationService,
    ChangeR<PERSON>ord,
    SyncOperation,
    SyncDirection,
)
from src.core.database.connection_manager import DynamicConnectionManager
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.errors.exceptions import SynchronizationError


class TestSynchronizationServiceUtilities:
    """Test suite for SynchronizationService utility methods."""

    @pytest.fixture
    def mock_connection_manager(self) -> MagicMock:
        """Create a mock DynamicConnectionManager."""
        return MagicMock(spec=DynamicConnectionManager)

    @pytest.fixture
    def mock_project_repository(self) -> AsyncMock:
        """Create a mock ProjectRepository."""
        return AsyncMock(spec=ProjectRepository)

    @pytest.fixture
    def sync_service(
        self, mock_connection_manager: MagicMock, mock_project_repository: AsyncMock
    ) -> SynchronizationService:
        """Create a SynchronizationService instance with mocked dependencies."""
        return SynchronizationService(mock_connection_manager, mock_project_repository)

    @pytest.fixture
    def custom_sync_config(self) -> Dict[str, Any]:
        """Create a custom sync configuration for testing."""
        return {
            "batch_size": 50,
            "timeout_seconds": 180,
            "retry_attempts": 5,
            "conflict_resolution": "manual",
            "sync_interval_minutes": 30,
            "enable_incremental_sync": False,
            "monitored_entities": ["project", "component"],
        }

    def test_synchronization_service_initialization_default_config(
        self, mock_connection_manager: MagicMock, mock_project_repository: AsyncMock
    ) -> None:
        """Test SynchronizationService initialization with default configuration."""
        service = SynchronizationService(
            mock_connection_manager, mock_project_repository
        )

        assert service.connection_manager is mock_connection_manager
        assert service.project_repository is mock_project_repository
        assert service.sync_config is not None

        # Check default configuration values
        assert service.sync_config["batch_size"] == 100
        assert service.sync_config["timeout_seconds"] == 300
        assert service.sync_config["retry_attempts"] == 3
        assert service.sync_config["conflict_resolution"] == "last_write_wins"
        assert service.sync_config["sync_interval_minutes"] == 15
        assert service.sync_config["enable_incremental_sync"] is True
        assert "project" in service.sync_config["monitored_entities"]
        assert "component" in service.sync_config["monitored_entities"]

    def test_synchronization_service_initialization_custom_config(
        self,
        mock_connection_manager: MagicMock,
        mock_project_repository: AsyncMock,
        custom_sync_config: Dict[str, Any],
    ) -> None:
        """Test SynchronizationService initialization with custom configuration."""
        service = SynchronizationService(
            mock_connection_manager, mock_project_repository, custom_sync_config
        )

        assert service.sync_config == custom_sync_config
        assert service.sync_config["batch_size"] == 50
        assert service.sync_config["conflict_resolution"] == "manual"

    @pytest.mark.asyncio
    async def test_get_last_sync_timestamp_placeholder(
        self, sync_service: SynchronizationService
    ) -> None:
        """Test _get_last_sync_timestamp returns None as placeholder."""
        project_id = uuid4()

        result = await sync_service._get_last_sync_timestamp(project_id)

        assert result is None

    def test_compare_entity_data_no_differences(
        self, sync_service: SynchronizationService
    ) -> None:
        """Test _compare_entity_data with identical data."""
        local_data = {
            "id": 1,
            "name": "Test Project",
            "status": "active",
            "created_at": "2024-01-01T00:00:00Z",
        }
        central_data = {
            "id": 1,
            "name": "Test Project",
            "status": "active",
            "created_at": "2024-01-01T00:00:00Z",
        }

        result = sync_service._compare_entity_data(local_data, central_data)

        assert result["has_differences"] is False
        assert result["changed_fields"] == {}
        assert result["local_only"] == {}
        assert result["central_only"] == {}
        assert set(result["identical_fields"]) == {"id", "name", "status", "created_at"}

    def test_compare_entity_data_with_differences(
        self, sync_service: SynchronizationService
    ) -> None:
        """Test _compare_entity_data with differing data."""
        local_data = {
            "id": 1,
            "name": "Test Project Local",
            "status": "active",
            "description": "Local description",
        }
        central_data = {
            "id": 1,
            "name": "Test Project Central",
            "status": "active",
            "location": "Central location",
        }

        result = sync_service._compare_entity_data(local_data, central_data)

        assert result["has_differences"] is True
        assert result["changed_fields"] == {
            "name": {"local": "Test Project Local", "central": "Test Project Central"}
        }
        assert result["local_only"] == {"description": "Local description"}
        assert result["central_only"] == {"location": "Central location"}
        assert set(result["identical_fields"]) == {"id", "status"}

    def test_compare_entity_data_empty_dictionaries(
        self, sync_service: SynchronizationService
    ) -> None:
        """Test _compare_entity_data with empty dictionaries."""
        local_data: Dict[str, Any] = {}
        central_data: Dict[str, Any] = {}

        result = sync_service._compare_entity_data(local_data, central_data)

        assert result["has_differences"] is False
        assert result["changed_fields"] == {}
        assert result["local_only"] == {}
        assert result["central_only"] == {}
        assert result["identical_fields"] == []

    def test_compare_entity_data_one_empty_dictionary(
        self, sync_service: SynchronizationService
    ) -> None:
        """Test _compare_entity_data with one empty dictionary."""
        local_data = {"id": 1, "name": "Test Project"}
        central_data: Dict[str, Any] = {}

        result = sync_service._compare_entity_data(local_data, central_data)

        assert result["has_differences"] is True
        assert result["changed_fields"] == {}
        assert result["local_only"] == {"id": 1, "name": "Test Project"}
        assert result["central_only"] == {}
        assert result["identical_fields"] == []

    def test_compare_entity_data_invalid_input_types(
        self, sync_service: SynchronizationService
    ) -> None:
        """Test _compare_entity_data with invalid input types."""
        with pytest.raises(ValueError) as exc_info:
            sync_service._compare_entity_data("not_a_dict", {"key": "value"})

        assert "Both local_data and central_data must be dictionaries" in str(
            exc_info.value
        )

        with pytest.raises(ValueError) as exc_info:
            sync_service._compare_entity_data({"key": "value"}, "not_a_dict")

        assert "Both local_data and central_data must be dictionaries" in str(
            exc_info.value
        )

    def test_compare_entity_data_complex_values(
        self, sync_service: SynchronizationService
    ) -> None:
        """Test _compare_entity_data with complex data types."""
        local_data = {
            "id": 1,
            "tags": ["tag1", "tag2"],
            "metadata": {"version": 1, "author": "local"},
            "settings": None,
        }
        central_data = {
            "id": 1,
            "tags": ["tag1", "tag3"],
            "metadata": {"version": 2, "author": "central"},
            "settings": {"theme": "dark"},
        }

        result = sync_service._compare_entity_data(local_data, central_data)

        assert result["has_differences"] is True
        assert result["changed_fields"]["tags"]["local"] == ["tag1", "tag2"]
        assert result["changed_fields"]["tags"]["central"] == ["tag1", "tag3"]
        assert result["changed_fields"]["metadata"]["local"] == {
            "version": 1,
            "author": "local",
        }
        assert result["changed_fields"]["metadata"]["central"] == {
            "version": 2,
            "author": "central",
        }
        assert result["changed_fields"]["settings"]["local"] is None
        assert result["changed_fields"]["settings"]["central"] == {"theme": "dark"}
        assert result["identical_fields"] == ["id"]


class TestChangeRecord:
    """Test suite for ChangeRecord class."""

    def test_change_record_initialization_minimal(self) -> None:
        """Test ChangeRecord initialization with minimal parameters."""
        record = ChangeRecord(
            entity_type="project", entity_id=123, operation=SyncOperation.CREATE
        )

        assert record.entity_type == "project"
        assert record.entity_id == 123
        assert record.operation == SyncOperation.CREATE
        assert record.changed_fields == {}
        assert record.old_values == {}
        assert record.new_values == {}
        assert isinstance(record.timestamp, datetime)
        assert record.source is None

    def test_change_record_initialization_complete(self) -> None:
        """Test ChangeRecord initialization with all parameters."""
        timestamp = datetime(2024, 1, 1, 12, 0, 0)
        changed_fields = {"name": "New Name"}
        old_values = {"name": "Old Name"}
        new_values = {"name": "New Name"}

        record = ChangeRecord(
            entity_type="component",
            entity_id="comp_123",
            operation=SyncOperation.UPDATE,
            changed_fields=changed_fields,
            old_values=old_values,
            new_values=new_values,
            timestamp=timestamp,
            source="local",
        )

        assert record.entity_type == "component"
        assert record.entity_id == "comp_123"
        assert record.operation == SyncOperation.UPDATE
        assert record.changed_fields == changed_fields
        assert record.old_values == old_values
        assert record.new_values == new_values
        assert record.timestamp == timestamp
        assert record.source == "local"

    def test_change_record_str_representation(self) -> None:
        """Test ChangeRecord string representation."""
        timestamp = datetime(2024, 1, 1, 12, 0, 0)
        record = ChangeRecord(
            entity_type="user",
            entity_id=456,
            operation=SyncOperation.DELETE,
            timestamp=timestamp,
        )

        str_repr = str(record)

        assert "ChangeRecord" in str_repr
        assert "entity_type='user'" in str_repr
        assert "entity_id=456" in str_repr
        assert "operation=delete" in str_repr
        assert "timestamp=2024-01-01 12:00:00" in str_repr

    def test_change_record_with_uuid(self) -> None:
        """Test ChangeRecord with UUID entity_id."""
        entity_id = uuid4()
        record = ChangeRecord(
            entity_type="project", entity_id=entity_id, operation=SyncOperation.CREATE
        )

        assert record.entity_id == entity_id


class TestSyncEnums:
    """Test suite for synchronization enums."""

    def test_sync_operation_enum_values(self) -> None:
        """Test SyncOperation enum values."""
        assert SyncOperation.CREATE.value == "create"
        assert SyncOperation.UPDATE.value == "update"
        assert SyncOperation.DELETE.value == "delete"

    def test_sync_direction_enum_values(self) -> None:
        """Test SyncDirection enum values."""
        assert SyncDirection.LOCAL_TO_CENTRAL.value == "local_to_central"
        assert SyncDirection.CENTRAL_TO_LOCAL.value == "central_to_local"
        assert SyncDirection.BIDIRECTIONAL.value == "bidirectional"
