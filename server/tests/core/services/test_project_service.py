import pytest
from unittest.mock import MagicMock, patch
from src.core.services.general.project_service import ProjectService
from src.core.schemas.general.project_schemas import (
    ProjectCreateSchema,
    ProjectUpdateSchema,
)
from src.core.models.general.project import Project


class TestProjectService:
    """Unit tests for the ProjectService class."""

    def test_create_and_update_project_with_offline_status(
        self, project_service: ProjectService
    ):
        """Test creating and updating a project with offline status."""
        # 1. Test creating a project with is_offline = True
        create_data = ProjectCreateSchema(
            name="Offline Project",
            description="A test project for offline mode.",
            is_offline=True,
        )

        # Mock the repository's create method
        mock_repo = MagicMock()
        project_service.project_repository = mock_repo
        mock_repo.create.return_value = Project(
            id=1, name=create_data.name, is_offline=create_data.is_offline
        )
        mock_repo.db_session.commit = MagicMock()
        mock_repo.db_session.refresh = MagicMock()

        created_project = project_service.create_project(create_data)

        assert created_project.is_offline is True
        mock_repo.create.assert_called_once()
        # Ensure the dictionary passed to the repo contains the is_offline flag
        call_args = mock_repo.create.call_args[0][0]
        assert call_args.get("is_offline") is True

        # 2. Test updating a project to is_offline = False
        update_data = ProjectUpdateSchema(is_offline=False)

        # Mock the repository's get_by_id to return the created project
        existing_project = Project(id=1, name="Offline Project", is_offline=True)
        with patch.object(
            project_service, "_get_project_by_id_or_code", return_value=existing_project
        ):
            updated_project = project_service.update_project("1", update_data)

            assert updated_project.is_offline is False
            # Verify that the project attribute was set and commit was called
            assert existing_project.is_offline is False
            mock_repo.db_session.commit.assert_called()
