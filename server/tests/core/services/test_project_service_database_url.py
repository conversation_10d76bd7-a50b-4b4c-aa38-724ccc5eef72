"""Unit tests for ProjectService database URL integration.

This module contains comprehensive unit tests for the ProjectService
database URL functionality including validation, connection management,
and integration with the DynamicConnectionManager.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Optional

from src.core.services.general.project_service import ProjectService
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.database.connection_manager import DynamicConnectionManager
from src.core.errors.exceptions import DataValidationError, DatabaseError
from src.core.schemas.general.project_schemas import (
    ProjectCreateSchema,
    ProjectUpdateSchema,
)
from src.core.models.general.project import Project


class TestProjectServiceDatabaseURL:
    """Test suite for ProjectService database URL functionality."""

    @pytest.fixture
    def mock_project_repository(self) -> AsyncMock:
        """Create a mock ProjectRepository."""
        return AsyncMock(spec=ProjectRepository)

    @pytest.fixture
    def mock_connection_manager(self) -> MagicMock:
        """Create a mock DynamicConnectionManager."""
        return MagicMock(spec=DynamicConnectionManager)

    @pytest.fixture
    def project_service(
        self, mock_project_repository: AsyncMock, mock_connection_manager: MagicMock
    ) -> ProjectService:
        """Create a ProjectService instance with mocked dependencies."""
        return ProjectService(mock_project_repository, mock_connection_manager)

    @pytest.fixture
    def project_service_no_manager(
        self, mock_project_repository: AsyncMock
    ) -> ProjectService:
        """Create a ProjectService instance without connection manager."""
        return ProjectService(mock_project_repository, None)

    @pytest.mark.asyncio
    async def test_validate_database_url_valid_postgresql(
        self, project_service: ProjectService, mock_connection_manager: MagicMock
    ) -> None:
        """Test database URL validation with valid PostgreSQL URL."""
        database_url = "postgresql+asyncpg://user:pass@localhost/testdb"
        mock_session_factory = MagicMock()
        mock_connection_manager._get_local_session_factory.return_value = (
            mock_session_factory
        )

        # Should not raise any exception
        await project_service._validate_database_url(database_url)

        mock_connection_manager._get_local_session_factory.assert_called_once_with(
            database_url
        )

    @pytest.mark.asyncio
    async def test_validate_database_url_invalid_scheme(
        self, project_service: ProjectService
    ) -> None:
        """Test database URL validation with invalid scheme."""
        database_url = "mysql://user:pass@localhost/testdb"

        with pytest.raises(DataValidationError) as exc_info:
            await project_service._validate_database_url(database_url)

        # Check the validation error details contain the database URL scheme validation
        validation_errors = exc_info.value.metadata.get("validation_errors", {}).get("validation_errors", {})
        assert "database_url" in validation_errors
        assert "supported schemes" in validation_errors["database_url"]

    @pytest.mark.asyncio
    async def test_validate_database_url_connection_failure(
        self, mock_project_repository: AsyncMock
    ) -> None:
        """Test database URL validation with connection failure."""
        database_url = "postgresql+asyncpg://user:pass@localhost/testdb"
        
        # Create a fresh mock connection manager with side effect
        mock_connection_manager = MagicMock()
        mock_connection_manager._get_local_session_factory.side_effect = Exception(
            "Connection failed"
        )
        
        # Create service with the properly configured mock
        project_service = ProjectService(mock_project_repository, mock_connection_manager)


        with pytest.raises(DataValidationError) as exc_info:
            await project_service._validate_database_url(database_url)

        # Check that the validation error contains connection failure details
        validation_errors = exc_info.value.metadata.get("validation_errors", {}).get("validation_errors", {})
        assert "database_url" in validation_errors
        assert "Cannot connect to database" in validation_errors["database_url"]
        assert "Connection failed" in validation_errors["database_url"]

    @pytest.mark.asyncio
    async def test_validate_database_url_none(
        self, project_service: ProjectService
    ) -> None:
        """Test database URL validation with None value."""
        # Should not raise any exception
        await project_service._validate_database_url(None)

    @pytest.mark.asyncio
    async def test_validate_database_url_empty_string(
        self, project_service: ProjectService
    ) -> None:
        """Test database URL validation with empty string."""
        # Should not raise any exception
        await project_service._validate_database_url("")
        await project_service._validate_database_url("   ")

    @pytest.mark.asyncio
    async def test_validate_database_url_no_connection_manager(
        self, project_service_no_manager: ProjectService
    ) -> None:
        """Test database URL validation without connection manager."""
        database_url = "postgresql+asyncpg://user:pass@localhost/testdb"

        # Should not raise any exception, just log warning
        await project_service_no_manager._validate_database_url(database_url)

    @pytest.mark.asyncio
    async def test_handle_database_url_in_creation_with_url(
        self, project_service: ProjectService
    ) -> None:
        """Test database URL handling during project creation with URL."""
        project_data = ProjectCreateSchema(
            name="Test Project",
            database_url="postgresql+asyncpg://user:pass@localhost/testdb",
        )

        with patch.object(project_service, "_validate_database_url", new_callable=AsyncMock) as mock_validate:
            await project_service._handle_database_url_in_creation(project_data)
            mock_validate.assert_called_once_with(project_data.database_url)

    @pytest.mark.asyncio
    async def test_handle_database_url_in_creation_without_url(
        self, project_service: ProjectService
    ) -> None:
        """Test database URL handling during project creation without URL."""
        project_data = ProjectCreateSchema(name="Test Project")

        with patch.object(project_service, "_validate_database_url", new_callable=AsyncMock) as mock_validate:
            await project_service._handle_database_url_in_creation(project_data)
            mock_validate.assert_not_called()

    @pytest.mark.asyncio
    async def test_handle_database_url_in_update_changing_url(
        self, project_service: ProjectService
    ) -> None:
        """Test database URL handling during project update with changing URL."""
        existing_project = MagicMock(spec=Project)
        existing_project.database_url = "********************************/old_db"

        project_data = ProjectUpdateSchema(
            database_url="postgresql+asyncpg://user:pass@localhost/newdb"
        )

        with patch.object(project_service, "_validate_database_url", new_callable=AsyncMock) as mock_validate:
            await project_service._handle_database_url_in_update(
                existing_project, project_data
            )
            mock_validate.assert_called_once_with(project_data.database_url)

    @pytest.mark.asyncio
    async def test_handle_database_url_in_update_same_url(
        self, project_service: ProjectService
    ) -> None:
        """Test database URL handling during project update with same URL."""
        existing_project = MagicMock(spec=Project)
        existing_project.database_url = (
            "postgresql+asyncpg://user:pass@localhost/testdb"
        )

        project_data = ProjectUpdateSchema(
            database_url="postgresql+asyncpg://user:pass@localhost/testdb"
        )

        with patch.object(project_service, "_validate_database_url", new_callable=AsyncMock) as mock_validate:
            await project_service._handle_database_url_in_update(
                existing_project, project_data
            )
            mock_validate.assert_not_called()

    @pytest.mark.asyncio
    async def test_handle_database_url_in_update_no_url_field(
        self, project_service: ProjectService
    ) -> None:
        """Test database URL handling during project update without URL field."""
        existing_project = MagicMock(spec=Project)
        existing_project.database_url = (
            "postgresql+asyncpg://user:pass@localhost/testdb"
        )

        project_data = ProjectUpdateSchema(name="Updated Name")

        with patch.object(project_service, "_validate_database_url", new_callable=AsyncMock) as mock_validate:
            await project_service._handle_database_url_in_update(
                existing_project, project_data
            )
            mock_validate.assert_not_called()

    @pytest.mark.asyncio
    async def test_create_project_with_database_url(
        self, project_service: ProjectService, mock_project_repository: AsyncMock
    ) -> None:
        """Test project creation with database URL integration."""
        project_data = ProjectCreateSchema(
            name="Test Project",
            database_url="postgresql+asyncpg://user:pass@localhost/testdb",
        )

        # Mock the validation methods
        with patch.object(
            project_service, "_validate_project_creation", new_callable=AsyncMock
        ) as mock_validate_creation:
            with patch.object(
                project_service, "_handle_database_url_in_creation", new_callable=AsyncMock
            ) as mock_handle_url:
                # Mock repository and session operations
                mock_project = MagicMock(spec=Project)
                mock_project.id = 1
                mock_project.name = "Test Project"
                mock_project.description = "Test project description"
                mock_project.status = "Draft"
                mock_project.client = "Test Client"
                mock_project.location = "Test Location"
                mock_project.database_url = "postgresql+asyncpg://user:pass@localhost/testdb"
                mock_project.project_number = "PRJ-001"
                mock_project_repository.create.return_value = mock_project
                mock_project_repository.db_session = AsyncMock()

                result = await project_service.create_project(project_data)

                mock_validate_creation.assert_called_once_with(project_data)
                mock_handle_url.assert_called_once_with(project_data)
                mock_project_repository.create.assert_called_once()

    @pytest.mark.asyncio
    async def test_update_project_with_database_url(
        self, project_service: ProjectService, mock_project_repository: AsyncMock
    ) -> None:
        """Test project update with database URL integration."""
        project_id = "1"
        project_data = ProjectUpdateSchema(
            database_url="postgresql+asyncpg://user:pass@localhost/newdb"
        )

        # Mock existing project
        existing_project = MagicMock(spec=Project)
        existing_project.id = 1
        existing_project.name = "Test Project"
        existing_project.description = "Test project description"
        existing_project.status = "Draft"
        existing_project.client = "Test Client"
        existing_project.location = "Test Location"
        existing_project.database_url = "********************************/old_db"
        existing_project.project_number = "PRJ-001"

        with patch.object(
            project_service, "_get_project_by_id_or_code", new_callable=AsyncMock
        ) as mock_get_project:
            with patch.object(
                project_service, "_validate_project_update", new_callable=AsyncMock
            ) as mock_validate_update:
                with patch.object(
                    project_service, "_handle_database_url_in_update", new_callable=AsyncMock
                ) as mock_handle_url:
                    mock_get_project.return_value = existing_project

                    # Mock repository and session operations
                    mock_project_repository.update.return_value = existing_project
                    mock_project_repository.db_session = AsyncMock()

                    result = await project_service.update_project(
                        project_id, project_data
                    )

                    mock_get_project.assert_called_once_with(project_id)
                    mock_validate_update.assert_called_once_with(
                        existing_project, project_data
                    )
                    mock_handle_url.assert_called_once_with(
                        existing_project, project_data
                    )
                    mock_project_repository.update.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_project_database_url_validation_failure(
        self, project_service: ProjectService, mock_project_repository: AsyncMock
    ) -> None:
        """Test project creation failure due to database URL validation."""
        # The validation now happens at Pydantic level, not service level
        # So this test should catch pydantic_core.ValidationError
        from pydantic_core import ValidationError
        
        with pytest.raises(ValidationError) as exc_info:
            project_data = ProjectCreateSchema(
                name="Test Project", database_url="invalid://url"
            )

        # Check that the validation error mentions supported schemes
        assert "supported schemes" in str(exc_info.value)
        mock_project_repository.create.assert_not_called()

    @pytest.mark.asyncio
    async def test_update_project_database_url_validation_failure(
        self, project_service: ProjectService, mock_project_repository: AsyncMock
    ) -> None:
        """Test project update failure due to database URL validation."""
        project_id = "1"
        # The validation now happens at Pydantic level, not service level
        # So this test should catch pydantic_core.ValidationError
        from pydantic_core import ValidationError
        
        with pytest.raises(ValidationError) as exc_info:
            project_data = ProjectUpdateSchema(database_url="invalid://url")

        # Check that the validation error mentions supported schemes
        assert "supported schemes" in str(exc_info.value)
        mock_project_repository.update.assert_not_called()
