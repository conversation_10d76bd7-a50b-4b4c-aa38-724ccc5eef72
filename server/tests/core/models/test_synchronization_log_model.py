"""Unit tests for SynchronizationLog and SynchronizationConflict models.

This module contains comprehensive unit tests for the synchronization logging
models, including field validation, relationships, and utility methods.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import MagicMock, patch
from typing import Dict, Any

from src.core.enums.system_enums import (
    ErrorSeverity,
    SyncDirection,
    SyncOperation,
    SyncStatus,
)
from src.core.models.general.synchronization_log import (
    SynchronizationConflict,
    SynchronizationLog,
)
from src.core.utils.datetime_utils import utcnow_aware


class TestSynchronizationLog:
    """Test suite for SynchronizationLog model."""

    def test_synchronization_log_initialization_minimal(self) -> None:
        """Test SynchronizationLog initialization with minimal required fields."""
        sync_log = SynchronizationLog(
            project_id=1,
            operation_type=SyncOperation.INCREMENTAL_SYNC,
            sync_direction=SyncDirection.BIDIRECTIONAL,
        )

        assert sync_log.project_id == 1
        assert sync_log.operation_type == SyncOperation.INCREMENTAL_SYNC
        assert sync_log.sync_direction == SyncDirection.BIDIRECTIONAL
        assert sync_log.status == SyncStatus.PENDING
        assert sync_log.records_processed == 0
        assert sync_log.records_created == 0
        assert sync_log.records_updated == 0
        assert sync_log.records_deleted == 0
        assert sync_log.conflicts_detected == 0
        assert sync_log.conflicts_resolved == 0
        assert sync_log.is_automatic is False
        assert sync_log.is_retry is False
        assert sync_log.is_critical is False
        assert sync_log.retry_count == 0
        assert sync_log.max_retries == 3
        assert isinstance(sync_log.started_at, datetime)

    def test_synchronization_log_initialization_complete(self) -> None:
        """Test SynchronizationLog initialization with all fields."""
        started_at = utcnow_aware()
        completed_at = started_at + timedelta(seconds=30)

        sync_log = SynchronizationLog(
            project_id=2,
            session_id="session_123",
            user_id=10,
            operation_type=SyncOperation.FULL_SYNC,
            sync_direction=SyncDirection.LOCAL_TO_CENTRAL,
            status=SyncStatus.COMPLETED,
            started_at=started_at,
            completed_at=completed_at,
            duration_ms=30000,
            source_database_url="postgresql://local/db",
            target_database_url="postgresql://central/db",
            records_processed=100,
            records_created=10,
            records_updated=85,
            records_deleted=5,
            conflicts_detected=2,
            conflicts_resolved=2,
            error_message=None,
            sync_metadata='{"batch_size": 50}',
            is_automatic=True,
            is_retry=False,
            is_critical=True,
            retry_count=0,
            max_retries=5,
        )

        assert sync_log.project_id == 2
        assert sync_log.session_id == "session_123"
        assert sync_log.user_id == 10
        assert sync_log.operation_type == SyncOperation.FULL_SYNC
        assert sync_log.sync_direction == SyncDirection.LOCAL_TO_CENTRAL
        assert sync_log.status == SyncStatus.COMPLETED
        assert sync_log.started_at == started_at
        assert sync_log.completed_at == completed_at
        assert sync_log.duration_ms == 30000
        assert sync_log.source_database_url == "postgresql://local/db"
        assert sync_log.target_database_url == "postgresql://central/db"
        assert sync_log.records_processed == 100
        assert sync_log.records_created == 10
        assert sync_log.records_updated == 85
        assert sync_log.records_deleted == 5
        assert sync_log.conflicts_detected == 2
        assert sync_log.conflicts_resolved == 2
        assert sync_log.is_automatic is True
        assert sync_log.is_critical is True
        assert sync_log.max_retries == 5

    def test_synchronization_log_str_representation(self) -> None:
        """Test SynchronizationLog string representation."""
        sync_log = SynchronizationLog(
            id=123,
            project_id=456,
            operation_type=SyncOperation.MANUAL_SYNC,
            sync_direction=SyncDirection.CENTRAL_TO_LOCAL,
            status=SyncStatus.IN_PROGRESS,
        )

        str_repr = str(sync_log)

        assert "SynchronizationLog" in str_repr
        assert "id=123" in str_repr
        assert "project_id=456" in str_repr
        assert "operation_type='Manual Sync'" in str_repr
        assert "status='In Progress'" in str_repr

    def test_synchronization_log_is_completed_property(self) -> None:
        """Test is_completed property for various statuses."""
        sync_log = SynchronizationLog(
            project_id=1,
            operation_type=SyncOperation.INCREMENTAL_SYNC,
            sync_direction=SyncDirection.BIDIRECTIONAL,
        )

        # Test pending status
        sync_log.status = SyncStatus.PENDING
        assert sync_log.is_completed is False

        # Test in progress status
        sync_log.status = SyncStatus.IN_PROGRESS
        assert sync_log.is_completed is False

        # Test completed status
        sync_log.status = SyncStatus.COMPLETED
        assert sync_log.is_completed is True

        # Test failed status
        sync_log.status = SyncStatus.FAILED
        assert sync_log.is_completed is True

        # Test cancelled status
        sync_log.status = SyncStatus.CANCELLED
        assert sync_log.is_completed is True

    def test_synchronization_log_is_successful_property(self) -> None:
        """Test is_successful property for various statuses."""
        sync_log = SynchronizationLog(
            project_id=1,
            operation_type=SyncOperation.INCREMENTAL_SYNC,
            sync_direction=SyncDirection.BIDIRECTIONAL,
        )

        # Test completed status (successful)
        sync_log.status = SyncStatus.COMPLETED
        assert sync_log.is_successful is True

        # Test failed status
        sync_log.status = SyncStatus.FAILED
        assert sync_log.is_successful is False

        # Test pending status
        sync_log.status = SyncStatus.PENDING
        assert sync_log.is_successful is False

    def test_synchronization_log_has_conflicts_property(self) -> None:
        """Test has_conflicts property."""
        sync_log = SynchronizationLog(
            project_id=1,
            operation_type=SyncOperation.INCREMENTAL_SYNC,
            sync_direction=SyncDirection.BIDIRECTIONAL,
        )

        # No conflicts
        sync_log.conflicts_detected = 0
        assert sync_log.has_conflicts is False

        # Has conflicts
        sync_log.conflicts_detected = 3
        assert sync_log.has_conflicts is True

    def test_synchronization_log_calculate_duration(self) -> None:
        """Test calculate_duration method."""
        sync_log = SynchronizationLog(
            project_id=1,
            operation_type=SyncOperation.INCREMENTAL_SYNC,
            sync_direction=SyncDirection.BIDIRECTIONAL,
        )

        # No completion time
        assert sync_log.calculate_duration() is None

        # With start and completion times
        started_at = datetime(2024, 1, 1, 12, 0, 0)
        completed_at = datetime(2024, 1, 1, 12, 0, 30)  # 30 seconds later

        sync_log.started_at = started_at
        sync_log.completed_at = completed_at

        duration = sync_log.calculate_duration()
        assert duration == 30000  # 30 seconds in milliseconds

    @patch("src.core.models.general.synchronization_log.utcnow_aware")
    def test_synchronization_log_update_completion_metrics(
        self, mock_utcnow: MagicMock
    ) -> None:
        """Test update_completion_metrics method."""
        mock_now = datetime(2024, 1, 1, 12, 1, 0)
        mock_utcnow.return_value = mock_now

        sync_log = SynchronizationLog(
            project_id=1,
            operation_type=SyncOperation.INCREMENTAL_SYNC,
            sync_direction=SyncDirection.BIDIRECTIONAL,
            started_at=datetime(2024, 1, 1, 12, 0, 0),
            records_processed=120,
        )

        sync_log.update_completion_metrics()

        assert sync_log.completed_at == mock_now
        assert sync_log.duration_ms == 60000  # 60 seconds
        assert sync_log.throughput_records_per_second == 2.0  # 120 records / 60 seconds

    @patch("src.core.models.general.synchronization_log.utcnow_aware")
    def test_synchronization_log_update_completion_metrics_no_records(
        self, mock_utcnow: MagicMock
    ) -> None:
        """Test update_completion_metrics method with no records processed."""
        mock_now = datetime(2024, 1, 1, 12, 1, 0)
        mock_utcnow.return_value = mock_now

        sync_log = SynchronizationLog(
            project_id=1,
            operation_type=SyncOperation.INCREMENTAL_SYNC,
            sync_direction=SyncDirection.BIDIRECTIONAL,
            started_at=datetime(2024, 1, 1, 12, 0, 0),
            records_processed=0,
        )

        sync_log.update_completion_metrics()

        assert sync_log.completed_at == mock_now
        assert sync_log.duration_ms == 60000  # 60 seconds
        assert sync_log.throughput_records_per_second == 0.0


class TestSynchronizationConflict:
    """Test suite for SynchronizationConflict model."""

    def test_synchronization_conflict_initialization_minimal(self) -> None:
        """Test SynchronizationConflict initialization with minimal required fields."""
        conflict = SynchronizationConflict(
            sync_log_id=1,
            entity_type="project",
            entity_id="123",
            table_name="Project",
            conflict_type="field_conflict",
        )

        assert conflict.sync_log_id == 1
        assert conflict.entity_type == "project"
        assert conflict.entity_id == "123"
        assert conflict.table_name == "Project"
        assert conflict.conflict_type == "field_conflict"
        assert conflict.is_resolved is False
        assert conflict.severity == ErrorSeverity.MEDIUM
        assert conflict.requires_manual_intervention is False
        assert isinstance(conflict.detected_at, datetime)

    def test_synchronization_conflict_initialization_complete(self) -> None:
        """Test SynchronizationConflict initialization with all fields."""
        detected_at = utcnow_aware()
        resolved_at = detected_at + timedelta(minutes=15)
        local_timestamp = detected_at - timedelta(hours=1)
        central_timestamp = detected_at - timedelta(minutes=30)

        conflict = SynchronizationConflict(
            sync_log_id=2,
            entity_type="component",
            entity_id="comp_456",
            table_name="Component",
            conflict_type="delete_vs_modify",
            field_name="status",
            local_value="active",
            central_value="deleted",
            local_timestamp=local_timestamp,
            central_timestamp=central_timestamp,
            is_resolved=True,
            resolution_strategy="last_write_wins",
            resolved_value="deleted",
            resolved_at=resolved_at,
            resolved_by_user_id=10,
            severity=ErrorSeverity.HIGH,
            requires_manual_intervention=False,
            conflict_metadata='{"source": "automated"}',
            resolution_notes="Automatically resolved using last write wins strategy",
            detected_at=detected_at,
        )

        assert conflict.sync_log_id == 2
        assert conflict.entity_type == "component"
        assert conflict.entity_id == "comp_456"
        assert conflict.table_name == "Component"
        assert conflict.conflict_type == "delete_vs_modify"
        assert conflict.field_name == "status"
        assert conflict.local_value == "active"
        assert conflict.central_value == "deleted"
        assert conflict.local_timestamp == local_timestamp
        assert conflict.central_timestamp == central_timestamp
        assert conflict.is_resolved is True
        assert conflict.resolution_strategy == "last_write_wins"
        assert conflict.resolved_value == "deleted"
        assert conflict.resolved_at == resolved_at
        assert conflict.resolved_by_user_id == 10
        assert conflict.severity == ErrorSeverity.HIGH
        assert conflict.requires_manual_intervention is False
        assert (
            conflict.resolution_notes
            == "Automatically resolved using last write wins strategy"
        )

    def test_synchronization_conflict_str_representation(self) -> None:
        """Test SynchronizationConflict string representation."""
        conflict = SynchronizationConflict(
            id=789,
            sync_log_id=1,
            entity_type="user",
            entity_id="user_123",
            table_name="User",
            conflict_type="field_conflict",
            is_resolved=False,
        )

        str_repr = str(conflict)

        assert "SynchronizationConflict" in str_repr
        assert "id=789" in str_repr
        assert "entity_type='user'" in str_repr
        assert "entity_id='user_123'" in str_repr
        assert "conflict_type='field_conflict'" in str_repr
        assert "is_resolved=False" in str_repr

    def test_synchronization_conflict_is_critical_property(self) -> None:
        """Test is_critical property for various severities."""
        conflict = SynchronizationConflict(
            sync_log_id=1,
            entity_type="project",
            entity_id="123",
            table_name="Project",
            conflict_type="field_conflict",
        )

        # Test critical severity
        conflict.severity = ErrorSeverity.CRITICAL
        assert conflict.is_critical is True

        # Test high severity
        conflict.severity = ErrorSeverity.HIGH
        assert conflict.is_critical is True

        # Test medium severity
        conflict.severity = ErrorSeverity.MEDIUM
        assert conflict.is_critical is False

        # Test low severity
        conflict.severity = ErrorSeverity.LOW
        assert conflict.is_critical is False

    def test_synchronization_conflict_age_hours_property(self) -> None:
        """Test age_hours property calculation."""
        detected_at = datetime(2024, 1, 1, 12, 0, 0)

        conflict = SynchronizationConflict(
            sync_log_id=1,
            entity_type="project",
            entity_id="123",
            table_name="Project",
            conflict_type="field_conflict",
            detected_at=detected_at,
        )

        # Mock current time to be 2.5 hours later
        with patch(
            "src.core.models.general.synchronization_log.utcnow_aware"
        ) as mock_utcnow:
            mock_utcnow.return_value = datetime(2024, 1, 1, 14, 30, 0)

            age = conflict.age_hours
            assert age == 2.5

    @patch("src.core.models.general.synchronization_log.utcnow_aware")
    def test_synchronization_conflict_resolve_conflict(
        self, mock_utcnow: MagicMock
    ) -> None:
        """Test resolve_conflict method."""
        mock_resolved_at = datetime(2024, 1, 1, 13, 0, 0)
        mock_utcnow.return_value = mock_resolved_at

        conflict = SynchronizationConflict(
            sync_log_id=1,
            entity_type="project",
            entity_id="123",
            table_name="Project",
            conflict_type="field_conflict",
            is_resolved=False,
        )

        conflict.resolve_conflict(
            resolution_strategy="manual_selection",
            resolved_value="corrected_value",
            resolved_by_user_id=20,
            resolution_notes="Manually reviewed and corrected by admin",
        )

        assert conflict.is_resolved is True
        assert conflict.resolution_strategy == "manual_selection"
        assert conflict.resolved_value == "corrected_value"
        assert conflict.resolved_by_user_id == 20
        assert conflict.resolution_notes == "Manually reviewed and corrected by admin"
        assert conflict.resolved_at == mock_resolved_at

    def test_synchronization_conflict_resolve_conflict_minimal(self) -> None:
        """Test resolve_conflict method with minimal parameters."""
        with patch(
            "src.core.models.general.synchronization_log.utcnow_aware"
        ) as mock_utcnow:
            mock_resolved_at = datetime(2024, 1, 1, 13, 0, 0)
            mock_utcnow.return_value = mock_resolved_at

            conflict = SynchronizationConflict(
                sync_log_id=1,
                entity_type="project",
                entity_id="123",
                table_name="Project",
                conflict_type="field_conflict",
                is_resolved=False,
            )

            conflict.resolve_conflict(resolution_strategy="auto_resolve")

            assert conflict.is_resolved is True
            assert conflict.resolution_strategy == "auto_resolve"
            assert conflict.resolved_value is None
            assert conflict.resolved_by_user_id is None
            assert conflict.resolution_notes is None
            assert conflict.resolved_at == mock_resolved_at


class TestSynchronizationEnumsIntegration:
    """Test suite for enum integration in synchronization models."""

    def test_sync_operation_enum_values(self) -> None:
        """Test SyncOperation enum values in model."""
        sync_log = SynchronizationLog(
            project_id=1,
            operation_type=SyncOperation.CONFLICT_RESOLUTION,
            sync_direction=SyncDirection.BIDIRECTIONAL,
        )

        assert sync_log.operation_type.value == "Conflict Resolution"

    def test_sync_direction_enum_values(self) -> None:
        """Test SyncDirection enum values in model."""
        sync_log = SynchronizationLog(
            project_id=1,
            operation_type=SyncOperation.FULL_SYNC,
            sync_direction=SyncDirection.CENTRAL_TO_LOCAL,
        )

        assert sync_log.sync_direction.value == "Central to Local"

    def test_sync_status_enum_values(self) -> None:
        """Test SyncStatus enum values in model."""
        sync_log = SynchronizationLog(
            project_id=1,
            operation_type=SyncOperation.SCHEDULED_SYNC,
            sync_direction=SyncDirection.LOCAL_TO_CENTRAL,
            status=SyncStatus.RETRY_REQUIRED,
        )

        assert sync_log.status.value == "Retry Required"

    def test_error_severity_enum_values(self) -> None:
        """Test ErrorSeverity enum values in conflict model."""
        conflict = SynchronizationConflict(
            sync_log_id=1,
            entity_type="project",
            entity_id="123",
            table_name="Project",
            conflict_type="field_conflict",
            severity=ErrorSeverity.CRITICAL,
        )

        assert conflict.severity.value == "Critical"
