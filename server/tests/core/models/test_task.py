"""Unit tests for Task and TaskAssignment models.

This module contains comprehensive unit tests for the Task and TaskAssignment
database models, testing all functionality including validation, relationships,
and business logic.
"""

import pytest
from datetime import datetime, timedelta
from sqlalchemy.exc import IntegrityError

from src.core.enums import TaskPriority, TaskStatus
from src.core.models.general.task import Task, TaskAssignment
from src.core.models.general.project import Project
from src.core.models.general.user import User


class TestTaskModel:
    """Test suite for Task model functionality."""

    def create_test_project(self, db_session, name="Test Project"):
        """Helper method to create a test project."""
        project = Project(
            name=name,
            project_number=f"PROJ-{name.replace(' ', '-').upper()}",
            description=f"Test project: {name}",
            status="active",
        )
        db_session.add(project)
        db_session.flush()
        return project

    def create_test_user(self, db_session, name="Test User", email="<EMAIL>"):
        """Helper method to create a test user."""
        user = User(
            name=name,
            email=email,
            is_active=True,
        )
        db_session.add(user)
        db_session.flush()
        return user

    def test_task_creation_basic(self, db_session):
        """Test basic task creation with required fields."""
        project = self.create_test_project(db_session)

        task = Task(
            project_id=project.id,
            name="Test Task",
            description="This is a test task",
            priority=TaskPriority.MEDIUM,
            status=TaskStatus.NOT_STARTED,
        )

        db_session.add(task)
        db_session.flush()

        assert task.id is not None
        assert task.task_id is not None
        assert task.project_id == project.id
        assert task.title == "Test Task"
        assert task.description == "This is a test task"
        assert task.priority == TaskPriority.MEDIUM
        assert task.status == TaskStatus.NOT_STARTED
        assert task.due_date is None
        assert task.created_at is not None
        assert task.updated_at is not None
        assert not task.is_deleted

    def test_task_creation_with_due_date(self, db_session):
        """Test task creation with due date."""
        project = self.create_test_project(db_session)
        due_date = datetime.utcnow() + timedelta(days=7)

        task = Task(
            project_id=project.id,
            name="Task with Due Date",
            due_date=due_date,
            priority=TaskPriority.HIGH,
            status=TaskStatus.IN_PROGRESS,
        )

        db_session.add(task)
        db_session.flush()

        assert task.due_date == due_date
        assert task.priority == TaskPriority.HIGH
        assert task.status == TaskStatus.IN_PROGRESS

    def test_task_creation_minimal(self, db_session):
        """Test task creation with minimal required fields."""
        project = self.create_test_project(db_session)

        task = Task(
            project_id=project.id,
            name="Minimal Task",
        )

        db_session.add(task)
        db_session.flush()

        # Check defaults
        assert task.priority == TaskPriority.MEDIUM
        assert task.status == TaskStatus.NOT_STARTED
        assert task.description is None
        assert task.due_date is None

    def test_task_project_relationship(self, db_session):
        """Test task-project relationship."""
        project = self.create_test_project(db_session, "Project with Tasks")

        task1 = Task(
            project_id=project.id,
            name="Task 1",
            priority=TaskPriority.LOW,
        )
        task2 = Task(
            project_id=project.id,
            name="Task 2",
            priority=TaskPriority.HIGH,
        )

        db_session.add_all([task1, task2])
        db_session.flush()

        # Test relationship from task to project
        assert task1.project == project
        assert task2.project == project

        # Test relationship from project to tasks
        assert len(project.tasks) == 2
        assert task1 in project.tasks
        assert task2 in project.tasks

    def test_task_unique_task_id(self, db_session):
        """Test that task_id is unique."""
        project = self.create_test_project(db_session)

        task1 = Task(
            project_id=project.id,
            name="Task 1",
        )
        task2 = Task(
            project_id=project.id,
            name="Task 2",
        )

        db_session.add_all([task1, task2])
        db_session.flush()

        # task_id should be unique for each task
        assert task1.task_id != task2.task_id

    def test_task_invalid_project_id(self, db_session):
        """Test task creation with invalid project_id."""
        task = Task(
            project_id=99999,  # Non-existent project
            name="Invalid Task",
        )

        db_session.add(task)

        with pytest.raises(IntegrityError):
            db_session.flush()

    def test_task_repr(self, db_session):
        """Test task string representation."""
        project = self.create_test_project(db_session)

        task = Task(
            project_id=project.id,
            name="Test Task for Repr",
            status=TaskStatus.IN_PROGRESS,
        )

        db_session.add(task)
        db_session.flush()

        repr_str = repr(task)
        assert "Task(" in repr_str
        assert task.task_id in repr_str
        assert "Test Task for Repr" in repr_str
        assert "In Progress" in repr_str


class TestTaskAssignmentModel:
    """Test suite for TaskAssignment model functionality."""

    def create_test_project(self, db_session, name="Test Project"):
        """Helper method to create a test project."""
        project = Project(
            name=name,
            project_number=f"PROJ-{name.replace(' ', '-').upper()}",
            description=f"Test project: {name}",
            status="active",
        )
        db_session.add(project)
        db_session.flush()
        return project

    def create_test_user(self, db_session, name="Test User", email="<EMAIL>"):
        """Helper method to create a test user."""
        user = User(
            name=name,
            email=email,
            is_active=True,
        )
        db_session.add(user)
        db_session.flush()
        return user

    def create_test_task(self, db_session, project, title="Test Task"):
        """Helper method to create a test task."""
        task = Task(
            project_id=project.id,
            name=title,
            priority=TaskPriority.MEDIUM,
            status=TaskStatus.NOT_STARTED,
        )
        db_session.add(task)
        db_session.flush()
        return task

    def test_task_assignment_creation_basic(self, db_session):
        """Test basic task assignment creation."""
        project = self.create_test_project(db_session)
        user = self.create_test_user(db_session)
        task = self.create_test_task(db_session, project)

        assignment = TaskAssignment(
            task_id=task.id,
            user_id=user.id,
            name=f"Assignment for {task.title}",
        )

        db_session.add(assignment)
        db_session.flush()

        assert assignment.id is not None
        assert assignment.task_id == task.id
        assert assignment.user_id == user.id
        assert assignment.is_active is True
        assert assignment.assigned_at is not None
        assert assignment.assigned_by_user_id is None
        assert assignment.created_at is not None
        assert assignment.updated_at is not None
        assert not assignment.is_deleted

    def test_task_assignment_with_assigned_by(self, db_session):
        """Test task assignment with assigned_by_user_id."""
        project = self.create_test_project(db_session)
        user = self.create_test_user(db_session, "Assigned User", "<EMAIL>")
        assigner = self.create_test_user(db_session, "Assigner User", "<EMAIL>")
        task = self.create_test_task(db_session, project)

        assignment = TaskAssignment(
            task_id=task.id,
            user_id=user.id,
            assigned_by_user_id=assigner.id,
            name=f"Assignment for {task.title}",
        )

        db_session.add(assignment)
        db_session.flush()

        assert assignment.assigned_by_user_id == assigner.id
        assert assignment.assigned_by_user == assigner

    def test_task_assignment_relationships(self, db_session):
        """Test task assignment relationships."""
        project = self.create_test_project(db_session)
        user1 = self.create_test_user(db_session, "User 1", "<EMAIL>")
        user2 = self.create_test_user(db_session, "User 2", "<EMAIL>")
        task = self.create_test_task(db_session, project)

        assignment1 = TaskAssignment(
            task_id=task.id,
            user_id=user1.id,
            name=f"Assignment 1 for {task.title}",
        )
        assignment2 = TaskAssignment(
            task_id=task.id,
            user_id=user2.id,
            name=f"Assignment 2 for {task.title}",
        )

        db_session.add_all([assignment1, assignment2])
        db_session.flush()

        # Test relationships from assignment to task and user
        assert assignment1.task == task
        assert assignment1.user == user1
        assert assignment2.task == task
        assert assignment2.user == user2

        # Test relationships from task to assignments
        assert len(task.assignments) == 2
        assert assignment1 in task.assignments
        assert assignment2 in task.assignments

        # Test relationships from user to assignments
        assert len(user1.task_assignments) == 1
        assert assignment1 in user1.task_assignments
        assert len(user2.task_assignments) == 1
        assert assignment2 in user2.task_assignments

    def test_task_assignment_unique_constraint(self, db_session):
        """Test unique constraint on task_id and user_id."""
        project = self.create_test_project(db_session)
        user = self.create_test_user(db_session)
        task = self.create_test_task(db_session, project)

        assignment1 = TaskAssignment(
            task_id=task.id,
            user_id=user.id,
            name=f"Assignment 1 for {task.title}",
        )
        assignment2 = TaskAssignment(
            task_id=task.id,
            user_id=user.id,  # Same task and user
            name=f"Assignment 2 for {task.title}",
        )

        db_session.add_all([assignment1, assignment2])

        with pytest.raises(IntegrityError):
            db_session.flush()

    def test_task_assignment_repr(self, db_session):
        """Test task assignment string representation."""
        project = self.create_test_project(db_session)
        user = self.create_test_user(db_session)
        task = self.create_test_task(db_session, project)

        assignment = TaskAssignment(
            task_id=task.id,
            user_id=user.id,
            name=f"Assignment for {task.title}",
        )

        db_session.add(assignment)
        db_session.flush()

        repr_str = repr(assignment)
        assert "TaskAssignment(" in repr_str
        assert str(task.id) in repr_str
        assert str(user.id) in repr_str
        assert "True" in repr_str  # is_active
