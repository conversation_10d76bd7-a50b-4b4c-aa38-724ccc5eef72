"""Integration tests for Task API endpoints.

This module contains comprehensive integration tests for the task management
API endpoints, testing request/response validation, authentication, RBAC,
and all CRUD operations.
"""

import pytest
from datetime import datetime, timedelta
from httpx import AsyncClient
from unittest.mock import patch

from src.core.enums import TaskPriority, TaskStatus
from src.core.models.general.task import Task, TaskAssignment
from src.core.models.general.project import Project
from src.core.models.general.user import User


@pytest.mark.asyncio
class TestTaskRoutes:
    """Test suite for task management API endpoints."""

    @pytest.fixture
    def test_task(self, db_session, test_project):
        """Create a test task."""
        task = Task(
            project_id=test_project.id,
            name="Test Task",
            description="Test task description",
            priority=TaskPriority.MEDIUM,
            status=TaskStatus.NOT_STARTED,
        )
        db_session.add(task)
        db_session.flush()
        return task

    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer test-token"}

    @pytest.fixture
    def mock_auth_user(self):
        """Mock authenticated user data."""
        return {"id": 1, "email": "<EMAIL>", "name": "Test User"}

    async def test_create_task_success(self, authenticated_client: AsyncClient, test_project):
        """Test successful task creation."""
        task_data = {
            "project_id": test_project.id,
            "title": "New Task",
            "description": "New task description",
            "priority": "Medium",
            "status": "Not Started",
            "assigned_user_ids": [],
        }

        response = await authenticated_client.post(f"/api/v1/projects/{test_project.id}/tasks/", json=task_data)

        assert response.status_code == 201
        data = response.json()
        assert data["title"] == "New Task"
        assert data["description"] == "New task description"
        assert data["priority"] == "Medium"
        assert data["status"] == "Not Started"
        assert data["project_id"] == test_project.id

    async def test_create_task_invalid_project(self, async_client: AsyncClient, auth_headers, mock_auth_user):
        """Test task creation with invalid project ID."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            task_data = {"project_id": 999, "title": "New Task", "priority": "Medium", "status": "Not Started"}

            response = await async_client.post("/api/v1/projects/999/tasks/", json=task_data, headers=auth_headers)

            assert response.status_code == 404
            data = response.json()
            assert "not found" in data["detail"].lower()

    async def test_create_task_validation_error(
        self, async_client: AsyncClient, test_project, auth_headers, mock_auth_user
    ):
        """Test task creation with validation errors."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            task_data = {
                "project_id": test_project.id,
                "title": "",  # Empty title should fail validation
                "priority": "Invalid Priority",  # Invalid priority
                "status": "Not Started",
            }

            response = await async_client.post(
                f"/api/v1/projects/{test_project.id}/tasks/", json=task_data, headers=auth_headers
            )

            assert response.status_code == 422

    async def test_list_tasks_success(
        self, async_client: AsyncClient, test_project, test_task, auth_headers, mock_auth_user
    ):
        """Test successful task listing."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            response = await async_client.get(f"/api/v1/projects/{test_project.id}/tasks/", headers=auth_headers)

            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)
            assert len(data) >= 1
            assert any(task["task_id"] == test_task.task_id for task in data)

    async def test_list_tasks_with_filters(
        self, async_client: AsyncClient, test_project, test_task, auth_headers, mock_auth_user
    ):
        """Test task listing with filters."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            response = await async_client.get(
                f"/api/v1/projects/{test_project.id}/tasks/?status=Not Started&priority=Medium", headers=auth_headers
            )

            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)

    async def test_list_tasks_with_pagination(
        self, async_client: AsyncClient, test_project, auth_headers, mock_auth_user
    ):
        """Test task listing with pagination."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            response = await async_client.get(
                f"/api/v1/projects/{test_project.id}/tasks/?page=1&size=10", headers=auth_headers
            )

            assert response.status_code == 200
            data = response.json()
            assert isinstance(data, list)

    async def test_get_task_success(
        self, async_client: AsyncClient, test_project, test_task, auth_headers, mock_auth_user
    ):
        """Test successful task retrieval."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            response = await async_client.get(
                f"/api/v1/projects/{test_project.id}/tasks/{test_task.task_id}", headers=auth_headers
            )

            assert response.status_code == 200
            data = response.json()
            assert data["task_id"] == test_task.task_id
            assert data["title"] == test_task.title
            assert data["project_id"] == test_project.id

    async def test_get_task_not_found(self, async_client: AsyncClient, test_project, auth_headers, mock_auth_user):
        """Test retrieval of non-existent task."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            response = await async_client.get(
                f"/api/v1/projects/{test_project.id}/tasks/non-existent-uuid", headers=auth_headers
            )

            assert response.status_code == 404

    async def test_get_task_wrong_project(
        self, async_client: AsyncClient, test_project, test_task, auth_headers, mock_auth_user
    ):
        """Test retrieval of task from wrong project."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            response = await async_client.get(f"/api/v1/projects/999/tasks/{test_task.task_id}", headers=auth_headers)

            assert response.status_code == 404

    async def test_update_task_success(
        self, async_client: AsyncClient, test_project, test_task, auth_headers, mock_auth_user
    ):
        """Test successful task update."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            update_data = {
                "title": "Updated Task",
                "description": "Updated description",
                "priority": "High",
                "status": "In Progress",
            }

            response = await async_client.put(
                f"/api/v1/projects/{test_project.id}/tasks/{test_task.task_id}", json=update_data, headers=auth_headers
            )

            assert response.status_code == 200
            data = response.json()
            assert data["title"] == "Updated Task"
            assert data["description"] == "Updated description"
            assert data["priority"] == "High"
            assert data["status"] == "In Progress"

    async def test_update_task_partial(
        self, async_client: AsyncClient, test_project, test_task, auth_headers, mock_auth_user
    ):
        """Test partial task update."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            update_data = {"status": "In Progress"}

            response = await async_client.put(
                f"/api/v1/projects/{test_project.id}/tasks/{test_task.task_id}", json=update_data, headers=auth_headers
            )

            assert response.status_code == 200
            data = response.json()
            assert data["status"] == "In Progress"
            # Other fields should remain unchanged
            assert data["title"] == test_task.title

    async def test_update_task_not_found(self, async_client: AsyncClient, test_project, auth_headers, mock_auth_user):
        """Test update of non-existent task."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            update_data = {"title": "Updated Task"}

            response = await async_client.put(
                f"/api/v1/projects/{test_project.id}/tasks/non-existent-uuid", json=update_data, headers=auth_headers
            )

            assert response.status_code == 404

    async def test_delete_task_success(
        self, async_client: AsyncClient, test_project, test_task, auth_headers, mock_auth_user
    ):
        """Test successful task deletion."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            response = await async_client.delete(
                f"/api/v1/projects/{test_project.id}/tasks/{test_task.task_id}", headers=auth_headers
            )

            assert response.status_code == 204

    async def test_delete_task_not_found(self, async_client: AsyncClient, test_project, auth_headers, mock_auth_user):
        """Test deletion of non-existent task."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            response = await async_client.delete(
                f"/api/v1/projects/{test_project.id}/tasks/non-existent-uuid", headers=auth_headers
            )

            assert response.status_code == 404

    async def test_assign_users_to_task_success(
        self, async_client: AsyncClient, test_project, test_task, test_user, auth_headers, mock_auth_user
    ):
        """Test successful user assignment to task."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            assignment_data = {"user_ids": [test_user.id]}

            response = await async_client.post(
                f"/api/v1/projects/{test_project.id}/tasks/{test_task.task_id}/assignments",
                json=assignment_data,
                headers=auth_headers,
            )

            assert response.status_code == 200
            data = response.json()
            assert len(data["assignments"]) >= 1

    async def test_unassign_user_from_task_success(
        self, async_client: AsyncClient, test_project, test_task, test_user, auth_headers, mock_auth_user, db_session
    ):
        """Test successful user unassignment from task."""
        # First create an assignment
        assignment = TaskAssignment(
            task_id=test_task.id, user_id=test_user.id, name=f"Assignment for {test_task.title}", is_active=True
        )
        db_session.add(assignment)
        await db_session.flush()

        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            response = await async_client.delete(
                f"/api/v1/projects/{test_project.id}/tasks/{test_task.task_id}/assignments/{test_user.id}",
                headers=auth_headers,
            )

            assert response.status_code == 200

    async def test_get_task_statistics_success(
        self, async_client: AsyncClient, test_project, auth_headers, mock_auth_user
    ):
        """Test successful retrieval of task statistics."""
        with patch("src.core.security.enhanced_dependencies.require_authenticated_user", return_value=mock_auth_user):
            response = await async_client.get(
                f"/api/v1/projects/{test_project.id}/tasks/statistics", headers=auth_headers
            )

            assert response.status_code == 200
            data = response.json()
            assert "total_tasks" in data
            assert "status_counts" in data
            assert "priority_counts" in data
            assert "overdue_count" in data

    async def test_unauthorized_access(self, async_client: AsyncClient, test_project):
        """Test unauthorized access to task endpoints."""
        response = await async_client.get(f"/api/v1/projects/{test_project.id}/tasks/")

        assert response.status_code == 401
