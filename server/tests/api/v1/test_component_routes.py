"""Component API Routes Tests.

This module provides comprehensive tests for component management API endpoints,
including CRUD operations, search functionality, filtering, and error handling.

Key Test Areas:
- Component CRUD operations with validation
- Advanced search and filtering with pagination
- Component categorization and type filtering
- Authentication and authorization testing
- Error handling and edge cases
- Performance and load testing scenarios
- Advanced search endpoints with complex filtering
- Enhanced bulk operations endpoints
- Performance optimization endpoints
- Cache management endpoints
- Error handling and validation
"""

import json
from datetime import datetime
from decimal import Decimal
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, Mock, patch

from fastapi import status
from fastapi.testclient import TestClient
from src.core.enums.electrical_enums import ComponentCategoryType
from src.core.errors.exceptions import (
    BusinessLogicError,
    NotFoundError,
    ValidationError,
)
from src.core.schemas.general.component_schemas import (
    ComponentAdvancedSearchResponseSchema,
    ComponentCreateSchema,
    ComponentReadSchema,
    ComponentSearchSchema,
    ComponentUpdateSchema,
)
from src.core.services.general.component_service import ComponentService


class TestComponentCRUDEndpoints:
    """Test suite for component CRUD operations."""

    async def test_create_component_success(
        self, authenticated_client, sample_component_data: Dict[str, Any], test_project
    ):
        """Test successful component creation."""
        # Make real API call without mocking to test actual functionality
        response = await authenticated_client.post(
            f"/api/v1/projects/{test_project.id}/components/", json=sample_component_data
        )

        assert response.status_code == status.HTTP_201_CREATED
        response_data = response.json()
        assert response_data["name"] == sample_component_data["name"]
        assert response_data["manufacturer"] == sample_component_data["manufacturer"]
        assert response_data["model_number"] == sample_component_data["model_number"]
        assert "id" in response_data

    async def test_create_component_validation_error(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test component creation with validation error."""
        invalid_data = {
            "name": "",  # Invalid: empty name
            "manufacturer": "ABB",
            "model_number": "S203-C16",
            "component_type_id": 999999,  # Invalid: non-existent ID
        }

        # FastAPI will return 422 for validation errors at the request level
        response = await authenticated_client.post(f"/api/v1/projects/{test_project.id}/components/", json=invalid_data)

        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        response_data = response.json()
        assert "detail" in response_data

    async def test_create_component_duplicate_error(
        self,
        authenticated_client,
        sample_component_data: Dict[str, Any],
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test component creation with duplicate error."""
        from src.api.v1.component_routes import get_component_service

        mock_component_service.create_component = AsyncMock(side_effect=BusinessLogicError(detail="Component already exists"))

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.post(f"/api/v1/projects/{test_project.id}/components/", json=sample_component_data)

            assert response.status_code == status.HTTP_409_CONFLICT
            assert "Component already exists" in response.json()["detail"]
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_get_component_success(
        self,
        authenticated_client,
        sample_component_data: Dict[str, Any],
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test successful component retrieval."""
        # Fix the schema data to match ComponentReadSchema expectations
        component_data = sample_component_data.copy()
        # ComponentReadSchema expects 'dimensions' and 'metadata' fields (not _json variants)
        # Keep the original field names as they are correct for the schema

        component = ComponentReadSchema(
            id=1,
            **component_data,
            created_at=datetime.fromisoformat("2024-01-01T00:00:00+00:00"),
            updated_at=datetime.fromisoformat("2024-01-01T00:00:00+00:00"),
        )
        mock_component_service.get_component = AsyncMock(return_value=component)

        # Override dependency using FastAPI app dependency overrides
        from src.api.v1.component_routes import get_component_service
        
        # Use the test client's app and override the dependency
        test_app = authenticated_client._transport.app  # Access the FastAPI app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/1")
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["id"] == 1
            assert response_data["name"] == sample_component_data["name"]
            mock_component_service.get_component.assert_called_once_with(1)

    async def test_get_component_not_found(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test component retrieval with not found error."""
        mock_component_service.get_component = AsyncMock(side_effect=NotFoundError(
            code="COMPONENT_NOT_FOUND", detail="Component with ID 999 was not found"
        ))

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/999")

            assert response.status_code == status.HTTP_404_NOT_FOUND
            assert "not found" in response.json()["detail"].lower()
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_update_component_success(
        self,
        authenticated_client,
        sample_component_data: Dict[str, Any],
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test successful component update."""
        update_data = {"name": "Updated Circuit Breaker", "unit_price": "30.00"}

        # Fix the schema data to match ComponentReadSchema expectations
        component_data = {**sample_component_data, **update_data}
        # ComponentReadSchema expects 'dimensions' and 'metadata' fields (not _json variants)
        # Keep the original field names as they are correct for the schema

        updated_component = ComponentReadSchema(
            id=1,
            **component_data,
            created_at=datetime.fromisoformat("2024-01-01T00:00:00+00:00"),
            updated_at=datetime.fromisoformat("2024-01-01T01:00:00+00:00"),
        )
        mock_component_service.update_component = AsyncMock(return_value=updated_component)

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.put(f"/api/v1/projects/{test_project.id}/components/1", json=update_data)

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["name"] == update_data["name"]
            assert response_data["unit_price"] == update_data["unit_price"]
            mock_component_service.update_component.assert_called_once()
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_delete_component_success(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test successful component deletion."""
        mock_component_service.delete_component = AsyncMock(return_value=True)

        # Override dependency using FastAPI app dependency overrides
        from src.api.v1.component_routes import get_component_service
        
        # Use the test client's app and override the dependency
        test_app = authenticated_client._transport.app  # Access the FastAPI app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.delete(f"/api/v1/projects/{test_project.id}/components/1")
            
            assert response.status_code == status.HTTP_204_NO_CONTENT
            mock_component_service.delete_component.assert_called_once_with(1, deleted_by_user_id=1)
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_delete_component_with_dependencies(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test component deletion with dependency error."""
        mock_component_service.delete_component = AsyncMock(side_effect=BusinessLogicError(
            detail="Component has dependencies and cannot be deleted"
        ))

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.delete(f"/api/v1/projects/{test_project.id}/components/1")

            assert response.status_code == status.HTTP_409_CONFLICT
            assert "dependencies" in response.json()["detail"].lower()
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)


class TestComponentSearchEndpoints:
    """Test suite for component search and filtering."""

    async def test_list_components_success(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        mock_search_result,
        test_project,
    ):
        """Test successful component listing."""
        from src.core.schemas.base_schemas import PaginationSchema
        from src.core.schemas.general.component_schemas import (
            ComponentPaginatedResponseSchema,
            ComponentReadSchema,
            ComponentDimensionsSchema,
        )
        from datetime import datetime

        # Create proper ComponentReadSchema objects instead of dictionaries
        now = datetime.now()
        component_items = []
        for item_data in mock_search_result["items"]:
            # Create ComponentReadSchema objects from mock data
            component_item = ComponentReadSchema(
                id=item_data["id"],
                name=item_data["name"],
                manufacturer=item_data["manufacturer"],
                model_number=item_data["model_number"],
                description=item_data["description"],
                component_type_id=1,  # Required field
                category_id=1,  # Required field
                specifications=item_data.get("specifications", {}),
                unit_price=item_data.get("unit_price", 0.0),
                currency=item_data.get("currency", "EUR"),
                supplier=item_data.get("supplier"),
                part_number=item_data.get("part_number"),
                weight_kg=item_data.get("weight_kg"),
                dimensions=ComponentDimensionsSchema(
                    length=item_data["dimensions"]["length"],
                    width=item_data["dimensions"]["width"],
                    height=item_data["dimensions"]["height"],
                    unit="mm"
                ) if item_data.get("dimensions") else None,
                is_active=item_data.get("is_active", True),
                is_preferred=item_data.get("is_preferred", False),
                stock_status=item_data.get("stock_status", "available"),
                version=item_data.get("version", "1.0"),
                metadata={},
                full_name=f"{item_data['manufacturer']} {item_data['model_number']}",
                display_name=f"{item_data['name']} ({item_data['manufacturer']} {item_data['model_number']})",
                created_at=now,
                updated_at=now,
            )
            component_items.append(component_item)

        # Create proper schema object with ComponentReadSchema objects
        mock_response = ComponentPaginatedResponseSchema(
            items=component_items,
            pagination=PaginationSchema(
                page=mock_search_result["page"],
                size=mock_search_result["size"],
                total=mock_search_result["total"],
                pages=mock_search_result["pages"],
            )
        )
        mock_component_service.search_components = AsyncMock(return_value=mock_response)

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/")

            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert "items" in response_data
            assert "pagination" in response_data
            mock_component_service.search_components.assert_called_once()
            call_args, call_kwargs = mock_component_service.search_components.call_args
            assert call_args[1].page == 1
            assert call_args[1].per_page == 20
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_list_components_with_filters(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        mock_search_result,
        test_project,
    ):
        """Test component listing with filters."""
        from src.core.schemas.base_schemas import PaginationSchema
        from src.core.schemas.general.component_schemas import (
            ComponentPaginatedResponseSchema,
            ComponentReadSchema,
            ComponentDimensionsSchema,
        )
        from datetime import datetime

        # Create proper ComponentReadSchema objects instead of dictionaries
        now = datetime.now()
        component_items = []
        for item_data in mock_search_result["items"]:
            # Create ComponentReadSchema objects from mock data
            component_item = ComponentReadSchema(
                id=item_data["id"],
                name=item_data["name"],
                manufacturer=item_data["manufacturer"],
                model_number=item_data["model_number"],
                description=item_data["description"],
                component_type_id=1,  # Required field
                category_id=1,  # Required field
                specifications=item_data.get("specifications", {}),
                unit_price=item_data.get("unit_price", 0.0),
                currency=item_data.get("currency", "EUR"),
                supplier=item_data.get("supplier"),
                part_number=item_data.get("part_number"),
                weight_kg=item_data.get("weight_kg"),
                dimensions=ComponentDimensionsSchema(
                    length=item_data["dimensions"]["length"],
                    width=item_data["dimensions"]["width"],
                    height=item_data["dimensions"]["height"],
                    unit="mm"
                ) if item_data.get("dimensions") else None,
                is_active=item_data.get("is_active", True),
                is_preferred=item_data.get("is_preferred", False),
                stock_status=item_data.get("stock_status", "available"),
                version=item_data.get("version", "1.0"),
                metadata={},
                full_name=f"{item_data['manufacturer']} {item_data['model_number']}",
                display_name=f"{item_data['name']} ({item_data['manufacturer']} {item_data['model_number']})",
                created_at=now,
                updated_at=now,
            )
            component_items.append(component_item)

        # Create proper schema object with ComponentReadSchema objects
        mock_response = ComponentPaginatedResponseSchema(
            items=component_items,
            pagination=PaginationSchema(
                page=mock_search_result["page"],
                size=mock_search_result["size"],
                total=mock_search_result["total"],
                pages=mock_search_result["pages"],
            )
        )
        mock_component_service.search_components = AsyncMock(return_value=mock_response)

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(
                f"/api/v1/projects/{test_project.id}/components/",
                params={
                    "search_term": "circuit breaker",
                    "category_id": 1,
                    "manufacturer": "ABB",
                    "is_preferred": True,
                    "page": 1,
                    "size": 10,
                },
            )

            assert response.status_code == status.HTTP_200_OK
            mock_component_service.search_components.assert_called_once()

            # Verify search parameters were passed correctly
            call_args, call_kwargs = mock_component_service.search_components.call_args
            search_params = call_args[0]  # First argument
            pagination_params = call_args[1]  # Second argument
            assert search_params.search_term == "circuit breaker"
            assert search_params.category_id == 1
            assert search_params.manufacturer == "ABB"
            assert search_params.is_preferred is True
            assert pagination_params.page == 1
            assert pagination_params.per_page == 10
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_advanced_search_components(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        mock_search_result,
        test_project,
    ):
        """Test advanced component search."""
        from src.core.schemas.base_schemas import PaginationSchema
        from src.core.schemas.general.component_schemas import (
            ComponentAdvancedSearchResponseSchema,
            ComponentSearchResultSchema,
            ComponentReadSchema,
            ComponentDimensionsSchema,
        )

        search_data = {
            "search_term": "circuit breaker",
            "manufacturer": "ABB",
            "is_preferred": False,
            "is_active": True,
            "min_price": 10.0,
            "max_price": 50.0,
            "currency": "EUR",
            "stock_status": "available",
            "specifications": {"electrical": {"current_rating": "16A"}},
        }

        # Create proper schema object for advanced search
        component_schema = ComponentReadSchema(
            id=1,
            manufacturer="ABB",
            model_number="S203-C16",
            name="Circuit Breaker 16A",
            description="Test circuit breaker",
            category_id=1,
            component_type_id=1,
            unit_price=25.99,
            currency="EUR",
            supplier="Test Supplier",
            part_number="TS-CB-001",
            weight_kg=0.5,
            dimensions=ComponentDimensionsSchema(
                length=100, width=50, height=75, diameter=None, unit="mm"
            ),
            specifications={"electrical": {"current_rating": "16A"}},
            is_active=True,
            is_preferred=False,
            stock_status="available",
            version="1.0",
            metadata={},
            full_name="ABB S203-C16",
            display_name="Circuit Breaker 16A (S203-C16)",
            created_at="2024-01-01T00:00:00",
            updated_at="2024-01-01T00:00:00",
        )

        search_result = ComponentSearchResultSchema(
            component=component_schema,
            relevance_score=0.95,
            matched_fields=["name", "manufacturer"],
        )

        mock_response = ComponentAdvancedSearchResponseSchema(
            items=[search_result],
            pagination=PaginationSchema(page=1, size=20, total=1, pages=1),
            search_metadata={
                "total_matches": 1,
                "search_time_ms": 10,
                "filters_applied": [],
            },
            suggestions=[],
        )

        mock_component_service.search_components_with_builder = AsyncMock(return_value=mock_response)

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            # Use POST to the correct advanced search endpoint
            response = await authenticated_client.post(
                f"/api/v1/projects/{test_project.id}/components/search/advanced",
                json=search_data,
                params={"page": 1, "size": 20},
            )

            assert response.status_code == status.HTTP_200_OK
            # Verify the correct method was called
            mock_component_service.search_components_with_builder.assert_called_once()
            call_args, call_kwargs = mock_component_service.search_components_with_builder.call_args
            assert call_args[1].page == 1
            assert call_args[1].per_page == 20
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)


class TestComponentCategoryEndpoints:
    """Test suite for component category and type endpoints."""

    async def test_list_component_categories(self, authenticated_client, mock_auth_user: Dict[str, Any], test_project):
        """Test listing component categories."""
        from src.core.schemas.general.component_category_schemas import (
            ComponentCategoryListResponseSchema,
            ComponentCategorySummarySchema,
        )
        from src.api.v1.component_routes import get_component_category_service
        from src.core.services.general.component_category_service import (
            ComponentCategoryService,
        )

        # Create proper schema with all required fields
        mock_category = ComponentCategorySummarySchema(
            id=1, 
            name="cat1", 
            description="Category 1",
            parent_category_id=None,
            is_active=True,
            component_count=5,
            child_count=0
        )
        mock_response = ComponentCategoryListResponseSchema(
            categories=[mock_category],
            total_count=1,
            page=1,
            page_size=10,
            total_pages=1,
            has_next=False,
            has_previous=False
        )

        mock_service = MagicMock(spec=ComponentCategoryService)
        mock_service.list_categories = AsyncMock(return_value=mock_response)

        # Use FastAPI dependency override pattern
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Create a proper override function that matches the signature
        def override_get_component_category_service(project_id: int, session=None):
            return mock_service
            
        test_app.dependency_overrides[get_component_category_service] = override_get_component_category_service

        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/categories")
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert "categories" in data
            assert isinstance(data["categories"], list)
            assert len(data["categories"]) > 0
            mock_service.list_categories.assert_called_once()
        finally:
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_list_component_types(self, authenticated_client, mock_auth_user: Dict[str, Any], test_project):
        """Test listing component types."""
        from src.core.services.general.component_type_service import (
            ComponentTypeService,
        )
        from src.api.v1.component_routes import get_component_type_service

        mock_service = MagicMock(spec=ComponentTypeService)
        mock_service.get_types_by_category = AsyncMock(return_value=[{"value": "1", "description": "type 1"}])

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_type_service] = lambda project_id=None, session=None: mock_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/types")
            assert response.status_code == status.HTTP_200_OK
            types = response.json()
            assert isinstance(types, list)
            assert len(types) > 0
            mock_service.get_types_by_category.assert_called_once_with(None)
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_list_component_types_filtered_by_category(
        self, authenticated_client, mock_auth_user: Dict[str, Any], test_project
    ):
        """Test listing component types filtered by category."""
        from src.core.services.general.component_type_service import (
            ComponentTypeService,
        )
        from src.api.v1.component_routes import get_component_type_service

        mock_service = MagicMock(spec=ComponentTypeService)
        mock_service.get_types_by_category = AsyncMock(return_value=[{"value": "1", "description": "type 1"}])

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_type_service] = lambda project_id=None, session=None: mock_service
        
        try:
            response = await authenticated_client.get(
                f"/api/v1/projects/{test_project.id}/components/types",
                params={"category_id": 1},
            )
            assert response.status_code == status.HTTP_200_OK
            types = response.json()
            assert isinstance(types, list)
            mock_service.get_types_by_category.assert_called_once_with(1)
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_get_components_by_category(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test getting components by category."""
        mock_component_service.get_components_by_category = AsyncMock(return_value=[])

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/by-category/1")

            assert response.status_code == status.HTTP_200_OK
            components = response.json()
            assert isinstance(components, list)
            mock_component_service.get_components_by_category.assert_called_once()
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_get_components_by_type(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test getting components by type."""
        mock_component_service.get_components_by_type = AsyncMock(return_value=[])

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(
                f"/api/v1/projects/{test_project.id}/components/by-type/1"  # Use type ID instead of enum
            )

            assert response.status_code == status.HTTP_200_OK
            components = response.json()
            assert isinstance(components, list)
            mock_component_service.get_components_by_type.assert_called_once()
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_get_preferred_components(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test getting preferred components."""
        mock_component_service.get_preferred_components = AsyncMock(return_value=[])

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/preferred", params={"skip": 0, "limit": 100})

            assert response.status_code == status.HTTP_200_OK
            components = response.json()
            assert isinstance(components, list)
            mock_component_service.get_preferred_components.assert_called_once_with(0, 100)
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)


class TestComponentStatsEndpoint:
    """Test suite for component statistics endpoint."""

    async def test_get_component_stats(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        mock_auth_user: Dict[str, Any],
        test_project,
    ):
        """Test getting component statistics."""
        from src.core.schemas.general.component_schemas import ComponentStatsSchema

        mock_stats = ComponentStatsSchema(
            total_components=100,
            active_components=95,
            preferred_components=25,
            components_by_category={"protection_devices": 30, "cables": 40},
            components_by_manufacturer={"ABB": 25, "Schneider": 20},
            average_price=Decimal("25.50"),
            price_range={"min": Decimal("5.00"), "max": Decimal("100.00")},
        )
        mock_component_service.get_component_stats = AsyncMock(return_value=mock_stats)

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/stats")

            assert response.status_code == status.HTTP_200_OK
            stats = response.json()
            assert stats["total_components"] == 100
            assert stats["active_components"] == 95
            assert stats["preferred_components"] == 25
            assert "components_by_category" in stats
            assert "components_by_manufacturer" in stats
            mock_component_service.get_component_stats.assert_called_once_with()
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)


class TestComponentAuthenticationAndAuthorization:
    """Test suite for authentication and authorization."""

    def test_create_component_without_auth(self, client, test_project):
        """Test component creation without authentication."""
        component_data = {
            "name": "Test Component",
            "manufacturer": "Test Manufacturer",
            "model_number": "TEST-001",
            "component_type_id": 1,  # Use type ID instead of enum
        }

        response = client.post(f"/api/v1/projects/{test_project.id}/components/", json=component_data)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_get_component_without_auth(self, client, test_project):
        """Test component retrieval without authentication."""
        response = client.get(f"/api/v1/projects/{test_project.id}/components/1")

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    def test_list_components_without_auth(self, client, test_project):
        """Test component listing without authentication."""
        response = client.get(f"/api/v1/projects/{test_project.id}/components/")

        assert response.status_code == status.HTTP_401_UNAUTHORIZED


class TestComponentErrorHandling:
    """Test suite for error handling scenarios."""

    async def test_invalid_component_id_format(self, authenticated_client, mock_component_service: MagicMock, test_project):
        """Test invalid component ID format."""
        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/invalid_id")
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_invalid_pagination_parameters(self, authenticated_client, mock_component_service: MagicMock, test_project):
        """Test invalid pagination parameters."""
        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/", params={"page": 0, "size": -1})
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_invalid_enum_values(self, authenticated_client, mock_component_service: MagicMock, test_project):
        """Test invalid enum values in filters."""
        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(
                f"/api/v1/projects/{test_project.id}/components/",
                params={
                    "category_id": "invalid",  # Invalid: non-integer ID
                    "component_type_id": "invalid",  # Invalid: non-integer ID
                },
            )
            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)


# Performance and load testing scenarios
class TestComponentPerformance:
    """Test suite for performance scenarios."""

    async def test_large_component_list_pagination(self, authenticated_client, mock_component_service: MagicMock, test_project):
        """Test pagination with large component lists."""
        from src.core.schemas.base_schemas import PaginationSchema
        from src.core.schemas.general.component_schemas import (
            ComponentPaginatedResponseSchema,
        )

        # Mock large result set
        mock_result = ComponentPaginatedResponseSchema(
            items=[],
            pagination=PaginationSchema(page=1, size=100, total=10000, pages=100),
        )
        mock_component_service.search_components = AsyncMock(return_value=mock_result)

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/", params={"size": 100})
            assert response.status_code == status.HTTP_200_OK
            response_data = response.json()
            assert response_data["pagination"]["total"] == 10000
            assert response_data["pagination"]["pages"] == 100
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_complex_search_query_performance(
        self, authenticated_client, mock_component_service: MagicMock, test_project
    ):
        """Test performance with complex search queries."""
        from src.core.schemas.base_schemas import PaginationSchema
        from src.core.schemas.general.component_schemas import (
            ComponentAdvancedSearchResponseSchema,
            ComponentPaginatedResponseSchema,
        )

        mock_result = ComponentAdvancedSearchResponseSchema(
            items=[],
            pagination=PaginationSchema(page=1, size=20, total=0, pages=0),
            search_metadata={
                "total_matches": 0,
                "search_time_ms": 10,
                "filters_applied": [],
            },
            suggestions=[],
        )
        mock_component_service.search_components_with_builder = AsyncMock(return_value=mock_result)

        complex_search = {
            "search_term": "high voltage circuit breaker protection device",
            "manufacturer": "ABB",
            "min_price": 100.0,
            "max_price": 1000.0,
            "specifications": {
                "electrical": {
                    "voltage_rating": "400V",
                    "current_rating": "63A",
                    "breaking_capacity": "10kA",
                },
                "standards_compliance": ["IEC-60898-1"],
            },
        }

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.post(f"/api/v1/projects/{test_project.id}/components/search/advanced", json=complex_search)
            assert response.status_code == status.HTTP_200_OK
            mock_component_service.search_components_with_builder.assert_called_once()
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)


class TestAdvancedSearchEndpoints:
    """Test cases for advanced search API endpoints."""

    async def test_advanced_search_endpoint_success(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        sample_advanced_search_request: Dict[str, Any],
        sample_search_response: ComponentAdvancedSearchResponseSchema,
        test_project,
    ):
        """Test successful advanced search endpoint."""
        mock_component_service.search_components_with_builder = AsyncMock(return_value=sample_search_response)

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.post(
                f"/api/v1/projects/{test_project.id}/components/search/advanced",
                json=sample_advanced_search_request,
                params={"page": 1, "size": 20},
            )

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert "items" in response_data
            assert "pagination" in response_data
            assert "search_metadata" in response_data
            assert len(response_data["items"]) == 1
            assert response_data["items"][0]["component"]["manufacturer"] == "Schneider Electric"
            assert response_data["search_metadata"]["search_type"] == "advanced_builder"
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_advanced_search_endpoint_validation_error(
        self, authenticated_client, mock_component_service: MagicMock, test_project
    ):
        """Test advanced search endpoint with validation error."""
        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            # Invalid request data (missing required fields)
            invalid_request = {
                "basic_filters": [
                    {
                        "field": "manufacturer",
                        "operator": "invalid_operator",  # Invalid operator
                        "value": "Schneider Electric",
                    }
                ]
            }

            response = await authenticated_client.post(f"/api/v1/projects/{test_project.id}/components/search/advanced", json=invalid_request)

            assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_relevance_search_endpoint_success(
        self,
        authenticated_client,
        mock_component_service: MagicMock,
        sample_search_response: ComponentAdvancedSearchResponseSchema,
        test_project,
    ):
        """Test successful relevance search endpoint."""
        mock_component_service.search_components_with_relevance = AsyncMock(return_value=sample_search_response)

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(
                f"/api/v1/projects/{test_project.id}/components/search/relevance",
                params={
                    "search_term": "circuit breaker",
                    "search_fields": "name,description",
                    "fuzzy": False,
                    "page": 1,
                    "size": 20,
                },
            )

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert "items" in response_data
            assert len(response_data["items"]) == 1
            assert response_data["items"][0]["relevance_score"] == 0.95
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_relevance_search_endpoint_invalid_fields(
        self, authenticated_client, mock_component_service: MagicMock, test_project
    ):
        """Test relevance search endpoint with invalid search fields."""
        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(
                f"/api/v1/projects/{test_project.id}/components/search/relevance",
                params={
                    "search_term": "circuit breaker",
                    "search_fields": "invalid_field,another_invalid",
                    "page": 1,
                    "size": 20,
                },
            )

            assert response.status_code == status.HTTP_400_BAD_REQUEST
            response_data = response.json()
            assert "Invalid search fields" in response_data["detail"]
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)


class TestEnhancedBulkOperationsEndpoints:
    """Test cases for enhanced bulk operations API endpoints."""

    async def test_enhanced_bulk_create_endpoint_success(
        self,
        authenticated_client,
        sample_bulk_create_request: List[Dict[str, Any]],
        test_project,
    ):
        """Test successful enhanced bulk create endpoint."""
        # Create mock service instance
        mock_service_instance = Mock(spec=ComponentService)
        mock_service_instance.bulk_create_with_validation = AsyncMock(return_value={
            "total_processed": 2,
            "created": 2,
            "errors": 0,
            "success_rate": 1.0,
            "created_components": [],
            "validation_errors": [],
        })

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_service_instance
        
        try:
            response = await authenticated_client.post(
                f"/api/v1/projects/{test_project.id}/components/bulk/create-validated",
                json=sample_bulk_create_request,
                params={"validate_duplicates": True, "batch_size": 100},
            )

            assert response.status_code == status.HTTP_201_CREATED

            response_data = response.json()
            assert response_data["total_processed"] == 2
            assert response_data["created"] == 2
            assert response_data["errors"] == 0
            assert response_data["success_rate"] == 1.0
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_selective_bulk_update_endpoint_success(
        self, authenticated_client, mock_component_service: MagicMock, test_project
    ):
        """Test successful selective bulk update endpoint."""
        update_data = [
            {"id": 1, "unit_price": 28.99, "is_preferred": True},
            {"id": 2, "description": "Updated description"},
        ]

        mock_component_service.bulk_update_selective = AsyncMock(return_value={
            "total_processed": 2,
            "updated": 2,
            "errors": 0,
            "success_rate": 1.0,
            "validation_errors": [],
        })

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.put(
                f"/api/v1/projects/{test_project.id}/components/bulk/update-selective",
                json=update_data,
                params={"batch_size": 100},
            )

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert response_data["total_processed"] == 2
            assert response_data["updated"] == 2
            assert response_data["success_rate"] == 1.0
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_bulk_delete_endpoint_success(self, authenticated_client, mock_component_service: MagicMock, test_project):
        """Test successful bulk delete endpoint."""
        component_ids = [1, 2, 3, 4, 5]

        mock_component_service.bulk_delete_components = AsyncMock(return_value={
            "total_processed": 5,
            "deleted": 5,
            "not_found": 0,
            "success_rate": 1.0,
            "not_found_ids": [],
            "delete_type": "soft",
        })

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            # For DELETE with body, we need to use a different approach
            # The endpoint expects component_ids as a request body parameter
            response = await authenticated_client.request(
                "DELETE",
                f"/api/v1/projects/{test_project.id}/components/bulk/delete",
                json=component_ids,
                params={"soft_delete": True},
            )

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert response_data["total_processed"] == 5
            assert response_data["deleted"] == 5
            assert response_data["delete_type"] == "soft"
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)


class TestPerformanceOptimizationEndpoints:
    """Test cases for performance optimization API endpoints."""

    async def test_get_performance_metrics_endpoint(
        self, authenticated_client, mock_component_service: MagicMock, test_project
    ):
        """Test get performance metrics endpoint."""
        mock_component_service.get_performance_metrics = AsyncMock(return_value={
            "component_statistics": {
                "total_components": 1000,
                "active_components": 950,
                "preferred_components": 100,
            },
            "cache_performance": {"memory_cache_size": 50, "redis_connected": True},
            "query_performance": {"slow_queries_count": 2, "slowest_query_time": 1.5},
            "system_health": {
                "database_connected": True,
                "cache_connected": True,
                "performance_monitoring_active": True,
            },
        })

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}/components/performance/metrics")

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert "component_statistics" in response_data
            assert "cache_performance" in response_data
            assert "query_performance" in response_data
            assert "system_health" in response_data
            assert response_data["component_statistics"]["total_components"] == 1000
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_optimize_system_performance_endpoint(
        self, authenticated_client, mock_component_service: MagicMock, test_project
    ):
        """Test optimize system performance endpoint."""
        mock_component_service.optimize_system_performance = AsyncMock(return_value={
            "cache_warming": True,
            "cache_cleanup": True,
            "index_analysis": True,
            "query_optimization": True,
            "errors": [],
            "index_suggestions": [
                "CREATE INDEX CONCURRENTLY idx_components_manufacturer_model ON components(manufacturer, model_number);"
            ],
        })

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.post(f"/api/v1/projects/{test_project.id}/components/performance/optimize")

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert response_data["cache_warming"] == True
            assert response_data["cache_cleanup"] == True
            assert response_data["index_analysis"] == True
            assert len(response_data["errors"]) == 0
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)

    async def test_invalidate_cache_endpoint(self, authenticated_client, mock_component_service: MagicMock, test_project):
        """Test invalidate cache endpoint."""
        mock_component_service.invalidate_component_cache = AsyncMock(return_value={
            "success": True,
            "component_id": 123,
            "scope": "specific",
            "timestamp": "2024-01-01T00:00:00Z",
        })

        from src.api.v1.component_routes import get_component_service

        # Use FastAPI dependency override pattern for httpx.AsyncClient
        test_app = authenticated_client._transport.app
        original_override = test_app.dependency_overrides.copy()
        
        # Override the dependency to return our mock
        test_app.dependency_overrides[get_component_service] = lambda project_id=None, session=None: mock_component_service
        
        try:
            response = await authenticated_client.delete(f"/api/v1/projects/{test_project.id}/components/cache/invalidate", params={"component_id": 123})

            assert response.status_code == status.HTTP_200_OK

            response_data = response.json()
            assert response_data["success"] == True
            assert response_data["component_id"] == 123
            assert response_data["scope"] == "specific"
        finally:
            # Restore original overrides
            test_app.dependency_overrides.clear()
            test_app.dependency_overrides.update(original_override)
