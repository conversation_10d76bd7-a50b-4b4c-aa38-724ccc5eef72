"""Tests for project management API endpoints."""

import httpx
from fastapi.testclient import TestClient


class TestProjectMemberRoutes:
    """Test suite for project member management endpoints."""

    async def test_add_project_member_success(
        self, authenticated_client: httpx.AsyncClient, test_project, test_user, test_user_role
    ):
        """Test successfully adding a member to a project."""
        member_data = {"name": "Test Member", "user_id": test_user.id, "role_id": test_user_role.id}
        response = await authenticated_client.post(
            f"/api/v1/projects/{test_project.id}/members", json=member_data
        )
        assert response.status_code == 201
        data = response.json()
        assert data["user_id"] == test_user.id
        assert data["project_id"] == test_project.id
        assert data["role_id"] == test_user_role.id

    async def test_add_project_member_project_not_found(
        self, authenticated_client: httpx.AsyncClient, test_user, test_user_role
    ):
        """Test adding a member to a non-existent project."""
        member_data = {"name": "Test Member", "user_id": test_user.id, "role_id": test_user_role.id}
        response = await authenticated_client.post(
            "/api/v1/projects/999/members", json=member_data
        )
        assert response.status_code == 404

    async def test_add_project_member_user_not_found(
        self, authenticated_client: httpx.AsyncClient, test_project, test_user_role
    ):
        """Test adding a non-existent user to a project."""
        member_data = {"name": "Test Member", "user_id": 999, "role_id": test_user_role.id}
        response = await authenticated_client.post(
            f"/api/v1/projects/{test_project.id}/members", json=member_data
        )
        assert response.status_code == 404

    async def test_add_project_member_duplicate_entry(
        self, authenticated_client: httpx.AsyncClient, test_project_member
    ):
        """Test adding a user who is already a member of the project."""
        member_data = {
            "user_id": test_project_member.user_id,
            "role_id": test_project_member.role_id,
        }
        response = await authenticated_client.post(
            f"/api/v1/projects/{test_project_member.project_id}/members",
            json=member_data,
        )
        assert response.status_code == 409

    async def test_remove_project_member_success(
        self, authenticated_client: httpx.AsyncClient, test_project_member
    ):
        """Test successfully removing a member from a project."""
        response = await authenticated_client.delete(
            f"/api/v1/projects/{test_project_member.project_id}/members/{test_project_member.user_id}"
        )
        assert response.status_code == 204

    async def test_remove_project_member_not_found(
        self, authenticated_client: httpx.AsyncClient, test_project
    ):
        """Test removing a non-existent member from a project."""
        response = await authenticated_client.delete(
            f"/api/v1/projects/{test_project.id}/members/999"
        )
        assert response.status_code == 404

    async def test_update_project_member_success(
        self, authenticated_client: httpx.AsyncClient, test_project_member, test_user_role
    ):
        """Test successfully updating a project member's role."""
        update_data = {"role_id": test_user_role.id}
        response = await authenticated_client.put(
            f"/api/v1/projects/members/{test_project_member.id}", json=update_data
        )
        assert response.status_code == 200
        data = response.json()
        assert data["role_id"] == test_user_role.id

    async def test_update_project_member_not_found(
        self, authenticated_client: httpx.AsyncClient, test_user_role
    ):
        """Test updating a non-existent project member."""
        update_data = {"role_id": test_user_role.id}
        response = await authenticated_client.put(
            "/api/v1/projects/members/999", json=update_data
        )
        assert response.status_code == 404

    async def test_list_project_members_success(
        self, authenticated_client: httpx.AsyncClient, test_project_member
    ):
        """Test successfully listing all members of a project."""
        response = await authenticated_client.get(
            f"/api/v1/projects/{test_project_member.project_id}/members"
        )
        assert response.status_code == 200
        data = response.json()
        assert isinstance(data, list)
        assert len(data) > 0

    async def test_list_project_members_project_not_found(
        self, authenticated_client: httpx.AsyncClient
    ):
        """Test listing the members of a non-existent project."""
        response = await authenticated_client.get("/api/v1/projects/999/members")
        assert response.status_code == 404


class TestProjectRoutes:
    """Test suite for project management endpoints."""

    async def test_create_project_success(self, authenticated_client: httpx.AsyncClient, test_user):
        """Test successfully creating a project."""
        project_data = {
            "name": "New Project",
            "description": "New Project Description",
        }
        response = await authenticated_client.post("/api/v1/projects/", json=project_data)
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "New Project"
        assert data["description"] == "New Project Description"

    async def test_get_project_success(self, authenticated_client: httpx.AsyncClient, test_project):
        """Test successfully getting a project."""
        response = await authenticated_client.get(f"/api/v1/projects/{test_project.id}")
        assert response.status_code == 200
        data = response.json()
        assert data["id"] == test_project.id
        assert data["name"] == test_project.name

    async def test_get_project_not_found(self, authenticated_client: httpx.AsyncClient):
        """Test getting a non-existent project."""
        response = await authenticated_client.get("/api/v1/projects/999")
        assert response.status_code == 404

    async def test_update_project_success(
        self, authenticated_client: httpx.AsyncClient, test_project
    ):
        """Test successfully updating a project."""
        update_data = {"name": "Updated Project"}
        response = await authenticated_client.put(
            f"/api/v1/projects/{test_project.id}", json=update_data
        )
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == "Updated Project"

    async def test_update_project_not_found(self, authenticated_client: httpx.AsyncClient):
        """Test updating a non-existent project."""
        update_data = {"name": "Updated Project"}
        response = await authenticated_client.put("/api/v1/projects/999", json=update_data)
        assert response.status_code == 404

    async def test_delete_project_success(
        self, authenticated_client: httpx.AsyncClient, test_project
    ):
        """Test successfully deleting a project."""
        response = await authenticated_client.delete(f"/api/v1/projects/{test_project.id}")
        assert response.status_code == 204

    async def test_delete_project_not_found(self, authenticated_client: httpx.AsyncClient):
        """Test deleting a non-existent project."""
        response = await authenticated_client.delete("/api/v1/projects/999")
        assert response.status_code == 404

    async def test_get_projects_success(self, authenticated_client: httpx.AsyncClient):
        """Test successfully getting a list of projects."""
        response = await authenticated_client.get("/api/v1/projects/")
        assert response.status_code == 200
        data = response.json()
        # The response should be a paginated object with items and pagination keys
        assert isinstance(data, dict)
        assert "items" in data
        assert "pagination" in data
        assert isinstance(data["items"], list)

    async def test_create_project_with_empty_name_returns_422(
        self, authenticated_client: httpx.AsyncClient
    ):
        """
        Tests that creating a project with an empty name returns a 422 Unprocessable Entity error.
        """
        # Arrange: Project data with an empty name
        invalid_project_data = {
            "name": "",
            "project_number": "API-TEST-001",
            "description": "A project with an invalid name",
        }

        # Act
        response = await authenticated_client.post(
            "/api/v1/projects/", json=invalid_project_data
        )

        # Assert
        assert response.status_code == 422

    async def test_create_and_update_project_with_offline_status(
        self, authenticated_client: httpx.AsyncClient
    ):
        """Test creating and updating a project with offline status."""
        # Create project with offline status
        project_data = {
            "name": "Offline Project",
            "description": "A project that is offline",
            "is_offline": True,
        }
        response = await authenticated_client.post("/api/v1/projects/", json=project_data)
        assert response.status_code == 201
        data = response.json()
        assert data["name"] == "Offline Project"
        assert data["is_offline"] is True
        project_id = data["id"]

        # Update project to be online
        update_data = {"is_offline": False}
        response = await authenticated_client.put(
            f"/api/v1/projects/{project_id}", json=update_data
        )
        assert response.status_code == 200
        data = response.json()
        assert data["is_offline"] is False
