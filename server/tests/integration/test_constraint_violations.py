"""
Comprehensive Constraint Violation Testing

This module tests all database constraints to ensure proper enforcement
of data integrity rules at the database level.
"""

import pytest
import uuid
from sqlalchemy.exc import IntegrityError
from sqlalchemy.orm import Session

from src.core.errors.exceptions import DuplicateEntryError, DataValidationError
from src.core.models.general.user import User
from src.core.models.general.project import Project, ProjectMember
from src.core.models.general.user_role import UserRole

pytestmark = [pytest.mark.integration]


class TestPrimaryKeyConstraints:
    """Test primary key constraint enforcement."""

    def test_user_id_uniqueness(self, db_session: Session):
        """Test that user IDs are automatically unique."""
        unique_suffix = str(uuid.uuid4())[:8]

        # Create two users
        user1 = User()
        user1.name = f"User One {unique_suffix}"
        user1.email = f"user1.{unique_suffix}@example.com"
        user1.password_hash = "password123"
        db_session.add(user1)

        user2 = User()
        user2.name = f"User Two {unique_suffix}"
        user2.email = f"user2.{unique_suffix}@example.com"
        user2.password_hash = "password123"
        db_session.add(user2)

        db_session.commit()

        # Verify they have different IDs
        assert user1.id != user2.id
        assert user1.id is not None
        assert user2.id is not None


class TestUniqueConstraints:
    """Test unique constraint enforcement across all models."""

    def test_user_email_uniqueness_variations(self, db_session: Session):
        """Test email uniqueness with various formats."""
        base_email = f"unique.{uuid.uuid4().hex}@example.com"

        # Create first user
        user1 = User()
        user1.name = "User One"
        user1.email = base_email
        user1.password_hash = "password123"
        db_session.add(user1)
        db_session.commit()

        # Test exact duplicate
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            user2 = User()
            user2.name = "User Two"
            user2.email = base_email  # Exact same email
            user2.password_hash = "password456"
            db_session.add(user2)
            db_session.commit()

    def test_project_name_uniqueness(self, db_session: Session):
        """Test project name uniqueness constraint."""
        unique_suffix = str(uuid.uuid4())[:8]
        project_name = f"Unique Project {unique_suffix}"

        # Create first project
        project1 = Project()
        project1.name = project_name
        project1.project_number = f"PROJ1-{unique_suffix}"
        db_session.add(project1)
        db_session.commit()

        # Try to create second project with same name
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            project2 = Project()
            project2.name = project_name  # Duplicate name
            project2.project_number = f"PROJ2-{unique_suffix}"
            db_session.add(project2)
            db_session.commit()

    def test_project_number_uniqueness(self, db_session: Session):
        """Test project number uniqueness constraint."""
        unique_suffix = str(uuid.uuid4())[:8]
        project_number = f"UNIQUE-{unique_suffix}"

        # Create first project
        project1 = Project()
        project1.name = f"Project One {unique_suffix}"
        project1.project_number = project_number
        db_session.add(project1)
        db_session.commit()

        # Try to create second project with same number
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            project2 = Project()
            project2.name = f"Project Two {unique_suffix}"
            project2.project_number = project_number  # Duplicate number
            db_session.add(project2)
            db_session.commit()

    def test_user_role_name_uniqueness(self, db_session: Session):
        """Test user role name uniqueness constraint."""
        unique_suffix = str(uuid.uuid4())[:8]
        role_name = f"UNIQUE_ROLE_{unique_suffix}"

        # Create first role
        role1 = UserRole()
        role1.name = role_name
        role1.description = "First role"
        db_session.add(role1)
        db_session.commit()

        # Try to create second role with same name
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            role2 = UserRole()
            role2.name = role_name  # Duplicate name
            role2.description = "Second role"
            db_session.add(role2)
            db_session.commit()


class TestNotNullConstraints:
    """Test NOT NULL constraint enforcement."""

    def test_user_required_fields(self, db_session: Session):
        """Test that required user fields cannot be NULL."""
        # Test NULL name
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            user = User()
            user.email = f"null.name.{uuid.uuid4().hex}@example.com"
            user.password_hash = "password123"
            user.name = None  # NULL name should fail
            db_session.add(user)
            db_session.commit()

        db_session.rollback()

        # Test NULL email
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            user = User()
            user.name = "Valid Name"
            user.email = None  # NULL email should fail
            user.password_hash = "password123"
            db_session.add(user)
            db_session.commit()

    def test_project_required_fields(self, db_session: Session):
        """Test that required project fields cannot be NULL."""
        unique_suffix = str(uuid.uuid4())[:8]

        # Test NULL name
        with pytest.raises((IntegrityError, DuplicateEntryError, DataValidationError)):
            project = Project()
            project.name = None  # NULL name should fail
            project.project_number = f"NULL-NAME-{unique_suffix}"
            db_session.add(project)
            db_session.commit()

        db_session.rollback()

        # Test NULL project_number
        with pytest.raises((IntegrityError, DuplicateEntryError, DataValidationError)):
            project = Project()
            project.name = f"Valid Name {unique_suffix}"
            project.project_number = None  # NULL project_number should fail
            db_session.add(project)
            db_session.commit()

    def test_user_role_required_fields(self, db_session: Session):
        """Test that required user role fields cannot be NULL."""
        unique_suffix = str(uuid.uuid4())[:8]

        # Test NULL name
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            role = UserRole()
            role.name = None  # NULL name should fail
            role.description = f"Valid description {unique_suffix}"
            db_session.add(role)
            db_session.commit()


class TestForeignKeyConstraints:
    """Test foreign key constraint enforcement."""

    def test_project_member_invalid_user_id(self, db_session: Session):
        """Test project member with invalid user ID."""
        unique_suffix = str(uuid.uuid4())[:8]

        # Create valid project and role
        project = Project()
        project.name = f"FK Test Project {unique_suffix}"
        project.project_number = f"FK-{unique_suffix}"
        db_session.add(project)

        role = UserRole()
        role.name = f"FK_TEST_ROLE_{unique_suffix}"
        role.description = "Role for FK testing"
        db_session.add(role)

        db_session.commit()

        # Try to create project member with non-existent user
        try:
            member = ProjectMember()
            member.user_id = 99999  # Non-existent user
            member.project_id = project.id
            member.role_id = role.id
            member.name = f"Invalid FK Member {unique_suffix}"
            db_session.add(member)
            db_session.commit()

            assert member.user_id == 99999

        except (IntegrityError, DuplicateEntryError):
            db_session.rollback()

    def test_project_member_invalid_project_id(self, db_session: Session, test_user):
        """Test project member with invalid project ID."""
        unique_suffix = str(uuid.uuid4())[:8]

        # Create valid role
        role = UserRole()
        role.name = f"FK_TEST_ROLE_{unique_suffix}"
        role.description = "Role for FK testing"
        db_session.add(role)
        db_session.commit()

        # Try to create project member with non-existent project
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            member = ProjectMember()
            member.user_id = test_user.id
            member.project_id = 99999  # Non-existent project
            member.role_id = role.id
            member.name = f"Invalid FK Member {unique_suffix}"
            db_session.add(member)
            db_session.commit()

    def test_project_member_invalid_role_id(
        self, db_session: Session, test_user, test_project
    ):
        """Test project member with invalid role ID."""
        unique_suffix = str(uuid.uuid4())[:8]

        # Try to create project member with non-existent role
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            member = ProjectMember()
            member.user_id = test_user.id
            member.project_id = test_project.id
            member.role_id = 99999  # Non-existent role
            member.name = f"Invalid FK Member {unique_suffix}"
            db_session.add(member)
            db_session.commit()


class TestCheckConstraints:
    """Test CHECK constraint enforcement (if any)."""

    def test_user_email_format_validation(self, db_session: Session):
        """Test email format validation if implemented."""
        unique_suffix = str(uuid.uuid4())[:8]

        # Test various invalid email formats
        invalid_emails = [
            "not-an-email",
            "@missing-local.com",
            "missing-at-sign.com",
            "spaces <EMAIL>",
            "",
        ]

        for invalid_email in invalid_emails:
            try:
                user = User()
                user.name = f"Test User {unique_suffix}"
                user.email = invalid_email
                user.password_hash = "password123"
                db_session.add(user)
                db_session.commit()

                # If we reach here, the email was accepted
                # Clean up for next iteration
                db_session.delete(user)
                db_session.commit()

            except (IntegrityError, DuplicateEntryError, DataValidationError):
                # Email validation caught the invalid format
                db_session.rollback()
                continue


class TestComplexConstraintCombinations:
    """Test combinations of constraints working together."""

    def test_unique_and_not_null_combination(self, db_session: Session):
        """Test that unique and NOT NULL constraints work together."""
        unique_suffix = str(uuid.uuid4())[:8]

        # First, create a valid user
        user1 = User()
        user1.name = f"Valid User {unique_suffix}"
        user1.email = f"valid.{unique_suffix}@example.com"
        user1.password_hash = "password123"
        db_session.add(user1)
        db_session.commit()

        # Try to create user with NULL email (should fail NOT NULL)
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            user2 = User()
            user2.name = f"NULL Email User {unique_suffix}"
            user2.email = None  # NULL email
            user2.password_hash = "password123"
            db_session.add(user2)
            db_session.commit()

        db_session.rollback()

        # Try to create user with duplicate email (should fail UNIQUE)
        with pytest.raises((IntegrityError, DuplicateEntryError)):
            user3 = User()
            user3.name = f"Duplicate Email User {unique_suffix}"
            user3.email = user1.email  # Duplicate email
            user3.password_hash = "password123"
            db_session.add(user3)
            db_session.commit()

    def test_foreign_key_cascade_behavior(self, db_session: Session):
        """Test foreign key cascade behavior."""
        unique_suffix = str(uuid.uuid4())[:8]

        # Create user, project, and role
        user = User()
        user.name = f"Cascade User {unique_suffix}"
        user.email = f"cascade.{unique_suffix}@example.com"
        user.password_hash = "password123"
        db_session.add(user)

        project = Project()
        project.name = f"Cascade Project {unique_suffix}"
        project.project_number = f"CASC-{unique_suffix}"
        db_session.add(project)

        role = UserRole()
        role.name = f"CASCADE_ROLE_{unique_suffix}"
        role.description = "Role for cascade testing"
        db_session.add(role)

        db_session.commit()

        # Create project member
        member = ProjectMember()
        member.user_id = user.id
        member.project_id = project.id
        member.role_id = role.id
        member.name = f"Cascade Member {unique_suffix}"
        db_session.add(member)
        db_session.commit()

        # Try to delete the project (should handle cascade or prevent deletion)
        try:
            db_session.delete(project)
            db_session.commit()

            # If deletion succeeded, verify member was handled appropriately
            member_after = db_session.get(ProjectMember, member.id)
            # The behavior depends on cascade configuration

        except IntegrityError:
            # If deletion failed, that's acceptable - referential integrity working
            db_session.rollback()

            # Verify project still exists
            project_after = db_session.get(Project, project.id)
            assert project_after is not None
