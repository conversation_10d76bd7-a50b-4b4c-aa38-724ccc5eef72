"""Integration tests for SynchronizationService conflict resolution integration.

This module contains comprehensive integration tests for the conflict resolution
functionality integrated into the main synchronize_project method, focusing on
scenarios where conflicting changes exist in both local and central databases.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Dict, Any, List

from src.core.services.general.synchronization_service import SynchronizationService
from src.core.database.connection_manager import DynamicConnectionManager
from src.core.repositories.general.project_repository import ProjectRepository


# Helper class for async context manager mocking
class AsyncMockContext:
    def __init__(self, mock_session):
        self.mock_session = mock_session

    async def __aenter__(self):
        return self.mock_session

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        return None


class TestSynchronizationServiceConflictIntegration:
    """Test suite for SynchronizationService conflict resolution integration."""

    @pytest.fixture
    def mock_connection_manager(self) -> MagicMock:
        """Create a mock DynamicConnectionManager."""
        manager = MagicMock(spec=DynamicConnectionManager)
        manager.get_session = AsyncMock()
        return manager

    @pytest.fixture
    def mock_project_repository(self) -> AsyncMock:
        """Create a mock ProjectRepository."""
        return AsyncMock(spec=ProjectRepository)

    @pytest.fixture
    def sync_service(
        self, mock_connection_manager: MagicMock, mock_project_repository: AsyncMock
    ) -> SynchronizationService:
        """Create a SynchronizationService instance with mocked dependencies."""
        return SynchronizationService(mock_connection_manager, mock_project_repository)

    @pytest.fixture
    def mock_local_session(self) -> AsyncMock:
        """Create a mock local database session."""
        session = AsyncMock()
        session.commit = AsyncMock()
        return session

    @pytest.fixture
    def mock_central_session(self) -> AsyncMock:
        """Create a mock central database session."""
        session = AsyncMock()
        session.commit = AsyncMock()
        return session

    @pytest.fixture
    def conflicting_local_changes(self) -> List[Dict[str, Any]]:
        """Create local changes that conflict with central changes."""
        return [
            {
                "entity_type": "project",
                "entity_id": 123,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 14, 0, 0),  # More recent
                "new_values": {"name": "Local Project Name"},
                "source": "local",
            },
            {
                "entity_type": "component",
                "entity_id": 456,
                "operation": "delete",
                "timestamp": datetime(2024, 1, 1, 12, 0, 0),
                "new_values": {},
                "source": "local",
            },
            {
                "entity_type": "user",
                "entity_id": 789,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 10, 0, 0),  # Older
                "new_values": {"email": "<EMAIL>"},
                "source": "local",
            },
        ]

    @pytest.fixture
    def conflicting_central_changes(self) -> List[Dict[str, Any]]:
        """Create central changes that conflict with local changes."""
        return [
            {
                "entity_type": "project",
                "entity_id": 123,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 12, 0, 0),  # Older
                "new_values": {"name": "Central Project Name"},
                "source": "central",
            },
            {
                "entity_type": "component",
                "entity_id": 456,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 13, 0, 0),
                "new_values": {"description": "Modified centrally"},
                "source": "central",
            },
            {
                "entity_type": "user",
                "entity_id": 789,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 15, 0, 0),  # More recent
                "new_values": {"email": "<EMAIL>"},
                "source": "central",
            },
        ]

    @pytest.fixture
    def non_conflicting_changes(
        self,
    ) -> tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """Create non-conflicting local and central changes."""
        local_changes = [
            {
                "entity_type": "project",
                "entity_id": 100,
                "operation": "create",
                "timestamp": datetime(2024, 1, 1, 12, 0, 0),
                "new_values": {"name": "Local Only Project"},
                "source": "local",
            }
        ]

        central_changes = [
            {
                "entity_type": "component",
                "entity_id": 200,
                "operation": "create",
                "timestamp": datetime(2024, 1, 1, 13, 0, 0),
                "new_values": {"name": "Central Only Component"},
                "source": "central",
            }
        ]

        return local_changes, central_changes

    @pytest.mark.asyncio
    async def test_synchronize_project_with_conflicts_last_write_wins(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_local_session: AsyncMock,
        mock_central_session: AsyncMock,
        conflicting_local_changes: List[Dict[str, Any]],
        conflicting_central_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project with conflicting changes using last-write-wins strategy."""
        project_id = 123
        last_sync_timestamp = datetime(2024, 1, 1, 9, 0, 0)

        # Mock the internal methods
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(
                sync_service,
                "_get_local_changes",
                return_value=conflicting_local_changes,
            ),
            patch.object(
                sync_service,
                "_get_central_changes",
                return_value=conflicting_central_changes,
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                side_effect=[
                    {
                        "created": 1,
                        "updated": 0,
                        "deleted": 1,
                        "errors": 0,
                    },  # local-to-central (1 local wins, 1 local loses)
                    {
                        "created": 0,
                        "updated": 1,
                        "deleted": 0,
                        "errors": 0,
                    },  # central-to-local (1 central wins)
                ],
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.side_effect = [
                AsyncMockContext(mock_central_session),
                AsyncMockContext(mock_local_session),
            ]

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify conflict detection and resolution
            assert result["project_id"] == project_id
            assert result["status"] == "completed"
            assert result["conflicts_detected"] == 3  # All 3 entities have conflicts
            assert result["conflicts_resolved"] == 3

            # Verify changes were filtered based on last-write-wins
            # Expected: local project (newer), local component delete (vs central update), central user (newer)
            assert (
                result["total_changes_processed"] == 6
            )  # 3 local + 3 central original
            assert (
                result["changes_applied_after_resolution"] < 6
            )  # Some changes filtered out

            # Verify both sessions were used and committed
            mock_central_session.commit.assert_called_once()
            mock_local_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_synchronize_project_with_mixed_conflicts_and_non_conflicts(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_local_session: AsyncMock,
        mock_central_session: AsyncMock,
        non_conflicting_changes: tuple[List[Dict[str, Any]], List[Dict[str, Any]]],
    ) -> None:
        """Test synchronize_project with both conflicting and non-conflicting changes."""
        project_id = 456
        last_sync_timestamp = datetime(2024, 1, 1, 9, 0, 0)

        local_non_conflict, central_non_conflict = non_conflicting_changes

        # Add one conflicting change to each side
        local_changes = local_non_conflict + [
            {
                "entity_type": "project",
                "entity_id": 500,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 14, 0, 0),  # More recent
                "new_values": {"name": "Local Conflicting Project"},
                "source": "local",
            }
        ]

        central_changes = central_non_conflict + [
            {
                "entity_type": "project",
                "entity_id": 500,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 12, 0, 0),  # Older
                "new_values": {"name": "Central Conflicting Project"},
                "source": "central",
            }
        ]

        # Mock the internal methods
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(
                sync_service, "_get_local_changes", return_value=local_changes
            ),
            patch.object(
                sync_service, "_get_central_changes", return_value=central_changes
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                side_effect=[
                    {
                        "created": 2,
                        "updated": 0,
                        "deleted": 0,
                        "errors": 0,
                    },  # local-to-central
                    {
                        "created": 1,
                        "updated": 0,
                        "deleted": 0,
                        "errors": 0,
                    },  # central-to-local
                ],
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.side_effect = [
                AsyncMockContext(mock_central_session),
                AsyncMockContext(mock_local_session),
            ]

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify results
            assert result["conflicts_detected"] == 1  # Only one entity conflicts
            assert result["conflicts_resolved"] == 1
            assert (
                result["total_changes_processed"] == 4
            )  # 2 local + 2 central original
            # All non-conflicting + 1 winning conflicting change = 3 changes applied
            assert result["changes_applied_after_resolution"] == 3

    @pytest.mark.asyncio
    async def test_synchronize_project_no_conflicts(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_local_session: AsyncMock,
        mock_central_session: AsyncMock,
        non_conflicting_changes: tuple[List[Dict[str, Any]], List[Dict[str, Any]]],
    ) -> None:
        """Test synchronize_project with no conflicting changes."""
        project_id = 789
        last_sync_timestamp = datetime(2024, 1, 1, 9, 0, 0)

        local_changes, central_changes = non_conflicting_changes

        # Mock the internal methods
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(
                sync_service, "_get_local_changes", return_value=local_changes
            ),
            patch.object(
                sync_service, "_get_central_changes", return_value=central_changes
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                side_effect=[
                    {
                        "created": 1,
                        "updated": 0,
                        "deleted": 0,
                        "errors": 0,
                    },  # local-to-central
                    {
                        "created": 1,
                        "updated": 0,
                        "deleted": 0,
                        "errors": 0,
                    },  # central-to-local
                ],
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.side_effect = [
                AsyncMockContext(mock_central_session),
                AsyncMockContext(mock_local_session),
            ]

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify no conflicts detected
            assert result["conflicts_detected"] == 0
            assert result["conflicts_resolved"] == 0
            assert result["total_changes_processed"] == 2
            assert (
                result["changes_applied_after_resolution"] == 2
            )  # All changes applied

    @pytest.mark.asyncio
    async def test_synchronize_project_double_delete_conflict(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_central_session: AsyncMock,
    ) -> None:
        """Test synchronize_project handles double delete conflicts correctly."""
        project_id = 321
        last_sync_timestamp = datetime(2024, 1, 1, 9, 0, 0)

        # Both sides delete the same entity
        local_changes = [
            {
                "entity_type": "component",
                "entity_id": 999,
                "operation": "delete",
                "timestamp": datetime(2024, 1, 1, 14, 0, 0),  # More recent
                "new_values": {},
                "source": "local",
            }
        ]

        central_changes = [
            {
                "entity_type": "component",
                "entity_id": 999,
                "operation": "delete",
                "timestamp": datetime(2024, 1, 1, 12, 0, 0),  # Older
                "new_values": {},
                "source": "central",
            }
        ]

        # Mock the internal methods
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(
                sync_service, "_get_local_changes", return_value=local_changes
            ),
            patch.object(
                sync_service, "_get_central_changes", return_value=central_changes
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 0, "updated": 0, "deleted": 1, "errors": 0},
            ),
        ):
            # Setup connection manager - only central session needed for local change
            mock_connection_manager.get_session.return_value = AsyncMockContext(
                mock_central_session
            )

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify double delete conflict resolution
            assert result["conflicts_detected"] == 1
            assert result["conflicts_resolved"] == 1
            assert (
                result["changes_applied_after_resolution"] == 1
            )  # Only winning delete applied

    @pytest.mark.asyncio
    async def test_synchronize_project_delete_vs_modify_conflict(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_local_session: AsyncMock,
    ) -> None:
        """Test synchronize_project handles delete vs modify conflicts correctly."""
        project_id = 654
        last_sync_timestamp = datetime(2024, 1, 1, 9, 0, 0)

        # Local deletes, central modifies
        local_changes = [
            {
                "entity_type": "user",
                "entity_id": 888,
                "operation": "delete",
                "timestamp": datetime(2024, 1, 1, 14, 0, 0),  # More recent
                "new_values": {},
                "source": "local",
            }
        ]

        central_changes = [
            {
                "entity_type": "user",
                "entity_id": 888,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 12, 0, 0),  # Older
                "new_values": {"email": "<EMAIL>"},
                "source": "central",
            }
        ]

        # Mock the internal methods
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(
                sync_service, "_get_local_changes", return_value=local_changes
            ),
            patch.object(
                sync_service, "_get_central_changes", return_value=central_changes
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 0, "updated": 0, "deleted": 1, "errors": 0},
            ),
        ):
            # Setup connection manager - only local session needed for winning local delete
            mock_connection_manager.get_session.return_value = AsyncMockContext(
                mock_local_session
            )

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify delete vs modify conflict resolution (delete should win due to recency)
            assert result["conflicts_detected"] == 1
            assert result["conflicts_resolved"] == 1
            # Only the winning delete change applied (to central database)
            assert result["changes_applied_after_resolution"] == 1

    @pytest.mark.asyncio
    async def test_synchronize_project_conflict_resolution_error_handling(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        conflicting_local_changes: List[Dict[str, Any]],
        conflicting_central_changes: List[Dict[str, Any]],
    ) -> None:
        """Test synchronize_project handles conflict resolution errors gracefully."""
        project_id = 987
        last_sync_timestamp = datetime(2024, 1, 1, 9, 0, 0)

        # Mock the internal methods with conflict resolution failure
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(
                sync_service,
                "_get_local_changes",
                return_value=conflicting_local_changes,
            ),
            patch.object(
                sync_service,
                "_get_central_changes",
                return_value=conflicting_central_changes,
            ),
            patch.object(
                sync_service,
                "_resolve_conflict",
                side_effect=Exception("Resolution failed"),
            ),
        ):
            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify conflicts were detected but resolution failed
            assert result["conflicts_detected"] == 3
            assert (
                result["conflicts_resolved"] == 3
            )  # Still marked as resolved (failed gracefully)
            # No changes should be applied due to resolution failures (safety measure)
            assert result["changes_applied_after_resolution"] == 0

    @pytest.mark.asyncio
    async def test_synchronize_project_response_format_with_conflicts(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_central_session: AsyncMock,
        conflicting_local_changes: List[Dict[str, Any]],
        conflicting_central_changes: List[Dict[str, Any]],
    ) -> None:
        """Test that synchronize_project returns proper format with conflict metrics."""
        project_id = 555
        last_sync_timestamp = datetime(2024, 1, 1, 9, 0, 0)

        # Mock the internal methods
        with (
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(
                sync_service,
                "_get_local_changes",
                return_value=conflicting_local_changes,
            ),
            patch.object(
                sync_service,
                "_get_central_changes",
                return_value=conflicting_central_changes,
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 1, "updated": 1, "deleted": 0, "errors": 0},
            ),
        ):
            # Setup connection manager
            mock_connection_manager.get_session.return_value = AsyncMockContext(
                mock_central_session
            )

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify all required fields including conflict metrics
            required_fields = [
                "project_id",
                "status",
                "local_to_central",
                "central_to_local",
                "conflicts_detected",
                "conflicts_resolved",
                "sync_direction",
                "timestamp",
                "last_sync_timestamp",
                "total_changes_processed",
                "changes_applied_after_resolution",
            ]
            for field in required_fields:
                assert field in result

            # Verify data types
            assert isinstance(result["conflicts_detected"], int)
            assert isinstance(result["conflicts_resolved"], int)
            assert isinstance(result["changes_applied_after_resolution"], int)

            # Verify conflict metrics make sense
            assert result["conflicts_detected"] >= 0
            assert result["conflicts_resolved"] >= 0
            assert (
                result["changes_applied_after_resolution"]
                <= result["total_changes_processed"]
            )
