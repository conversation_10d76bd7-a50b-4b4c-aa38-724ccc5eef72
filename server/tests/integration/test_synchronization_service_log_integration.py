"""Integration tests for SynchronizationService with SynchronizationLog model.

This module contains integration tests for the SynchronizationService class
focusing on the integration with the SynchronizationLog database model.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Any

from src.core.services.general.synchronization_service import SynchronizationService
from src.core.database.connection_manager import DynamicConnectionManager
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.enums.system_enums import SyncStatus
from src.core.models.general.synchronization_log import SynchronizationLog


class TestSynchronizationServiceLogIntegration:
    """Test suite for SynchronizationService integration with SynchronizationLog model."""

    @pytest.fixture
    def mock_connection_manager(self) -> MagicMock:
        """Create a mock DynamicConnectionManager."""
        manager = MagicMock(spec=DynamicConnectionManager)
        manager.get_session = AsyncMock()
        return manager

    @pytest.fixture
    def mock_project_repository(self) -> AsyncMock:
        """Create a mock ProjectRepository."""
        return AsyncMock(spec=ProjectRepository)

    @pytest.fixture
    def sync_service(
        self, mock_connection_manager: MagicMock, mock_project_repository: AsyncMock
    ) -> SynchronizationService:
        """Create a SynchronizationService instance with mocked dependencies."""
        return SynchronizationService(mock_connection_manager, mock_project_repository)

    @pytest.mark.asyncio
    async def test_get_last_sync_timestamp_with_existing_sync(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test _get_last_sync_timestamp when successful sync exists."""
        project_id = 123
        expected_timestamp = datetime(2024, 1, 1, 12, 0, 0)

        # Mock database session and query result
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = expected_timestamp
        mock_session.execute.return_value = mock_result

        # Setup connection manager to return mocked session
        mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(
            return_value=mock_session
        )
        mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(
            return_value=None
        )

        # Execute method
        result = await sync_service._get_last_sync_timestamp(project_id)

        # Verify result
        assert result == expected_timestamp

        # Verify database query was executed
        mock_session.execute.assert_called_once()
        executed_stmt = mock_session.execute.call_args[0][0]

        # Verify that the statement includes the correct filters
        assert hasattr(executed_stmt, "whereclause")
        mock_connection_manager.get_session.assert_called_once_with(
            sync_service.project_repository,
            None,  # Central database
        )

    @pytest.mark.asyncio
    async def test_get_last_sync_timestamp_no_previous_sync(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test _get_last_sync_timestamp when no previous sync exists."""
        project_id = 456

        # Mock database session and query result
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Setup connection manager to return mocked session
        mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(
            return_value=mock_session
        )
        mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(
            return_value=None
        )

        # Execute method
        result = await sync_service._get_last_sync_timestamp(project_id)

        # Verify result
        assert result is None

        # Verify database query was executed
        mock_session.execute.assert_called_once()
        mock_connection_manager.get_session.assert_called_once_with(
            sync_service.project_repository,
            None,  # Central database
        )

    @pytest.mark.asyncio
    async def test_get_last_sync_timestamp_database_error(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test _get_last_sync_timestamp handles database errors gracefully."""
        project_id = 789

        # Mock database session to raise an exception
        mock_session = AsyncMock()
        mock_session.execute.side_effect = Exception("Database connection failed")

        # Setup connection manager to return mocked session
        mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(
            return_value=mock_session
        )
        mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(
            return_value=None
        )

        # Execute method
        result = await sync_service._get_last_sync_timestamp(project_id)

        # Verify that None is returned on error
        assert result is None

        # Verify database query was attempted
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_last_sync_timestamp_uses_central_database(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test that _get_last_sync_timestamp uses central database for sync logs."""
        project_id = 100

        # Mock database session
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Setup connection manager
        mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(
            return_value=mock_session
        )
        mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(
            return_value=None
        )

        # Execute method
        await sync_service._get_last_sync_timestamp(project_id)

        # Verify that connection manager was called with None (central database)
        mock_connection_manager.get_session.assert_called_once_with(
            sync_service.project_repository, None
        )

    @pytest.mark.asyncio
    async def test_get_last_sync_timestamp_filters_completed_status(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test that _get_last_sync_timestamp only considers completed synchronizations."""
        project_id = 200

        # Mock database session
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = datetime(2024, 1, 1, 12, 0, 0)
        mock_session.execute.return_value = mock_result

        # Setup connection manager
        mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(
            return_value=mock_session
        )
        mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(
            return_value=None
        )

        # Execute method
        await sync_service._get_last_sync_timestamp(project_id)

        # Verify database query was executed
        mock_session.execute.assert_called_once()

        # The SQL statement should filter for completed status
        # This is implicit in the implementation, but we verify the query was made
        assert mock_session.execute.called

    @pytest.mark.asyncio
    async def test_get_last_sync_timestamp_orders_by_completion_desc(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test that _get_last_sync_timestamp orders by completion time descending."""
        project_id = 300

        # Mock database session
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = datetime(2024, 1, 1, 12, 0, 0)
        mock_session.execute.return_value = mock_result

        # Setup connection manager
        mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(
            return_value=mock_session
        )
        mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(
            return_value=None
        )

        # Execute method
        await sync_service._get_last_sync_timestamp(project_id)

        # Verify database query was executed
        mock_session.execute.assert_called_once()

        # Verify the statement includes limit(1) for getting the most recent
        executed_stmt = mock_session.execute.call_args[0][0]
        assert hasattr(executed_stmt, "_limit")

    @pytest.mark.asyncio
    async def test_get_last_sync_timestamp_parameter_types(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test that _get_last_sync_timestamp accepts integer project_id."""
        project_id = 999  # Integer type

        # Mock database session
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Setup connection manager
        mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(
            return_value=mock_session
        )
        mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(
            return_value=None
        )

        # Execute method - should not raise type errors
        result = await sync_service._get_last_sync_timestamp(project_id)

        # Verify method completes successfully
        assert result is None
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_last_sync_timestamp_logging(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test that _get_last_sync_timestamp includes proper logging."""
        project_id = 555

        # Mock database session
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = datetime(2024, 1, 1, 12, 0, 0)
        mock_session.execute.return_value = mock_result

        # Setup connection manager
        mock_connection_manager.get_session.return_value.__aenter__ = AsyncMock(
            return_value=mock_session
        )
        mock_connection_manager.get_session.return_value.__aexit__ = AsyncMock(
            return_value=None
        )

        # Execute method with logging patch
        with patch(
            "src.core.services.general.synchronization_service.logger"
        ) as mock_logger:
            result = await sync_service._get_last_sync_timestamp(project_id)

            # Verify logging calls were made
            mock_logger.debug.assert_called()

            # Check that appropriate log messages were generated
            debug_calls = mock_logger.debug.call_args_list
            assert len(debug_calls) >= 2  # Initial and result logging

            # Verify first call includes project_id
            first_call_args = debug_calls[0][0][0]
            assert str(project_id) in first_call_args
