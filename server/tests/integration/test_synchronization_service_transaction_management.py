"""Integration tests for SynchronizationService transaction management and sync log integration.

This module contains comprehensive integration tests for the enhanced transaction
management, sync log integration, and error handling in the SynchronizationService.
"""

import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
from typing import Dict, Any, List

from src.core.services.general.synchronization_service import SynchronizationService
from src.core.database.connection_manager import DynamicConnectionManager
from src.core.repositories.general.project_repository import ProjectRepository
from src.core.errors.exceptions import SynchronizationError


# Helper class for async context manager mocking
class AsyncMockContext:
    def __init__(self, mock_session):
        self.mock_session = mock_session

    async def __aenter__(self):
        return self.mock_session

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        return None


class TestSynchronizationServiceTransactionManagement:
    """Test suite for SynchronizationService transaction management and sync log integration."""

    @pytest.fixture
    def mock_connection_manager(self) -> MagicMock:
        """Create a mock DynamicConnectionManager."""
        manager = MagicMock(spec=DynamicConnectionManager)
        manager.get_session = AsyncMock()
        return manager

    @pytest.fixture
    def mock_project_repository(self) -> AsyncMock:
        """Create a mock ProjectRepository."""
        return AsyncMock(spec=ProjectRepository)

    @pytest.fixture
    def sync_service(
        self, mock_connection_manager: MagicMock, mock_project_repository: AsyncMock
    ) -> SynchronizationService:
        """Create a SynchronizationService instance with mocked dependencies."""
        return SynchronizationService(mock_connection_manager, mock_project_repository)

    @pytest.fixture
    def mock_sync_log(self) -> MagicMock:
        """Create a mock SynchronizationLog instance."""
        sync_log = MagicMock()
        sync_log.id = 123
        sync_log.update_completion_metrics = MagicMock()
        return sync_log

    @pytest.fixture
    def sample_local_changes(self) -> List[Dict[str, Any]]:
        """Create sample local changes for testing."""
        return [
            {
                "entity_type": "project",
                "entity_id": 123,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 12, 0, 0),
                "new_values": {"name": "Updated Project Name"},
                "source": "local",
            }
        ]

    @pytest.mark.asyncio
    async def test_create_sync_log_entry_success(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_sync_log: MagicMock,
    ) -> None:
        """Test successful creation of sync log entry."""
        project_id = 123

        # Mock database session
        mock_session = AsyncMock()
        mock_session.add = MagicMock()
        mock_session.flush = AsyncMock()
        mock_session.commit = AsyncMock()

        # Setup connection manager
        mock_connection_manager.get_session.return_value = AsyncMockContext(
            mock_session
        )

        # Mock SynchronizationLog creation
        with patch(
            "src.core.services.general.synchronization_service.SynchronizationLog"
        ) as MockSyncLog:
            MockSyncLog.return_value = mock_sync_log

            # Execute method
            result = await sync_service._create_sync_log_entry(project_id)

            # Verify result
            assert result == 123

            # Verify sync log was created correctly
            MockSyncLog.assert_called_once()
            call_args = MockSyncLog.call_args[1]
            assert call_args["project_id"] == project_id
            assert "started_at" in call_args

            # Verify database operations
            mock_session.add.assert_called_once_with(mock_sync_log)
            mock_session.flush.assert_called_once()
            mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_sync_log_entry_database_error(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test sync log entry creation handles database errors gracefully."""
        project_id = 456

        # Mock database session to raise exception
        mock_session = AsyncMock()
        mock_session.add.side_effect = Exception("Database error")

        # Setup connection manager
        mock_connection_manager.get_session.return_value = AsyncMockContext(
            mock_session
        )

        # Execute method
        result = await sync_service._create_sync_log_entry(project_id)

        # Verify it returns dummy ID instead of failing
        assert result == -1

    @pytest.mark.asyncio
    async def test_complete_sync_log_entry_success(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_sync_log: MagicMock,
    ) -> None:
        """Test successful completion of sync log entry."""
        sync_log_id = 123

        # Mock database session and query result
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_sync_log
        mock_session.execute.return_value = mock_result
        mock_session.commit = AsyncMock()

        # Setup connection manager
        mock_connection_manager.get_session.return_value = AsyncMockContext(
            mock_session
        )

        # Execute method
        await sync_service._complete_sync_log_entry(
            sync_log_id=sync_log_id,
            status="completed",
            records_processed=5,
            conflicts_detected=2,
            local_to_central_counts={
                "created": 1,
                "updated": 2,
                "deleted": 0,
                "errors": 0,
            },
            central_to_local_counts={
                "created": 0,
                "updated": 1,
                "deleted": 1,
                "errors": 0,
            },
        )

        # Verify sync log was updated
        assert mock_sync_log.records_processed == 5
        assert mock_sync_log.conflicts_detected == 2
        mock_sync_log.update_completion_metrics.assert_called_once()

        # Verify database operations
        mock_session.execute.assert_called_once()
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_complete_sync_log_entry_with_error(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        mock_sync_log: MagicMock,
    ) -> None:
        """Test completion of sync log entry with error message."""
        sync_log_id = 456
        error_message = "Synchronization failed due to database error"

        # Mock database session and query result
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = mock_sync_log
        mock_session.execute.return_value = mock_result
        mock_session.commit = AsyncMock()

        # Setup connection manager
        mock_connection_manager.get_session.return_value = AsyncMockContext(
            mock_session
        )

        # Execute method
        await sync_service._complete_sync_log_entry(
            sync_log_id=sync_log_id,
            status="failed",
            records_processed=0,
            conflicts_detected=0,
            error_message=error_message,
        )

        # Verify error message was set
        assert mock_sync_log.error_message == error_message
        mock_session.commit.assert_called_once()

    @pytest.mark.asyncio
    async def test_complete_sync_log_entry_not_found(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test completion when sync log entry is not found."""
        sync_log_id = 999

        # Mock database session to return None
        mock_session = AsyncMock()
        mock_result = AsyncMock()
        mock_result.scalar_one_or_none.return_value = None
        mock_session.execute.return_value = mock_result

        # Setup connection manager
        mock_connection_manager.get_session.return_value = AsyncMockContext(
            mock_session
        )

        # Execute method - should not raise exception
        await sync_service._complete_sync_log_entry(
            sync_log_id=sync_log_id,
            status="completed",
            records_processed=0,
            conflicts_detected=0,
        )

        # Verify query was executed but no commit happened
        mock_session.execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_complete_sync_log_entry_with_dummy_id(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test completion with dummy ID (-1) is skipped gracefully."""
        sync_log_id = -1  # Dummy ID from failed creation

        # Execute method
        await sync_service._complete_sync_log_entry(
            sync_log_id=sync_log_id,
            status="completed",
            records_processed=0,
            conflicts_detected=0,
        )

        # Verify no database operations were attempted
        mock_connection_manager.get_session.assert_not_called()

    @pytest.mark.asyncio
    async def test_synchronize_project_with_sync_log_integration(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test that synchronize_project properly integrates with sync log."""
        project_id = 123
        last_sync_timestamp = datetime(2024, 1, 1, 10, 0, 0)

        # Mock all the internal methods
        with (
            patch.object(sync_service, "_create_sync_log_entry", return_value=123),
            patch.object(
                sync_service, "_complete_sync_log_entry", new_callable=AsyncMock
            ),
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                return_value=last_sync_timestamp,
            ),
            patch.object(
                sync_service, "_get_local_changes", return_value=sample_local_changes
            ),
            patch.object(sync_service, "_get_central_changes", return_value=[]),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={"created": 1, "updated": 0, "deleted": 0, "errors": 0},
            ),
        ):
            # Setup connection manager
            mock_session = AsyncMock()
            mock_session.commit = AsyncMock()
            mock_connection_manager.get_session.return_value = AsyncMockContext(
                mock_session
            )

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify sync log was created
            sync_service._create_sync_log_entry.assert_called_once_with(project_id)

            # Verify sync log was completed
            sync_service._complete_sync_log_entry.assert_called_once()
            completion_call = sync_service._complete_sync_log_entry.call_args[1]
            assert completion_call["sync_log_id"] == 123
            assert completion_call["status"] == "completed"
            assert completion_call["records_processed"] == 1

    @pytest.mark.asyncio
    async def test_synchronize_project_with_sync_log_on_error(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test that sync log is updated on synchronization error."""
        project_id = 456

        # Mock methods to create sync log but fail on getting changes
        with (
            patch.object(sync_service, "_create_sync_log_entry", return_value=456),
            patch.object(
                sync_service, "_complete_sync_log_entry", new_callable=AsyncMock
            ),
            patch.object(
                sync_service,
                "_get_last_sync_timestamp",
                side_effect=Exception("Database error"),
            ),
        ):
            # Execute synchronization and expect error
            with pytest.raises(SynchronizationError):
                await sync_service.synchronize_project(project_id)

            # Verify sync log was created
            sync_service._create_sync_log_entry.assert_called_once_with(project_id)

            # Verify sync log was completed with failure
            sync_service._complete_sync_log_entry.assert_called_once()
            completion_call = sync_service._complete_sync_log_entry.call_args[1]
            assert completion_call["sync_log_id"] == 456
            assert completion_call["status"] == "failed"
            assert "error_message" in completion_call

    @pytest.mark.asyncio
    async def test_transaction_management_central_database_error(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test transaction management when central database operation fails."""
        project_id = 789

        # Mock methods except central database operations
        with (
            patch.object(sync_service, "_create_sync_log_entry", return_value=789),
            patch.object(
                sync_service, "_complete_sync_log_entry", new_callable=AsyncMock
            ),
            patch.object(
                sync_service, "_get_last_sync_timestamp", return_value=datetime.now()
            ),
            patch.object(
                sync_service, "_get_local_changes", return_value=sample_local_changes
            ),
            patch.object(sync_service, "_get_central_changes", return_value=[]),
        ):
            # Mock connection manager to fail for central database
            mock_connection_manager.get_session.side_effect = Exception(
                "Central DB connection failed"
            )

            # Execute synchronization and expect error
            with pytest.raises(SynchronizationError) as exc_info:
                await sync_service.synchronize_project(project_id)

            # Verify error message contains context
            assert "Central database synchronization failed" in str(exc_info.value)

            # Verify sync log completion was called with failure
            sync_service._complete_sync_log_entry.assert_called_once()
            completion_call = sync_service._complete_sync_log_entry.call_args[1]
            assert completion_call["status"] == "failed"

    @pytest.mark.asyncio
    async def test_transaction_management_partial_errors(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test transaction management with partial errors in change application."""
        project_id = 321

        # Mock all internal methods
        with (
            patch.object(sync_service, "_create_sync_log_entry", return_value=321),
            patch.object(
                sync_service, "_complete_sync_log_entry", new_callable=AsyncMock
            ),
            patch.object(
                sync_service, "_get_last_sync_timestamp", return_value=datetime.now()
            ),
            patch.object(
                sync_service, "_get_local_changes", return_value=sample_local_changes
            ),
            patch.object(sync_service, "_get_central_changes", return_value=[]),
            patch.object(
                sync_service,
                "_apply_changes",
                return_value={
                    "created": 0,
                    "updated": 1,
                    "deleted": 0,
                    "errors": 1,  # Some errors
                },
            ),
        ):
            # Setup connection manager
            mock_session = AsyncMock()
            mock_session.commit = AsyncMock()
            mock_connection_manager.get_session.return_value = AsyncMockContext(
                mock_session
            )

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify status shows completed with errors
            assert result["status"] == "completed_with_errors"
            assert result["local_to_central"]["errors"] == 1

            # Verify transaction was still committed
            mock_session.commit.assert_called_once()

            # Verify sync log was updated with proper status
            completion_call = sync_service._complete_sync_log_entry.call_args[1]
            assert completion_call["status"] == "completed_with_errors"

    @pytest.mark.asyncio
    async def test_sync_log_completion_error_handling(
        self, sync_service: SynchronizationService, mock_connection_manager: MagicMock
    ) -> None:
        """Test that errors in sync log completion don't break main synchronization."""
        project_id = 555

        # Mock methods to succeed but fail on sync log completion
        with (
            patch.object(sync_service, "_create_sync_log_entry", return_value=555),
            patch.object(
                sync_service,
                "_complete_sync_log_entry",
                side_effect=Exception("Log update failed"),
            ),
            patch.object(sync_service, "_get_last_sync_timestamp", return_value=None),
            patch.object(sync_service, "_get_local_changes", return_value=[]),
            patch.object(sync_service, "_get_central_changes", return_value=[]),
        ):
            # Execute synchronization - should complete despite log error
            result = await sync_service.synchronize_project(project_id)

            # Verify main synchronization completed successfully
            assert result["status"] == "completed"
            assert result["project_id"] == project_id

    @pytest.mark.asyncio
    async def test_bidirectional_transaction_isolation(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test that local and central transactions are properly isolated."""
        project_id = 666
        sample_central_changes = [
            {
                "entity_type": "user",
                "entity_id": 789,
                "operation": "update",
                "timestamp": datetime(2024, 1, 1, 11, 0, 0),
                "new_values": {"email": "<EMAIL>"},
                "source": "central",
            }
        ]

        # Mock all internal methods
        with (
            patch.object(sync_service, "_create_sync_log_entry", return_value=666),
            patch.object(
                sync_service, "_complete_sync_log_entry", new_callable=AsyncMock
            ),
            patch.object(
                sync_service, "_get_last_sync_timestamp", return_value=datetime.now()
            ),
            patch.object(
                sync_service, "_get_local_changes", return_value=sample_local_changes
            ),
            patch.object(
                sync_service,
                "_get_central_changes",
                return_value=sample_central_changes,
            ),
            patch.object(
                sync_service,
                "_apply_changes",
                side_effect=[
                    {
                        "created": 1,
                        "updated": 0,
                        "deleted": 0,
                        "errors": 0,
                    },  # local-to-central
                    {
                        "created": 0,
                        "updated": 1,
                        "deleted": 0,
                        "errors": 0,
                    },  # central-to-local
                ],
            ),
        ):
            # Setup separate mock sessions for each database
            mock_central_session = AsyncMock()
            mock_local_session = AsyncMock()
            mock_central_session.commit = AsyncMock()
            mock_local_session.commit = AsyncMock()

            mock_connection_manager.get_session.side_effect = [
                AsyncMockContext(mock_central_session),  # Central database session
                AsyncMockContext(mock_local_session),  # Local database session
            ]

            # Execute synchronization
            result = await sync_service.synchronize_project(project_id)

            # Verify both sessions were committed independently
            mock_central_session.commit.assert_called_once()
            mock_local_session.commit.assert_called_once()

            # Verify proper connection calls for both databases
            calls = mock_connection_manager.get_session.call_args_list
            assert len(calls) == 2

            # First call should be for central database (project_id=None)
            assert calls[0][0][1] is None

            # Second call should be for local database (project_id=project_id)
            assert calls[1][0][1] == project_id

    @pytest.mark.asyncio
    async def test_enhanced_error_context_in_exceptions(
        self,
        sync_service: SynchronizationService,
        mock_connection_manager: MagicMock,
        sample_local_changes: List[Dict[str, Any]],
    ) -> None:
        """Test that enhanced error handling provides proper context."""
        project_id = 777

        # Mock to fail during local-to-central application
        with (
            patch.object(sync_service, "_create_sync_log_entry", return_value=777),
            patch.object(
                sync_service, "_complete_sync_log_entry", new_callable=AsyncMock
            ),
            patch.object(
                sync_service, "_get_last_sync_timestamp", return_value=datetime.now()
            ),
            patch.object(
                sync_service, "_get_local_changes", return_value=sample_local_changes
            ),
            patch.object(sync_service, "_get_central_changes", return_value=[]),
        ):
            # Mock connection manager to fail with specific error
            mock_connection_manager.get_session.side_effect = Exception(
                "Connection timeout"
            )

            # Execute synchronization and expect specific error
            with pytest.raises(SynchronizationError) as exc_info:
                await sync_service.synchronize_project(project_id)

            # Verify error context is preserved
            error_message = str(exc_info.value)
            assert "Central database synchronization failed" in error_message
            assert "Connection timeout" in error_message
