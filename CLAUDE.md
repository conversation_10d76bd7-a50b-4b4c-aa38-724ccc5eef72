# CLAUDE.md

This file provides **strict and non-negotiable guidance** to <PERSON>
(claude.ai/code) when working with code in this repository. All instructions and
references herein are **mandatory** and supersede any internal assumptions.

## Project Overview

The Ultimate Electrical Designer is a comprehensive electrical design platform
built with FastAPI (Python) backend and Next.js (React) frontend. It follows a
**5-layer architecture pattern** with **engineering-grade quality standards**
for professional electrical system design.

## Project Guidelines Reference (AUTHORITATIVE SOURCE)

This project follows comprehensive guidelines documented in the `docs/` folder.
**These documents are the single source of truth for all project decisions,
standards, and methodologies.** Always reference these documents **first and
thoroughly** for any architectural decisions, coding standards, or project
requirements.

- **docs/structure.md**: Project structure and architectural patterns
- **docs/tech.md**: Technology stack specification and tool versions
- **docs/rules.md**: **CRITICAL: Development standards and quality gates
  (Mandatory Adherence)**
- **docs/requirements.md**: Functional and non-functional requirements
- **docs/design.md**: Technical architecture and design decisions
- **docs/tasks.md**: Implementation task breakdown

**ABSOLUTELY CRITICAL**:

- **NEVER DEVIATE** from the standards, policies, and methodologies documented
  in `docs/rules.md`.
- **ZERO EXCEPTIONS** will be made for linting errors, type safety violations,
  test failures, or any form of technical debt.
- All code must reflect **immaculate attention to detail** and adhere to
  professional electrical design standards.

---

## Development Commands

### Backend (Server)

```bash
# Run uv commands from server/
cd /mnt/d/Projects/ued/server/
# Start the backend server
uv run uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
# Code quality (MUST PASS WITHOUT ERRORS OR WARNINGS)
uv run mypy src/ --show-error-codes
uv run ruff format .
uv run ruff format . --check
uv run bandit -r src/ -f json -o bandit-report.json
# Testing (MUST PASS WITH REQUIRED COVERAGE)
uv run pytest --html=test-report-all.html --self-contained-html
uv run pytest -v -m "not integration and not performance" --html=test-report-unit.html --self-contained-html
uv run pytest -v -m tests/integration --html=test-report-integration.html --self-contained-html
uv run pytest -v -m tests/performance --html=test-report-performance.html --self-contained-html
uv run pytest tests/ --cov=src --cov-report=term-missing --cov-report=xml --html=test-report-cov.html --self-contained-html

# Database
# Run alembic commands from server/src/
cd /mnt/d/Projects/ued/server/src/
uv run alembic check
uv run alembic version
uv run alembic current
uv run alembic upgrade head

# Run uv commands from server/
cd /mnt/d/Projects/ued/server/
# Wipe database
uv run python main.py wipe-database --confirm       # WARNING: This will DELETE your development database and re-migrate.
# Seed database
uv run python main.py seed-general-data         # Seed database with Phase 1 data (src/core/models/general/)

# Documentation
uv run mkdocs build --clean --site-dir ../docs/api/python
```

### Frontend (Client)

```bash
# Run pnpm commands from client/
cd /mnt/d/Projects/ued/client/
# Start the frontend server
pnpm run dev

# Code quality (MUST PASS WITHOUT ERRORS OR WARNINGS)
pnpm tsc --noEmit
pnpm next lint --fix
pnpm prettier --write --log-level=warn \"**/*.{ts,tsx,mdx}\" --cache

# Testing (MUST PASS WITH REQUIRED COVERAGE)
pnpm vitest [source] --run
pnpm vitest [source] --coverage --run
pnpm playwright test tests/e2e/[source]
```

---

## Key Architectural Patterns (MANDATORY ADOPTION)

### CRUD Endpoint Factory Pattern

**CRITICAL**: Use the CRUD endpoint factory for all new entities to avoid
boilerplate code. This is a unified pattern and its usage is **mandatory**
unless explicitly documented otherwise.

```python
# For new entities, use the factory pattern
from src.core.utils.crud_endpoint_factory import create_simple_crud_router

# Create CRUD router
crud_router = create_simple_crud_router(
    entity_name="your_entity",
    entity_name_plural="your_entities",
    create_schema=YourCreateSchema,
    read_schema=YourReadSchema,
    update_schema=YourUpdateSchema,
    list_response_schema=YourListResponseSchema,
    service_class=YourService,
    service_dependency=get_your_service,
    id_type=int,
    searchable_fields=["name", "description"],
    sortable_fields=["name", "created_at", "updated_at"],
)
```

---

## Testing Strategy (MANDATORY ADHERENCE)

All tests must be implemented according to the specified strategy and pass with
**100% pass rate**. Test coverage must meet documented targets (refer to
`docs/rules.md`).

### Backend Testing

- **Unit Tests**: Individual component testing with pytest.
- **Integration Tests**: Multi-component workflow testing.
- **API Tests**: Complete endpoint testing with TestClient.
- **Performance Tests**: Load testing with specific markers.
- **Security Tests**: Security validation testing.

### Frontend Testing

- **Unit Tests**: Component testing with Vitest + React Testing Library.
- **Integration Tests**: Feature workflow testing.
- **E2E Tests**: Full user workflow testing with Playwright.
- **MSW Mocking**: Mock Service Worker for API mocking (for robust frontend
  testing).

---

## Development Standards (STRICTLY ENFORCED)

**Adherence to these standards is paramount and non-negotiable for every single
line of code.**

1. **Robust design principles:** Apply **SOLID** principles for structural
   design, ensuring maintainability and flexibility through focused
   responsibilities, extensibility, and proper abstraction. Complement these
   with practices like **DRY**, **KISS**, and **TDD** to streamline
   implementation, reduce complexity, and enhance overall code quality. **No
   exceptions.**
2. **5-Phase Methodology:** Adopt a systematic 5-phase approach for each feature
   or task. This structured, quality-driven development process is **mandatory**
   for all work.
   1. **Discovery & Analysis:** Understand the current state of the system,
      identify requirements, and define the scope of the main task.
   2. **Task Planning:** Break down tasks into smaller, manageable units (max
      30-minute work batches) to ensure efficient progress.
   3. **Implementation:** Execute changes with **engineering-grade quality**,
      focusing on unified patterns and professional electrical design standards.
   4. **Verification:** Ensure all requirements are met through comprehensive
      testing and **100% compliance verification** against all documented
      quality gates.
   5. **Documentation & Handover:** Prepare comprehensive documentation and
      create a handover package for future development and AI agent transfer.
3. **Unified Patterns:** Apply consistent "unified patterns" for calculations,
   service layers, and repositories. Utilize decorators for error handling,
   performance monitoring, and memory optimization as specified in
   `docs/design.md`. **Consistency is key.**
4. **Quality & Standards Focus:** Ensure **immaculate attention to detail**.
   Adhere to professional electrical design standards (IEC/EN). Maintain
   **complete type safety** with MyPy validation (100% compliance) and
   comprehensive testing (including real database connections where
   appropriate).
5. **Key Success Metrics:** Define success through high unified patterns
   compliance (≥90%), extensive test coverage (≥85% overall, 100% for critical
   logic), 100% test pass rates, and zero remaining placeholder implementations.
   **These are minimum success criteria.**

---

## Module Relationships (UNDERSTAND AND RESPECT)

### Backend Layer Dependencies

1. **API Layer** → **Services Layer** → **Repositories Layer** → **Models
   Layer**
2. **Unified Error Handling** spans all layers.
3. **Security Validation** integrated at API and Service layers.
4. **Performance Monitoring** integrated at all layers.

### Frontend Module Dependencies

1. **App Router** → **Modules** → **Components** → **UI Primitives**
2. **State Management** (Zustand) for client state.
3. **React Query** for server state management.
4. **API Client** for backend communication.

### Cross-System Integration

- **Authentication Flow**: Frontend auth module ↔ Backend auth services
- **Component Management**: Frontend component module ↔ Backend component
  services
- **Real-time Updates**: WebSocket connections for live collaboration
- **File Uploads**: Direct integration for component specifications and drawings

---

## Performance Optimization

- **Backend**: Use performance monitoring decorators on services. Implement
  efficient queries and caching strategies.
- **Frontend**: Implement React Query for server state caching, code splitting,
  and image optimization.
- **Database**: Leverage existing indexing and ensure query optimization.
- **Caching**: Utilize the built-in caching middleware.

---

## Memory Log

### Backend Environment

- Use `uv run` for Python environment

### Frontend Environment

- Use `pnpm` for JavaScript environment
