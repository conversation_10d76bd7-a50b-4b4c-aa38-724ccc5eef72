# Technical Design Specification

## Ultimate Electrical Designer

**Document Version:** 1.0  
**Last Updated:** July 2025 **References:** [product.md](product.md), [structure.md](structure.md), [tech.md](tech.md),
[rules.md](rules.md), [requirements.md](requirements.md)

---

## Architecture Overview

The Ultimate Electrical Designer implements a modern, scalable architecture designed for professional electrical
engineering applications. The system follows a 5-layer backend architecture with a React-based frontend, unified error
handling, and comprehensive security framework.

---

## System Architecture

### High-Level Architecture Diagram

```
┌─────────────────────────────────────────────────────────────────┐
│                        Client Layer                             │
├─────────────────────────────────────────────────────────────────┤
│  Next.js React Frontend (TypeScript)                           │
│  ├── App Router (Pages & Layouts)                              │
│  ├── Component Library (Radix UI + Tailwind)                   │
│  ├── State Management (React Query + Zustand)                  │
│  └── Real-time Communication (WebSocket)                       │
└─────────────────────────────────────────────────────────────────┘
                                │
                        HTTPS/WebSocket
                                │
┌─────────────────────────────────────────────────────────────────┐
│                     API Gateway Layer                          │
├─────────────────────────────────────────────────────────────────┤
│  FastAPI Application (Python)                                  │
│  ├── Authentication Middleware                                 │
│  ├── Security Middleware                                       │
│  ├── Performance Monitoring                                    │
│  └── CORS & Rate Limiting                                      │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                    5-Layer Backend Architecture                 │
├─────────────────────────────────────────────────────────────────┤
│  Layer 1: API Routes (FastAPI Endpoints)                       │
│  ├── Authentication Routes (/api/v1/auth)                      │
│  ├── Component Routes (/api/v1/components)                     │
│  ├── Calculation Routes (/api/v1/calculations)                 │
│  └── Project Routes (/api/v1/projects)                         │
├─────────────────────────────────────────────────────────────────┤
│  Layer 2: Business Services                                    │
│  ├── Authentication Service                                    │
│  ├── Component Management Service                              │
│  ├── Calculation Engine Service                                │
│  └── Project Management Service                                │
├─────────────────────────────────────────────────────────────────┤
│  Layer 3: Data Repositories                                    │
│  ├── User Repository                                           │
│  ├── Component Repository                                      │
│  ├── Calculation Repository                                    │
│  └── Project Repository                                        │
├─────────────────────────────────────────────────────────────────┤
│  Layer 4: Data Models (SQLAlchemy)                             │
│  ├── User Models                                               │
│  ├── Component Models                                          │
│  ├── Calculation Models                                        │
│  └── Project Models                                            │
├─────────────────────────────────────────────────────────────────┤
│  Layer 5: Validation Schemas (Pydantic)                        │
│  ├── Request/Response Schemas                                  │
│  ├── Data Transfer Objects                                     │
│  └── Validation Rules                                          │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                      Data Layer                                │
├─────────────────────────────────────────────────────────────────┤
│  PostgreSQL (Production / Shared Local)                      │
│  ├── User Management Tables                                    │
│  ├── Component Library Tables                                  │
│  ├── Calculation Results Tables                                │
│  └── Project Data Tables                                       │
└─────────────────────────────────────────────────────────────────┘
```

---

## Shared Local Database Architecture

The Ultimate Electrical Designer supports a flexible and robust data management strategy that enables both fully online
and locally-hosted collaborative workflows. This is achieved through a **shared local database architecture**, where a
project's data can be hosted on a local PostgreSQL server, which in turn synchronizes with a central, cloud-hosted
PostgreSQL database. This ensures data consistency and allows teams to work efficiently on a local network with the
safety of a cloud backup and the option for broader collaboration.

---

### 1. High-Level Architecture Diagram (Shared Local Database)

The following diagram illustrates the data flow when operating in a shared local database mode.

```mermaid
graph TD
    subgraph Local Network
        C1[Client App 1] --> SLP
        C2[Client App 2] --> SLP
        C3[Client App 3] --> SLP
    end

    subgraph Backend Services
        API(API Gateway / FastAPI)
    end

    subgraph Cloud Infrastructure
        CP(Central PostgreSQL)
    end

    SLP(Shared Local PostgreSQL) -- Bi-directional Sync --> API
    API -- Bi-directional Sync --> CP

    style SLP fill:#d6eaff
    style CP fill:#d6eaff
```

### 2. Synchronization Strategy: Bi-Directional with "Last-Write Wins"

A **bi-directional synchronization** strategy ensures data consistency between the shared local PostgreSQL and the
central online PostgreSQL. This strategy relies on timestamps and a **"last-write wins"** conflict resolution rule.

#### Mechanism

- **Change Data Capture (CDC)**: Changes in both the local and central PostgreSQL databases will be tracked. This can be
  achieved through logical replication, custom triggers, or a dedicated CDC tool like Debezium.
- **Synchronization Triggers**: Synchronization can be initiated through various mechanisms, such as periodic schedules
  (e.g., every 5 minutes), on-demand user actions, or webhooks from the central server signaling a change.
- **Conflict Resolution**: The `last-write wins` policy is applied. When a synchronization event occurs, the record with
  the most recent `last_modified_at` timestamp will be considered the authoritative version. Conflicting changes are
  logged for audit purposes.

#### Synchronization Flow

1. **Initiate Sync**: The `SynchronizationService` in the backend is triggered.
2. **Exchange Changes**: Both the local and central databases exchange data that has changed since the last successful
   sync timestamp.
3. **Apply Changes & Resolve Conflicts**: Each database applies the incoming changes, respecting the "last-write wins"
   rule for any conflicts.
4. **Log & Notify**: All sync operations and conflict resolutions are logged. Notifications can be sent for significant
   events.

### 3. Client-Side Data Access

Client applications (frontend or other services) will primarily interact with the backend API. The backend is
responsible for managing the connection to the appropriate database (local or central) based on the project's
configuration. This abstraction ensures that the client application does not need to manage database connections
directly.

If a true "offline from local network" capability is required for individual clients, a lightweight caching mechanism
(e.g., using IndexedDB) can be implemented to store transient data, which is then synchronized with the shared local
PostgreSQL server upon reconnection to the local network.

### 4. Architectural Pattern Refinement (5-Layer Adaptation)

The 5-layer backend architecture is adapted to support this new model, primarily by enhancing the
`SynchronizationService` and making the data access layer (Repositories) more dynamic.

- **`SynchronizationService`**: This service is now responsible for the full bi-directional sync between a designated
  shared local PostgreSQL instance and the central PostgreSQL database.
- **Repository Layer**: Repositories will be configured dynamically to connect to either the central PostgreSQL or a
  specific shared local PostgreSQL based on the active project's settings. This is managed through the dependency
  injection system.
- **Database Connection Management**: The connection management system will be enhanced to handle a pool of connections
  to the central database as well as dynamic connections to various local PostgreSQL instances as required by active
  projects.

---

## Backend Synchronization Service Architecture

The Ultimate Electrical Designer implements a sophisticated **Backend Synchronization Service** that enables seamless
bi-directional data synchronization between shared local PostgreSQL databases and the central cloud PostgreSQL database.
This service is a critical component of the 5-layer backend architecture, designed to handle complex synchronization
scenarios while maintaining data integrity and providing robust conflict resolution.

### Architecture Overview

The **SynchronizationService** operates within **Layer 2: Business Services** of the 5-layer backend architecture and
coordinates with all other layers to provide comprehensive synchronization capabilities.

```mermaid
graph TB
    subgraph "Layer 1: API Routes"
        SR[Sync Routes<br>/api/v1/sync]
        PR[Project Routes<br>/api/v1/projects]
    end

    subgraph "Layer 2: Business Services"
        SS[SynchronizationService<br>Core Orchestrator]
        PS[ProjectService<br>Business Logic]
    end

    subgraph "Layer 3: Data Repositories"
        SLR[SyncLog Repository<br>Audit Trail]
        PR2[Project Repository<br>CRUD Operations]
    end

    subgraph "Layer 4: Data Models"
        SLM[SynchronizationLog Model<br>Change Tracking]
        PM[Project Model<br>Entity Data]
    end

    subgraph "Layer 5: Validation Schemas"
        SSS[Sync Schemas<br>Request/Response]
        PSS[Project Schemas<br>Data Transfer]
    end

    subgraph "Data Layer"
        LDB[(Local PostgreSQL<br>Shared Database)]
        CDB[(Central PostgreSQL<br>Cloud Database)]
    end

    SR --> SS
    PR --> PS
    SS --> SLR
    SS --> PR2
    PS --> PR2
    SLR --> SLM
    PR2 --> PM
    SS --> SSS
    PS --> PSS
    SLR --> LDB
    SLR --> CDB
    PR2 --> LDB
    PR2 --> CDB

    style SS fill:#ff9999,stroke:#333,stroke-width:3px
    style SLR fill:#99ccff,stroke:#333,stroke-width:2px
```

### Core Components

#### 1. SynchronizationService (Core Orchestrator)

**Location:** `server/src/core/services/general/synchronization_service.py` **Responsibilities:**

- Orchestrate bi-directional synchronization between local and central databases
- Implement Change Data Capture (CDC) mechanisms
- Handle conflict detection and resolution
- Manage synchronization logging and audit trails
- Coordinate with other business services for data operations

**Key Methods:**

```python
class SynchronizationService:
    async def synchronize_project(self, project_id: int) -> SynchronizationResult:
        """
        Main synchronization orchestrator for a specific project.

        Flow:
        1. Retrieve last sync timestamp for the project
        2. Get local changes since last sync (Change Data Capture)
        3. Get central changes since last sync (Change Data Capture)
        4. Apply bidirectional changes with conflict resolution
        5. Update sync timestamps and log operations

        Returns:
            SynchronizationResult with detailed sync statistics
        """

    async def _detect_conflicts(
        self,
        local_changes: List[ChangeRecord],
        central_changes: List[ChangeRecord]
    ) -> List[ConflictRecord]:
        """
        Detect conflicts between local and central changes.

        Conflict Detection Logic:
        - Same entity modified in both databases
        - Timestamps overlap or concurrent modifications
        - Delete vs Update conflicts
        - Schema version mismatches
        """

    async def _resolve_conflict(
        self,
        conflict: ConflictRecord,
        strategy: ConflictResolutionStrategy = ConflictResolutionStrategy.LAST_WRITE_WINS
    ) -> ConflictResolution:
        """
        Resolve detected conflicts using specified strategy.

        Last-Write Wins Implementation:
        1. Compare updated_at timestamps
        2. Choose record with latest timestamp
        3. Log conflict resolution details
        4. Apply winning changes to both databases
        """
```

#### 2. Change Data Capture (CDC) Mechanism

The synchronization service implements a **timestamp-based CDC mechanism** that efficiently identifies and captures
changes across all synchronized entities.

**CDC Implementation:**

```python
async def _get_local_changes(
    self,
    project_id: int,
    since_timestamp: datetime
) -> List[ChangeRecord]:
    """
    Capture local changes since last synchronization.

    Detection Strategy:
    - Query entities with updated_at > since_timestamp
    - Include created_at for new entities
    - Track deleted entities through soft-delete flags
    - Capture related entity changes (cascading updates)

    Returns:
        List of ChangeRecord objects with operation type and data
    """

async def _get_central_changes(
    self,
    project_id: int,
    since_timestamp: datetime
) -> List[ChangeRecord]:
    """
    Capture central database changes since last synchronization.

    Optimizations:
    - Efficient timestamp-based queries with proper indexing
    - Batch processing for large change sets
    - Incremental loading to manage memory usage
    - Change deduplication and ordering
    """
```

#### 3. Conflict Detection and Resolution Framework

The service implements a comprehensive conflict detection and resolution system with support for multiple strategies,
currently featuring **Last-Write Wins** as the primary strategy.

**Conflict Types Handled:**

```python
class ConflictType(Enum):
    UPDATE_UPDATE = "update_update"      # Same entity updated in both DBs
    CREATE_CREATE = "create_create"      # Same entity created in both DBs
    UPDATE_DELETE = "update_delete"      # Entity updated locally, deleted centrally
    DELETE_UPDATE = "delete_update"      # Entity deleted locally, updated centrally
    SCHEMA_MISMATCH = "schema_mismatch"  # Different schema versions
```

**Last-Write Wins Resolution:**

```python
async def _apply_last_write_wins(
    self,
    conflict: ConflictRecord
) -> ConflictResolution:
    """
    Apply Last-Write Wins conflict resolution strategy.

    Algorithm:
    1. Extract timestamps from local and central versions
    2. Compare timestamps with microsecond precision
    3. Handle edge cases (identical timestamps, null timestamps)
    4. Apply winning version to both databases
    5. Create detailed resolution audit log

    Edge Cases:
    - Identical timestamps: Use tie-breaker (server ID, creation order)
    - Missing timestamps: Treat as oldest version
    - Clock skew: Apply tolerance window for near-simultaneous updates
    """

    local_timestamp = conflict.local_data.get('updated_at')
    central_timestamp = conflict.central_data.get('updated_at')

    if not local_timestamp or not central_timestamp:
        # Handle missing timestamp edge case
        winner = 'central' if central_timestamp else 'local'
    else:
        # Compare with microsecond precision
        local_dt = datetime.fromisoformat(local_timestamp)
        central_dt = datetime.fromisoformat(central_timestamp)
        winner = 'central' if central_dt > local_dt else 'local'

    # Apply winning version and log resolution
    return await self._apply_resolution(conflict, winner)
```

#### 4. Bi-Directional Synchronization Flow

The service orchestrates a comprehensive bi-directional synchronization process that ensures data consistency across
both database systems.

**Synchronization Sequence Diagram:**

```mermaid
sequenceDiagram
    participant API as Sync API
    participant SS as SynchronizationService
    participant LDB as Local PostgreSQL
    participant CDB as Central PostgreSQL
    participant SL as SyncLog Repository

    API->>SS: synchronize_project(project_id)
    SS->>SL: get_last_sync_timestamp(project_id)
    SL-->>SS: last_sync_timestamp

    par Local Changes
        SS->>LDB: query_changes_since(timestamp)
        LDB-->>SS: local_changes[]
    and Central Changes
        SS->>CDB: query_changes_since(timestamp)
        CDB-->>SS: central_changes[]
    end

    SS->>SS: detect_conflicts(local_changes, central_changes)

    loop For each conflict
        SS->>SS: resolve_conflict(conflict, LAST_WRITE_WINS)
        SS->>SL: log_conflict_resolution(conflict, resolution)
    end

    par Apply Local to Central
        SS->>CDB: apply_changes(local_changes)
        CDB-->>SS: local_to_central_result
    and Apply Central to Local
        SS->>LDB: apply_changes(central_changes)
        LDB-->>SS: central_to_local_result
    end

    SS->>SL: update_sync_timestamp(project_id, now())
    SS->>SL: log_sync_operation(sync_result)
    SS-->>API: SynchronizationResult
```

#### 5. SynchronizationLog Functionality

The service maintains comprehensive audit trails and synchronization history through the **SynchronizationLog** system.

**SynchronizationLog Model Structure:**

```python
class SynchronizationLog(Base):
    """
    Comprehensive audit trail for all synchronization operations.

    Tracks:
    - Sync operation metadata (timestamps, duration, status)
    - Change statistics (created, updated, deleted counts)
    - Conflict resolution details (detected, resolved, strategies)
    - Performance metrics (processing time, data volume)
    - Error information (failures, retries, recovery)
    """

    id: int = Column(Integer, primary_key=True)
    project_id: int = Column(Integer, ForeignKey('project.id'))

    # Operation Metadata
    sync_direction: str = Column(String(20))  # 'bidirectional', 'local_to_central', 'central_to_local'
    operation_type: str = Column(String(50))  # 'scheduled', 'manual', 'conflict_resolution'

    # Timestamps
    started_at: datetime = Column(DateTime(timezone=True))
    completed_at: datetime = Column(DateTime(timezone=True))
    duration_ms: int = Column(Integer)

    # Change Statistics
    local_to_central: dict = Column(JSON)     # {'created': 5, 'updated': 3, 'deleted': 1, 'errors': 0}
    central_to_local: dict = Column(JSON)     # {'created': 2, 'updated': 8, 'deleted': 0, 'errors': 0}

    # Conflict Resolution
    conflicts_detected: int = Column(Integer, default=0)
    conflicts_resolved: int = Column(Integer, default=0)
    conflict_resolution_strategy: str = Column(String(50))

    # Status and Error Handling
    status: str = Column(String(20))          # 'completed', 'failed', 'partial'
    error_message: str = Column(Text, nullable=True)
    retry_count: int = Column(Integer, default=0)
```

**Key Sync Logging Methods:**

```python
async def log_sync_operation(
    self,
    project_id: int,
    sync_result: SynchronizationResult
) -> SynchronizationLog:
    """
    Create comprehensive sync operation log entry.

    Captures:
    - Complete operation timeline and performance metrics
    - Detailed change statistics for both directions
    - Conflict resolution summary and strategy effectiveness
    - Error details and recovery information
    - Data volume and processing efficiency metrics
    """

async def get_sync_history(
    self,
    project_id: int,
    limit: int = 50
) -> List[SynchronizationLog]:
    """
    Retrieve synchronization history for analysis and debugging.

    Uses:
    - Performance monitoring and optimization
    - Conflict pattern analysis
    - Troubleshooting sync issues
    - Audit compliance and reporting
    """
```

### Integration with 5-Layer Architecture

The Backend Synchronization Service is seamlessly integrated within the existing 5-layer architecture:

**Layer 1 Integration (API Routes):**

```python
@router.post("/projects/{project_id}/sync")
async def synchronize_project(
    project_id: int,
    sync_service: SynchronizationService = Depends(get_sync_service),
    current_user: User = Depends(get_current_user)
) -> SynchronizationResultSchema:
    """
    Trigger manual project synchronization.

    Validates:
    - User permissions for project access
    - Project exists and is sync-enabled
    - No concurrent sync operations
    """
    return await sync_service.synchronize_project(project_id)
```

**Layer 3 Integration (Data Repositories):**

```python
class ProjectRepository:
    async def get_changes_since(
        self,
        project_id: int,
        since_timestamp: datetime
    ) -> List[ChangeRecord]:
        """
        Repository method supporting CDC for synchronization.

        Optimized for:
        - Efficient timestamp-based queries
        - Proper indexing strategy
        - Change record formatting
        - Batch processing support
        """
```

**Layer 5 Integration (Validation Schemas):**

```python
class SynchronizationResultSchema(BaseModel):
    """
    Response schema for synchronization operations.

    Provides:
    - Type-safe API responses
    - Comprehensive result validation
    - Client-side TypeScript generation
    - API documentation integration
    """

    project_id: int
    status: SyncStatus
    local_to_central: ChangeStatistics
    central_to_local: ChangeStatistics
    conflicts_detected: int
    conflicts_resolved: int
    conflict_resolution_strategy: str
    sync_direction: str
    started_at: datetime
    completed_at: datetime
    duration_ms: int
    message: str
```

### Performance and Scalability Considerations

The Backend Synchronization Service is designed with enterprise-grade performance and scalability requirements:

**Optimization Strategies:**

- **Batched Processing**: Large change sets processed in configurable batches
- **Concurrent Operations**: Parallel processing of local-to-central and central-to-local sync
- **Efficient Queries**: Optimized timestamp-based queries with proper indexing
- **Memory Management**: Streaming large datasets to prevent memory exhaustion
- **Connection Pooling**: Efficient database connection reuse across sync operations
- **Async Processing**: Non-blocking operations for improved throughput

**Scalability Features:**

- **Horizontal Scaling**: Multiple sync service instances with coordination
- **Database Partitioning**: Project-based data partitioning for large deployments
- **Caching Strategy**: Intelligent caching of sync metadata and conflict patterns
- **Monitoring Integration**: Comprehensive metrics and alerting for production monitoring

---

## Frontend Caching & Offline Handling Architecture

The Ultimate Electrical Designer implements a sophisticated **Frontend Caching & Offline Handling system** that provides
seamless offline capabilities and intelligent client-side data management. This system enables users to continue working
productively even when disconnected from the server, with automatic synchronization when connectivity is restored.

### Architecture Overview

The frontend offline system integrates multiple advanced technologies to create a robust, user-friendly offline
experience:

```mermaid
graph TB
    subgraph "React Frontend Layer"
        UI[User Interface Components<br>Projects, Forms, Views]
        OM[useOfflineMutation Hook<br>Offline Operation Handler]
    end

    subgraph "State Management Layer"
        RQ[React Query<br>Server State Manager]
        ZU[Zustand<br>Client State Manager]
    end

    subgraph "Persistence Layer"
        IDP[IndexedDBPersister<br>React Query Persister]
        MO[Mutation Outbox<br>IndexedDB Store]
        QC[Query Cache<br>IndexedDB Store]
    end

    subgraph "Synchronization Layer"
        SM[SyncManager<br>Network & Sync Orchestrator]
        NM[NetworkMonitor<br>Connection Quality Detection]
        CR[ConflictResolver<br>Client-Side Resolution]
    end

    subgraph "Storage Layer"
        IDB[(IndexedDB<br>Browser Database)]
        LS[LocalStorage<br>Preferences & Settings]
    end

    subgraph "Network Layer"
        API[Backend API<br>Sync Endpoints]
        WS[WebSocket<br>Real-time Updates]
    end

    UI --> OM
    UI --> RQ
    UI --> ZU
    OM --> MO
    RQ --> IDP
    RQ --> QC
    IDP --> IDB
    MO --> IDB
    QC --> IDB
    SM --> MO
    SM --> NM
    SM --> CR
    SM --> API
    NM --> SM
    CR --> RQ
    ZU --> LS
    API --> WS

    style SM fill:#ff9999,stroke:#333,stroke-width:3px
    style OM fill:#99ccff,stroke:#333,stroke-width:3px
    style IDP fill:#99ff99,stroke:#333,stroke-width:2px
```

### Core Components

#### 1. IndexedDBPersister (React Query Integration)

**Location:** `client/src/lib/cache/indexeddb-persister.ts` **Purpose:** Provides seamless persistence of React Query
server state to IndexedDB, enabling data survival across browser sessions and page reloads.

**Key Features:**

- **Comprehensive State Persistence**: Stores complete React Query client state including queries, mutations, and
  metadata
- **Efficient Serialization**: Optimized JSON serialization with data deduplication
- **Automatic Restoration**: Seamless state restoration on application initialization
- **Performance Optimized**: Batched operations and intelligent caching strategies

**Implementation Details:**

```typescript
export class IndexedDBPersister implements Persister {
  private readonly dbName = "query-cache";
  private readonly version = 1;
  private db: IDBDatabase | null = null;

  async persistClient(client: PersistedClient): Promise<void> {
    /**
     * Persist React Query client state to IndexedDB.
     *
     * Process:
     * 1. Serialize client state with metadata
     * 2. Store in query-cache object store
     * 3. Handle storage quota limits gracefully
     * 4. Maintain integrity with atomic transactions
     */

    const db = await this.getDatabase();
    const transaction = db.transaction(["query-cache"], "readwrite");
    const store = transaction.objectStore("query-cache");

    // Enhanced serialization with compression
    const serializedData = JSON.stringify({
      clientState: client.clientState,
      timestamp: Date.now(),
      version: this.version,
      metadata: {
        queryCount: Object.keys(client.clientState.queries).length,
        mutationCount: Object.keys(client.clientState.mutations).length,
      },
    });

    await store.put(serializedData, "react-query-state");
  }

  async restoreClient(): Promise<PersistedClient | undefined> {
    /**
     * Restore React Query client state from IndexedDB.
     *
     * Features:
     * - Version compatibility checking
     * - Graceful degradation for corrupted data
     * - Selective restoration based on data freshness
     * - Automatic cleanup of stale entries
     */

    try {
      const db = await this.getDatabase();
      const transaction = db.transaction(["query-cache"], "readonly");
      const store = transaction.objectStore("query-cache");
      const data = await store.get("react-query-state");

      if (data) {
        const parsed = JSON.parse(data);

        // Version and freshness validation
        if (this.isValidAndFresh(parsed)) {
          return {
            clientState: parsed.clientState,
            timestamp: parsed.timestamp,
          };
        }
      }
    } catch (error) {
      console.warn("Failed to restore client state:", error);
      // Graceful degradation - continue without cached state
    }

    return undefined;
  }
}
```

#### 2. useOfflineMutation Hook (Outbox Pattern)

**Location:** `client/src/hooks/cache/useOfflineMutation.ts` **Purpose:** Provides seamless offline mutation capability
using the outbox pattern, queuing operations when offline and automatically synchronizing when online.

**Outbox Pattern Implementation:**

```typescript
export function useOfflineMutation<TData, TError, TVariables>(
  options: OfflineMutationOptions<TData, TError, TVariables>
) {
  /**
   * Enhanced offline mutation hook with comprehensive error handling.
   *
   * Features:
   * - Automatic offline detection and outbox queuing
   * - Optimistic UI updates with rollback capability
   * - Conflict resolution integration
   * - Retry logic with exponential backoff
   * - Progress tracking and user feedback
   */

  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [syncManager] = useState(() => SyncManager.getInstance());

  return useMutation<TData, TError, TVariables>({
    mutationFn: async (variables) => {
      if (isOnline) {
        // Direct API call when online
        return await options.mutationFn(variables);
      } else {
        // Queue in outbox when offline
        const outboxEntry: OutboxEntry = {
          id: generateId(),
          endpoint: options.endpoint,
          method: options.method || "POST",
          variables,
          timestamp: new Date().toISOString(),
          retryCount: 0,
          status: "pending",
        };

        await syncManager.addToOutbox(outboxEntry);

        // Return optimistic result
        return options.optimisticResponse?.(variables) || (variables as TData);
      }
    },

    onSuccess: (data, variables) => {
      // Handle optimistic updates and cache invalidation
      options.onSuccess?.(data, variables);
    },

    onError: (error, variables) => {
      // Enhanced error handling with offline context
      if (!isOnline) {
        // Queue failed operations for retry when online
        console.log("Operation queued for retry when online");
      }
      options.onError?.(error, variables);
    },
  });
}
```

**Outbox Data Structure:**

```typescript
interface OutboxEntry {
  id: string;
  endpoint: string;
  method: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  variables: any;
  timestamp: string;
  retryCount: number;
  status: "pending" | "processing" | "completed" | "failed";

  // Enhanced metadata
  metadata?: {
    entityType: string;
    entityId?: string | number;
    operation: "create" | "update" | "delete";
    priority: "high" | "medium" | "low";
    dependencies?: string[]; // Other outbox entry IDs this depends on
  };

  // Conflict resolution data
  baseVersion?: string;
  optimisticResult?: any;

  // Error information
  lastError?: string;
  maxRetries?: number;
}
```

#### 3. SyncManager (Network & Synchronization Orchestration)

**Location:** `client/src/lib/sync/sync-manager.ts` **Purpose:** Central orchestrator for all offline synchronization
activities, network monitoring, and conflict resolution coordination.

**Core Responsibilities:**

```typescript
export class SyncManager {
  private static instance: SyncManager;
  private networkMonitor: NetworkMonitor;
  private isProcessing = false;
  private processingQueue = new Set<string>();

  private constructor() {
    this.networkMonitor = new NetworkMonitor();
    this.setupEventListeners();
  }

  async processOutbox(): Promise<SyncResult> {
    /**
     * Process all pending mutations in the outbox.
     *
     * Processing Strategy:
     * 1. Retrieve all pending entries from IndexedDB
     * 2. Sort by priority and timestamp
     * 3. Process in batches with dependency resolution
     * 4. Handle conflicts using client-side resolution
     * 5. Update outbox status and clean up completed entries
     *
     * Returns:
     *   Comprehensive sync result with statistics
     */

    if (this.isProcessing) {
      return {
        status: "already_processing",
        message: "Sync already in progress",
      };
    }

    this.isProcessing = true;
    const startTime = Date.now();

    try {
      // Get pending mutations with dependency ordering
      const pendingMutations = await this.getOutboxEntries();
      const sortedMutations = this.sortByDependencies(pendingMutations);

      const results = {
        processed: 0,
        completed: 0,
        failed: 0,
        conflicts: 0,
      };

      // Process in batches to avoid overwhelming the server
      for (const batch of this.createBatches(sortedMutations, 5)) {
        await Promise.allSettled(batch.map((entry) => this.processSingleEntry(entry, results)));
      }

      return {
        status: "completed",
        duration: Date.now() - startTime,
        ...results,
      };
    } finally {
      this.isProcessing = false;
    }
  }

  private async processSingleEntry(entry: OutboxEntry, results: SyncResults): Promise<void> {
    /**
     * Process individual outbox entry with comprehensive error handling.
     *
     * Processing Flow:
     * 1. Validate entry and check dependencies
     * 2. Execute API call with retry logic
     * 3. Handle conflicts with resolution strategies
     * 4. Update local cache with server response
     * 5. Mark entry as completed or retry if failed
     */

    try {
      results.processed++;
      this.processingQueue.add(entry.id);

      // Execute API call
      const response = await this.executeApiCall(entry);

      if (response.status === "conflict") {
        // Handle conflict using resolution strategy
        const resolution = await this.resolveConflict(entry, response);
        results.conflicts++;

        if (resolution.success) {
          await this.updateLocalCache(resolution.resolvedData);
          results.completed++;
        }
      } else {
        // Successful operation
        await this.updateLocalCache(response.data);
        results.completed++;
      }

      // Clean up completed entry
      await this.removeFromOutbox(entry.id);
    } catch (error) {
      results.failed++;

      // Update retry count and schedule retry if applicable
      if (entry.retryCount < (entry.maxRetries || 3)) {
        entry.retryCount++;
        entry.lastError = error instanceof Error ? error.message : "Unknown error";
        await this.updateOutboxEntry(entry);
      } else {
        // Max retries exceeded - log and remove
        console.error(`Max retries exceeded for outbox entry ${entry.id}:`, error);
        await this.removeFromOutbox(entry.id);
      }
    } finally {
      this.processingQueue.delete(entry.id);
    }
  }
}
```

#### 4. NetworkMonitor (Connection Quality Detection)

**Location:** `client/src/lib/sync/network-monitor.ts` **Purpose:** Intelligent network status monitoring with
connection quality assessment and adaptive behavior.

**Advanced Network Detection:**

```typescript
export class NetworkMonitor {
  private connectionQuality: ConnectionQuality = "unknown";
  private listeners = new Set<NetworkStatusListener>();
  private lastOnlineCheck = Date.now();
  private readonly QUALITY_CHECK_INTERVAL = 30000; // 30 seconds

  constructor() {
    this.setupEventListeners();
    this.startQualityMonitoring();
  }

  async detectConnectionQuality(): Promise<ConnectionQuality> {
    /**
     * Assess network connection quality through multiple indicators.
     *
     * Assessment Factors:
     * - Network Information API (connection type, downlink speed)
     * - Response time to lightweight API endpoint
     * - Historical success/failure rates
     * - Browser-reported connection status
     *
     * Returns:
     *   'fast' | 'slow' | 'unstable' | 'offline' | 'unknown'
     */

    if (!navigator.onLine) {
      return "offline";
    }

    try {
      // Use Network Information API if available
      const connection = (navigator as any).connection;
      if (connection) {
        const { effectiveType, downlink, rtt } = connection;

        if (effectiveType === "4g" && downlink > 1.5) {
          return "fast";
        } else if (effectiveType === "3g" || (downlink > 0.5 && rtt < 300)) {
          return "slow";
        } else if (rtt > 500 || downlink < 0.2) {
          return "unstable";
        }
      }

      // Fallback: Measure response time to API health check
      const startTime = Date.now();
      const response = await fetch("/api/v1/health/ping", {
        method: "HEAD",
        signal: AbortSignal.timeout(5000),
      });

      const responseTime = Date.now() - startTime;

      if (response.ok && responseTime < 200) {
        return "fast";
      } else if (response.ok && responseTime < 1000) {
        return "slow";
      } else {
        return "unstable";
      }
    } catch (error) {
      return "unstable";
    }
  }

  getAdaptiveSyncStrategy(): SyncStrategy {
    /**
     * Determine optimal sync strategy based on connection quality.
     *
     * Strategies:
     * - Fast: Immediate sync, large batches, real-time updates
     * - Slow: Batched sync, compressed payloads, reduced frequency
     * - Unstable: Conservative sync, small batches, aggressive retries
     * - Offline: Queue all operations, no network activity
     */

    switch (this.connectionQuality) {
      case "fast":
        return {
          batchSize: 10,
          syncInterval: 5000,
          retryStrategy: "immediate",
          compression: false,
        };
      case "slow":
        return {
          batchSize: 3,
          syncInterval: 15000,
          retryStrategy: "exponential",
          compression: true,
        };
      case "unstable":
        return {
          batchSize: 1,
          syncInterval: 30000,
          retryStrategy: "exponential_with_jitter",
          compression: true,
        };
      case "offline":
        return {
          batchSize: 0,
          syncInterval: 0,
          retryStrategy: "none",
          compression: false,
        };
      default:
        return {
          batchSize: 5,
          syncInterval: 10000,
          retryStrategy: "linear",
          compression: false,
        };
    }
  }
}
```

#### 5. Client-Side Cache Integration

The system provides comprehensive integration with React Query for optimal server state management:

**Cache Provider Setup:**

```typescript
// client/src/components/providers/cache-provider.tsx
export function CacheProvider({ children }: { children: React.ReactNode }) {
  /**
   * Global cache provider with IndexedDB persistence and offline capabilities.
   *
   * Features:
   * - Automatic state persistence across browser sessions
   * - Intelligent cache invalidation strategies
   * - Background sync with conflict resolution
   * - Performance optimization with query deduplication
   */

  const [queryClient] = useState(
    () =>
      new QueryClient({
        defaultOptions: {
          queries: {
            // Extended stale time for offline capability
            staleTime: 1000 * 60 * 5, // 5 minutes
            cacheTime: 1000 * 60 * 30, // 30 minutes

            // Retry with exponential backoff
            retry: (failureCount, error) => {
              // Don't retry on 4xx errors (client errors)
              if (error instanceof Error && error.message.includes("4")) {
                return false;
              }
              return failureCount < 3;
            },

            // Custom retry delay with network-aware backoff
            retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
          },
          mutations: {
            // Enhanced mutation error handling
            onError: (error, variables, context) => {
              console.error("Mutation failed:", error);

              // Automatic retry for network errors
              if (!navigator.onLine || error.message.includes("NetworkError")) {
                // Will be handled by offline mutation system
                console.log("Queueing mutation for offline retry");
              }
            },
          },
        },
      })
  );

  const [persister] = useState(() => new IndexedDBPersister());

  return (
    <QueryClientProvider client={queryClient}>
      <PersistQueryClientProvider
        client={queryClient}
        persistOptions={{
          persister,
          maxAge: 1000 * 60 * 60 * 24 * 7, // 1 week
          buster: process.env.NEXT_PUBLIC_APP_VERSION, // Cache bust on version change
        }}
      >
        {children}
      </PersistQueryClientProvider>
    </QueryClientProvider>
  );
}
```

### Data Flow and User Experience

**Offline-to-Online User Journey:**

```mermaid
sequenceDiagram
    participant U as User
    participant UI as React UI
    participant OM as useOfflineMutation
    participant O as Outbox (IndexedDB)
    participant SM as SyncManager
    participant NM as NetworkMonitor
    participant API as Backend API
    participant RQ as React Query

    U->>UI: Create Project (Offline)
    UI->>OM: Execute Mutation
    OM->>O: Add to Outbox
    OM->>UI: Optimistic Update
    UI->>U: Show Success (Offline Badge)

    Note over NM: Network comes online

    NM->>SM: Trigger Online Event
    SM->>O: Process Outbox
    SM->>API: Sync Project Creation
    API->>SM: Success Response
    SM->>RQ: Update Cache
    RQ->>UI: Remove Offline Badge
    UI->>U: Sync Complete
```

### Integration with React Query Ecosystem

The offline system seamlessly integrates with the React Query ecosystem:

**Query Invalidation and Updates:**

```typescript
// Automatic cache updates after sync
const syncManager = SyncManager.getInstance();

syncManager.onSyncComplete((result) => {
  // Invalidate affected queries
  queryClient.invalidateQueries({
    queryKey: ["projects"],
  });

  // Update specific cached data
  result.completedOperations.forEach((operation) => {
    if (operation.type === "create") {
      queryClient.setQueryData(["projects", operation.entityId], operation.result);
    }
  });
});
```

**Optimistic Updates with Rollback:**

```typescript
const updateProject = useOfflineMutation({
  mutationFn: updateProjectApi,
  onMutate: async (variables) => {
    // Cancel any outgoing refetches
    await queryClient.cancelQueries({ queryKey: ["projects", variables.id] });

    // Snapshot previous value
    const previousProject = queryClient.getQueryData(["projects", variables.id]);

    // Optimistic update
    queryClient.setQueryData(["projects", variables.id], (old) => ({
      ...old,
      ...variables,
      _optimistic: true,
    }));

    return { previousProject };
  },
  onError: (err, variables, context) => {
    // Rollback on error
    if (context?.previousProject) {
      queryClient.setQueryData(["projects", variables.id], context.previousProject);
    }
  },
  onSettled: () => {
    // Always refetch after mutation
    queryClient.invalidateQueries({ queryKey: ["projects"] });
  },
});
```

### Performance and User Experience Optimizations

**Intelligent Caching Strategy:**

- **Layered Caching**: Memory → IndexedDB → Network with intelligent fallbacks
- **Selective Persistence**: Only cache frequently accessed and offline-critical data
- **Compression**: LZ-string compression for large cached datasets
- **Cleanup**: Automatic cleanup of stale cache entries with configurable TTL

**User Experience Enhancements:**

- **Offline Indicators**: Visual indicators for offline status and pending sync
- **Progress Tracking**: Real-time sync progress with detailed status updates
- **Conflict Notifications**: User-friendly conflict resolution with clear explanations
- **Background Sync**: Non-intrusive background synchronization with minimal user interruption
- **Graceful Degradation**: Seamless experience transition between online and offline modes

---

## Component Interactions

### Authentication Flow

```
┌─────────────┐    1. Login Request    ┌─────────────────┐
│   Client    │ ────────────────────► │   Auth Routes   │
│  (React)    │                       │   (FastAPI)     │
└─────────────┘                       └─────────────────┘
       │                                       │
       │                              2. Validate Credentials
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │  Auth Service   │
       │                               │   (Business)    │
       │                               └─────────────────┘
       │                                       │
       │                              3. Query User Data
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │ User Repository │
       │                               │  (Data Access)  │
       │                               └─────────────────┘
       │                                       │
       │                              4. Database Query
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │   Database      │
       │                               │ (PostgreSQL)    │
       │                               └─────────────────┘
       │
       │ 5. JWT Token Response
       ◄────────────────────────────────────────────────────
```

### Component Management Flow

```
┌─────────────┐  1. Component Request  ┌─────────────────┐
│   Client    │ ────────────────────► │Component Routes │
│  (React)    │                       │   (FastAPI)     │
└─────────────┘                       └─────────────────┘
       │                                       │
       │                              2. Business Logic
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │Component Service│
       │                               │   (Business)    │
       │                               └─────────────────┘
       │                                       │
       │                              3. Data Operations
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │Component Repo   │
       │                               │  (Data Access)  │
       │                               └─────────────────┘
       │                                       │
       │                              4. CRUD Operations
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │   Database      │
       │                               │ (PostgreSQL)    │
       │                               └─────────────────┘
       │
       │ 5. Component Data Response
       ◄────────────────────────────────────────────────────
```

---

## Database Schema Design

### User Management

- **Table:** User
  - **Email Field Enhancement**: Case-insensitive uniqueness constraint with application-level normalization
  - **Database Constraints**: Foreign key constraints enabled for referential integrity
  - **Validation Layer**: Comprehensive string length validation with boundary condition testing

* **Table:** UserPreference
* **Table:** UserRole
* **Table:** UserRoleAssignment

### Component Library

- **Table:** Component

* **Table:** ComponentCategory
* **Table:** ComponentType

### Electrical System Design

- **Table:** Project

* **Table:** ElectricalSystem
* **Table:** Circuit
* **Table:** Load

### Cable Management

- **Table:** CableType

### Calculations

- **Table:** CalculationTemplate

* **Table:** Calculation

### Activity Logging and Audit

- **Table:** ActivityLog

* **Table:** AuditTrail

---

## Frontend Component Architecture

### Atomic Design System

The Ultimate Electrical Designer implements **Atomic Design methodology** across all feature modules, providing a
consistent, scalable, and maintainable component architecture. This system ensures architectural consistency and
reusability across the entire application.

#### Atomic Design Hierarchy

```
App (layout.tsx)
├── Header
│   ├── Navigation
│   ├── UserMenu
│   └── ThemeToggle
├── Main Content
│   ├── Dashboard
│   │   ├── ProjectSummary
│   │   ├── RecentCalculations
│   │   └── QuickActions
│   ├── Components Module (Atomic Design)
│   │   ├── Atoms/
│   │   │   ├── ComponentBadge
│   │   │   ├── StatusIndicator
│   │   │   ├── ActionButton
│   │   │   └── SearchInput
│   │   ├── Molecules/
│   │   │   ├── ComponentCard
│   │   │   ├── FilterPanel
│   │   │   ├── SearchBar
│   │   │   └── BulkActionBar
│   │   ├── Organisms/
│   │   │   ├── ComponentList
│   │   │   ├── ComponentForm
│   │   │   └── ComponentTable
│   │   └── Templates/
│   │       ├── ComponentListPage
│   │       └── ComponentDetailPage
│   ├── Projects Module (Atomic Design)
│   │   ├── Atoms/
│   │   │   ├── StatusBadge
│   │   │   ├── PriorityBadge
│   │   │   ├── ActionButton
│   │   │   ├── FormInput
│   │   │   ├── LoadingSpinner
│   │   │   └── EmptyState
│   │   ├── Molecules/
│   │   │   ├── SearchBar
│   │   │   ├── BulkActionBar
│   │   │   ├── FilterPanel
│   │   │   ├── MemberCard
│   │   │   └── MemberForm
│   │   ├── Organisms/
│   │   │   ├── ProjectList
│   │   │   ├── TeamManagement
│   │   │   └── ProjectForm
│   │   └── Pages/
│   │       ├── /projects (list)
│   │       ├── /projects/new (creation)
│   │       └── /projects/[id] (detail)
│   └── Calculations Module
│       ├── CalculationWizard
│       ├── ResultsDisplay
│       ├── ReportGenerator
│       └── ParameterForm
└── Footer
    ├── StatusBar
    └── HelpLinks
```

#### Atomic Design Principles

**Atoms**: Basic building blocks that can't be broken down further

- Single responsibility (one UI element, one purpose)
- Highly reusable across multiple contexts
- No business logic, pure presentation
- Examples: `StatusBadge`, `ActionButton`, `FormInput`

**Molecules**: Simple combinations of atoms with a specific function

- Combine multiple atoms for a focused purpose
- Reusable components with defined interfaces
- May contain simple state management
- Examples: `SearchBar`, `MemberCard`, `FilterPanel`

**Organisms**: Complex components combining molecules and atoms

- Represent distinct sections of an interface
- Can contain business logic and domain integration
- Connected to application state and APIs
- Examples: `ProjectList`, `TeamManagement`, `ComponentTable`

**Templates**: Page-level layouts defining content structure

- Define content areas and responsive behavior
- No specific content, just layout structure
- Consistent spacing and accessibility patterns

**Pages**: Specific instances of templates with real content

- Complete user interfaces for specific routes
- Integration with routing and data fetching
- Examples: `/projects`, `/projects/new`, `/projects/[id]`

### Domain Integration

Each atomic design module maintains **domain-aware integration**:

```typescript
// Domain-aware hooks integration
const { useProjectHooks } = useProjectHooks(); // Application services
const { useProjectTeam } = useProjectTeam(); // Team management
const { useProjectStatus } = useProjectStatus(); // Status management

// State management integration
const projects = useProjectStore((state) => state.projects); // Zustand
const { data, isLoading } = useProjectsQuery(); // React Query
```

### Component Composition Patterns

**Compound Components**: Complex components built from atomic parts

```typescript
<ProjectList>
  <ProjectList.Header>
    <SearchBar />
    <FilterPanel />
    <BulkActionBar />
  </ProjectList.Header>
  <ProjectList.Content>
    {projects.map((project) => (
      <ProjectCard key={project.id} project={project} />
    ))}
  </ProjectList.Content>
</ProjectList>
```

**Render Props**: Flexible component composition

```typescript
<DataTable
  data={projects}
  renderRow={(project) => <ProjectRow project={project} />}
  renderHeader={() => <ProjectHeader />}
  renderEmpty={() => <EmptyState />}
/>
```

### Accessibility & Performance

**Accessibility Integration**:

- WCAG 2.1 AA compliance at atomic level
- Semantic HTML structure in all atoms
- Keyboard navigation support
- Screen reader compatibility
- Focus management

**Performance Optimization**:

- React.memo for atoms and molecules
- useMemo for expensive calculations
- useCallback for event handlers
- Code splitting at organism level
- Lazy loading for large components

---

## Data Validation & Integrity Architecture

### Email Normalization System

```python
# Schema Layer (Pydantic) - Email Normalization
@field_validator("email")
@classmethod
def normalize_email(cls, v: str) -> str:
    """Normalize email to lowercase for consistency."""
    return v.lower().strip() if v else v
```

```python
# Repository Layer - Case-Insensitive Lookup
def get_by_email(self, email: str) -> Optional[User]:
    """Get user by email address (case-insensitive)."""
    normalized_email = email.lower().strip()
    stmt = select(self.model).where(
        and_(
            func.lower(self.model.email) == normalized_email,
            self.model.is_active == True,
        )
    )
    return self.db_session.scalar(stmt)
```

```python
# Service Layer - Enhanced Validation
def _validate_user_creation(self, user_data: UserCreateSchema) -> None:
    """Perform business validation for user creation."""
    if self.user_repo.check_email_exists(user_data.email):
        raise InvalidInputError(
            f"Email address '{user_data.email}' is already registered. "
            "Please use a different email or sign in to your existing account."
        )
```

### String Length Validation System

```python
# Schema-Level Validation with Specific Error Messages
@field_validator("name")
@classmethod
def validate_name(cls, v: str) -> str:
    """Validate and normalize name field."""
    if not v or not v.strip():
        raise ValueError("Name cannot be empty or just whitespace")
    name = v.strip()
    if len(name) < 3:
        raise ValueError("Name must be at least 3 characters long")
    if len(name) > 50:
        raise ValueError("Name cannot exceed 50 characters")
    return name
```

### Database Constraint Enforcement

```python
# Test Environment - PostgreSQL Configuration
# PostgreSQL automatically enforces foreign key constraints
# No additional configuration needed for constraint enforcement
```

---

## Security Architecture

### Authentication & Authorization Flow

```
┌─────────────┐    1. Login Request    ┌─────────────────┐
│   Client    │ ────────────────────► │Security Middleware│
└─────────────┘                       └─────────────────┘
       │                                       │
       │                              2. Validate JWT
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │Security Validator│
       │                               └─────────────────┘
       │                                       │
       │                              3. Check Permissions
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │   Auth Service  │
       │                               └─────────────────┘
       │                                       │
       │                              4. Route to Handler
       │                                       ▼
       │                               ┌─────────────────┐
       │                               │  API Endpoint   │
       │                               └─────────────────┘
```

### Security Layers

1. **Transport Security**: TLS 1.3 encryption for all communications
2. **Authentication**: JWT tokens with RS256 signing
3. **Authorization**: Role-based access control (RBAC)
4. **Input Validation**: Pydantic schemas for all API inputs
5. **Output Encoding**: XSS prevention through proper encoding
6. **SQL Injection Prevention**: Parameterized queries only
7. **Rate Limiting**: API endpoint protection against abuse
8. **CORS Configuration**: Controlled cross-origin access

---

## Performance Architecture

### Caching Strategy

```
┌─────────────┐    Request    ┌─────────────────┐
│   Client    │ ──────────► │   API Gateway   │
└─────────────┘              └─────────────────┘
                                      │
                              Check Cache
                                      ▼
                              ┌─────────────────┐
                              │  Redis Cache    │
                              │  (Session Data) │
                              └─────────────────┘
                                      │
                              Cache Miss
                                      ▼
                              ┌─────────────────┐
                              │   Database      │
                              │  (PostgreSQL)   │
                              └─────────────────┘
```

### Performance Optimization

1. **Database Optimization**:

   - Strategic indexing for common queries
   - Connection pooling with configurable limits
   - Query optimization with EXPLAIN analysis
   - Read replicas for reporting queries

2. **Application Optimization**:

   - Async/await for non-blocking operations
   - Lazy loading for large datasets
   - Pagination for list endpoints
   - Background tasks for heavy calculations

3. **Frontend Optimization**:
   - Code splitting with dynamic imports
   - Image optimization with Next.js
   - Bundle analysis and tree shaking
   - Service worker for offline capabilities

---

## Integration Patterns

### Real-time Collaboration

```typescript
// WebSocket connection management
class CollaborationService {
  private ws: WebSocket;

  connect(projectId: string) {
    this.ws = new WebSocket(`wss://api.example.com/ws/project/${projectId}`);
    this.ws.onmessage = this.handleMessage;
  }

  sendUpdate(update: ProjectUpdate) {
    this.ws.send(
      JSON.stringify({
        type: "project_update",
        data: update,
        timestamp: new Date().toISOString(),
      })
    );
  }

  private handleMessage(event: MessageEvent) {
    const message = JSON.parse(event.data);
    // Handle real-time updates
  }
}
```

### Data Flow Patterns

1. **Unidirectional Data Flow**: React Query for server state, Zustand for client state
2. **Optimistic Updates**: Immediate UI updates with rollback on failure
3. **Background Sync**: Automatic data synchronization with conflict resolution
4. **Offline Support**: Local storage with sync on reconnection

---

This technical design specification provides the comprehensive architectural foundation for implementing the Ultimate
Electrical Designer platform, ensuring scalability, maintainability, and alignment with engineering-grade standards.
