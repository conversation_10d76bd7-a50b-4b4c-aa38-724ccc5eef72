/**
 * Component Category API Handlers for MSW
 *
 * Provides comprehensive mocking for component category management including:
 * - CRUD operations with hierarchical support
 * - Tree structure operations and navigation
 * - Move and copy operations with validation
 * - Bulk restructure operations
 * - Search and filtering with pagination
 */

import { http, HttpResponse } from 'msw'
import { AuthErrors, getUserFromRequest, isAuthenticatedUser } from '../fixtures/auth'
import {
  ComponentCategory,
  createCategory,
  deleteCategory,
  findCategoryById,
  getCategoriesTree,
  getPaginatedCategories,
  mockComponentCategories,
  moveCategory,
  searchCategories,
  updateCategory,
} from '../fixtures/componentCategories'

const API_BASE = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3000'

// Define request body types for clarity and type safety
type CreateCategoryBody = Omit<ComponentCategory, 'id' | 'created_at' | 'updated_at'>
type UpdateCategoryBody = Partial<Omit<ComponentCategory, 'id' | 'created_at' | 'updated_at'>>
interface MoveCategoryBody {
  new_parent_id: number | null
}

export const componentCategoryHandlers = [
  /**
   * POST /api/v1/component-categories/
   * Create component category
   */
  http.post(`${API_BASE}/api/v1/component-categories/`, async ({ request }) => {
    const currentUser = getUserFromRequest(request)

    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    if (!isAuthenticatedUser(currentUser)) {
      return HttpResponse.json(AuthErrors.USER_INACTIVE, { status: 403 })
    }

    const categoryData = (await request.json()) as CreateCategoryBody

    // Basic validation
    if (!categoryData || !categoryData.name) {
      return HttpResponse.json(
        {
          error: 'VALIDATION_ERROR',
          detail: 'Category name is required',
          status_code: 400,
        },
        { status: 400 }
      )
    }

    // Check for duplicate names at the same level
    const existing = mockComponentCategories.find(
      (cat) =>
        cat.name === categoryData.name && cat.parent_category_id === categoryData.parent_category_id
    )

    if (existing) {
      return HttpResponse.json(
        {
          error: 'CATEGORY_ALREADY_EXISTS',
          detail: 'Category with this name already exists at this level',
          status_code: 409,
        },
        { status: 409 }
      )
    }

    const newCategory = createCategory(categoryData)

    return HttpResponse.json(newCategory, { status: 201 })
  }),

  /**
   * GET /api/v1/component-categories/tree
   * Get hierarchical category tree
   */
  http.get(`${API_BASE}/api/v1/component-categories/tree`, ({ request }) => {
    const currentUser = getUserFromRequest(request)

    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const url = new URL(request.url)
    const rootId = url.searchParams.get('root_id')

    const tree = getCategoriesTree(rootId ? parseInt(rootId) : null)

    return HttpResponse.json({
      tree: tree.categories,
      total_categories: tree.total_categories,
      max_depth: tree.max_depth,
    })
  }),

  /**
   * PUT /api/v1/component-categories/{category_id}/move
   * Move category to new parent location
   */
  http.put(
    `${API_BASE}/api/v1/component-categories/:categoryId/move`,
    async ({ request, params }) => {
      const currentUser = getUserFromRequest(request)

      if (!currentUser) {
        return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
      }

      const categoryId = parseInt(params.categoryId as string)
      const { new_parent_id } = (await request.json()) as MoveCategoryBody

      try {
        const movedCategory = moveCategory(categoryId, new_parent_id)
        return HttpResponse.json(movedCategory)
      } catch (error: any) {
        if (error.message.includes('not found')) {
          return HttpResponse.json(
            {
              error: 'CATEGORY_NOT_FOUND',
              detail: error.message,
              status_code: 404,
            },
            { status: 404 }
          )
        }

        if (error.message.includes('circular')) {
          return HttpResponse.json(
            {
              error: 'CIRCULAR_REFERENCE',
              detail: error.message,
              status_code: 422,
            },
            { status: 422 }
          )
        }

        return HttpResponse.json(
          {
            error: 'MOVE_FAILED',
            detail: error.message,
            status_code: 400,
          },
          { status: 400 }
        )
      }
    }
  ),

  /**
   * GET /api/v1/component-categories/{category_id}
   * Get component category by ID
   */
  http.get(`${API_BASE}/api/v1/component-categories/:categoryId`, ({ request, params }) => {
    const currentUser = getUserFromRequest(request)

    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const categoryId = parseInt(params.categoryId as string)
    const category = findCategoryById(categoryId)

    if (!category) {
      return HttpResponse.json(
        {
          error: 'CATEGORY_NOT_FOUND',
          detail: `Category with ID ${categoryId} not found`,
          status_code: 404,
        },
        { status: 404 }
      )
    }

    return HttpResponse.json(category)
  }),

  /**
   * PUT /api/v1/component-categories/{category_id}
   * Update component category
   */
  http.put(`${API_BASE}/api/v1/component-categories/:categoryId`, async ({ request, params }) => {
    const currentUser = getUserFromRequest(request)

    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const categoryId = parseInt(params.categoryId as string)
    const updates = (await request.json()) as UpdateCategoryBody

    try {
      const updatedCategory = updateCategory(categoryId, updates)
      return HttpResponse.json(updatedCategory)
    } catch (error: any) {
      if (error.message.includes('not found')) {
        return HttpResponse.json(
          {
            error: 'CATEGORY_NOT_FOUND',
            detail: error.message,
            status_code: 404,
          },
          { status: 404 }
        )
      }

      return HttpResponse.json(
        {
          error: 'UPDATE_FAILED',
          detail: error.message,
          status_code: 400,
        },
        { status: 400 }
      )
    }
  }),

  /**
   * DELETE /api/v1/component-categories/{category_id}
   * Delete component category
   */
  http.delete(`${API_BASE}/api/v1/component-categories/:categoryId`, ({ request, params }) => {
    const currentUser = getUserFromRequest(request)

    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const categoryId = parseInt(params.categoryId as string)

    try {
      const success = deleteCategory(categoryId)

      if (!success) {
        return HttpResponse.json(
          {
            error: 'CATEGORY_NOT_FOUND',
            detail: `Category with ID ${categoryId} not found`,
            status_code: 404,
          },
          { status: 404 }
        )
      }

      return new HttpResponse(null, { status: 204 })
    } catch (error: any) {
      if (error.message.includes('has dependencies')) {
        return HttpResponse.json(
          {
            error: 'CATEGORY_HAS_DEPENDENCIES',
            detail: error.message,
            status_code: 409,
          },
          { status: 409 }
        )
      }

      return HttpResponse.json(
        {
          error: 'DELETE_FAILED',
          detail: error.message,
          status_code: 400,
        },
        { status: 400 }
      )
    }
  }),

  /**
   * GET /api/v1/component-categories/
   * List component categories with pagination
   */
  http.get(`${API_BASE}/api/v1/component-categories/`, ({ request }) => {
    const currentUser = getUserFromRequest(request)

    if (!currentUser) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const size = parseInt(url.searchParams.get('size') || '20')
    const searchTerm = url.searchParams.get('search_term')
    const parentCategoryId = url.searchParams.get('parent_category_id')
    const isActive = url.searchParams.get('is_active')

    const skip = (page - 1) * size

    let result
    if (searchTerm) {
      result = searchCategories(searchTerm, skip, size, {
        parent_category_id: parentCategoryId ? parseInt(parentCategoryId) : undefined,
        is_active: isActive ? isActive === 'true' : undefined,
      })
    } else {
      result = getPaginatedCategories(skip, size, {
        parent_category_id: parentCategoryId ? parseInt(parentCategoryId) : undefined,
        is_active: isActive ? isActive === 'true' : undefined,
      })
    }

    return HttpResponse.json({
      categories: result.categories,
      total: result.total,
      page,
      size,
      total_pages: Math.ceil(result.total / size),
    })
  }),
]
