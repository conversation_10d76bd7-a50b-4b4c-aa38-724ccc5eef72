/**
 * Authentication API Handlers for MSW
 *
 * Provides comprehensive mocking for authentication workflows including:
 * - User login and session creation
 * - Token refresh and validation
 * - User registration
 * - Password management (change/reset)
 * - Logout and session invalidation
 */

import { http, HttpResponse } from 'msw'
import { AuthErrors, AuthUtils, getUserFromRequest, MockUser, UserUtils } from '../fixtures/auth'

// Hardcode the API base URL to ensure MSW intercepts the correct endpoint
const API_BASE = 'http://localhost:8000'

export const authHandlers = [
  /**
   * POST /api/v1/auth/login
   * User login
   */
  http.post(`${API_BASE}/api/v1/auth/login`, async ({ request }) => {
    const { username, password } = (await request.json()) as any

    if (!username || !password) {
      return HttpResponse.json(
        {
          error: 'VALIDATION_ERROR',
          detail: 'Username and password are required',
          status_code: 400,
        },
        { status: 400 }
      )
    }

    const user = UserUtils.authenticate(username, password)

    if (!user) {
      return HttpResponse.json(AuthErrors.INVALID_CREDENTIALS, { status: 401 })
    }

    if (!user.is_active) {
      return HttpResponse.json(AuthErrors.USER_INACTIVE, { status: 403 })
    }

    const tokens = AuthUtils.createAuthTokens(user)
    return HttpResponse.json(tokens)
  }),

  /**
   * POST /api/v1/auth/logout
   * User logout
   */
  http.post(`${API_BASE}/api/v1/auth/logout`, ({ request }) => {
    const authHeader = request.headers.get('Authorization')
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    // In a real scenario, we might use the refresh token from the body
    // For simplicity, we'll use the access token to identify the user and clear sessions.
    const user = getUserFromRequest(request)
    if (user) {
      UserUtils.deleteUser(user.id) // This is a mock, so we can just delete the user
    }

    return new HttpResponse(null, { status: 204 })
  }),

  /**
   * POST /api/v1/auth/refresh
   * Refresh access token
   */
  http.post(`${API_BASE}/api/v1/auth/refresh`, async ({ request }) => {
    const { refresh_token } = (await request.json()) as any

    if (!refresh_token) {
      return HttpResponse.json(
        {
          error: 'VALIDATION_ERROR',
          detail: 'Refresh token is required',
          status_code: 400,
        },
        { status: 400 }
      )
    }

    const validationResult = AuthUtils.validateRefreshToken(refresh_token)
    if (!validationResult) {
      return HttpResponse.json(AuthErrors.INVALID_TOKEN, { status: 401 })
    }

    const user = UserUtils.findById(validationResult.userId)
    if (!user || !user.is_active) {
      return HttpResponse.json(AuthErrors.USER_INACTIVE, { status: 403 })
    }

    const newAccessToken = AuthUtils.generateAccessToken(user)
    return HttpResponse.json({
      access_token: newAccessToken,
      token_type: 'bearer',
      expires_in: 15 * 60,
    })
  }),

  /**
   * POST /api/v1/auth/register
   * Register a new user
   */
  http.post(`${API_BASE}/api/v1/auth/register`, async ({ request }) => {
    const userData = (await request.json()) as Partial<MockUser>

    if (!userData.email || !userData.username || !userData.password_hash) {
      return HttpResponse.json(
        {
          error: 'VALIDATION_ERROR',
          detail: 'Email, username, and password are required',
          status_code: 400,
        },
        { status: 400 }
      )
    }

    if (UserUtils.findByEmail(userData.email) || UserUtils.findByUsername(userData.username)) {
      return HttpResponse.json(
        {
          error: 'USER_ALREADY_EXISTS',
          detail: 'User with this email or username already exists',
          status_code: 409,
        },
        { status: 409 }
      )
    }

    const newUser = UserUtils.createUser({ ...userData, role: 'user', is_active: true })
    const { password_hash, ...userResponse } = newUser
    return HttpResponse.json(userResponse, { status: 201 })
  }),

  /**
   * POST /api/v1/auth/change-password
   * Change current user's password
   */
  http.post(`${API_BASE}/api/v1/auth/change-password`, async ({ request }) => {
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const { old_password, new_password } = (await request.json()) as any

    // Mock password check
    if (old_password !== 'password123' && old_password !== 'admin123') {
      return HttpResponse.json(
        {
          error: 'INVALID_CREDENTIALS',
          detail: 'Incorrect old password',
          status_code: 400,
        },
        { status: 400 }
      )
    }

    UserUtils.updateUser(user.id, { password_hash: `hashed_${new_password}` })
    return new HttpResponse(null, { status: 204 })
  }),

  /**
   * GET /api/v1/auth/me
   * Get current user profile
   */
  http.get(`${API_BASE}/api/v1/auth/me`, ({ request }) => {
    const user = getUserFromRequest(request)
    if (!user) {
      return HttpResponse.json(AuthErrors.AUTHENTICATION_REQUIRED, { status: 401 })
    }

    const { password_hash, ...userResponse } = user
    return HttpResponse.json(userResponse)
  }),
]
