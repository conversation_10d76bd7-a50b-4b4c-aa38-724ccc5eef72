/**
 * Component Type Fixtures for MSW Testing
 *
 * Provides comprehensive mock data and utilities for component type management, including:
 * - CRUD operations
 * - Specifications template management
 * - Category-based filtering and search
 * - Pagination and advanced filtering
 * - Dependency tracking for deletion
 */

import { findCategoryById } from './componentCategories'

export interface ComponentType {
  id: number
  name: string
  description?: string
  category_id: number
  specifications_template?: Record<string, any>
  is_active: boolean
  created_at: string
  updated_at: string
}

// Mock database for component types
export const mockComponentTypes: ComponentType[] = [
  {
    id: 1,
    name: 'Resistor',
    description: 'Standard electrical resistor',
    category_id: 1, // Electronics Components
    specifications_template: {
      resistance: { type: 'number', unit: 'Ohm' },
      tolerance: { type: 'number', unit: '%' },
      power_rating: { type: 'number', unit: 'W' },
    },
    is_active: true,
    created_at: '2024-01-01T10:00:00Z',
    updated_at: '2024-01-01T10:00:00Z',
  },
  {
    id: 2,
    name: 'Capacitor',
    description: 'Standard electrical capacitor',
    category_id: 1, // Electronics Components
    specifications_template: {
      capacitance: { type: 'number', unit: 'F' },
      voltage_rating: { type: 'number', unit: 'V' },
    },
    is_active: true,
    created_at: '2024-01-02T10:00:00Z',
    updated_at: '2024-01-02T10:00:00Z',
  },
  {
    id: 3,
    name: 'Circuit Breaker',
    description: 'Protective device for electrical circuits',
    category_id: 2, // Electrical Protection
    specifications_template: {
      rated_current: { type: 'number', unit: 'A' },
      breaking_capacity: { type: 'number', unit: 'kA' },
      poles: { type: 'number' },
    },
    is_active: true,
    created_at: '2024-01-03T10:00:00Z',
    updated_at: '2024-01-03T10:00:00Z',
  },
  {
    id: 4,
    name: 'Transformer',
    description: 'Device for changing AC voltage levels',
    category_id: 3, // Power Distribution
    specifications_template: {
      power_rating: { type: 'number', unit: 'kVA' },
      primary_voltage: { type: 'number', unit: 'V' },
      secondary_voltage: { type: 'number', unit: 'V' },
    },
    is_active: true,
    created_at: '2024-01-04T10:00:00Z',
    updated_at: '2024-01-04T10:00:00Z',
  },
  {
    id: 5,
    name: 'LED Lamp',
    description: 'Light Emitting Diode lamp',
    category_id: 4, // Lighting
    specifications_template: {
      lumen_output: { type: 'number', unit: 'lm' },
      color_temperature: { type: 'number', unit: 'K' },
      wattage: { type: 'number', unit: 'W' },
    },
    is_active: true,
    created_at: '2024-01-05T10:00:00Z',
    updated_at: '2024-01-05T10:00:00Z',
  },
  {
    id: 6,
    name: 'Inactive Resistor',
    description: 'An inactive resistor type',
    category_id: 1, // Electronics Components
    specifications_template: undefined,
    is_active: false,
    created_at: '2024-01-06T10:00:00Z',
    updated_at: '2024-01-06T10:00:00Z',
  },
  {
    id: 7,
    name: 'Contactor',
    description: 'Electrically controlled switch for power circuits',
    category_id: 2, // Electrical Protection
    specifications_template: {
      rated_current: { type: 'number', unit: 'A' },
      coil_voltage: { type: 'number', unit: 'V' },
    },
    is_active: true,
    created_at: '2024-01-07T10:00:00Z',
    updated_at: '2024-01-07T10:00:00Z',
  },
  {
    id: 8,
    name: 'Power Cable',
    description: 'Cable for power transmission',
    category_id: 3, // Power Distribution
    specifications_template: {
      conductor_material: { type: 'string' },
      cross_section: { type: 'number', unit: 'mm²' },
      voltage_rating: { type: 'number', unit: 'V' },
    },
    is_active: true,
    created_at: '2024-01-08T10:00:00Z',
    updated_at: '2024-01-08T10:00:00Z',
  },
]

let nextTypeId = Math.max(...mockComponentTypes.map((t) => t.id)) + 1

// Utility functions for component types
export const findTypeById = (id: number): ComponentType | undefined => {
  return mockComponentTypes.find((type) => type.id === id)
}

export const createType = (
  newTypeData: Omit<ComponentType, 'id' | 'created_at' | 'updated_at'>
): ComponentType => {
  // Validate category_id exists
  const category = findCategoryById(newTypeData.category_id)
  if (!category) {
    throw new Error(`Category with ID ${newTypeData.category_id} not found.`)
  }

  const newType: ComponentType = {
    id: nextTypeId++,
    ...newTypeData,
    is_active: newTypeData.is_active ?? true,
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
  }
  mockComponentTypes.push(newType)
  return newType
}

export const updateType = (id: number, updates: Partial<ComponentType>): ComponentType => {
  const typeIndex = mockComponentTypes.findIndex((type) => type.id === id)
  if (typeIndex === -1) {
    throw new Error(`Component type with ID ${id} not found.`)
  }

  // If category_id is updated, validate it
  if (
    updates.category_id !== undefined &&
    updates.category_id !== mockComponentTypes[typeIndex].category_id
  ) {
    const category = findCategoryById(updates.category_id)
    if (!category) {
      throw new Error(`Category with ID ${updates.category_id} not found.`)
    }
  }

  mockComponentTypes[typeIndex] = {
    ...mockComponentTypes[typeIndex],
    ...updates,
    updated_at: new Date().toISOString(),
  }
  return mockComponentTypes[typeIndex]
}

export const deleteType = (id: number): boolean => {
  const typeIndex = mockComponentTypes.findIndex((type) => type.id === id)
  if (typeIndex === -1) {
    return false
  }

  // TODO: Add logic to check for actual component instances using this type
  // For now, assume no dependencies for simplicity in mock
  // if (hasDependencies(id)) {
  //   throw new Error(`Component type with ID ${id} has dependencies and cannot be deleted.`);
  // }

  mockComponentTypes.splice(typeIndex, 1)
  return true
}

export const getPaginatedTypes = (
  skip: number,
  limit: number,
  filters?: {
    category_id?: number
    is_active?: boolean
    has_specifications_template?: boolean
  }
): { types: ComponentType[]; total: number } => {
  let filteredTypes = [...mockComponentTypes]

  if (filters?.category_id !== undefined) {
    filteredTypes = filteredTypes.filter((type) => type.category_id === filters.category_id)
  }
  if (filters?.is_active !== undefined) {
    filteredTypes = filteredTypes.filter((type) => type.is_active === filters.is_active)
  }
  if (filters?.has_specifications_template !== undefined) {
    filteredTypes = filteredTypes.filter((type) =>
      filters.has_specifications_template
        ? !!type.specifications_template
        : !type.specifications_template
    )
  }

  const total = filteredTypes.length
  const types = filteredTypes.slice(skip, skip + limit)
  return { types, total }
}

export const searchTypes = (
  searchTerm: string,
  skip: number,
  limit: number,
  filters?: {
    category_id?: number
    is_active?: boolean
    has_specifications_template?: boolean
  }
): { types: ComponentType[]; total: number } => {
  const lowerCaseSearchTerm = searchTerm.toLowerCase()
  let filteredTypes = mockComponentTypes.filter(
    (type) =>
      type.name.toLowerCase().includes(lowerCaseSearchTerm) ||
      type.description?.toLowerCase().includes(lowerCaseSearchTerm)
  )

  if (filters?.category_id !== undefined) {
    filteredTypes = filteredTypes.filter((type) => type.category_id === filters.category_id)
  }
  if (filters?.is_active !== undefined) {
    filteredTypes = filteredTypes.filter((type) => type.is_active === filters.is_active)
  }
  if (filters?.has_specifications_template !== undefined) {
    filteredTypes = filteredTypes.filter((type) =>
      filters.has_specifications_template
        ? !!type.specifications_template
        : !type.specifications_template
    )
  }

  const total = filteredTypes.length
  const types = filteredTypes.slice(skip, skip + limit)
  return { types, total }
}

export const getTypesByCategory = (
  categoryId: number,
  includeInactive: boolean = false,
  skip: number = 0,
  limit: number = 100
): ComponentType[] => {
  let types = mockComponentTypes.filter((type) => type.category_id === categoryId)
  if (!includeInactive) {
    types = types.filter((type) => type.is_active)
  }
  return types.slice(skip, skip + limit)
}

export const updateSpecificationsTemplate = (
  typeId: number,
  template: Record<string, any>
): ComponentType => {
  const type = findTypeById(typeId)
  if (!type) {
    throw new Error(`Component type with ID ${typeId} not found.`)
  }
  type.specifications_template = template
  type.updated_at = new Date().toISOString()
  return type
}

// Reset function for test isolation
export function resetComponentTypesState(): void {
  mockComponentTypes.splice(0, mockComponentTypes.length)
  mockComponentTypes.push(
    {
      id: 1,
      name: 'Resistor',
      description: 'Standard electrical resistor',
      category_id: 1, // Electronics Components
      specifications_template: {
        resistance: { type: 'number', unit: 'Ohm' },
        tolerance: { type: 'number', unit: '%' },
        power_rating: { type: 'number', unit: 'W' },
      },
      is_active: true,
      created_at: '2024-01-01T10:00:00Z',
      updated_at: '2024-01-01T10:00:00Z',
    },
    {
      id: 2,
      name: 'Capacitor',
      description: 'Standard electrical capacitor',
      category_id: 1, // Electronics Components
      specifications_template: {
        capacitance: { type: 'number', unit: 'F' },
        voltage_rating: { type: 'number', unit: 'V' },
      },
      is_active: true,
      created_at: '2024-01-02T10:00:00Z',
      updated_at: '2024-01-02T10:00:00Z',
    },
    {
      id: 3,
      name: 'Circuit Breaker',
      description: 'Protective device for electrical circuits',
      category_id: 2, // Electrical Protection
      specifications_template: {
        rated_current: { type: 'number', unit: 'A' },
        breaking_capacity: { type: 'number', unit: 'kA' },
        poles: { type: 'number' },
      },
      is_active: true,
      created_at: '2024-01-03T10:00:00Z',
      updated_at: '2024-01-03T10:00:00Z',
    },
    {
      id: 4,
      name: 'Transformer',
      description: 'Device for changing AC voltage levels',
      category_id: 3, // Power Distribution
      specifications_template: {
        power_rating: { type: 'number', unit: 'kVA' },
        primary_voltage: { type: 'number', unit: 'V' },
        secondary_voltage: { type: 'number', unit: 'V' },
      },
      is_active: true,
      created_at: '2024-01-04T10:00:00Z',
      updated_at: '2024-01-04T10:00:00Z',
    },
    {
      id: 5,
      name: 'LED Lamp',
      description: 'Light Emitting Diode lamp',
      category_id: 4, // Lighting
      specifications_template: {
        lumen_output: { type: 'number', unit: 'lm' },
        color_temperature: { type: 'number', unit: 'K' },
        wattage: { type: 'number', unit: 'W' },
      },
      is_active: true,
      created_at: '2024-01-05T10:00:00Z',
      updated_at: '2024-01-05T10:00:00Z',
    },
    {
      id: 6,
      name: 'Inactive Resistor',
      description: 'An inactive resistor type',
      category_id: 1, // Electronics Components
      specifications_template: undefined,
      is_active: false,
      created_at: '2024-01-06T10:00:00Z',
      updated_at: '2024-01-06T10:00:00Z',
    },
    {
      id: 7,
      name: 'Contactor',
      description: 'Electrically controlled switch for power circuits',
      category_id: 2, // Electrical Protection
      specifications_template: {
        rated_current: { type: 'number', unit: 'A' },
        coil_voltage: { type: 'number', unit: 'V' },
      },
      is_active: true,
      created_at: '2024-01-07T10:00:00Z',
      updated_at: '2024-01-07T10:00:00Z',
    },
    {
      id: 8,
      name: 'Power Cable',
      description: 'Cable for power transmission',
      category_id: 3, // Power Distribution
      specifications_template: {
        conductor_material: { type: 'string' },
        cross_section: { type: 'number', unit: 'mm²' },
        voltage_rating: { type: 'number', unit: 'V' },
      },
      is_active: true,
      created_at: '2024-01-08T10:00:00Z',
      updated_at: '2024-01-08T10:00:00Z',
    }
  )
  nextTypeId = Math.max(...mockComponentTypes.map((t) => t.id)) + 1
}
