/**
 * Test fixtures for component management
 */

export const mockComponentCategories = [
  { value: 'RESISTOR', name: '<PERSON>sist<PERSON>' },
  { value: 'CAPACITOR', name: 'Capacitor' },
  { value: 'INDUCTOR', name: 'Inductor' },
  { value: 'TRANSISTOR', name: 'Transistor' },
  { value: 'DIO<PERSON>', name: 'Diode' },
  { value: 'INTEGRATED_CIRCUIT', name: 'Integrated Circuit' },
  { value: 'CONNECTOR', name: 'Connector' },
  { value: 'SWITCH', name: 'Switch' },
  { value: 'RELAY', name: 'Relay' },
  { value: 'TRANSFORMER', name: 'Transformer' },
]

export const mockComponentTypes = [
  { value: 'RESISTOR', name: 'Fixed Resistor' },
  { value: 'VARIABLE_RESISTOR', name: 'Variable Resistor' },
  { value: 'CERAMIC_CAPACITOR', name: 'Ceramic Capacitor' },
  { value: 'ELECTROLYTIC_CAPACITOR', name: 'Electrolytic Capacitor' },
  { value: 'POWER_INDUCTOR', name: 'Power Inductor' },
  { value: 'RF_INDUCTOR', name: 'RF Inductor' },
  { value: 'BJT', name: 'Bipolar Junction Transistor' },
  { value: 'MOSFET', name: 'MOSFET' },
  { value: 'SCHOTTKY_DIODE', name: 'Schottky Diode' },
  { value: 'ZENER_DIODE', name: 'Zener Diode' },
]

export const mockComponents = [
  {
    id: 1,
    name: 'Test E2E Resistor',
    manufacturer: 'E2E Electronics',
    model_number: 'E2ER001',
    part_number: 'E2E-R-001',
    description: 'High precision resistor for E2E testing',
    unit_price: 0.25,
    currency: 'EUR',
    weight_kg: 0.001,
    component_category_id: 1,
    component_type_id: 1,
    category: 'RESISTOR',
    component_type: 'RESISTOR',
    supplier: 'E2E Supplier',
    is_active: true,
    is_preferred: false,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 2,
    name: 'Premium Capacitor',
    manufacturer: 'Schneider Electric',
    model_number: 'SC-CAP-100',
    part_number: 'SC-C-100',
    description: 'High-quality ceramic capacitor',
    unit_price: 1.5,
    currency: 'EUR',
    weight_kg: 0.002,
    component_category_id: 2,
    component_type_id: 3,
    category: 'CAPACITOR',
    component_type: 'CERAMIC_CAPACITOR',
    supplier: 'Schneider Direct',
    is_active: true,
    is_preferred: true,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 3,
    name: 'Power Inductor',
    manufacturer: 'ABB',
    model_number: 'ABB-IND-50',
    part_number: 'ABB-I-50',
    description: 'High current power inductor',
    unit_price: 3.75,
    currency: 'EUR',
    weight_kg: 0.015,
    component_category_id: 3,
    component_type_id: 5,
    category: 'INDUCTOR',
    component_type: 'POWER_INDUCTOR',
    supplier: 'ABB Components',
    is_active: true,
    is_preferred: false,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 4,
    name: 'MOSFET Transistor',
    manufacturer: 'Siemens',
    model_number: 'SIE-MOS-200',
    part_number: 'SIE-M-200',
    description: 'N-channel MOSFET for switching applications',
    unit_price: 2.25,
    currency: 'EUR',
    weight_kg: 0.003,
    component_category_id: 4,
    component_type_id: 8,
    category: 'TRANSISTOR',
    component_type: 'MOSFET',
    supplier: 'Siemens Components',
    is_active: true,
    is_preferred: true,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
  },
  {
    id: 5,
    name: 'Zener Diode',
    manufacturer: 'General Electric',
    model_number: 'GE-ZEN-12V',
    part_number: 'GE-Z-12V',
    description: '12V Zener diode for voltage regulation',
    unit_price: 0.85,
    currency: 'EUR',
    weight_kg: 0.001,
    component_category_id: 5,
    component_type_id: 10,
    category: 'DIODE',
    component_type: 'ZENER_DIODE',
    supplier: 'GE Electronics',
    is_active: true,
    is_preferred: false,
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:00:00Z',
  },
]

export const mockComponentsResponse = {
  data: mockComponents,
  total: mockComponents.length,
  page: 1,
  per_page: 10,
  total_pages: 1,
}

export const mockComponentSuggestions = [
  'Test E2E Resistor',
  'Premium Capacitor',
  'Power Inductor',
  'MOSFET Transistor',
  'Zener Diode',
]

// Helper function to create a new component for testing
export const createMockComponent = (overrides: Partial<(typeof mockComponents)[0]> = {}) => {
  const baseComponent = mockComponents[0]
  return {
    ...baseComponent,
    id: overrides.id || Date.now(),
    created_at: new Date().toISOString(),
    updated_at: new Date().toISOString(),
    ...overrides,
  }
}

// Helper function to find component by ID
export const findComponentById = (id: number) => {
  return mockComponents.find((component) => component.id === id)
}

// Helper function to filter components
export const filterComponents = (filters: {
  search_term?: string
  category?: string
  manufacturer?: string
  is_preferred?: boolean
  is_active?: boolean
}) => {
  return mockComponents.filter((component) => {
    if (filters.search_term) {
      const searchTerm = (filters.search_term || '').toLowerCase()
      const searchableFields = [
        component.name,
        component.manufacturer,
        component.model_number,
        component.part_number,
        component.description,
      ]
        .join(' ')
        .toLowerCase()

      if (!searchableFields.includes(searchTerm)) {
        return false
      }
    }

    if (filters.category && component.category !== filters.category) {
      return false
    }

    if (filters.manufacturer && component.manufacturer !== filters.manufacturer) {
      return false
    }

    if (filters.is_preferred !== undefined && component.is_preferred !== filters.is_preferred) {
      return false
    }

    if (filters.is_active !== undefined && component.is_active !== filters.is_active) {
      return false
    }

    return true
  })
}
