import path from "path"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vitest/config"

/// <reference types="@vitest/browser/providers/playwright" />
/// <reference types="vitest" />

export default defineConfig({
  plugins: [react()],
  server: {
    host: "**************"
  },
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: ["./vitest.setup.ts"],
    include: ["src/**/*.{test,spec}.{ts,tsx}"],
    reporters: [
      "dot",
      [
        "./src/test/reporters/domain-reporter.ts",
        { outputFile: "./src/test/reporters/domain-report.txt" },
      ],
    ],
    coverage: {
      provider: "v8",
      reporter: ["lcov", "text", "json", "html"],
    },
    pool: "forks",
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    __dirname: JSON.stringify(__dirname),
  },
})
