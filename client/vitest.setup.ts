import "@testing-library/jest-dom"
import { vi } from "vitest"

// Mock matchMedia
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
})

// Mock HTMLFormElement.prototype.requestSubmit
// Patch the internal jsdom method to prevent the "Not implemented" error
const originalRequestSubmit = HTMLFormElement.prototype.requestSubmit
HTMLFormElement.prototype.requestSubmit = vi.fn(function (submitter) {
  // Simple mock implementation that just calls submit
  this.submit()
})

// Also patch the internal _doRequestSubmit method if it exists
if (HTMLFormElement.prototype._doRequestSubmit) {
  HTMLFormElement.prototype._doRequestSubmit = vi.fn(function (submitter) {
    this.submit()
  })
}

// Mock missing DOM methods for Radix UI compatibility
Object.defineProperty(Element.prototype, "hasPointerCapture", {
  value: vi.fn(() => false),
  writable: true,
})

Object.defineProperty(Element.prototype, "setPointerCapture", {
  value: vi.fn(),
  writable: true,
})

Object.defineProperty(Element.prototype, "releasePointerCapture", {
  value: vi.fn(),
  writable: true,
})

Object.defineProperty(Element.prototype, "scrollIntoView", {
  value: vi.fn(),
  writable: true,
})

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}))

// Mock IndexedDB for testing environment with idb library support

// In-memory storage for IndexedDB mock
const mockIndexedDBStorage = new Map<string, Map<string, any>>()

const createMockIDBRequest = (result: any = null, error: any = null) => {
  const request = {
    result,
    error,
    readyState: "done" as const,
    source: null,
    transaction: null,
    onsuccess: null as ((event: Event) => void) | null,
    onerror: null as ((event: Event) => void) | null,
    onblocked: null as ((event: Event) => void) | null,
    onupgradeneeded: null as ((event: Event) => void) | null,
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  }

  // Simulate successful completion
  setTimeout(() => {
    if (request.onsuccess && !error) {
      request.onsuccess(new Event("success"))
    } else if (request.onerror && error) {
      request.onerror(new Event("error"))
    }
  }, 0)

  return request
}

const mockIDBObjectStore = (dbName: string, storeName: string) => {
  const getStore = () => {
    if (!mockIndexedDBStorage.has(dbName)) {
      mockIndexedDBStorage.set(dbName, new Map())
    }
    return mockIndexedDBStorage.get(dbName)!
  }

  return {
    add: vi.fn().mockImplementation((value: any, key?: any) => {
      const store = getStore()
      const actualKey = key || Date.now().toString()
      if (store.has(actualKey)) {
        return createMockIDBRequest(null, new Error("Key already exists"))
      }
      store.set(actualKey, value)
      return createMockIDBRequest(actualKey)
    }),
    clear: vi.fn().mockImplementation(() => {
      getStore().clear()
      return createMockIDBRequest(undefined)
    }),
    count: vi.fn().mockImplementation(() => {
      return createMockIDBRequest(getStore().size)
    }),
    delete: vi.fn().mockImplementation((key: any) => {
      const store = getStore()
      store.delete(key)
      return createMockIDBRequest(undefined)
    }),
    get: vi.fn().mockImplementation((key: any) => {
      const store = getStore()
      return createMockIDBRequest(store.get(key))
    }),
    getAll: vi.fn().mockImplementation(() => {
      const store = getStore()
      return createMockIDBRequest(Array.from(store.values()))
    }),
    getAllKeys: vi.fn().mockImplementation(() => {
      const store = getStore()
      return createMockIDBRequest(Array.from(store.keys()))
    }),
    getKey: vi.fn().mockImplementation(() => createMockIDBRequest(undefined)),
    put: vi.fn().mockImplementation((value: any, key?: any) => {
      const store = getStore()
      const actualKey = key || Date.now().toString()
      store.set(actualKey, value)
      return createMockIDBRequest(actualKey)
    }),
    openCursor: vi.fn().mockImplementation(() => createMockIDBRequest(null)),
    openKeyCursor: vi.fn().mockImplementation(() => createMockIDBRequest(null)),
    createIndex: vi.fn(),
    deleteIndex: vi.fn(),
    index: vi.fn(),
    indexNames: [],
    keyPath: null,
    name: storeName,
    transaction: null,
    autoIncrement: false,
  }
}

const mockIDBTransaction = (dbName: string) => ({
  abort: vi.fn(),
  commit: vi.fn(),
  objectStore: vi.fn().mockImplementation((storeName: string) => 
    mockIDBObjectStore(dbName, storeName)
  ),
  db: null,
  durability: "default" as const,
  error: null,
  mode: "readwrite" as const,
  objectStoreNames: ["cache"],
  onabort: null,
  oncomplete: null,
  onerror: null,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
})

const mockIDBDatabase = (dbName: string) => ({
  close: vi.fn(),
  createObjectStore: vi.fn().mockImplementation((storeName: string) => 
    mockIDBObjectStore(dbName, storeName)
  ),
  deleteObjectStore: vi.fn(),
  transaction: vi.fn().mockImplementation((storeNames: string | string[], mode = "readonly") => 
    mockIDBTransaction(dbName)
  ),
  name: dbName,
  objectStoreNames: ["cache"],
  onabort: null,
  onclose: null,
  onerror: null,
  onversionchange: null,
  version: 1,
  addEventListener: vi.fn(),
  removeEventListener: vi.fn(),
  dispatchEvent: vi.fn(),
})

// Mock IDBFactory
Object.defineProperty(window, "indexedDB", {
  writable: true,
  value: {
    open: vi.fn().mockImplementation((name: string, version?: number) => {
      const request = createMockIDBRequest(mockIDBDatabase(name))
      // Simulate database opening
      setTimeout(() => {
        if (request.onupgradeneeded) {
          request.onupgradeneeded(new Event("upgradeneeded"))
        }
        if (request.onsuccess) {
          request.onsuccess(new Event("success"))
        }
      }, 0)
      return request
    }),
    deleteDatabase: vi.fn().mockImplementation((name: string) => {
      mockIndexedDBStorage.delete(name)
      return createMockIDBRequest(undefined)
    }),
    databases: vi.fn().mockResolvedValue([]),
    cmp: vi.fn().mockImplementation((a: any, b: any) => {
      if (a < b) return -1
      if (a > b) return 1
      return 0
    }),
  },
})

// Add required IndexedDB globals for idb library
global.IDBRequest = class MockIDBRequest extends EventTarget {
  result: any = null
  error: any = null
  readyState: "pending" | "done" = "done"
  source: any = null
  transaction: any = null
  onsuccess: ((event: Event) => void) | null = null
  onerror: ((event: Event) => void) | null = null
}

global.IDBOpenDBRequest = class MockIDBOpenDBRequest extends global.IDBRequest {
  onblocked: ((event: Event) => void) | null = null
  onupgradeneeded: ((event: Event) => void) | null = null
}

global.IDBDatabase = class MockIDBDatabase extends EventTarget {
  name: string = "test-db"
  version: number = 1
  objectStoreNames: string[] = []
  onabort: ((event: Event) => void) | null = null
  onclose: ((event: Event) => void) | null = null
  onerror: ((event: Event) => void) | null = null
  onversionchange: ((event: Event) => void) | null = null
  
  close() {}
  createObjectStore() { return mockIDBObjectStore() }
  deleteObjectStore() {}
  transaction() { return mockIDBTransaction() }
}

global.IDBTransaction = class MockIDBTransaction extends EventTarget {
  db: any = null
  durability: string = "default"
  error: any = null
  mode: string = "readonly"
  objectStoreNames: string[] = []
  onabort: ((event: Event) => void) | null = null
  oncomplete: ((event: Event) => void) | null = null
  onerror: ((event: Event) => void) | null = null
  
  abort() {}
  commit() {}
  objectStore() { return mockIDBObjectStore() }
}

global.IDBObjectStore = class MockIDBObjectStore {
  autoIncrement: boolean = false
  indexNames: string[] = []
  keyPath: any = null
  name: string = "test-store"
  transaction: any = null
  
  constructor(dbName: string = "default-db", storeName: string = "default-store") {
    this.name = storeName
  }
  
  add(value: any, key?: any) { 
    const actualKey = key || Date.now().toString()
    return createMockIDBRequest(actualKey) 
  }
  clear() { return createMockIDBRequest(undefined) }
  count() { return createMockIDBRequest(0) }
  delete() { return createMockIDBRequest(undefined) }
  get() { return createMockIDBRequest(undefined) }
  getAll() { return createMockIDBRequest([]) }
  getAllKeys() { return createMockIDBRequest([]) }
  getKey() { return createMockIDBRequest(undefined) }
  put(value: any, key?: any) { 
    const actualKey = key || Date.now().toString()
    return createMockIDBRequest(actualKey) 
  }
  openCursor() { return createMockIDBRequest(null) }
  openKeyCursor() { return createMockIDBRequest(null) }
  createIndex() { return {} }
  deleteIndex() {}
  index() { return {} }
}

global.IDBKeyRange = class MockIDBKeyRange {
  lower: any
  upper: any
  lowerOpen: boolean = false
  upperOpen: boolean = false
  
  static bound(lower: any, upper: any, lowerOpen?: boolean, upperOpen?: boolean) {
    const range = new MockIDBKeyRange()
    range.lower = lower
    range.upper = upper
    range.lowerOpen = !!lowerOpen
    range.upperOpen = !!upperOpen
    return range
  }
  
  static only(value: any) {
    const range = new MockIDBKeyRange()
    range.lower = value
    range.upper = value
    return range
  }
  
  static lowerBound(lower: any, open?: boolean) {
    const range = new MockIDBKeyRange()
    range.lower = lower
    range.lowerOpen = !!open
    return range
  }
  
  static upperBound(upper: any, open?: boolean) {
    const range = new MockIDBKeyRange()
    range.upper = upper
    range.upperOpen = !!open
    return range
  }
  
  includes(value: any) { return true }
}

global.IDBIndex = class MockIDBIndex {
  keyPath: any = null
  multiEntry: boolean = false
  name: string = "test-index"
  objectStore: any = null
  unique: boolean = false
  
  count() { return createMockIDBRequest(0) }
  get() { return createMockIDBRequest(undefined) }
  getAll() { return createMockIDBRequest([]) }
  getAllKeys() { return createMockIDBRequest([]) }
  getKey() { return createMockIDBRequest(undefined) }
  openCursor() { return createMockIDBRequest(null) }
  openKeyCursor() { return createMockIDBRequest(null) }
}

global.IDBCursor = class MockIDBCursor {
  direction: string = "next"
  key: any = null
  primaryKey: any = null
  request: any = null
  source: any = null
  
  advance() {}
  continue() {}
  continuePrimaryKey() {}
  delete() { return createMockIDBRequest(undefined) }
  update() { return createMockIDBRequest(undefined) }
}

global.IDBCursorWithValue = class MockIDBCursorWithValue extends global.IDBCursor {
  value: any = null
}

// Consolidated idb library mock factory
export const createMockIDBDatabase = (storage: Map<string, any> = new Map()) => ({
  // Direct access methods (idb library convenience API)
  get: vi.fn().mockImplementation(async (storeName: string, key: any) => {
    return storage.get(key)
  }),
  put: vi.fn().mockImplementation(async (storeName: string, value: any, key?: any) => {
    const actualKey = key || Date.now().toString()
    storage.set(actualKey, value)
    return actualKey
  }),
  delete: vi.fn().mockImplementation(async (storeName: string, key: any) => {
    storage.delete(key)
    return undefined
  }),
  clear: vi.fn().mockImplementation(async (storeName: string) => {
    storage.clear()
    return undefined
  }),
  count: vi.fn().mockImplementation(async (storeName: string) => {
    return storage.size
  }),
  
  // Transaction methods (for compatibility)
  transaction: vi.fn().mockImplementation((storeNames: string | string[], mode = "readonly") => ({
    objectStore: vi.fn().mockImplementation((storeName: string) => ({
      get: vi.fn().mockImplementation(async (key: any) => storage.get(key)),
      put: vi.fn().mockImplementation(async (value: any, key?: any) => {
        const actualKey = key || Date.now().toString()
        storage.set(actualKey, value)
        return actualKey
      }),
      delete: vi.fn().mockImplementation(async (key: any) => {
        storage.delete(key)
        return undefined
      }),
      clear: vi.fn().mockImplementation(async () => {
        storage.clear()
        return undefined
      }),
      count: vi.fn().mockImplementation(async () => storage.size),
      add: vi.fn().mockImplementation(async (value: any, key?: any) => {
        const actualKey = key || Date.now().toString()
        if (storage.has(actualKey)) {
          throw new Error("Key already exists")
        }
        storage.set(actualKey, value)
        return actualKey
      }),
      getAll: vi.fn().mockImplementation(async () => Array.from(storage.values())),
      getAllKeys: vi.fn().mockImplementation(async () => Array.from(storage.keys())),
      index: vi.fn().mockImplementation(() => ({
        get: vi.fn().mockImplementation(async () => undefined),
        getAll: vi.fn().mockImplementation(async () => []),
      })),
    })),
    done: Promise.resolve(),
  })),
  
  // Database properties
  name: "test-db",
  version: 1,
  objectStoreNames: { contains: vi.fn(() => false) },
  createObjectStore: vi.fn().mockImplementation(() => ({
    createIndex: vi.fn(),
  })),
  close: vi.fn(),
})

// Mock idb library with shared factory
vi.mock("idb", () => {
  const defaultStorage = new Map<string, any>()
  const defaultDatabase = createMockIDBDatabase(defaultStorage)
  
  return {
    openDB: vi.fn().mockImplementation((name: string, version?: number, options?: any) => {
      // Create a new storage instance for each database
      const storage = new Map<string, any>()
      const database = createMockIDBDatabase(storage)
      
      // Simulate upgrade if provided
      if (options?.upgrade) {
        try {
          options.upgrade(database, 0, version || 1, null, null)
        } catch (error) {
          // Ignore upgrade errors in mock
        }
      }
      
      return Promise.resolve(database)
    }),
    deleteDB: vi.fn().mockResolvedValue(undefined),
    wrap: vi.fn().mockImplementation((value: any) => value),
    unwrap: vi.fn().mockImplementation((value: any) => value),
  }
})

// Mock Next.js router
vi.mock("next/navigation", () => ({
  useRouter: () => ({
    push: vi.fn(),
    replace: vi.fn(),
    back: vi.fn(),
    forward: vi.fn(),
    refresh: vi.fn(),
    prefetch: vi.fn(),
  }),
  usePathname: () => "/",
  useSearchParams: () => new URLSearchParams(),
  useParams: () => ({}),
}))

// Mock Next.js dynamic imports
vi.mock("next/dynamic", () => ({
  __esModule: true,
  default: (fn: () => Promise<any>) => {
    const Component = () => null
    Component.displayName = "DynamicComponent"
    return Component
  },
}))

// Mock window.location
Object.defineProperty(window, "location", {
  writable: true,
  value: {
    href: "http://localhost:3000",
    origin: "http://localhost:3000",
    protocol: "http:",
    host: "localhost:3000",
    hostname: "localhost",
    port: "3000",
    pathname: "/",
    search: "",
    hash: "",
    assign: vi.fn(),
    replace: vi.fn(),
    reload: vi.fn(),
  },
})
