
import { useOfflineMutation } from "./src/hooks/useOfflineMutation";
import { vi } from "vitest";

// Mock a mutation function
const mockMutationFn = vi.fn(async (variables: { id: string }) => {
  return { success: true, id: variables.id };
});

// Mock configuration
const mockConfig = {
  endpoint: "/api/test",
  method: "POST",
  mutationKey: "test-mutation",
};

describe("OfflineMutationResult usage", () => {
  it("should correctly mock OfflineMutationResult properties", () => {
    const { wasStoredOffline, pendingOfflineCount, getOfflineStats, clearOfflineMutations } = useOfflineMutation(
      mockMutationFn,
      mockConfig
    );

    // Assertions to trigger type checking
    expect(wasStoredOffline).toBeDefined();
    expect(pendingOfflineCount).toBeDefined();
    expect(getOfflineStats).toBeDefined();
    expect(clearOfflineMutations).toBeDefined();
  });
});
