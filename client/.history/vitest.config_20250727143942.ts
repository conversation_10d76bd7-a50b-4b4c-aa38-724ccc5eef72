import path from "path"
import react from "@vitejs/plugin-react"
import { defineConfig } from "vitest/config"

export default defineConfig({
  plugins: [react()],
  test: {
    globals: true,
    environment: "jsdom",
    setupFiles: ["./vitest.setup.ts"],
    reporters: [
      "dot",
      [
        "./src/test/reporters/domain-reporter.ts",
        { outputFile: "./src/test/reporters/domain-report.txt" },
      ],
      [
        "./src/test/reporters/verbose-domain-reporter.ts",
        { outputFile: "./src/test/reporters/verbose-domain-report.txt" },
      ],
    ],
    coverage: {
      provider: "v8",
      reporter: ["lcov", "text", "json", "html"],
    },
    pool: "forks",
  },
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  define: {
    __dirname: JSON.stringify(__dirname),
  },
})
