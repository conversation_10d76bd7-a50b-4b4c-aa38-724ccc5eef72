"use client"

import React, { useState } from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

const ProblemSearch = () => {
  const [query, setQuery] = useState("")

  return (
    <div className="flex w-full max-w-sm items-center space-x-2">
      <Input
        type="text"
        placeholder="Search for error codes, logs, or issues..."
        value={query}
        onChange={(e) => setQuery(e.target.value)}
      />
      <Button type="submit">Search</Button>
    </div>
  )
}

export default ProblemSearch
