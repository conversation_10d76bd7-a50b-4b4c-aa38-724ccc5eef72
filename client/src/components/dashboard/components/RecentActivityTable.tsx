"use client"

import React from "react"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"

const RecentActivityTable = () => {
  return (
    <Tabs defaultValue="recent-logs">
      <TabsList>
        <TabsTrigger value="recent-logs">Recent Logs</TabsTrigger>
        <TabsTrigger value="active-sessions">Active Sessions</TabsTrigger>
      </TabsList>
      <TabsContent value="recent-logs">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Activity</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>Log Entry 1</TableCell>
              <TableCell>Success</TableCell>
              <TableCell>2024-01-01</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TabsContent>
      <TabsContent value="active-sessions">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Activity</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow>
              <TableCell>Session 1</TableCell>
              <TableCell>In Progress</TableCell>
              <TableCell>2024-01-02</TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </TabsContent>
    </Tabs>
  )
}

export default RecentActivityTable
