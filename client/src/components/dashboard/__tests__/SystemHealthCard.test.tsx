import React from "react"

import { render, screen } from "@testing-library/react"

import SystemHealthCard from "../components/SystemHealthCard"

describe("SystemHealthCard Component", () => {
  it("should render the card title, status text, and progress bar", () => {
    render(<SystemHealthCard />)
    expect(screen.getByText(/System Health/i)).toBeInTheDocument()
    expect(screen.getByText(/All systems operational/i)).toBeInTheDocument()
    expect(screen.getByRole("progressbar")).toBeInTheDocument()
  })
})
