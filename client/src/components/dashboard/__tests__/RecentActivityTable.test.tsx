import React from "react"

import { render, screen } from "@testing-library/react"

import RecentActivityTable from "../components/RecentActivityTable"

describe("RecentActivityTable Component", () => {
  it("should render tabs for different views", () => {
    render(<RecentActivityTable />)
    expect(
      screen.getByRole("tab", { name: /Recent Logs/i })
    ).toBeInTheDocument()
    expect(
      screen.getByRole("tab", { name: /Active Sessions/i })
    ).toBeInTheDocument()
  })

  it("should render a table with sample data", () => {
    render(<RecentActivityTable />)
    // Check for table headers
    expect(
      screen.getByRole("columnheader", { name: /Activity/i })
    ).toBeInTheDocument()
    expect(
      screen.getByRole("columnheader", { name: /Status/i })
    ).toBeInTheDocument()
    expect(
      screen.getByRole("columnheader", { name: /Date/i })
    ).toBeInTheDocument()

    // Check for table content
    expect(
      screen.getByRole("cell", { name: /Log Entry 1/i })
    ).toBeInTheDocument()
  })

  it("should render tab interaction elements", () => {
    render(<RecentActivityTable />)
    const activeSessionsTab = screen.getByRole("tab", {
      name: /Active Sessions/i,
    })

    // Verify tabs are clickable
    expect(activeSessionsTab).toBeEnabled()
    expect(activeSessionsTab).toHaveAttribute("type", "button")
  })
})
