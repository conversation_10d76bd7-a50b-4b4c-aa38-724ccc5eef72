import React from "react"

import { render, screen } from "@testing-library/react"

import CriticalAlerts from "../components/CriticalAlerts"

describe("CriticalAlerts Component", () => {
  it("should render alert messages", () => {
    render(<CriticalAlerts />)
    expect(screen.getByText(/Critical Alert 1/i)).toBeInTheDocument()
    expect(screen.getByText(/Critical Alert 2/i)).toBeInTheDocument()
  })
})
