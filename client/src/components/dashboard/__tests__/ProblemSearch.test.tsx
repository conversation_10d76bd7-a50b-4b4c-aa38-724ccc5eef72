import React from "react"

import { fireEvent, render, screen } from "@testing-library/react"

import ProblemSearch from "../components/ProblemSearch"

describe("ProblemSearch Component", () => {
  it("should render the search input and button", () => {
    render(<ProblemSearch />)
    expect(
      screen.getByPlaceholderText(/Search for error codes, logs, or issues.../i)
    ).toBeInTheDocument()
    expect(screen.getByRole("button", { name: /Search/i })).toBeInTheDocument()
  })

  it("should update the input value on change", () => {
    render(<ProblemSearch />)
    const input = screen.getByPlaceholderText(
      /Search for error codes, logs, or issues.../i
    ) as HTMLInputElement
    fireEvent.change(input, { target: { value: "test query" } })
    expect(input.value).toBe("test query")
  })
})
