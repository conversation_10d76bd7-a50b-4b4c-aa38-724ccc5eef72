import React from "react"

import { fireEvent, render, screen } from "@testing-library/react"
import { vi } from "vitest"

import QuickLinks from "../components/QuickLinks"

describe("QuickLinks Component", () => {
  it("should render the quick links", () => {
    render(<QuickLinks />)
    expect(screen.getByText(/Quick Links/i)).toBeInTheDocument()
    expect(
      screen.getByRole("button", { name: /Scenario 1/i })
    ).toBeInTheDocument()
    expect(
      screen.getByRole("button", { name: /Scenario 2/i })
    ).toBeInTheDocument()
  })

  it("should handle link clicks", () => {
    const handleClick = vi.fn()
    render(<QuickLinks onLinkClick={handleClick} />)
    const linkButton = screen.getByRole("button", { name: /Scenario 1/i })
    fireEvent.click(linkButton)
    expect(handleClick).toHaveBeenCalled()
  })
})
