"use client"

import React from "react"

import { <PERSON>, HelpCircle, User as UserIcon } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import { SidebarTrigger } from "@/components/ui/sidebar"

const Header = () => {
  return (
    <header className="bg-background/95 supports-[backdrop-filter]:bg-background/60 flex items-center justify-between border-b px-4 py-3 backdrop-blur">
      <div className="flex items-center gap-3">
        <SidebarTrigger className="md:hidden" />
        <div className="hidden md:block">
          <h1 className="text-lg font-semibold">
            Ultimate Electrical Designer
          </h1>
          <p className="text-muted-foreground text-sm">
            Ultimate Electrical Designer
          </p>
        </div>
        <h1 className="text-lg font-semibold md:hidden">UED Debug</h1>
      </div>
      <div className="flex items-center gap-2 md:gap-4">
        <Input type="search" placeholder="Search..." className="w-32 md:w-64" />
        <div className="flex items-center gap-1 md:gap-2">
          <div
            data-testid="status-indicator"
            className="h-2 w-2 rounded-full bg-green-500 md:h-3 md:w-3"
            title="System Online"
          ></div>
          <span className="text-muted-foreground hidden text-xs md:inline">
            Online
          </span>
        </div>
        <div className="flex items-center gap-1 md:gap-2">
          <Button
            variant="ghost"
            size="icon"
            aria-label="Help"
            className="h-8 w-8 md:h-10 md:w-10"
          >
            <HelpCircle className="h-4 w-4 md:h-5 md:w-5" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            aria-label="Notifications"
            className="h-8 w-8 md:h-10 md:w-10"
          >
            <Bell className="h-4 w-4 md:h-5 md:w-5" />
          </Button>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="ghost"
                size="icon"
                aria-label="user-profile"
                className="h-8 w-8 md:h-10 md:w-10"
              >
                <UserIcon className="h-4 w-4 md:h-5 md:w-5" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>My Account</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem>Profile</DropdownMenuItem>
              <DropdownMenuItem>Settings</DropdownMenuItem>
              <DropdownMenuItem>Logout</DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  )
}

export default Header
