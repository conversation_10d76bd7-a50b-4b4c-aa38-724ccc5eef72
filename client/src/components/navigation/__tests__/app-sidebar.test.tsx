import { render, screen } from "@testing-library/react"

import { SidebarProvider } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/navigation/app-sidebar"
import { NavItemss } from "@/components/navigation/nav-items"

describe("AppSidebar", () => {
  const renderWithProvider = (ui: React.ReactElement) => {
    return render(<SidebarProvider>{ui}</SidebarProvider>)
  }

  it("renders the correct number of navigation items", () => {
    renderWithProvider(<AppSidebar navItems={NavItemss} />)
    const navLinks = screen.getAllByRole("link")
    expect(navLinks.length).toBe(NavItemss.length)
  })

  it("renders the correct names for each navigation item", () => {
    renderWithProvider(<AppSidebar navItems={NavItemss} />)
    NavItemss.forEach((item) => {
      // The accessible name includes the badge when present
      const expectedName = item.badge ? `${item.name} ${item.badge}` : item.name
      const link = screen.getByRole("link", { name: expectedName })
      expect(link).toBeInTheDocument()
    })
  })
})
