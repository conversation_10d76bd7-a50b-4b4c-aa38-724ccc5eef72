"use client"

import * as React from "react"

import type { NavItems } from "@/types/nav"
import type { LucideIcon } from "lucide-react"

import {
  Bug,
  LayoutDashboard,
  Logs,
  MessageSquareWarning,
  Network,
  Settings,
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

import { Badge } from "@/components/ui/badge"
import { SidebarMenuButton, SidebarMenuItem } from "@/components/ui/sidebar"

const iconMap: Record<string, LucideIcon> = {
  LayoutDashboard,
  Logs,
  Bug,
  Network,
  MessageSquareWarning,
  Settings,
}

interface NavListItemProps {
  item: NavItems
  isCollapsed: boolean
}

export function NavListItem({ item, isCollapsed }: NavListItemProps) {
  const pathname = usePathname()
  const isActive = pathname === item.path
  const IconComponent = iconMap[item.icon]

  const getBadgeVariant = (badge: string) => {
    if (badge === "OK") return "default"
    if (badge === "Home") return "secondary"
    if (/^\d+$/.test(badge)) {
      const num = parseInt(badge)
      return num > 0 ? "destructive" : "secondary"
    }
    return "outline"
  }

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        isActive={isActive}
        tooltip={isCollapsed ? item.name : undefined}
      >
        <Link href={item.path} className="flex items-center gap-3">
          {IconComponent && <IconComponent className="h-4 w-4" />}
          {!isCollapsed && (
            <>
              <span className="flex-1">{item.name}</span>
              {item.badge && (
                <Badge
                  variant={getBadgeVariant(item.badge)}
                  className="h-5 px-1.5 text-xs"
                >
                  {item.badge}
                </Badge>
              )}
            </>
          )}
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}
