/**
 * Role Management Component
 * Stub implementation for integration testing
 */

import { useQuery } from '@tanstack/react-query'
import { rbacApiClient } from '@/lib/api/rbac'
import { QueryKeys } from '@/types/api'

export function RoleManagement() {
  const { data, isLoading: loading } = useQuery({
    queryKey: QueryKeys.roles.rolesList(),
    queryFn: () => rbacApiClient.getRoles(),
  })
  const roles = data?.items

  return (
    <div data-testid="role-management">
      <h1>Role Management</h1>
      <p>Manage user roles and permissions</p>
      <div>
        <button>Roles</button>
        <button>Hierarchy</button>
      </div>
      {loading && <div data-testid="loading">Loading...</div>}
      <div data-testid="roles-list">
        {roles?.map((role) => (
          <div key={role.id} data-testid={`role-${role.id}`}>
            {role.name}
            <button>Edit</button>
            <button>Delete</button>
          </div>
        ))}
      </div>
      <button data-testid="add-role-button">Create Role</button>
    </div>
  )
}

export default RoleManagement