/**
 * Route Guard Component
 * Integration testing implementation with redirect logic
 */

import { ReactNode, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/hooks/useAuth'

interface RouteGuardProps {
  children: ReactNode
  fallback?: ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
}

export function RouteGuard({ 
  children, 
  fallback,
  requireAuth = true,
  requireAdmin = false 
}: RouteGuardProps) {
  const router = useRouter()
  const { isAuthenticated, user, isLoading } = useAuth()

  useEffect(() => {
    if (isLoading) return // Wait for auth to load

    if (requireAuth && !isAuthenticated) {
      router.push('/login')
      return
    }

    if (requireAdmin && (!isAuthenticated || !user?.is_superuser)) {
      router.push('/dashboard')
      return
    }
  }, [requireAuth, requireAdmin, isAuthenticated, user, isLoading, router])

  if (isLoading) {
    return fallback || <div>Loading...</div>
  }

  if (requireAuth && !isAuthenticated) {
    return fallback || <div>Redirecting to login...</div>
  }

  if (requireAdmin && (!isAuthenticated || !user?.is_superuser)) {
    return fallback || <div>Redirecting to dashboard...</div>
  }

  return <>{children}</>
}

export default RouteGuard