/**
 * TaskStatusBadge Unit Tests
 * Comprehensive testing for the TaskStatusBadge component
 */

import { render, screen } from "@testing-library/react"
import { describe, expect, it } from "vitest"

import {
  TaskStatusBadge,
  TaskPriorityBadge,
  TaskBadge,
  TaskStatusBadgeWithIcon,
  TaskPriorityBadgeWithIcon,
  getStatusIcon,
  getPriorityIcon,
  isTaskCompleted,
  isTaskActive,
  isTaskBlocked,
  isTaskOverdue,
  getStatusColor,
  getPriorityColor,
} from "../TaskStatusBadge"

describe("TaskStatusBadge", () => {
  describe("Rendering", () => {
    it("renders with default status", () => {
      render(<TaskStatusBadge status="Not Started" />)
      
      const badge = screen.getByText("Not Started")
      expect(badge).toBeInTheDocument()
      expect(badge).toHaveClass("bg-gray-100", "text-gray-800")
    })

    it("renders all status variants correctly", () => {
      const statuses = [
        "Not Started",
        "In Progress", 
        "On Hold",
        "Completed",
        "Blocked",
        "Overdue",
        "Review Pending",
        "Approved"
      ] as const

      statuses.forEach(status => {
        const { unmount } = render(<TaskStatusBadge status={status} />)
        expect(screen.getByText(status)).toBeInTheDocument()
        unmount()
      })
    })

    it("applies custom className", () => {
      render(<TaskStatusBadge status="In Progress" className="custom-class" />)
      
      const badge = screen.getByText("In Progress")
      expect(badge).toHaveClass("custom-class")
    })

    it("forwards additional props", () => {
      render(<TaskStatusBadge status="Completed" data-testid="status-badge" />)
      
      const badge = screen.getByTestId("status-badge")
      expect(badge).toBeInTheDocument()
    })
  })

  describe("Status Colors", () => {
    it("applies correct colors for In Progress status", () => {
      render(<TaskStatusBadge status="In Progress" />)
      
      const badge = screen.getByText("In Progress")
      expect(badge).toHaveClass("bg-blue-100", "text-blue-800")
    })

    it("applies correct colors for Completed status", () => {
      render(<TaskStatusBadge status="Completed" />)
      
      const badge = screen.getByText("Completed")
      expect(badge).toHaveClass("bg-green-100", "text-green-800")
    })

    it("applies correct colors for Blocked status", () => {
      render(<TaskStatusBadge status="Blocked" />)
      
      const badge = screen.getByText("Blocked")
      expect(badge).toHaveClass("bg-red-100", "text-red-800")
    })
  })
})

describe("TaskPriorityBadge", () => {
  describe("Rendering", () => {
    it("renders with default priority", () => {
      render(<TaskPriorityBadge priority="Medium" />)
      
      const badge = screen.getByText("Medium")
      expect(badge).toBeInTheDocument()
      expect(badge).toHaveClass("bg-blue-100", "text-blue-700")
    })

    it("renders all priority variants correctly", () => {
      const priorities = ["Low", "Medium", "High", "Critical"] as const

      priorities.forEach(priority => {
        const { unmount } = render(<TaskPriorityBadge priority={priority} />)
        expect(screen.getByText(priority)).toBeInTheDocument()
        unmount()
      })
    })

    it("applies custom className", () => {
      render(<TaskPriorityBadge priority="High" className="custom-priority" />)
      
      const badge = screen.getByText("High")
      expect(badge).toHaveClass("custom-priority")
    })
  })

  describe("Priority Colors", () => {
    it("applies correct colors for Low priority", () => {
      render(<TaskPriorityBadge priority="Low" />)
      
      const badge = screen.getByText("Low")
      expect(badge).toHaveClass("bg-gray-100", "text-gray-700")
    })

    it("applies correct colors for Critical priority", () => {
      render(<TaskPriorityBadge priority="Critical" />)
      
      const badge = screen.getByText("Critical")
      expect(badge).toHaveClass("bg-red-100", "text-red-700")
    })
  })
})

describe("TaskBadge", () => {
  it("renders both status and priority by default", () => {
    render(<TaskBadge status="In Progress" priority="High" />)
    
    expect(screen.getByText("In Progress")).toBeInTheDocument()
    expect(screen.getByText("High")).toBeInTheDocument()
  })

  it("renders only status when showPriority is false", () => {
    render(<TaskBadge status="Completed" priority="Low" showPriority={false} />)
    
    expect(screen.getByText("Completed")).toBeInTheDocument()
    expect(screen.queryByText("Low")).not.toBeInTheDocument()
  })

  it("renders only priority when showStatus is false", () => {
    render(<TaskBadge status="Blocked" priority="Critical" showStatus={false} />)
    
    expect(screen.queryByText("Blocked")).not.toBeInTheDocument()
    expect(screen.getByText("Critical")).toBeInTheDocument()
  })

  it("applies custom className to container", () => {
    render(<TaskBadge status="On Hold" priority="Medium" className="custom-container" />)
    
    const container = screen.getByText("On Hold").parentElement
    expect(container).toHaveClass("custom-container")
  })
})

describe("TaskStatusBadgeWithIcon", () => {
  it("renders status with icon", () => {
    render(<TaskStatusBadgeWithIcon status="Completed" />)
    
    const badge = screen.getByText("Completed")
    expect(badge).toBeInTheDocument()
    
    // Check that the badge has gap class for icon spacing
    expect(badge).toHaveClass("gap-1")
  })

  it("includes icon in the badge content", () => {
    render(<TaskStatusBadgeWithIcon status="In Progress" />)
    
    const badge = screen.getByText("In Progress")
    const iconSpan = badge.querySelector("span.text-xs")
    expect(iconSpan).toBeInTheDocument()
    expect(iconSpan).toHaveTextContent("🔄")
  })
})

describe("TaskPriorityBadgeWithIcon", () => {
  it("renders priority with icon", () => {
    render(<TaskPriorityBadgeWithIcon priority="Critical" />)
    
    const badge = screen.getByText("Critical")
    expect(badge).toBeInTheDocument()
    expect(badge).toHaveClass("gap-1")
  })

  it("includes icon in the badge content", () => {
    render(<TaskPriorityBadgeWithIcon priority="High" />)
    
    const badge = screen.getByText("High")
    const iconSpan = badge.querySelector("span.text-xs")
    expect(iconSpan).toBeInTheDocument()
    expect(iconSpan).toHaveTextContent("🔼")
  })
})

describe("Utility Functions", () => {
  describe("getStatusIcon", () => {
    it("returns correct icons for all statuses", () => {
      expect(getStatusIcon("Not Started")).toBe("⭕")
      expect(getStatusIcon("In Progress")).toBe("🔄")
      expect(getStatusIcon("On Hold")).toBe("⏸️")
      expect(getStatusIcon("Completed")).toBe("✅")
      expect(getStatusIcon("Blocked")).toBe("🚫")
      expect(getStatusIcon("Overdue")).toBe("⚠️")
      expect(getStatusIcon("Review Pending")).toBe("👀")
      expect(getStatusIcon("Approved")).toBe("✅")
    })

    it("returns default icon for unknown status", () => {
      expect(getStatusIcon("Unknown" as any)).toBe("❓")
    })
  })

  describe("getPriorityIcon", () => {
    it("returns correct icons for all priorities", () => {
      expect(getPriorityIcon("Low")).toBe("🔽")
      expect(getPriorityIcon("Medium")).toBe("➖")
      expect(getPriorityIcon("High")).toBe("🔼")
      expect(getPriorityIcon("Critical")).toBe("🔴")
    })

    it("returns default icon for unknown priority", () => {
      expect(getPriorityIcon("Unknown" as any)).toBe("❓")
    })
  })

  describe("Task State Helpers", () => {
    it("correctly identifies completed tasks", () => {
      expect(isTaskCompleted("Completed")).toBe(true)
      expect(isTaskCompleted("Approved")).toBe(true)
      expect(isTaskCompleted("In Progress")).toBe(false)
      expect(isTaskCompleted("Blocked")).toBe(false)
    })

    it("correctly identifies active tasks", () => {
      expect(isTaskActive("In Progress")).toBe(true)
      expect(isTaskActive("Completed")).toBe(false)
      expect(isTaskActive("Not Started")).toBe(false)
    })

    it("correctly identifies blocked tasks", () => {
      expect(isTaskBlocked("Blocked")).toBe(true)
      expect(isTaskBlocked("On Hold")).toBe(true)
      expect(isTaskBlocked("In Progress")).toBe(false)
      expect(isTaskBlocked("Completed")).toBe(false)
    })

    it("correctly identifies overdue tasks", () => {
      expect(isTaskOverdue("Overdue")).toBe(true)
      expect(isTaskOverdue("In Progress")).toBe(false)
      expect(isTaskOverdue("Completed")).toBe(false)
    })
  })

  describe("Color Utilities", () => {
    it("returns correct colors for statuses", () => {
      expect(getStatusColor("Not Started")).toBe("#6b7280")
      expect(getStatusColor("In Progress")).toBe("#3b82f6")
      expect(getStatusColor("Completed")).toBe("#10b981")
      expect(getStatusColor("Blocked")).toBe("#ef4444")
      expect(getStatusColor("Overdue")).toBe("#dc2626")
    })

    it("returns correct colors for priorities", () => {
      expect(getPriorityColor("Low")).toBe("#6b7280")
      expect(getPriorityColor("Medium")).toBe("#3b82f6")
      expect(getPriorityColor("High")).toBe("#f97316")
      expect(getPriorityColor("Critical")).toBe("#dc2626")
    })

    it("returns default color for unknown values", () => {
      expect(getStatusColor("Unknown" as any)).toBe("#6b7280")
      expect(getPriorityColor("Unknown" as any)).toBe("#6b7280")
    })
  })
})
