"use client"

/**
 * TaskStatusBadge Component
 * 
 * A specialized badge component for displaying task status and priority.
 * Uses shadcn-ui Badge component with custom variants for task-specific styling.
 */
import * as React from "react"

import type { VariantProps } from "class-variance-authority"

import { cva } from "class-variance-authority"

import type { TaskPriorityEnum, TaskStatusEnum } from "../../types/api"

import { Badge } from "../ui/badge"
import { cn } from "../../lib/utils"

// Task Status Badge Variants
const taskStatusBadgeVariants = cva(
  "inline-flex items-center justify-center rounded-full border px-2 py-1 text-xs font-medium transition-colors",
  {
    variants: {
      status: {
        "Not Started": "bg-gray-100 text-gray-800 border-gray-200 dark:bg-gray-800 dark:text-gray-200 dark:border-gray-700",
        "In Progress": "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-900 dark:text-blue-200 dark:border-blue-700",
        "On Hold": "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-900 dark:text-yellow-200 dark:border-yellow-700",
        "Completed": "bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-700",
        "Blocked": "bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-200 dark:border-red-700",
        "Overdue": "bg-red-100 text-red-800 border-red-200 dark:bg-red-900 dark:text-red-200 dark:border-red-700",
        "Review Pending": "bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-900 dark:text-purple-200 dark:border-purple-700",
        "Approved": "bg-green-100 text-green-800 border-green-200 dark:bg-green-900 dark:text-green-200 dark:border-green-700",
      },
    },
    defaultVariants: {
      status: "Not Started",
    },
  }
)

// Task Priority Badge Variants
const taskPriorityBadgeVariants = cva(
  "inline-flex items-center justify-center rounded-full border px-2 py-1 text-xs font-medium transition-colors",
  {
    variants: {
      priority: {
        "Low": "bg-gray-100 text-gray-700 border-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-700",
        "Medium": "bg-blue-100 text-blue-700 border-blue-200 dark:bg-blue-900 dark:text-blue-300 dark:border-blue-700",
        "High": "bg-orange-100 text-orange-700 border-orange-200 dark:bg-orange-900 dark:text-orange-300 dark:border-orange-700",
        "Critical": "bg-red-100 text-red-700 border-red-200 dark:bg-red-900 dark:text-red-300 dark:border-red-700",
      },
    },
    defaultVariants: {
      priority: "Medium",
    },
  }
)

// Task Status Badge Props
interface TaskStatusBadgeProps extends React.ComponentProps<"span">, VariantProps<typeof taskStatusBadgeVariants> {
  status: TaskStatusEnum
}

// Task Priority Badge Props
interface TaskPriorityBadgeProps extends React.ComponentProps<"span">, VariantProps<typeof taskPriorityBadgeVariants> {
  priority: TaskPriorityEnum
}

/**
 * TaskStatusBadge - Displays task status with appropriate styling
 */
function TaskStatusBadge({ status, className, ...props }: TaskStatusBadgeProps) {
  return (
    <span
      className={cn(taskStatusBadgeVariants({ status }), className)}
      {...props}
    >
      {status}
    </span>
  )
}

/**
 * TaskPriorityBadge - Displays task priority with appropriate styling
 */
function TaskPriorityBadge({ priority, className, ...props }: TaskPriorityBadgeProps) {
  return (
    <span
      className={cn(taskPriorityBadgeVariants({ priority }), className)}
      {...props}
    >
      {priority}
    </span>
  )
}

/**
 * Combined TaskBadge - Displays both status and priority
 */
interface TaskBadgeProps {
  status: TaskStatusEnum
  priority: TaskPriorityEnum
  showPriority?: boolean
  showStatus?: boolean
  className?: string
}

function TaskBadge({ 
  status, 
  priority, 
  showPriority = true, 
  showStatus = true, 
  className 
}: TaskBadgeProps) {
  return (
    <div className={cn("flex items-center gap-2", className)}>
      {showStatus && <TaskStatusBadge status={status} />}
      {showPriority && <TaskPriorityBadge priority={priority} />}
    </div>
  )
}

/**
 * Get status icon for visual enhancement
 */
function getStatusIcon(status: TaskStatusEnum): string {
  switch (status) {
    case "Not Started":
      return "⭕"
    case "In Progress":
      return "🔄"
    case "On Hold":
      return "⏸️"
    case "Completed":
      return "✅"
    case "Blocked":
      return "🚫"
    case "Overdue":
      return "⚠️"
    case "Review Pending":
      return "👀"
    case "Approved":
      return "✅"
    default:
      return "❓"
  }
}

/**
 * Get priority icon for visual enhancement
 */
function getPriorityIcon(priority: TaskPriorityEnum): string {
  switch (priority) {
    case "Low":
      return "🔽"
    case "Medium":
      return "➖"
    case "High":
      return "🔼"
    case "Critical":
      return "🔴"
    default:
      return "❓"
  }
}

/**
 * TaskStatusBadge with icon
 */
function TaskStatusBadgeWithIcon({ status, className, ...props }: TaskStatusBadgeProps) {
  const icon = getStatusIcon(status)
  
  return (
    <span
      className={cn(taskStatusBadgeVariants({ status }), "gap-1", className)}
      {...props}
    >
      <span className="text-xs">{icon}</span>
      {status}
    </span>
  )
}

/**
 * TaskPriorityBadge with icon
 */
function TaskPriorityBadgeWithIcon({ priority, className, ...props }: TaskPriorityBadgeProps) {
  const icon = getPriorityIcon(priority)
  
  return (
    <span
      className={cn(taskPriorityBadgeVariants({ priority }), "gap-1", className)}
      {...props}
    >
      <span className="text-xs">{icon}</span>
      {priority}
    </span>
  )
}

/**
 * Utility function to determine if a status indicates completion
 */
export function isTaskCompleted(status: TaskStatusEnum): boolean {
  return status === "Completed" || status === "Approved"
}

/**
 * Utility function to determine if a status indicates the task is active
 */
export function isTaskActive(status: TaskStatusEnum): boolean {
  return status === "In Progress"
}

/**
 * Utility function to determine if a status indicates the task is blocked
 */
export function isTaskBlocked(status: TaskStatusEnum): boolean {
  return status === "Blocked" || status === "On Hold"
}

/**
 * Utility function to determine if a task is overdue
 */
export function isTaskOverdue(status: TaskStatusEnum): boolean {
  return status === "Overdue"
}

/**
 * Utility function to get status color for charts/graphs
 */
export function getStatusColor(status: TaskStatusEnum): string {
  switch (status) {
    case "Not Started":
      return "#6b7280" // gray-500
    case "In Progress":
      return "#3b82f6" // blue-500
    case "On Hold":
      return "#eab308" // yellow-500
    case "Completed":
      return "#10b981" // emerald-500
    case "Blocked":
      return "#ef4444" // red-500
    case "Overdue":
      return "#dc2626" // red-600
    case "Review Pending":
      return "#8b5cf6" // violet-500
    case "Approved":
      return "#059669" // emerald-600
    default:
      return "#6b7280" // gray-500
  }
}

/**
 * Utility function to get priority color for charts/graphs
 */
export function getPriorityColor(priority: TaskPriorityEnum): string {
  switch (priority) {
    case "Low":
      return "#6b7280" // gray-500
    case "Medium":
      return "#3b82f6" // blue-500
    case "High":
      return "#f97316" // orange-500
    case "Critical":
      return "#dc2626" // red-600
    default:
      return "#6b7280" // gray-500
  }
}

export {
  TaskStatusBadge,
  TaskPriorityBadge,
  TaskBadge,
  TaskStatusBadgeWithIcon,
  TaskPriorityBadgeWithIcon,
  taskStatusBadgeVariants,
  taskPriorityBadgeVariants,
  getStatusIcon,
  getPriorityIcon,
}
