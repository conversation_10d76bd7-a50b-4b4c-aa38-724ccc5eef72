/**
 * Task Components Export Index
 *
 * Centralized exports for all task-related components.
 * Provides clean imports for consuming components.
 */

// Badge Components
export {
  TaskStatusBadge,
  TaskPriorityBadge,
  TaskBadge,
  TaskStatusBadgeWithIcon,
  TaskPriorityBadgeWithIcon,
  taskStatusBadgeVariants,
  taskPriorityBadgeVariants,
  getStatusIcon,
  getPriorityIcon,
  isTaskCompleted,
  isTaskActive,
  isTaskBlocked,
  isTaskOverdue,
  getStatusColor,
  getPriorityColor,
} from "./TaskStatusBadge"

// Card Components
export { TaskCard, TaskCardSkeleton } from "./TaskCard"

// List Components
export { TaskList, TaskListSkeleton } from "./TaskList"

// Form Components
export { TaskForm } from "./TaskForm"

// Re-export types for convenience
export type {
  TaskRead,
  TaskWithAssignments,
  TaskPriorityEnum,
  TaskStatusEnum,
} from "../../types/api"
