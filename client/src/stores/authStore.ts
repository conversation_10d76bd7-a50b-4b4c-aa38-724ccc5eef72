"use client"

/**
 * Zustand store for authentication state management
 */
import type { UserRead } from "../types/api"

import { create } from "zustand"
import { createJSONStorage, persist } from "zustand/middleware"

import { apiClient } from "../lib/api/client"

interface AuthState {
  // State properties
  user: UserRead | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean

  // Actions
  setAuth: (user: UserRead, token: string) => void
  updateUser: (user: UserRead) => void
  clearAuth: () => void
  setLoading: (loading: boolean) => void
  initializeAuth: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // Initial state
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,

      // Set authentication data after successful login
      setAuth: (user: UserRead, token: string) => {
        // Set token in API client
        apiClient.setAuthToken(token)

        set({
          user,
          token,
          isAuthenticated: true,
          isLoading: false,
        })
      },

      // Update user data (for profile updates)
      updateUser: (user: UserRead) => {
        set((state) => ({
          ...state,
          user,
        }))
      },

      // Clear authentication data on logout
      clearAuth: () => {
        // Clear token from API client
        apiClient.clearAuthToken()

        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false,
        })
      },

      // Set loading state
      setLoading: (loading: boolean) => {
        set((state) => ({
          ...state,
          isLoading: loading,
        }))
      },

      // Initialize auth state (called on app startup)
      initializeAuth: () => {
        const state = get()
        if (state.token && state.user) {
          // Token exists in storage, set as authenticated AND set token in API client
          apiClient.setAuthToken(state.token)
          set({
            isAuthenticated: true,
            isLoading: false,
          })
        } else {
          // No token, ensure clean state
          apiClient.clearAuthToken()
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false,
          })
        }
      },
    }),
    {
      name: "auth-storage", // name of the item in localStorage
      storage: createJSONStorage(() => localStorage),
      // Only persist user and token, not loading states
      partialize: (state) => ({
        user: state.user,
        token: state.token,
      }),
      // Rehydrate the state on app load
      onRehydrateStorage: () => (state) => {
        if (state) {
          state.initializeAuth()
        }
      },
    }
  )
)

// Selectors for optimized component re-renders
export const useAuthUser = () => useAuthStore((state) => state.user)
export const useAuthToken = () => useAuthStore((state) => state.token)
export const useIsAuthenticated = () =>
  useAuthStore((state) => state.isAuthenticated)
export const useIsLoading = () => useAuthStore((state) => state.isLoading)

// Helper functions
export const getAuthToken = () => useAuthStore.getState().token
export const getAuthUser = () => useAuthStore.getState().user
export const isUserAuthenticated = () => useAuthStore.getState().isAuthenticated
