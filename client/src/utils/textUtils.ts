import z from "zod"

/**
 *
 * @param val Text
 * @returns Sanitized text fields for security and consistency
 */
export const sanitizeText = (
  val: string | null | undefined
): string | null | undefined => {
  if (val == null) return val
  let sanitized = val.trim()
  if (!sanitized) return undefined // Return undefined for empty strings after trim to make them optional
  sanitized = sanitized.replace(/[<>"\\']/g, "")
  sanitized = sanitized.replace(/\s+/g, " ")
  return sanitized
}

/**
 *
 * @param min Minimum number of characters
 * @param max  Maximum number of characters
 * @returns Sanitized text fields for security and consistency
 */
export const textValidation = (min: number, max: number) =>
  z.string().min(min).max(max).transform(sanitizeText).pipe(z.string().min(min))

/**
 *
 * @param v Search term
 * @returns Sanitized term for security and consistency
 */
export const sanitizeSearchTerm = (
  v: string | null | undefined
): string | null | undefined => {
  if (v == null) return v
  const sanitized = v.trim().replace(/[<>"\\';]/g, "")
  return sanitized.length > 0 ? sanitized : undefined
}
