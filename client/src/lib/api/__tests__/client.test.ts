import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { apiClient } from "../client"

// Mock fetch globally
const mockFetch = vi.fn()
global.fetch = mockFetch

describe("API Client", () => {
  beforeEach(() => {
    mockFetch.mockClear()
    // Reset any auth token
    apiClient.setAuthToken("")
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe("setAuthToken", () => {
    it("should set authorization header when token is provided", () => {
      const token = "test-token"
      apiClient.setAuthToken(token)

      // We can't directly test the private headers, but we can test through a request
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: "test" }),
      })

      apiClient.get("/test")

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test"),
        expect.objectContaining({
          headers: expect.objectContaining({
            Authorization: `Bearer ${token}`,
          }),
        })
      )
    })

    it("should remove authorization header when empty token is provided", () => {
      // First set a token
      apiClient.setAuthToken("test-token")
      // Then clear it
      apiClient.setAuthToken("")

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ data: "test" }),
      })

      apiClient.get("/test")

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test"),
        expect.objectContaining({
          headers: expect.not.objectContaining({
            Authorization: expect.any(String),
          }),
        })
      )
    })
  })

  describe("GET requests", () => {
    it("should make GET request with correct parameters", async () => {
      const mockResponseData = { data: "test" }
      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockResponseData),
      })

      const result = await apiClient.get("/test")

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test"),
        expect.objectContaining({
          method: "GET",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
          }),
        })
      )
      expect(result).toEqual({
        data: mockResponseData,
        status: 200,
      })
    })

    it("should handle GET request errors", async () => {
      const errorData = { error: "Not found" }
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 404,
        statusText: "Not Found",
        json: () => Promise.resolve(errorData),
      })

      const result = await apiClient.get("/test")

      expect(result).toEqual({
        error: errorData,
        status: 404,
      })
    })
  })

  describe("POST requests", () => {
    it("should make POST request with correct parameters", async () => {
      const mockResponseData = { data: "created" }
      const requestData = { name: "test" }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 201,
        json: () => Promise.resolve(mockResponseData),
      })

      const result = await apiClient.post("/test", requestData)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test"),
        expect.objectContaining({
          method: "POST",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
          }),
          body: JSON.stringify(requestData),
        })
      )
      expect(result).toEqual({
        data: mockResponseData,
        status: 201,
      })
    })

    it("should handle POST request errors", async () => {
      const errorData = { error: "Invalid data" }
      mockFetch.mockResolvedValueOnce({
        ok: false,
        status: 400,
        statusText: "Bad Request",
        json: () => Promise.resolve(errorData),
      })

      const result = await apiClient.post("/test", {})

      expect(result).toEqual({
        error: errorData,
        status: 400,
      })
    })
  })

  describe("PUT requests", () => {
    it("should make PUT request with correct parameters", async () => {
      const mockResponseData = { data: "updated" }
      const requestData = { name: "updated" }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockResponseData),
      })

      const result = await apiClient.put("/test/1", requestData)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test/1"),
        expect.objectContaining({
          method: "PUT",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
          }),
          body: JSON.stringify(requestData),
        })
      )
      expect(result).toEqual({
        data: mockResponseData,
        status: 200,
      })
    })
  })

  describe("DELETE requests", () => {
    it("should make DELETE request with correct parameters", async () => {
      const mockResponseData = { success: true }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () => Promise.resolve(mockResponseData),
      })

      const result = await apiClient.delete("/test/1")

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/test/1"),
        expect.objectContaining({
          method: "DELETE",
          headers: expect.objectContaining({
            "Content-Type": "application/json",
          }),
        })
      )
      expect(result).toEqual({
        data: mockResponseData,
        status: 200,
      })
    })
  })

  describe("Authentication methods", () => {
    it("should make login request", async () => {
      const credentials = { username: "test", password: "password" }
      const mockResponse = {
        access_token: "token",
        user: { id: 1, username: "test" },
      }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockResponse),
      })

      const result = await apiClient.login(credentials)

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/auth/login"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(credentials),
        })
      )
      expect(result).toEqual(mockResponse)
    })

    it("should make logout request", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve({ success: true }),
      })

      const result = await apiClient.logout()

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/auth/logout"),
        expect.objectContaining({
          method: "POST",
        })
      )
      expect(result).toEqual({ success: true })
    })

    it("should get current user", async () => {
      const mockUser = { id: 1, username: "test" }

      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.resolve(mockUser),
      })

      const result = await apiClient.getCurrentUser()

      expect(mockFetch).toHaveBeenCalledWith(
        expect.stringContaining("/users/me"),
        expect.objectContaining({
          method: "GET",
        })
      )
      expect(result).toEqual(mockUser)
    })
  })

  describe("Network errors", () => {
    it("should handle network errors", async () => {
      mockFetch.mockRejectedValueOnce(new Error("Network error"))

      const result = await apiClient.get("/test")

      expect(result).toEqual({
        error: {
          detail: "Network error",
        },
        status: 0,
      })
    })

    it("should handle JSON parsing errors", async () => {
      mockFetch.mockResolvedValueOnce({
        ok: true,
        json: () => Promise.reject(new Error("Invalid JSON")),
      })

      const result = await apiClient.get("/test")

      expect(result).toEqual({
        error: {
          detail: "Invalid JSON",
        },
        status: 0,
      })
    })
  })
})
