/**
 * Audit Trail API Client for Ultimate Electrical Designer
 * Provides type-safe HTTP client methods for audit trail and activity log operations
 */

import type {
  ActivityLog,
  ActivityLogCreate,
  ActivityLogFilter,
  ActivityLogUpdate,
  AuditSummary,
  AuditTrail,
  AuditTrailCreate,
  AuditTrailFilter,
  AuditTrailUpdate,
  ListQueryParams,
  PaginatedResponse,
  RecordHistory,
  UserActivitySummary,
} from "@/types/api"

import { apiClient } from "./client"

export class AuditApiClient {
  // Activity Log Management
  async getActivityLogs(
    params?: ActivityLogFilter & ListQueryParams
  ): Promise<PaginatedResponse<ActivityLog>> {
    const searchParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            value.forEach((v) => searchParams.append(key, v.toString()))
          } else {
            searchParams.append(key, value.toString())
          }
        }
      })
    }

    const endpoint = `/api/v1/activity-logs${searchParams.toString() ? `?${searchParams.toString()}` : ""}`
    const response =
      await apiClient.get<PaginatedResponse<ActivityLog>>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getActivityLog(id: number): Promise<ActivityLog> {
    const response = await apiClient.get<ActivityLog>(
      `/api/v1/activity-logs/${id}`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async createActivityLog(data: ActivityLogCreate): Promise<ActivityLog> {
    const response = await apiClient.post<ActivityLog>(
      "/api/v1/activity-logs",
      data
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async updateActivityLog(
    id: number,
    data: ActivityLogUpdate
  ): Promise<ActivityLog> {
    const response = await apiClient.put<ActivityLog>(
      `/api/v1/activity-logs/${id}`,
      data
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getUserActivityLogs(
    userId: number,
    params?: ActivityLogFilter & ListQueryParams
  ): Promise<PaginatedResponse<ActivityLog>> {
    const searchParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            value.forEach((v) => searchParams.append(key, v.toString()))
          } else {
            searchParams.append(key, value.toString())
          }
        }
      })
    }

    const endpoint = `/api/v1/users/${userId}/activity-logs${searchParams.toString() ? `?${searchParams.toString()}` : ""}`
    const response =
      await apiClient.get<PaginatedResponse<ActivityLog>>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getSecurityEvents(
    params?: ActivityLogFilter & ListQueryParams
  ): Promise<PaginatedResponse<ActivityLog>> {
    const securityParams = { ...params, is_security_related: true }
    return this.getActivityLogs(securityParams)
  }

  // Audit Trail Management
  async getAuditTrails(
    params?: AuditTrailFilter & ListQueryParams
  ): Promise<PaginatedResponse<AuditTrail>> {
    const searchParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          if (Array.isArray(value)) {
            value.forEach((v) => searchParams.append(key, v.toString()))
          } else {
            searchParams.append(key, value.toString())
          }
        }
      })
    }

    const endpoint = `/api/v1/audit-trails${searchParams.toString() ? `?${searchParams.toString()}` : ""}`
    const response =
      await apiClient.get<PaginatedResponse<AuditTrail>>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getAuditTrail(id: number): Promise<AuditTrail> {
    const response = await apiClient.get<AuditTrail>(
      `/api/v1/audit-trails/${id}`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async createAuditTrail(data: AuditTrailCreate): Promise<AuditTrail> {
    const response = await apiClient.post<AuditTrail>(
      "/api/v1/audit-trails",
      data
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async updateAuditTrail(
    id: number,
    data: AuditTrailUpdate
  ): Promise<AuditTrail> {
    const response = await apiClient.put<AuditTrail>(
      `/api/v1/audit-trails/${id}`,
      data
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getRecordHistory(
    tableName: string,
    recordId: number,
    params?: ListQueryParams
  ): Promise<RecordHistory> {
    const searchParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/audit-trails/record-history/${tableName}/${recordId}${searchParams.toString() ? `?${searchParams.toString()}` : ""}`
    const response = await apiClient.get<RecordHistory>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  // Summary and Analytics
  async getAuditSummary(params?: {
    start_date?: string
    end_date?: string
  }): Promise<AuditSummary> {
    const searchParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/audit-trails/summary${searchParams.toString() ? `?${searchParams.toString()}` : ""}`
    const response = await apiClient.get<AuditSummary>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getUserActivitySummary(
    userId: number,
    params?: { start_date?: string; end_date?: string }
  ): Promise<UserActivitySummary> {
    const searchParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/users/${userId}/activity-summary${searchParams.toString() ? `?${searchParams.toString()}` : ""}`
    const response = await apiClient.get<UserActivitySummary>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  // Maintenance Operations
  async cleanupOldLogs(
    daysToKeep: number = 365
  ): Promise<{ deleted_count: number }> {
    const response = await apiClient.post<{ deleted_count: number }>(
      "/api/v1/audit-trails/cleanup",
      {
        days_to_keep: daysToKeep,
      }
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  // Utility Methods
  async logUserActivity(data: {
    action_type: string
    action_description: string
    target_type?: string
    target_id?: number
    target_name?: string
    category?: string
    tags?: string[]
    metadata?: Record<string, any>
  }): Promise<ActivityLog> {
    const activityData: ActivityLogCreate = {
      name: `${data.action_type}_${Date.now()}`,
      action_type: data.action_type,
      action_description: data.action_description,
      target_type: data.target_type,
      target_id: data.target_id,
      target_name: data.target_name,
      category: data.category,
      tags: data.tags,
      metadata: data.metadata,
      is_data_change: false,
      is_security_related: false,
      is_system_event: false,
    }

    return this.createActivityLog(activityData)
  }

  async logSecurityEvent(data: {
    action_type: string
    action_description: string
    severity?: "INFO" | "LOW" | "MEDIUM" | "HIGH" | "CRITICAL"
    metadata?: Record<string, any>
  }): Promise<ActivityLog> {
    const activityData: ActivityLogCreate = {
      name: `SECURITY_${data.action_type}_${Date.now()}`,
      action_type: data.action_type,
      action_description: data.action_description,
      severity: data.severity || "INFO",
      category: "SECURITY",
      tags: ["security"],
      metadata: data.metadata,
      is_data_change: false,
      is_security_related: true,
      is_system_event: false,
    }

    return this.createActivityLog(activityData)
  }

  async logDataChange(data: {
    table_name: string
    record_id: number
    operation: "INSERT" | "UPDATE" | "DELETE"
    field_name?: string
    old_value?: string
    new_value?: string
    change_reason?: string
    activity_log_id?: number
  }): Promise<AuditTrail> {
    const auditData: AuditTrailCreate = {
      name: `${data.table_name}_${data.record_id}_${data.operation}_${Date.now()}`,
      table_name: data.table_name,
      record_id: data.record_id,
      operation: data.operation,
      field_name: data.field_name,
      old_value: data.old_value,
      new_value: data.new_value,
      change_reason: data.change_reason,
      activity_log_id: data.activity_log_id,
      is_sensitive: false,
      is_system_change: false,
    }

    return this.createAuditTrail(auditData)
  }

  // Batch Operations
  async batchLogActivities(
    activities: ActivityLogCreate[]
  ): Promise<ActivityLog[]> {
    const results = await Promise.all(
      activities.map((activity) => this.createActivityLog(activity))
    )
    return results
  }

  async batchLogDataChanges(
    changes: AuditTrailCreate[]
  ): Promise<AuditTrail[]> {
    const results = await Promise.all(
      changes.map((change) => this.createAuditTrail(change))
    )
    return results
  }
}

// Export singleton instance
export const auditApiClient = new AuditApiClient()
