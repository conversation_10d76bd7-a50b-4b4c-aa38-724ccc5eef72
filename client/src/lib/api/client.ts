/**
 * API Client for Ultimate Electrical Designer
 * Provides type-safe HTTP client with authentication support
 */

import type {
  ApiClientConfig,
  ApiResponse,
  HealthCheck,
  ListQueryParams,
  LoginRequest,
  LoginResponse,
  LogoutResponse,
  PasswordChangeRequest,
  PasswordChangeResponse,
  RegisterRequest,
  RegisterResponse,
  RequestOptions,
  TaskAssignmentRequest,
  TaskCreate,
  TaskFilters,
  TaskListResponse,
  TaskRead,
  TaskSearchRequest,
  TaskSortOptions,
  TaskStatistics,
  TaskUpdate,
  UserCreate,
  UserPaginatedResponse,
  UserRead,
  UserSummary,
  UserUpdate,
} from "../../types/api"

export class ApiClient {
  private baseURL: string
  private authToken: string = ""
  private timeout: number

  constructor(config: ApiClientConfig) {
    this.baseURL = config.baseURL.replace(/\/$/, "") // Remove trailing slash
    this.timeout = config.timeout || 30000
  }

  /**
   * Set authentication token for subsequent requests
   */
  setAuthToken(token: string): void {
    this.authToken = token
  }

  /**
   * Clear authentication token
   */
  clearAuthToken(): void {
    this.authToken = ""
  }

  /**
   * Get default headers for requests
   */
  private getDefaultHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    }

    if (this.authToken) {
      headers.Authorization = `Bearer ${this.authToken}`
    }

    return headers
  }

  /**
   * Make HTTP request with error handling
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit & RequestOptions = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`
    const { headers: customHeaders, timeout, signal, ...fetchOptions } = options

    const headers = {
      ...this.getDefaultHeaders(),
      ...customHeaders,
    }

    const controller = new AbortController()
    const timeoutId = setTimeout(
      () => controller.abort(),
      timeout || this.timeout
    )

    try {
      const response = await fetch(url, {
        ...fetchOptions,
        headers,
        signal: signal || controller.signal,
      })

      clearTimeout(timeoutId)

      const data = await response.json()

      if (!response.ok) {
        return {
          error: {
            code: data.code || "UNKNOWN_ERROR",
            detail: data.detail || "An unknown error occurred.",
            category: data.category || "ServerError",
            status_code: response.status,
            metadata: data.metadata || null,
          },
          status: response.status,
        }
      }

      return {
        data,
        status: response.status,
      }
    } catch (error) {
      clearTimeout(timeoutId)

      if (error instanceof Error) {
        return {
          error: {
            code: "CLIENT_ERROR",
            detail: error.message,
            category: "ClientError",
            status_code: 0,
          },
          status: 0,
        }
      }

      return {
        error: {
          code: "UNKNOWN_ERROR",
          detail: "Unknown error occurred",
          category: "ServerError",
          status_code: 0,
        },
        status: 0,
      }
    }
  }

  /**
   * GET request
   */
  async get<T>(
    endpoint: string,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "GET", ...options })
  }

  /**
   * POST request
   */
  async post<T>(
    endpoint: string,
    data?: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "POST",
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    })
  }

  /**
   * PUT request
   */
  async put<T>(
    endpoint: string,
    data?: any,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, {
      method: "PUT",
      body: data ? JSON.stringify(data) : undefined,
      ...options,
    })
  }

  /**
   * DELETE request
   */
  async delete<T>(
    endpoint: string,
    options?: RequestOptions
  ): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, { method: "DELETE", ...options })
  }

  // Authentication endpoints
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.post<LoginResponse>(
      "/api/v1/auth/login",
      credentials
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    const response = await this.post<RegisterResponse>(
      "/api/v1/auth/register",
      userData
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async logout(): Promise<LogoutResponse> {
    const response = await this.post<LogoutResponse>("/api/v1/auth/logout")
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async changePassword(
    data: PasswordChangeRequest
  ): Promise<PasswordChangeResponse> {
    const response = await this.post<PasswordChangeResponse>(
      "/api/v1/auth/change-password",
      data
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  // User endpoints
  async getCurrentUser(): Promise<UserRead> {
    const response = await this.get<UserRead>("/api/v1/users/me")
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async updateCurrentUser(data: UserUpdate): Promise<UserRead> {
    const response = await this.put<UserRead>("/api/v1/users/me", data)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getUsers(params?: ListQueryParams): Promise<UserPaginatedResponse> {
    const searchParams = new URLSearchParams()
    if (params?.skip) searchParams.set("skip", params.skip.toString())
    if (params?.limit) searchParams.set("limit", params.limit.toString())
    if (params?.search) searchParams.set("search", params.search)
    if (params?.sort_by) searchParams.set("sort_by", params.sort_by)
    if (params?.sort_order) searchParams.set("sort_order", params.sort_order)

    const query = searchParams.toString()
    const endpoint = `/api/v1/users${query ? `?${query}` : ""}`

    const response = await this.get<UserPaginatedResponse>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getUsersSummary(limit?: number): Promise<UserSummary[]> {
    const endpoint = `/api/v1/users/summary${limit ? `?limit=${limit}` : ""}`
    const response = await this.get<UserSummary[]>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getUser(id: number): Promise<UserRead> {
    const response = await this.get<UserRead>(`/api/v1/users/${id}`)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async createUser(data: UserCreate): Promise<UserRead> {
    const response = await this.post<UserRead>("/api/v1/users", data)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async updateUser(id: number, data: UserUpdate): Promise<UserRead> {
    const response = await this.put<UserRead>(`/api/v1/users/${id}`, data)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async deleteUser(id: number): Promise<void> {
    const response = await this.delete(`/api/v1/users/${id}`)
    if (response.error) {
      throw new Error(response.error.detail)
    }
  }

  // Task endpoints
  async getTasks(
    projectId: number,
    filters?: TaskFilters,
    sort?: TaskSortOptions,
    page?: number,
    size?: number
  ): Promise<TaskRead[]> {
    const searchParams = new URLSearchParams()
    if (page) searchParams.set("page", page.toString())
    if (size) searchParams.set("size", size.toString())
    if (sort?.field) searchParams.set("sort_by", sort.field)
    if (sort?.order) searchParams.set("sort_order", sort.order)
    if (filters?.status?.length) {
      filters.status.forEach((status) => searchParams.append("status", status))
    }
    if (filters?.priority?.length) {
      filters.priority.forEach((priority) =>
        searchParams.append("priority", priority)
      )
    }
    if (filters?.assigned_user_id) {
      searchParams.set("assigned_user_id", filters.assigned_user_id.toString())
    }
    if (filters?.due_date_from) {
      searchParams.set("due_date_from", filters.due_date_from.toISOString())
    }
    if (filters?.due_date_to) {
      searchParams.set("due_date_to", filters.due_date_to.toISOString())
    }
    if (filters?.search) {
      searchParams.set("search", filters.search)
    }

    const query = searchParams.toString()
    const endpoint = `/api/v1/projects/${projectId}/tasks${query ? `?${query}` : ""}`

    const response = await this.get<TaskRead[]>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getTask(projectId: number, taskId: string): Promise<TaskRead> {
    const response = await this.get<TaskRead>(
      `/api/v1/projects/${projectId}/tasks/${taskId}`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async createTask(projectId: number, data: TaskCreate): Promise<TaskRead> {
    const response = await this.post<TaskRead>(
      `/api/v1/projects/${projectId}/tasks/`,
      data
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async updateTask(
    projectId: number,
    taskId: string,
    data: TaskUpdate
  ): Promise<TaskRead> {
    const response = await this.put<TaskRead>(
      `/api/v1/projects/${projectId}/tasks/${taskId}`,
      data
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async deleteTask(projectId: number, taskId: string): Promise<void> {
    const response = await this.delete(
      `/api/v1/projects/${projectId}/tasks/${taskId}`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
  }

  async assignUsersToTask(
    projectId: number,
    taskId: string,
    data: TaskAssignmentRequest
  ): Promise<TaskRead> {
    const response = await this.post<TaskRead>(
      `/api/v1/projects/${projectId}/tasks/${taskId}/assignments`,
      data
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async unassignUserFromTask(
    projectId: number,
    taskId: string,
    userId: number
  ): Promise<TaskRead> {
    const response = await this.delete<TaskRead>(
      `/api/v1/projects/${projectId}/tasks/${taskId}/assignments/${userId}`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getTaskStatistics(projectId: number): Promise<TaskStatistics> {
    const response = await this.get<TaskStatistics>(
      `/api/v1/projects/${projectId}/tasks/statistics`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async searchTasks(data: TaskSearchRequest): Promise<TaskRead[]> {
    const response = await this.post<TaskRead[]>("/api/v1/tasks/search", data)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getUserTasks(
    userId: number,
    includeCompleted = true
  ): Promise<TaskRead[]> {
    const searchParams = new URLSearchParams()
    searchParams.set("include_completed", includeCompleted.toString())

    const query = searchParams.toString()
    const endpoint = `/api/v1/users/${userId}/tasks${query ? `?${query}` : ""}`

    const response = await this.get<TaskRead[]>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getOverdueTasks(projectId?: number): Promise<TaskRead[]> {
    const searchParams = new URLSearchParams()
    if (projectId) searchParams.set("project_id", projectId.toString())

    const query = searchParams.toString()
    const endpoint = `/api/v1/tasks/overdue${query ? `?${query}` : ""}`

    const response = await this.get<TaskRead[]>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  // Health check endpoint
  async healthCheck(): Promise<HealthCheck> {
    const response = await this.get<HealthCheck>("/api/v1/health")
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }
}

// Create singleton instance
export const apiClient = new ApiClient({
  baseURL: process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000",
})

export default apiClient
