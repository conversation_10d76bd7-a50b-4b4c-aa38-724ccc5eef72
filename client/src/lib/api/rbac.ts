/**
 * RBAC API Client for Ultimate Electrical Designer
 * Provides type-safe HTTP client methods for role-based access control operations
 */

import type {
  ListQueryParams,
  PaginatedResponse,
  UserRole,
  UserRoleAssignment,
  UserRoleAssignmentCreate,
  UserRoleAssignmentUpdate,
  UserRoleCreate,
  UserRoleHierarchy,
  UserRolePermissions,
  UserRolesSummary,
  UserRoleUpdate,
} from "@/types/api"

import { apiClient } from "./client"

export class RbacApiClient {
  // User Role Management
  async getRoles(
    params?: ListQueryParams
  ): Promise<PaginatedResponse<UserRoleResponse>> {
    const searchParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/roles${searchParams.toString() ? `?${searchParams.toString()}` : ""}`
    const response = await apiClient.get<PaginatedResponse<UserRoleResponse>>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getRole(id: number): Promise<UserRole> {
    const response = await apiClient.get<UserRole>(`/api/v1/roles/${id}`)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async createRole(data: UserRoleCreate): Promise<UserRole> {
    const response = await apiClient.post<UserRole>("/api/v1/roles", data)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async updateRole(id: number, data: UserRoleUpdate): Promise<UserRole> {
    const response = await apiClient.put<UserRole>(`/api/v1/roles/${id}`, data)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async deleteRole(id: number): Promise<void> {
    const response = await apiClient.delete<void>(`/api/v1/roles/${id}`)
    if (response.error) {
      throw new Error(response.error.detail)
    }
  }

  async getRoleHierarchy(): Promise<UserRoleHierarchy[]> {
    const response = await apiClient.get<UserRoleHierarchy[]>(
      "/api/v1/roles/hierarchy"
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getRolePermissions(id: number): Promise<UserRolePermissions> {
    const response = await apiClient.get<UserRolePermissions>(
      `/api/v1/roles/${id}/permissions`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  // User Role Assignment Management
  async getRoleAssignments(
    params?: ListQueryParams
  ): Promise<PaginatedResponse<UserRoleAssignment>> {
    const searchParams = new URLSearchParams()
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined) {
          searchParams.append(key, value.toString())
        }
      })
    }

    const endpoint = `/api/v1/role-assignments${searchParams.toString() ? `?${searchParams.toString()}` : ""}`
    const response =
      await apiClient.get<PaginatedResponse<UserRoleAssignment>>(endpoint)
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getRoleAssignment(id: number): Promise<UserRoleAssignment> {
    const response = await apiClient.get<UserRoleAssignment>(
      `/api/v1/role-assignments/${id}`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async createRoleAssignment(
    data: UserRoleAssignmentCreate
  ): Promise<UserRoleAssignment> {
    const response = await apiClient.post<UserRoleAssignment>(
      "/api/v1/role-assignments",
      data
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async updateRoleAssignment(
    id: number,
    data: UserRoleAssignmentUpdate
  ): Promise<UserRoleAssignment> {
    const response = await apiClient.put<UserRoleAssignment>(
      `/api/v1/role-assignments/${id}`,
      data
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async deleteRoleAssignment(id: number): Promise<void> {
    const response = await apiClient.delete<void>(
      `/api/v1/role-assignments/${id}`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
  }

  // User-specific Role Operations
  async getUserRoles(userId: number): Promise<UserRoleAssignment[]> {
    const response = await apiClient.get<UserRoleAssignment[]>(
      `/api/v1/users/${userId}/roles`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async getUserRolesSummary(userId: number): Promise<UserRolesSummary> {
    const response = await apiClient.get<UserRolesSummary>(
      `/api/v1/users/${userId}/roles/summary`
    )
    if (response.error) {
      throw new Error(response.error.detail)
    }
    return response.data!
  }

  async assignRoleToUser(
    userId: number,
    roleId: number,
    data: Partial<UserRoleAssignmentCreate>
  ): Promise<UserRoleAssignment> {
    const assignmentData: UserRoleAssignmentCreate = {
      name: `${data.name || "Role Assignment"}_${Date.now()}`,
      user_id: userId,
      role_id: roleId,
      ...data,
    }

    return this.createRoleAssignment(assignmentData)
  }

  async removeRoleFromUser(userId: number, roleId: number): Promise<void> {
    // Find the assignment first
    const assignments = await this.getUserRoles(userId)
    const assignment = assignments.find(
      (a) => a.role_id === roleId && a.is_active
    )

    if (!assignment) {
      throw new Error("Role assignment not found")
    }

    await this.deleteRoleAssignment(assignment.id)
  }

  // Batch Operations
  async assignMultipleRoles(
    userId: number,
    roleIds: number[],
    context?: string
  ): Promise<UserRoleAssignment[]> {
    const assignments = await Promise.all(
      roleIds.map((roleId) =>
        this.assignRoleToUser(userId, roleId, {
          name: `Batch Assignment ${Date.now()}`,
          assignment_context: context,
        })
      )
    )
    return assignments
  }

  async removeMultipleRoles(userId: number, roleIds: number[]): Promise<void> {
    await Promise.all(
      roleIds.map((roleId) => this.removeRoleFromUser(userId, roleId))
    )
  }

  // Utility Methods
  async checkUserHasRole(userId: number, roleName: string): Promise<boolean> {
    try {
      const summary = await this.getUserRolesSummary(userId)
      return summary.active_roles.some((role) => role.name === roleName)
    } catch (error) {
      console.error("Error checking user role:", error)
      return false
    }
  }

  async checkUserHasPermission(
    userId: number,
    permission: string
  ): Promise<boolean> {
    try {
      const summary = await this.getUserRolesSummary(userId)
      return summary.effective_permissions.includes(permission)
    } catch (error) {
      console.error("Error checking user permission:", error)
      return false
    }
  }

  async getUserEffectivePermissions(userId: number): Promise<string[]> {
    try {
      const summary = await this.getUserRolesSummary(userId)
      return summary.effective_permissions
    } catch (error) {
      console.error("Error getting user permissions:", error)
      return []
    }
  }
}

// Export singleton instance
export const rbacApiClient = new RbacApiClient()
