import {
  <PERSON><PERSON><PERSON>_Mon<PERSON> as <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON> as <PERSON>ont<PERSON>ans,
  Inter,
} from "next/font/google"

import { cn } from "@/lib/utils"

const fontSans = FontSans({
  subsets: ["latin"],
  variable: "--font-sans",
})

const fontMono = FontMono({
  subsets: ["latin"],
  variable: "--font-mono",
  weight: ["400"],
})

const fontInter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
})

export const fontVariables = cn(
  fontSans.variable,
  fontMono.variable,
  fontInter.variable
)
