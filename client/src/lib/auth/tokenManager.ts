/**
 * JWT Token Management Utilities
 */

import { useAuthStore } from "@/stores/authStore"

import { apiClient } from "@/lib/api/client"

export class TokenManager {
  private static readonly ACCESS_TOKEN_KEY = "access_token"
  private static readonly REFRESH_TOKEN_KEY = "refresh_token"

  /**
   * Set access token in localStorage and API client
   */
  static setAccessToken(token: string): void {
    if (typeof window !== "undefined") {
      localStorage.setItem(this.ACCESS_TOKEN_KEY, token)
    }
    apiClient.setAuthToken(token)
  }

  /**
   * Get access token from localStorage
   */
  static getAccessToken(): string | null {
    if (typeof window !== "undefined") {
      return localStorage.getItem(this.ACCESS_TOKEN_KEY)
    }
    return null
  }

  /**
   * Set refresh token in localStorage
   */
  static setRefreshToken(token: string): void {
    if (typeof window !== "undefined") {
      localStorage.setItem(this.REFRESH_TOKEN_KEY, token)
    }
  }

  /**
   * Get refresh token from localStorage
   */
  static getRefreshToken(): string | null {
    if (typeof window !== "undefined") {
      return localStorage.getItem(this.REFRESH_TOKEN_KEY)
    }
    return null
  }

  /**
   * Clear all tokens from localStorage and API client
   */
  static clearTokens(): void {
    if (typeof window !== "undefined") {
      localStorage.removeItem(this.ACCESS_TOKEN_KEY)
      localStorage.removeItem(this.REFRESH_TOKEN_KEY)
    }
    apiClient.clearAuthToken()
  }

  /**
   * Check if access token exists
   */
  static hasAccessToken(): boolean {
    return !!this.getAccessToken()
  }

  /**
   * Check if refresh token exists
   */
  static hasRefreshToken(): boolean {
    return !!this.getRefreshToken()
  }

  /**
   * Decode JWT token payload (without verification)
   * Note: This is for client-side use only, server should always verify
   */
  static decodeToken(token: string): any {
    try {
      const base64Url = token.split(".")[1]
      const base64 = base64Url.replace(/-/g, "+").replace(/_/g, "/")
      const jsonPayload = decodeURIComponent(
        atob(base64)
          .split("")
          .map((c) => "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2))
          .join("")
      )
      return JSON.parse(jsonPayload)
    } catch (error) {
      console.error("Error decoding token:", error)
      return null
    }
  }

  /**
   * Check if token is expired (client-side check only)
   */
  static isTokenExpired(token: string): boolean {
    try {
      const payload = this.decodeToken(token)
      if (!payload || !payload.exp) {
        return true
      }

      const currentTime = Math.floor(Date.now() / 1000)
      return payload.exp < currentTime
    } catch (error) {
      console.error("Error checking token expiration:", error)
      return true
    }
  }

  /**
   * Get token expiration time
   */
  static getTokenExpiration(token: string): Date | null {
    try {
      const payload = this.decodeToken(token)
      if (!payload || !payload.exp) {
        return null
      }
      return new Date(payload.exp * 1000)
    } catch (error) {
      console.error("Error getting token expiration:", error)
      return null
    }
  }

  /**
   * Initialize tokens on app startup
   */
  static initializeTokens(): void {
    const accessToken = this.getAccessToken()
    if (accessToken && !this.isTokenExpired(accessToken)) {
      // Set token in API client
      apiClient.setAuthToken(accessToken)
    } else {
      // Clear expired or invalid tokens
      this.clearTokens()
      useAuthStore.getState().clearAuth()
    }
  }

  /**
   * Auto-refresh token before expiration
   * Note: This would require a refresh endpoint on the backend
   */
  static async refreshTokenIfNeeded(): Promise<boolean> {
    const accessToken = this.getAccessToken()
    const refreshToken = this.getRefreshToken()

    if (!accessToken || !refreshToken) {
      return false
    }

    // Check if token expires in the next 5 minutes
    const payload = this.decodeToken(accessToken)
    if (!payload || !payload.exp) {
      return false
    }

    const currentTime = Math.floor(Date.now() / 1000)
    const expirationTime = payload.exp
    const timeUntilExpiration = expirationTime - currentTime
    const fiveMinutes = 5 * 60

    if (timeUntilExpiration > fiveMinutes) {
      return true // Token is still valid
    }

    // TODO: Implement refresh token logic when backend supports it
    // For now, just clear tokens if they're about to expire
    if (timeUntilExpiration <= 0) {
      this.clearTokens()
      useAuthStore.getState().clearAuth()
      return false
    }

    return true
  }
}
