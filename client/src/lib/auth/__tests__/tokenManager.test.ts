import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import { TokenManager } from "../tokenManager"

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
}

Object.defineProperty(window, "localStorage", {
  value: mockLocalStorage,
  writable: true,
})

// Mock apiClient
vi.mock("@/lib/api/client", () => ({
  apiClient: {
    setAuthToken: vi.fn(),
    clearAuthToken: vi.fn(),
  },
}))

describe("Token Manager", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    mockLocalStorage.getItem.mockReturnValue(null)
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe("setAccessToken", () => {
    it("should store token in localStorage", () => {
      const token = "test-token"
      TokenManager.setAccessToken(token)

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith(
        "access_token",
        token
      )
    })
  })

  describe("getAccessToken", () => {
    it("should retrieve token from localStorage", () => {
      const token = "stored-token"
      mockLocalStorage.getItem.mockReturnValue(token)

      const result = TokenManager.getAccessToken()

      expect(mockLocalStorage.getItem).toHaveBeenCalledWith("access_token")
      expect(result).toBe(token)
    })

    it("should return null when no token is stored", () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const result = TokenManager.getAccessToken()

      expect(result).toBeNull()
    })
  })

  describe("clearTokens", () => {
    it("should remove tokens from localStorage", () => {
      TokenManager.clearTokens()

      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith("access_token")
      expect(mockLocalStorage.removeItem).toHaveBeenCalledWith("refresh_token")
    })
  })

  describe("hasAccessToken", () => {
    it("should return true when token exists", () => {
      mockLocalStorage.getItem.mockReturnValue("test-token")

      const result = TokenManager.hasAccessToken()

      expect(result).toBe(true)
    })

    it("should return false when token does not exist", () => {
      mockLocalStorage.getItem.mockReturnValue(null)

      const result = TokenManager.hasAccessToken()

      expect(result).toBe(false)
    })
  })

  describe("isTokenExpired", () => {
    it("should return false for valid token", () => {
      // Create a token that expires in the future (1 hour from now)
      const futureTime = Math.floor(Date.now() / 1000) + 3600
      const payload = { exp: futureTime }
      const token = `header.${btoa(JSON.stringify(payload))}.signature`

      const result = TokenManager.isTokenExpired(token)

      expect(result).toBe(false)
    })

    it("should return true for expired token", () => {
      // Create a token that expired 1 hour ago
      const pastTime = Math.floor(Date.now() / 1000) - 3600
      const payload = { exp: pastTime }
      const token = `header.${btoa(JSON.stringify(payload))}.signature`

      const result = TokenManager.isTokenExpired(token)

      expect(result).toBe(true)
    })

    it("should return true for malformed token", () => {
      const result = TokenManager.isTokenExpired("invalid-token")

      expect(result).toBe(true)
    })

    it("should return true for token without exp claim", () => {
      const payload = { sub: "user123" } // No exp claim
      const token = `header.${btoa(JSON.stringify(payload))}.signature`

      const result = TokenManager.isTokenExpired(token)

      expect(result).toBe(true)
    })
  })

  describe("decodeToken", () => {
    it("should decode and return token payload", () => {
      const payload = { sub: "user123", exp: 1234567890 }
      const token = `header.${btoa(JSON.stringify(payload))}.signature`

      const result = TokenManager.decodeToken(token)

      expect(result).toEqual(payload)
    })

    it("should return null for invalid token", () => {
      const result = TokenManager.decodeToken("invalid-token")

      expect(result).toBeNull()
    })
  })

  describe("getTokenExpiration", () => {
    it("should return expiration date for valid token", () => {
      const expTime = 1234567890
      const payload = { sub: "user123", exp: expTime }
      const token = `header.${btoa(JSON.stringify(payload))}.signature`

      const result = TokenManager.getTokenExpiration(token)

      expect(result).toEqual(new Date(expTime * 1000))
    })

    it("should return null for invalid token", () => {
      const result = TokenManager.getTokenExpiration("invalid-token")

      expect(result).toBeNull()
    })

    it("should return null for token without exp claim", () => {
      const payload = { sub: "user123" }
      const token = `header.${btoa(JSON.stringify(payload))}.signature`

      const result = TokenManager.getTokenExpiration(token)

      expect(result).toBeNull()
    })
  })
})
