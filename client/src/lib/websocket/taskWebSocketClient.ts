"use client"

/**
 * Task WebSocket Client
 * 
 * Manages WebSocket connections for real-time task updates.
 * Integrates with React Query to invalidate and update cached data.
 */
import type { QueryClient } from "@tanstack/react-query"

import type { TaskRead } from "../../types/api"
import { QueryKeys } from "../../types/api"

// WebSocket message types
export type TaskWebSocketEventType = 
  | "TASK_CREATED"
  | "TASK_UPDATED" 
  | "TASK_DELETED"
  | "TASK_ASSIGNED"
  | "TASK_UNASSIGNED"
  | "TASK_STATUS_CHANGED"
  | "TASK_PRIORITY_CHANGED"

// WebSocket message structure
export interface TaskWebSocketMessage {
  type: TaskWebSocketEventType
  project_id: number
  task_id: string
  task?: TaskRead
  user_id?: number
  timestamp: string
  metadata?: Record<string, any>
}

// Connection states
export enum WebSocketConnectionState {
  CONNECTING = "connecting",
  CONNECTED = "connected",
  DISCONNECTED = "disconnected",
  ERROR = "error",
  RECONNECTING = "reconnecting",
}

// Configuration options
export interface TaskWebSocketConfig {
  baseUrl?: string
  reconnectAttempts?: number
  reconnectDelay?: number
  heartbeatInterval?: number
  enableLogging?: boolean
}

// Event listeners
export type TaskWebSocketEventListener = (message: TaskWebSocketMessage) => void
export type ConnectionStateListener = (state: WebSocketConnectionState) => void

/**
 * TaskWebSocketClient - Manages WebSocket connections for task updates
 */
export class TaskWebSocketClient {
  private ws: WebSocket | null = null
  private queryClient: QueryClient
  private config: Required<TaskWebSocketConfig>
  private projectId: number | null = null
  private connectionState: WebSocketConnectionState = WebSocketConnectionState.DISCONNECTED
  private reconnectAttempts = 0
  private heartbeatTimer: NodeJS.Timeout | null = null
  private reconnectTimer: NodeJS.Timeout | null = null

  // Event listeners
  private eventListeners = new Map<TaskWebSocketEventType, Set<TaskWebSocketEventListener>>()
  private connectionListeners = new Set<ConnectionStateListener>()

  constructor(queryClient: QueryClient, config: TaskWebSocketConfig = {}) {
    this.queryClient = queryClient
    this.config = {
      baseUrl: config.baseUrl || (typeof window !== "undefined" ? 
        `${window.location.protocol === "https:" ? "wss:" : "ws:"}//${window.location.host}` : 
        "ws://localhost:8000"),
      reconnectAttempts: config.reconnectAttempts ?? 5,
      reconnectDelay: config.reconnectDelay ?? 3000,
      heartbeatInterval: config.heartbeatInterval ?? 30000,
      enableLogging: config.enableLogging ?? false,
    }

    this.log("TaskWebSocketClient initialized", this.config)
  }

  /**
   * Connect to WebSocket for a specific project
   */
  public connect(projectId: number): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN && this.projectId === projectId) {
      this.log("Already connected to project", projectId)
      return
    }

    this.disconnect()
    this.projectId = projectId
    this.setConnectionState(WebSocketConnectionState.CONNECTING)

    const wsUrl = `${this.config.baseUrl}/ws/tasks/${projectId}`
    this.log("Connecting to WebSocket", wsUrl)

    try {
      this.ws = new WebSocket(wsUrl)
      this.setupEventHandlers()
    } catch (error) {
      this.log("Failed to create WebSocket connection", error)
      this.setConnectionState(WebSocketConnectionState.ERROR)
      this.scheduleReconnect()
    }
  }

  /**
   * Disconnect from WebSocket
   */
  public disconnect(): void {
    this.clearTimers()
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }

    this.projectId = null
    this.reconnectAttempts = 0
    this.setConnectionState(WebSocketConnectionState.DISCONNECTED)
    this.log("Disconnected from WebSocket")
  }

  /**
   * Get current connection state
   */
  public getConnectionState(): WebSocketConnectionState {
    return this.connectionState
  }

  /**
   * Check if connected
   */
  public isConnected(): boolean {
    return this.connectionState === WebSocketConnectionState.CONNECTED
  }

  /**
   * Add event listener for specific task events
   */
  public addEventListener(eventType: TaskWebSocketEventType, listener: TaskWebSocketEventListener): void {
    if (!this.eventListeners.has(eventType)) {
      this.eventListeners.set(eventType, new Set())
    }
    this.eventListeners.get(eventType)!.add(listener)
  }

  /**
   * Remove event listener
   */
  public removeEventListener(eventType: TaskWebSocketEventType, listener: TaskWebSocketEventListener): void {
    const listeners = this.eventListeners.get(eventType)
    if (listeners) {
      listeners.delete(listener)
    }
  }

  /**
   * Add connection state listener
   */
  public addConnectionListener(listener: ConnectionStateListener): void {
    this.connectionListeners.add(listener)
  }

  /**
   * Remove connection state listener
   */
  public removeConnectionListener(listener: ConnectionStateListener): void {
    this.connectionListeners.delete(listener)
  }

  /**
   * Setup WebSocket event handlers
   */
  private setupEventHandlers(): void {
    if (!this.ws) return

    this.ws.onopen = () => {
      this.log("WebSocket connected")
      this.setConnectionState(WebSocketConnectionState.CONNECTED)
      this.reconnectAttempts = 0
      this.startHeartbeat()
    }

    this.ws.onmessage = (event) => {
      try {
        const message: TaskWebSocketMessage = JSON.parse(event.data)
        this.log("Received message", message)
        this.handleMessage(message)
      } catch (error) {
        this.log("Failed to parse WebSocket message", error)
      }
    }

    this.ws.onclose = (event) => {
      this.log("WebSocket closed", { code: event.code, reason: event.reason })
      this.clearTimers()
      
      if (event.code !== 1000) { // Not a normal closure
        this.setConnectionState(WebSocketConnectionState.ERROR)
        this.scheduleReconnect()
      } else {
        this.setConnectionState(WebSocketConnectionState.DISCONNECTED)
      }
    }

    this.ws.onerror = (error) => {
      this.log("WebSocket error", error)
      this.setConnectionState(WebSocketConnectionState.ERROR)
    }
  }

  /**
   * Handle incoming WebSocket messages
   */
  private handleMessage(message: TaskWebSocketMessage): void {
    // Emit to custom event listeners
    const listeners = this.eventListeners.get(message.type)
    if (listeners) {
      listeners.forEach(listener => listener(message))
    }

    // Update React Query cache based on message type
    this.updateQueryCache(message)
  }

  /**
   * Update React Query cache based on WebSocket message
   */
  private updateQueryCache(message: TaskWebSocketMessage): void {
    const { type, project_id, task_id, task } = message

    switch (type) {
      case "TASK_CREATED":
        if (task) {
          // Invalidate task lists to include new task
          this.queryClient.invalidateQueries({
            queryKey: QueryKeys.tasks.list(project_id),
          })
          
          // Add task to cache
          this.queryClient.setQueryData(
            QueryKeys.tasks.detail(project_id, task_id),
            task
          )
        }
        break

      case "TASK_UPDATED":
      case "TASK_STATUS_CHANGED":
      case "TASK_PRIORITY_CHANGED":
        if (task) {
          // Update specific task in cache
          this.queryClient.setQueryData(
            QueryKeys.tasks.detail(project_id, task_id),
            task
          )
          
          // Invalidate task lists to reflect changes
          this.queryClient.invalidateQueries({
            queryKey: QueryKeys.tasks.list(project_id),
          })
          
          // Invalidate task statistics
          this.queryClient.invalidateQueries({
            queryKey: QueryKeys.tasks.statistics(project_id),
          })
        }
        break

      case "TASK_DELETED":
        // Remove task from cache
        this.queryClient.removeQueries({
          queryKey: QueryKeys.tasks.detail(project_id, task_id),
        })
        
        // Invalidate task lists
        this.queryClient.invalidateQueries({
          queryKey: QueryKeys.tasks.list(project_id),
        })
        
        // Invalidate task statistics
        this.queryClient.invalidateQueries({
          queryKey: QueryKeys.tasks.statistics(project_id),
        })
        break

      case "TASK_ASSIGNED":
      case "TASK_UNASSIGNED":
        if (task) {
          // Update specific task in cache
          this.queryClient.setQueryData(
            QueryKeys.tasks.detail(project_id, task_id),
            task
          )
          
          // Invalidate task lists
          this.queryClient.invalidateQueries({
            queryKey: QueryKeys.tasks.list(project_id),
          })
          
          // Invalidate user tasks if user_id is provided
          if (message.user_id) {
            this.queryClient.invalidateQueries({
              queryKey: QueryKeys.tasks.userTasks(message.user_id),
            })
          }
        }
        break
    }
  }

  /**
   * Set connection state and notify listeners
   */
  private setConnectionState(state: WebSocketConnectionState): void {
    if (this.connectionState !== state) {
      this.connectionState = state
      this.connectionListeners.forEach(listener => listener(state))
    }
  }

  /**
   * Start heartbeat to keep connection alive
   */
  private startHeartbeat(): void {
    this.heartbeatTimer = setInterval(() => {
      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({ type: "ping" }))
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * Schedule reconnection attempt
   */
  private scheduleReconnect(): void {
    if (this.reconnectAttempts >= this.config.reconnectAttempts) {
      this.log("Max reconnection attempts reached")
      return
    }

    this.reconnectAttempts++
    this.setConnectionState(WebSocketConnectionState.RECONNECTING)
    
    const delay = this.config.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1) // Exponential backoff
    this.log(`Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms`)
    
    this.reconnectTimer = setTimeout(() => {
      if (this.projectId !== null) {
        this.connect(this.projectId)
      }
    }, delay)
  }

  /**
   * Clear all timers
   */
  private clearTimers(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer)
      this.reconnectTimer = null
    }
  }

  /**
   * Log messages if logging is enabled
   */
  private log(message: string, data?: any): void {
    if (this.config.enableLogging) {
      console.log(`[TaskWebSocketClient] ${message}`, data || "")
    }
  }
}
