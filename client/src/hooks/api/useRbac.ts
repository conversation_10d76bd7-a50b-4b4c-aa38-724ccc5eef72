/**
 * React Query hooks for RBAC operations
 * Provides type-safe hooks for role-based access control
 */

import type {
  ListQueryParams,
  UserRoleAssignmentCreate,
  UserRoleAssignmentUpdate,
  UserRoleCreate,
  UserRoleUpdate,
} from "@/types/api"

import { MutationKeys, QueryKeys } from "@/types/api"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import { rbacApiClient } from "@/lib/api/rbac"

// Role Management Hooks
export function useRoles(params?: ListQueryParams) {
  return useQuery({
    queryKey: QueryKeys.rolesList(params),
    queryFn: () => rbacApiClient.getRoles(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useRole(id: number) {
  return useQuery({
    queryKey: QueryKeys.role(id),
    queryFn: () => rbacApiClient.getRole(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useRoleHierarchy() {
  return useQuery({
    queryKey: QueryKeys.roleHierarchy,
    queryFn: () => rbacApiClient.getRoleHierarchy(),
    staleTime: 10 * 60 * 1000, // 10 minutes
  })
}

export function useRolePermissions(id: number) {
  return useQuery({
    queryKey: QueryKeys.rolePermissions(id),
    queryFn: () => rbacApiClient.getRolePermissions(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}

export function useCreateRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createRole,
    mutationFn: (data: UserRoleCreate) => rbacApiClient.createRole(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.roles })
      queryClient.invalidateQueries({ queryKey: QueryKeys.roleHierarchy })
    },
  })
}

export function useUpdateRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.updateRole,
    mutationFn: ({ id, data }: { id: number; data: UserRoleUpdate }) =>
      rbacApiClient.updateRole(id, data),
    onSuccess: (updatedRole) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.roles })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.role(updatedRole.id),
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.roleHierarchy })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.rolePermissions(updatedRole.id),
      })
    },
  })
}

export function useDeleteRole() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.deleteRole,
    mutationFn: (id: number) => rbacApiClient.deleteRole(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.roles })
      queryClient.invalidateQueries({ queryKey: QueryKeys.roleHierarchy })
      queryClient.invalidateQueries({ queryKey: QueryKeys.roleAssignments })
    },
  })
}

// Role Assignment Hooks
export function useRoleAssignments(params?: ListQueryParams) {
  return useQuery({
    queryKey: QueryKeys.roleAssignmentsList(params),
    queryFn: () => rbacApiClient.getRoleAssignments(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useRoleAssignment(id: number) {
  return useQuery({
    queryKey: QueryKeys.roleAssignment(id),
    queryFn: () => rbacApiClient.getRoleAssignment(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useUserRoles(userId: number) {
  return useQuery({
    queryKey: QueryKeys.userRoles(userId),
    queryFn: () => rbacApiClient.getUserRoles(userId),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useUserRolesSummary(userId: number) {
  return useQuery({
    queryKey: QueryKeys.userRolesSummary(userId),
    queryFn: () => rbacApiClient.getUserRolesSummary(userId),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}

export function useCreateRoleAssignment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createRoleAssignment,
    mutationFn: (data: UserRoleAssignmentCreate) =>
      rbacApiClient.createRoleAssignment(data),
    onSuccess: (assignment) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.roleAssignments })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.userRoles(assignment.user_id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.userRolesSummary(assignment.user_id),
      })
    },
  })
}

export function useUpdateRoleAssignment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.updateRoleAssignment,
    mutationFn: ({
      id,
      data,
    }: {
      id: number
      data: UserRoleAssignmentUpdate
    }) => rbacApiClient.updateRoleAssignment(id, data),
    onSuccess: (assignment) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.roleAssignments })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.roleAssignment(assignment.id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.userRoles(assignment.user_id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.userRolesSummary(assignment.user_id),
      })
    },
  })
}

export function useDeleteRoleAssignment() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.deleteRoleAssignment,
    mutationFn: ({ id }: { id: number; userId: number }) =>
      rbacApiClient.deleteRoleAssignment(id),
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.roleAssignments })
      queryClient.invalidateQueries({ queryKey: QueryKeys.userRoles(userId) })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.userRolesSummary(userId),
      })
    },
  })
}

// Utility Hooks
export function useAssignRoleToUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createRoleAssignment,
    mutationFn: ({
      userId,
      roleId,
      data,
    }: {
      userId: number
      roleId: number
      data: Partial<UserRoleAssignmentCreate>
    }) => rbacApiClient.assignRoleToUser(userId, roleId, data),
    onSuccess: (assignment) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.roleAssignments })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.userRoles(assignment.user_id),
      })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.userRolesSummary(assignment.user_id),
      })
    },
  })
}

export function useRemoveRoleFromUser() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.deleteRoleAssignment,
    mutationFn: ({ userId, roleId }: { userId: number; roleId: number }) =>
      rbacApiClient.removeRoleFromUser(userId, roleId),
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.roleAssignments })
      queryClient.invalidateQueries({ queryKey: QueryKeys.userRoles(userId) })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.userRolesSummary(userId),
      })
    },
  })
}

export function useAssignMultipleRoles() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.createRoleAssignment,
    mutationFn: ({
      userId,
      roleIds,
      context,
    }: {
      userId: number
      roleIds: number[]
      context?: string
    }) => rbacApiClient.assignMultipleRoles(userId, roleIds, context),
    onSuccess: (assignments) => {
      if (assignments.length > 0) {
        const userId = assignments[0].user_id
        queryClient.invalidateQueries({ queryKey: QueryKeys.roleAssignments })
        queryClient.invalidateQueries({ queryKey: QueryKeys.userRoles(userId) })
        queryClient.invalidateQueries({
          queryKey: QueryKeys.userRolesSummary(userId),
        })
      }
    },
  })
}

export function useRemoveMultipleRoles() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.deleteRoleAssignment,
    mutationFn: ({ userId, roleIds }: { userId: number; roleIds: number[] }) =>
      rbacApiClient.removeMultipleRoles(userId, roleIds),
    onSuccess: (_, { userId }) => {
      queryClient.invalidateQueries({ queryKey: QueryKeys.roleAssignments })
      queryClient.invalidateQueries({ queryKey: QueryKeys.userRoles(userId) })
      queryClient.invalidateQueries({
        queryKey: QueryKeys.userRolesSummary(userId),
      })
    },
  })
}

// Permission Checking Hooks
export function useUserHasRole(userId: number, roleName: string) {
  return useQuery({
    queryKey: ["user-has-role", userId, roleName],
    queryFn: () => rbacApiClient.checkUserHasRole(userId, roleName),
    enabled: !!userId && !!roleName,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

export function useUserHasPermission(userId: number, permission: string) {
  return useQuery({
    queryKey: ["user-has-permission", userId, permission],
    queryFn: () => rbacApiClient.checkUserHasPermission(userId, permission),
    enabled: !!userId && !!permission,
    staleTime: 1 * 60 * 1000, // 1 minute
  })
}

export function useUserEffectivePermissions(userId: number) {
  return useQuery({
    queryKey: ["user-effective-permissions", userId],
    queryFn: () => rbacApiClient.getUserEffectivePermissions(userId),
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })
}
