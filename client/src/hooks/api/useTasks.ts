"use client"

/**
 * React Query hooks for task management
 *
 * Provides comprehensive hooks for task CRUD operations, assignments, and statistics.
 * All hooks include proper caching, invalidation, and error handling.
 */
import type {
  TaskAssignmentRequest,
  TaskCreate,
  TaskFilters,
  TaskRead,
  TaskSearchRequest,
  TaskSortOptions,
  TaskStatistics,
  TaskUpdate,
} from "../../types/api"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import { apiClient } from "../../lib/api/client"
import { MutationKeys, QueryKeys } from "../../types/api"

/**
 * Hook for getting tasks for a specific project
 */
export function useTasks(
  projectId: number,
  filters?: TaskFilters,
  sort?: TaskSortOptions,
  page?: number,
  size?: number
) {
  return useQuery({
    queryKey: QueryKeys.tasks.list(projectId, { filters, sort, page, size }),
    queryFn: async (): Promise<TaskRead[]> => {
      const response = await apiClient.getTasks(
        projectId,
        filters,
        sort,
        page,
        size
      )
      return response
    },
    enabled: !!projectId,
    staleTime: 30 * 1000, // 30 seconds (tasks change frequently)
    gcTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook for getting a single task
 */
export function useTask(projectId: number, taskId: string) {
  return useQuery({
    queryKey: QueryKeys.tasks.detail(projectId, taskId),
    queryFn: async (): Promise<TaskRead> => {
      const response = await apiClient.getTask(projectId, taskId)
      return response
    },
    enabled: !!projectId && !!taskId,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook for getting task statistics for a project
 */
export function useTaskStatistics(projectId: number) {
  return useQuery({
    queryKey: QueryKeys.tasks.statistics(projectId),
    queryFn: async (): Promise<TaskStatistics> => {
      const response = await apiClient.getTaskStatistics(projectId)
      return response
    },
    enabled: !!projectId,
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for searching tasks
 */
export function useSearchTasks(searchData: TaskSearchRequest) {
  return useQuery({
    queryKey: QueryKeys.tasks.search(
      searchData.project_id || 0,
      searchData.search_term
    ),
    queryFn: async (): Promise<TaskRead[]> => {
      const response = await apiClient.searchTasks(searchData)
      return response
    },
    enabled: !!searchData.search_term && searchData.search_term.length >= 2,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook for getting tasks assigned to a specific user
 */
export function useUserTasks(userId: number, includeCompleted = true) {
  return useQuery({
    queryKey: QueryKeys.tasks.userTasks(userId),
    queryFn: async (): Promise<TaskRead[]> => {
      const response = await apiClient.getUserTasks(userId, includeCompleted)
      return response
    },
    enabled: !!userId,
    staleTime: 30 * 1000, // 30 seconds
    gcTime: 2 * 60 * 1000, // 2 minutes
  })
}

/**
 * Hook for getting overdue tasks
 */
export function useOverdueTasks(projectId?: number) {
  return useQuery({
    queryKey: QueryKeys.tasks.overdue(projectId),
    queryFn: async (): Promise<TaskRead[]> => {
      const response = await apiClient.getOverdueTasks(projectId)
      return response
    },
    staleTime: 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  })
}

/**
 * Hook for creating a new task
 */
export function useCreateTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.tasks.create,
    mutationFn: async ({
      projectId,
      data,
    }: {
      projectId: number
      data: TaskCreate
    }): Promise<TaskRead> => {
      const response = await apiClient.createTask(projectId, data)
      return response
    },
    onSuccess: (data, variables) => {
      // Invalidate tasks queries for the project
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.lists(),
      })

      // Invalidate task statistics
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.statistics(variables.projectId),
      })

      // If user assignments were made, invalidate user tasks
      if (data.assignments?.length) {
        data.assignments.forEach((assignment) => {
          queryClient.invalidateQueries({
            queryKey: QueryKeys.tasks.userTasks(assignment.user_id),
          })
        })
      }
    },
  })
}

/**
 * Hook for updating a task
 */
export function useUpdateTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.tasks.update,
    mutationFn: async ({
      projectId,
      taskId,
      data,
    }: {
      projectId: number
      taskId: string
      data: TaskUpdate
    }): Promise<TaskRead> => {
      const response = await apiClient.updateTask(projectId, taskId, data)
      return response
    },
    onSuccess: (data, variables) => {
      // Update the specific task in cache
      queryClient.setQueryData(
        QueryKeys.tasks.detail(variables.projectId, variables.taskId),
        data
      )

      // Invalidate tasks list for the project
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.list(variables.projectId),
      })

      // Invalidate task statistics
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.statistics(variables.projectId),
      })

      // If task has assignments, invalidate user tasks
      if (data.assignments?.length) {
        data.assignments.forEach((assignment) => {
          queryClient.invalidateQueries({
            queryKey: QueryKeys.tasks.userTasks(assignment.user_id),
          })
        })
      }
    },
  })
}

/**
 * Hook for deleting a task
 */
export function useDeleteTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.tasks.delete,
    mutationFn: async ({
      projectId,
      taskId,
    }: {
      projectId: number
      taskId: string
    }): Promise<void> => {
      await apiClient.deleteTask(projectId, taskId)
    },
    onSuccess: (_, variables) => {
      // Remove the task from cache
      queryClient.removeQueries({
        queryKey: QueryKeys.tasks.detail(variables.projectId, variables.taskId),
      })

      // Invalidate tasks list for the project
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.list(variables.projectId),
      })

      // Invalidate task statistics
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.statistics(variables.projectId),
      })

      // Invalidate user tasks (we don't know which users were assigned)
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.all,
        predicate: (query) => {
          return query.queryKey.includes("user")
        },
      })
    },
  })
}

/**
 * Hook for assigning users to a task
 */
export function useAssignUsersToTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.tasks.assignUsers,
    mutationFn: async ({
      projectId,
      taskId,
      data,
    }: {
      projectId: number
      taskId: string
      data: TaskAssignmentRequest
    }): Promise<TaskRead> => {
      const response = await apiClient.assignUsersToTask(
        projectId,
        taskId,
        data
      )
      return response
    },
    onSuccess: (data, variables) => {
      // Update the specific task in cache
      queryClient.setQueryData(
        QueryKeys.tasks.detail(variables.projectId, variables.taskId),
        data
      )

      // Invalidate tasks list for the project
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.list(variables.projectId),
      })

      // Invalidate user tasks for assigned users
      variables.data.user_ids.forEach((userId) => {
        queryClient.invalidateQueries({
          queryKey: QueryKeys.tasks.userTasks(userId),
        })
      })
    },
  })
}

/**
 * Hook for unassigning a user from a task
 */
export function useUnassignUserFromTask() {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.tasks.unassignUser,
    mutationFn: async ({
      projectId,
      taskId,
      userId,
    }: {
      projectId: number
      taskId: string
      userId: number
    }): Promise<TaskRead> => {
      const response = await apiClient.unassignUserFromTask(
        projectId,
        taskId,
        userId
      )
      return response
    },
    onSuccess: (data, variables) => {
      // Update the specific task in cache
      queryClient.setQueryData(
        QueryKeys.tasks.detail(variables.projectId, variables.taskId),
        data
      )

      // Invalidate tasks list for the project
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.list(variables.projectId),
      })

      // Invalidate user tasks for the unassigned user
      queryClient.invalidateQueries({
        queryKey: QueryKeys.tasks.userTasks(variables.userId),
      })
    },
  })
}
