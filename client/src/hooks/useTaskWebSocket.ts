"use client"

/**
 * Task WebSocket Hook
 * 
 * React hook for managing WebSocket connections for real-time task updates.
 * Provides easy integration with React components and automatic cleanup.
 */
import * as React from "react"

import { useQueryClient } from "@tanstack/react-query"

import type {
  ConnectionStateListener,
  TaskWebSocketClient,
  TaskWebSocketConfig,
  TaskWebSocketEventListener,
  TaskWebSocketEventType,
  TaskWebSocketMessage,
  WebSocketConnectionState,
} from "../lib/websocket/taskWebSocketClient"

import { TaskWebSocketClient as TaskWebSocketClientClass } from "../lib/websocket/taskWebSocketClient"

// Global WebSocket client instance
let globalWebSocketClient: TaskWebSocketClient | null = null

/**
 * Get or create global WebSocket client instance
 */
function getWebSocketClient(queryClient: any, config?: TaskWebSocketConfig): TaskWebSocketClient {
  if (!globalWebSocketClient) {
    globalWebSocketClient = new TaskWebSocketClientClass(queryClient, config)
  }
  return globalWebSocketClient
}

/**
 * Hook for managing task WebSocket connection
 */
export function useTaskWebSocket(
  projectId: number | null,
  config?: TaskWebSocketConfig
) {
  const queryClient = useQueryClient()
  const [connectionState, setConnectionState] = React.useState<WebSocketConnectionState>(
    WebSocketConnectionState.DISCONNECTED
  )
  const [lastMessage, setLastMessage] = React.useState<TaskWebSocketMessage | null>(null)
  const [isEnabled, setIsEnabled] = React.useState(true)

  // Get WebSocket client instance
  const client = React.useMemo(() => {
    return getWebSocketClient(queryClient, config)
  }, [queryClient, config])

  // Connection state listener
  React.useEffect(() => {
    const listener: ConnectionStateListener = (state) => {
      setConnectionState(state)
    }

    client.addConnectionListener(listener)
    setConnectionState(client.getConnectionState())

    return () => {
      client.removeConnectionListener(listener)
    }
  }, [client])

  // Connect/disconnect based on projectId and enabled state
  React.useEffect(() => {
    if (projectId && isEnabled) {
      client.connect(projectId)
    } else {
      client.disconnect()
    }

    return () => {
      // Don't disconnect on unmount as other components might be using the same client
    }
  }, [client, projectId, isEnabled])

  // Generic message listener to track last message
  React.useEffect(() => {
    const messageListener: TaskWebSocketEventListener = (message) => {
      setLastMessage(message)
    }

    // Add listener for all event types
    const eventTypes: TaskWebSocketEventType[] = [
      "TASK_CREATED",
      "TASK_UPDATED",
      "TASK_DELETED",
      "TASK_ASSIGNED",
      "TASK_UNASSIGNED",
      "TASK_STATUS_CHANGED",
      "TASK_PRIORITY_CHANGED",
    ]

    eventTypes.forEach(eventType => {
      client.addEventListener(eventType, messageListener)
    })

    return () => {
      eventTypes.forEach(eventType => {
        client.removeEventListener(eventType, messageListener)
      })
    }
  }, [client])

  // Methods to control connection
  const connect = React.useCallback((newProjectId?: number) => {
    if (newProjectId) {
      client.connect(newProjectId)
    } else if (projectId) {
      client.connect(projectId)
    }
  }, [client, projectId])

  const disconnect = React.useCallback(() => {
    client.disconnect()
  }, [client])

  const enable = React.useCallback(() => {
    setIsEnabled(true)
  }, [])

  const disable = React.useCallback(() => {
    setIsEnabled(false)
  }, [])

  return {
    // Connection state
    connectionState,
    isConnected: connectionState === WebSocketConnectionState.CONNECTED,
    isConnecting: connectionState === WebSocketConnectionState.CONNECTING,
    isReconnecting: connectionState === WebSocketConnectionState.RECONNECTING,
    isDisconnected: connectionState === WebSocketConnectionState.DISCONNECTED,
    hasError: connectionState === WebSocketConnectionState.ERROR,

    // Last received message
    lastMessage,

    // Control methods
    connect,
    disconnect,
    enable,
    disable,
    isEnabled,

    // WebSocket client for advanced usage
    client,
  }
}

/**
 * Hook for listening to specific task WebSocket events
 */
export function useTaskWebSocketEvent(
  eventType: TaskWebSocketEventType,
  listener: TaskWebSocketEventListener,
  projectId?: number | null,
  config?: TaskWebSocketConfig
) {
  const queryClient = useQueryClient()
  const client = React.useMemo(() => {
    return getWebSocketClient(queryClient, config)
  }, [queryClient, config])

  React.useEffect(() => {
    client.addEventListener(eventType, listener)

    return () => {
      client.removeEventListener(eventType, listener)
    }
  }, [client, eventType, listener])

  // Connect if projectId is provided
  React.useEffect(() => {
    if (projectId) {
      client.connect(projectId)
    }
  }, [client, projectId])
}

/**
 * Hook for listening to task creation events
 */
export function useTaskCreated(
  listener: (message: TaskWebSocketMessage) => void,
  projectId?: number | null
) {
  useTaskWebSocketEvent("TASK_CREATED", listener, projectId)
}

/**
 * Hook for listening to task update events
 */
export function useTaskUpdated(
  listener: (message: TaskWebSocketMessage) => void,
  projectId?: number | null
) {
  useTaskWebSocketEvent("TASK_UPDATED", listener, projectId)
}

/**
 * Hook for listening to task deletion events
 */
export function useTaskDeleted(
  listener: (message: TaskWebSocketMessage) => void,
  projectId?: number | null
) {
  useTaskWebSocketEvent("TASK_DELETED", listener, projectId)
}

/**
 * Hook for listening to task assignment events
 */
export function useTaskAssigned(
  listener: (message: TaskWebSocketMessage) => void,
  projectId?: number | null
) {
  useTaskWebSocketEvent("TASK_ASSIGNED", listener, projectId)
}

/**
 * Hook for listening to task unassignment events
 */
export function useTaskUnassigned(
  listener: (message: TaskWebSocketMessage) => void,
  projectId?: number | null
) {
  useTaskWebSocketEvent("TASK_UNASSIGNED", listener, projectId)
}

/**
 * Hook for listening to task status change events
 */
export function useTaskStatusChanged(
  listener: (message: TaskWebSocketMessage) => void,
  projectId?: number | null
) {
  useTaskWebSocketEvent("TASK_STATUS_CHANGED", listener, projectId)
}

/**
 * Hook for listening to task priority change events
 */
export function useTaskPriorityChanged(
  listener: (message: TaskWebSocketMessage) => void,
  projectId?: number | null
) {
  useTaskWebSocketEvent("TASK_PRIORITY_CHANGED", listener, projectId)
}

/**
 * Hook for comprehensive task event listening with callbacks
 */
export function useTaskEvents(
  projectId: number | null,
  callbacks: {
    onTaskCreated?: (message: TaskWebSocketMessage) => void
    onTaskUpdated?: (message: TaskWebSocketMessage) => void
    onTaskDeleted?: (message: TaskWebSocketMessage) => void
    onTaskAssigned?: (message: TaskWebSocketMessage) => void
    onTaskUnassigned?: (message: TaskWebSocketMessage) => void
    onTaskStatusChanged?: (message: TaskWebSocketMessage) => void
    onTaskPriorityChanged?: (message: TaskWebSocketMessage) => void
    onAnyEvent?: (message: TaskWebSocketMessage) => void
  } = {}
) {
  const { client } = useTaskWebSocket(projectId)

  React.useEffect(() => {
    const listeners: Array<[TaskWebSocketEventType, TaskWebSocketEventListener]> = []

    if (callbacks.onTaskCreated) {
      listeners.push(["TASK_CREATED", callbacks.onTaskCreated])
    }
    if (callbacks.onTaskUpdated) {
      listeners.push(["TASK_UPDATED", callbacks.onTaskUpdated])
    }
    if (callbacks.onTaskDeleted) {
      listeners.push(["TASK_DELETED", callbacks.onTaskDeleted])
    }
    if (callbacks.onTaskAssigned) {
      listeners.push(["TASK_ASSIGNED", callbacks.onTaskAssigned])
    }
    if (callbacks.onTaskUnassigned) {
      listeners.push(["TASK_UNASSIGNED", callbacks.onTaskUnassigned])
    }
    if (callbacks.onTaskStatusChanged) {
      listeners.push(["TASK_STATUS_CHANGED", callbacks.onTaskStatusChanged])
    }
    if (callbacks.onTaskPriorityChanged) {
      listeners.push(["TASK_PRIORITY_CHANGED", callbacks.onTaskPriorityChanged])
    }

    // Add generic listener for all events if callback provided
    if (callbacks.onAnyEvent) {
      const allEventTypes: TaskWebSocketEventType[] = [
        "TASK_CREATED",
        "TASK_UPDATED",
        "TASK_DELETED",
        "TASK_ASSIGNED",
        "TASK_UNASSIGNED",
        "TASK_STATUS_CHANGED",
        "TASK_PRIORITY_CHANGED",
      ]
      
      allEventTypes.forEach(eventType => {
        listeners.push([eventType, callbacks.onAnyEvent!])
      })
    }

    // Add all listeners
    listeners.forEach(([eventType, listener]) => {
      client.addEventListener(eventType, listener)
    })

    return () => {
      // Remove all listeners
      listeners.forEach(([eventType, listener]) => {
        client.removeEventListener(eventType, listener)
      })
    }
  }, [client, callbacks])
}

/**
 * Hook for WebSocket connection status with notifications
 */
export function useTaskWebSocketStatus(projectId: number | null) {
  const { connectionState, lastMessage } = useTaskWebSocket(projectId)
  const [connectionHistory, setConnectionHistory] = React.useState<
    Array<{ state: WebSocketConnectionState; timestamp: number }>
  >([])

  // Track connection state changes
  React.useEffect(() => {
    setConnectionHistory(prev => [
      ...prev.slice(-9), // Keep last 10 entries
      { state: connectionState, timestamp: Date.now() }
    ])
  }, [connectionState])

  const isStable = React.useMemo(() => {
    if (connectionHistory.length < 2) return true
    
    // Check if connection has been stable for at least 30 seconds
    const now = Date.now()
    const recentChanges = connectionHistory.filter(
      entry => now - entry.timestamp < 30000
    )
    
    return recentChanges.length <= 1
  }, [connectionHistory])

  return {
    connectionState,
    connectionHistory,
    lastMessage,
    isStable,
    isHealthy: connectionState === WebSocketConnectionState.CONNECTED && isStable,
  }
}
