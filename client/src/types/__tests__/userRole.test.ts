/**
 * Unit tests for UserRole and UserRoleAssignment Zod schemas
 */
import { describe, it, expect } from "vitest"
import {
  UserRoleBaseSchema,
  UserRoleCreateSchema,
  UserRoleUpdateSchema,
  UserRoleResponseSchema,
  UserRoleHierarchySchema,
  UserRolePermissionsSchema,
  UserRoleSummarySchema,
  UserRoleAssignmentBaseSchema,
  UserRoleAssignmentCreateSchema,
  UserRoleAssignmentUpdateSchema,
  UserRoleAssignmentResponseSchema,
} from "../userRole"

describe("User Role Schemas", () => {
  describe("UserRoleBaseSchema", () => {
    it("should validate complete role data", () => {
      const validRole = {
        name: "Project Manager",
        description: "Manages projects and team members",
        is_system_role: false,
        is_active: true,
        permissions: '{"read": true, "write": true, "delete": false}',
        parent_role_id: 1,
        priority: 50,
        notes: "Standard project management role",
      }

      const result = UserRoleBaseSchema.safeParse(validRole)
      expect(result.success).toBe(true)
    })

    it("should validate minimal role data", () => {
      const minimalRole = {
        name: "Basic User",
      }

      const result = UserRoleBaseSchema.safeParse(minimalRole)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.is_system_role).toBe(false)
        expect(result.data.is_active).toBe(true)
        expect(result.data.priority).toBe(0)
      }
    })

    it("should trim and validate name", () => {
      const roleWithSpaces = {
        name: "  Administrator Role  ",
      }

      const result = UserRoleBaseSchema.safeParse(roleWithSpaces)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe("Administrator Role")
      }
    })

    it("should reject empty name", () => {
      const invalidRole = {
        name: "",
      }

      const result = UserRoleBaseSchema.safeParse(invalidRole)
      expect(result.success).toBe(false)
    })

    it("should reject name longer than 100 characters", () => {
      const invalidRole = {
        name: "a".repeat(101), // Too long
      }

      const result = UserRoleBaseSchema.safeParse(invalidRole)
      expect(result.success).toBe(false)
    })

    it("should validate JSON permissions", () => {
      const roleWithValidJson = {
        name: "API User",
        permissions: '{"api_access": true, "rate_limit": 1000}',
      }

      const result = UserRoleBaseSchema.safeParse(roleWithValidJson)
      expect(result.success).toBe(true)
    })

    it("should reject invalid JSON permissions", () => {
      const roleWithInvalidJson = {
        name: "Invalid Role",
        permissions: '{"invalid": json}', // Invalid JSON
      }

      const result = UserRoleBaseSchema.safeParse(roleWithInvalidJson)
      expect(result.success).toBe(false)
    })

    it("should validate priority range", () => {
      const validPriorities = [
        { name: "Low Priority", priority: 0 },
        { name: "High Priority", priority: 100 },
        { name: "Mid Priority", priority: 50 },
      ]

      validPriorities.forEach((role) => {
        const result = UserRoleBaseSchema.safeParse(role)
        expect(result.success).toBe(true)
      })
    })

    it("should reject priority outside valid range", () => {
      const invalidPriorities = [
        { name: "Negative Priority", priority: -1 },
        { name: "Too High Priority", priority: 101 },
      ]

      invalidPriorities.forEach((role) => {
        const result = UserRoleBaseSchema.safeParse(role)
        expect(result.success).toBe(false)
      })
    })
  })

  describe("UserRoleCreateSchema", () => {
    it("should validate role creation data", () => {
      const validCreate = {
        name: "New Role",
        description: "A newly created role",
        permissions: '{"basic": true}',
        priority: 25,
      }

      const result = UserRoleCreateSchema.safeParse(validCreate)
      expect(result.success).toBe(true)
    })

    it("should inherit all base schema validations", () => {
      const invalidCreate = {
        name: "", // Invalid empty name
      }

      const result = UserRoleCreateSchema.safeParse(invalidCreate)
      expect(result.success).toBe(false)
    })
  })

  describe("UserRoleUpdateSchema", () => {
    it("should validate partial role updates", () => {
      const validUpdate = {
        name: "Updated Role Name",
        is_active: false,
        priority: 75,
      }

      const result = UserRoleUpdateSchema.safeParse(validUpdate)
      expect(result.success).toBe(true)
    })

    it("should allow empty update object", () => {
      const result = UserRoleUpdateSchema.safeParse({})
      expect(result.success).toBe(true)
    })

    it("should validate permissions update", () => {
      const permissionsUpdate = {
        permissions: '{"updated": true, "version": 2}',
      }

      const result = UserRoleUpdateSchema.safeParse(permissionsUpdate)
      expect(result.success).toBe(true)
    })

    it("should reject invalid JSON in permissions update", () => {
      const invalidUpdate = {
        permissions: '{"invalid": json}',
      }

      const result = UserRoleUpdateSchema.safeParse(invalidUpdate)
      expect(result.success).toBe(false)
    })
  })

  describe("UserRoleResponseSchema", () => {
    it("should validate complete role response", () => {
      const validResponse = {
        id: 1,
        name: "Administrator",
        description: "Full system access",
        is_system_role: true,
        is_active: true,
        permissions: '{"admin": true}',
        parent_role_id: null,
        priority: 100,
        notes: "System administrator role",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-15T10:30:00Z",
        is_deleted: false,
        deleted_at: null,
        deleted_by_user_id: null,
      }

      const result = UserRoleResponseSchema.safeParse(validResponse)
      expect(result.success).toBe(true)
    })

    it("should validate soft-deleted role", () => {
      const deletedRole = {
        id: 2,
        name: "Deleted Role",
        description: null,
        is_system_role: false,
        is_active: false,
        permissions: null,
        parent_role_id: null,
        priority: 0,
        notes: null,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-15T10:30:00Z",
        is_deleted: true,
        deleted_at: "2024-01-15T10:30:00Z",
        deleted_by_user_id: 123,
      }

      const result = UserRoleResponseSchema.safeParse(deletedRole)
      expect(result.success).toBe(true)
    })
  })

  describe("UserRoleHierarchySchema", () => {
    it("should validate simple hierarchy", () => {
      const simpleHierarchy = {
        id: 1,
        name: "Root Role",
        description: "Top level role",
        priority: 100,
        parent_role_id: null,
        child_roles: [],
      }

      const result = UserRoleHierarchySchema.safeParse(simpleHierarchy)
      expect(result.success).toBe(true)
    })

    it("should validate nested hierarchy", () => {
      const nestedHierarchy = {
        id: 1,
        name: "Admin",
        description: "Administrator role",
        priority: 100,
        parent_role_id: null,
        child_roles: [
          {
            id: 2,
            name: "Manager",
            description: "Management role",
            priority: 75,
            parent_role_id: 1,
            child_roles: [
              {
                id: 3,
                name: "User",
                description: "Basic user role",
                priority: 25,
                parent_role_id: 2,
                child_roles: [],
              },
            ],
          },
        ],
      }

      const result = UserRoleHierarchySchema.safeParse(nestedHierarchy)
      expect(result.success).toBe(true)
    })

    it("should validate hierarchy without description", () => {
      const hierarchyWithoutDesc = {
        id: 1,
        name: "Simple Role",
        priority: 50,
        parent_role_id: null,
        child_roles: [],
      }

      const result = UserRoleHierarchySchema.safeParse(hierarchyWithoutDesc)
      expect(result.success).toBe(true)
    })
  })

  describe("UserRolePermissionsSchema", () => {
    it("should validate complete permissions data", () => {
      const validPermissions = {
        role_id: 1,
        role_name: "Project Manager",
        permissions: ["read", "write", "manage_team"],
        inherited_permissions: ["read"],
        effective_permissions: ["read", "write", "manage_team"],
      }

      const result = UserRolePermissionsSchema.safeParse(validPermissions)
      expect(result.success).toBe(true)
    })

    it("should validate permissions with defaults", () => {
      const minimalPermissions = {
        role_id: 2,
        role_name: "Basic User",
        permissions: ["read"],
      }

      const result = UserRolePermissionsSchema.safeParse(minimalPermissions)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.inherited_permissions).toEqual([])
        expect(result.data.effective_permissions).toEqual([])
      }
    })
  })

  describe("UserRoleSummarySchema", () => {
    it("should validate complete role summary", () => {
      const validSummary = {
        user_id: 123,
        active_roles: [
          {
            id: 1,
            name: "Active Role",
            description: "Currently active",
            is_system_role: false,
            is_active: true,
            permissions: null,
            parent_role_id: null,
            priority: 50,
            notes: null,
            created_at: "2024-01-01T00:00:00Z",
            updated_at: "2024-01-01T00:00:00Z",
            is_deleted: false,
            deleted_at: null,
            deleted_by_user_id: null,
          },
        ],
        expired_roles: [],
        inactive_roles: [],
        effective_permissions: ["read", "write"],
      }

      const result = UserRoleSummarySchema.safeParse(validSummary)
      expect(result.success).toBe(true)
    })
  })
})

describe("User Role Assignment Schemas", () => {
  describe("UserRoleAssignmentBaseSchema", () => {
    it("should validate complete assignment data", () => {
      const validAssignment = {
        user_id: 123,
        role_id: 456,
        assigned_by_user_id: 789,
        expires_at: "2025-12-31T23:59:59Z",
        is_active: true,
        assignment_context: "Project Alpha team assignment",
        notes: "Temporary assignment for project duration",
      }

      const result = UserRoleAssignmentBaseSchema.safeParse(validAssignment)
      expect(result.success).toBe(true)
    })

    it("should validate minimal assignment data", () => {
      const minimalAssignment = {
        user_id: 123,
        role_id: 456,
      }

      const result = UserRoleAssignmentBaseSchema.safeParse(minimalAssignment)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.is_active).toBe(true)
      }
    })

    it("should validate assignment without expiration", () => {
      const permanentAssignment = {
        user_id: 123,
        role_id: 456,
        expires_at: null,
      }

      const result = UserRoleAssignmentBaseSchema.safeParse(permanentAssignment)
      expect(result.success).toBe(true)
    })

    it("should validate future expiration date", () => {
      const futureDate = new Date()
      futureDate.setFullYear(futureDate.getFullYear() + 1)
      
      const assignmentWithFutureExpiry = {
        user_id: 123,
        role_id: 456,
        expires_at: futureDate.toISOString(),
      }

      const result = UserRoleAssignmentBaseSchema.safeParse(assignmentWithFutureExpiry)
      expect(result.success).toBe(true)
    })

    it("should reject past expiration date", () => {
      const pastDate = new Date()
      pastDate.setFullYear(pastDate.getFullYear() - 1)
      
      const invalidAssignment = {
        user_id: 123,
        role_id: 456,
        expires_at: pastDate.toISOString(),
      }

      const result = UserRoleAssignmentBaseSchema.safeParse(invalidAssignment)
      expect(result.success).toBe(false)
    })
  })

  describe("UserRoleAssignmentCreateSchema", () => {
    it("should validate assignment creation data", () => {
      const validCreate = {
        name: "Project Manager Assignment",
        user_id: 123,
        role_id: 456,
        expires_at: "2025-12-31T23:59:59Z",
      }

      const result = UserRoleAssignmentCreateSchema.safeParse(validCreate)
      expect(result.success).toBe(true)
    })

    it("should trim assignment name", () => {
      const createWithSpaces = {
        name: "  Trimmed Assignment Name  ",
        user_id: 123,
        role_id: 456,
      }

      const result = UserRoleAssignmentCreateSchema.safeParse(createWithSpaces)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe("Trimmed Assignment Name")
      }
    })

    it("should reject empty name", () => {
      const invalidCreate = {
        name: "",
        user_id: 123,
        role_id: 456,
      }

      const result = UserRoleAssignmentCreateSchema.safeParse(invalidCreate)
      expect(result.success).toBe(false)
    })

    it("should require name field", () => {
      const incompleteCreate = {
        user_id: 123,
        role_id: 456,
        // Missing name
      }

      const result = UserRoleAssignmentCreateSchema.safeParse(incompleteCreate)
      expect(result.success).toBe(false)
    })
  })

  describe("UserRoleAssignmentUpdateSchema", () => {
    it("should validate partial assignment updates", () => {
      const validUpdate = {
        expires_at: "2025-12-31T23:59:59Z",
        is_active: false,
        notes: "Updated assignment notes",
      }

      const result = UserRoleAssignmentUpdateSchema.safeParse(validUpdate)
      expect(result.success).toBe(true)
    })

    it("should allow empty update object", () => {
      const result = UserRoleAssignmentUpdateSchema.safeParse({})
      expect(result.success).toBe(true)
    })

    it("should validate context update", () => {
      const contextUpdate = {
        assignment_context: "Moved to different project team",
      }

      const result = UserRoleAssignmentUpdateSchema.safeParse(contextUpdate)
      expect(result.success).toBe(true)
    })

    it("should reject past expiration date in update", () => {
      const pastDate = new Date()
      pastDate.setFullYear(pastDate.getFullYear() - 1)
      
      const invalidUpdate = {
        expires_at: pastDate.toISOString(),
      }

      const result = UserRoleAssignmentUpdateSchema.safeParse(invalidUpdate)
      expect(result.success).toBe(false)
    })
  })

  describe("UserRoleAssignmentResponseSchema", () => {
    it("should validate complete assignment response", () => {
      const validResponse = {
        id: 1,
        name: "Manager Assignment",
        user_id: 123,
        role_id: 456,
        assigned_by_user_id: 789,
        assigned_at: "2024-01-01T00:00:00Z",
        expires_at: "2025-12-31T23:59:59Z",
        is_active: true,
        assignment_context: "Project team assignment",
        notes: "Temporary management role",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-15T10:30:00Z",
        is_deleted: false,
        deleted_at: null,
        deleted_by_user_id: null,
        is_expired: false,
      }

      const result = UserRoleAssignmentResponseSchema.safeParse(validResponse)
      expect(result.success).toBe(true)
    })

    it("should validate expired assignment", () => {
      const expiredAssignment = {
        id: 2,
        name: "Expired Assignment",
        user_id: 123,
        role_id: 456,
        assigned_by_user_id: 789,
        assigned_at: "2023-01-01T00:00:00Z",
        expires_at: "2023-12-31T23:59:59Z",
        is_active: false,
        assignment_context: null,
        notes: null,
        created_at: "2023-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        is_deleted: false,
        deleted_at: null,
        deleted_by_user_id: null,
        is_expired: true,
      }

      const result = UserRoleAssignmentResponseSchema.safeParse(expiredAssignment)
      expect(result.success).toBe(true)
    })

    it("should validate soft-deleted assignment", () => {
      const deletedAssignment = {
        id: 3,
        name: "Deleted Assignment",
        user_id: 123,
        role_id: 456,
        assigned_by_user_id: 789,
        assigned_at: "2024-01-01T00:00:00Z",
        expires_at: null,
        is_active: false,
        assignment_context: null,
        notes: null,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-15T10:30:00Z",
        is_deleted: true,
        deleted_at: "2024-01-15T10:30:00Z",
        deleted_by_user_id: 456,
        is_expired: false,
      }

      const result = UserRoleAssignmentResponseSchema.safeParse(deletedAssignment)
      expect(result.success).toBe(true)
    })
  })
})