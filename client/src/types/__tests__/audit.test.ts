/**
 * Unit tests for Audit and Activity Log Zod schemas
 */
import { describe, it, expect } from "vitest"
import {
  activityLogBaseSchema,
  activityLogCreateSchema,
  activityLogUpdateSchema,
  activityLogFilterSchema,
  auditTrailBaseSchema,
  auditTrailCreateSchema,
  auditSummarySchema,
  recordHistorySchema,
  userActivitySummarySchema,
  UserActivityLogSchema,
} from "../audit"

describe("Activity Log Schemas", () => {
  describe("activityLogBaseSchema", () => {
    it("should validate complete activity log data", () => {
      const validLog = {
        action_type: "USER_LOGIN",
        action_description: "User successfully logged into the system",
        user_id: 123,
        session_id: "sess_abc123",
        target_type: "User",
        target_id: 456,
        target_name: "<PERSON> Doe",
        request_method: "POST",
        request_path: "/api/auth/login",
        request_ip: "***********",
        user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
        status: "SUCCESS",
        severity: "Info",
        metadata: {
          browser: "Chrome",
          platform: "Windows",
        },
        execution_time_ms: 250,
        category: "Authentication",
        tags: ["auth", "login", "security"],
        is_security_related: true,
        is_data_change: false,
        is_system_event: false,
        notes: "Successful login from trusted IP",
      }

      const result = activityLogBaseSchema.safeParse(validLog)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.action_type).toBe("USER_LOGIN")
        expect(result.data.tags).toEqual(["auth", "login", "security"])
      }
    })

    it("should validate minimal activity log data", () => {
      const minimalLog = {
        action_type: "data_update",
        action_description: "Updated user profile",
        severity: "Info",
      }

      const result = activityLogBaseSchema.safeParse(minimalLog)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.action_type).toBe("DATA_UPDATE")
        expect(result.data.status).toBe("SUCCESS")
        expect(result.data.is_security_related).toBe(false)
      }
    })

    it("should transform and deduplicate tags", () => {
      const logWithDuplicateTags = {
        action_type: "test_action",
        action_description: "Test description",
        severity: "Info",
        tags: ["auth", "login", "auth", "", "security", "login"],
      }

      const result = activityLogBaseSchema.safeParse(logWithDuplicateTags)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.tags).toEqual(["auth", "login", "security"])
      }
    })

    it("should reject invalid execution time", () => {
      const invalidLog = {
        action_type: "test_action",
        action_description: "Test description",
        severity: "Info",
        execution_time_ms: -100, // Invalid: negative value
      }

      const result = activityLogBaseSchema.safeParse(invalidLog)
      expect(result.success).toBe(false)
    })
  })

  describe("activityLogCreateSchema", () => {
    it("should validate activity log creation data", () => {
      const validCreate = {
        name: "User Login Activity",
        action_type: "user_login",
        action_description: "User logged in successfully",
        severity: "Info",
        user_id: 123,
        is_security_related: true,
      }

      const result = activityLogCreateSchema.safeParse(validCreate)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe("User Login Activity")
        expect(result.data.action_type).toBe("USER_LOGIN")
      }
    })

    it("should trim name field", () => {
      const createWithSpaces = {
        name: "  Trimmed Activity Name  ",
        action_type: "test",
        action_description: "Test description",
        severity: "Info",
      }

      const result = activityLogCreateSchema.safeParse(createWithSpaces)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe("Trimmed Activity Name")
      }
    })

    it("should reject empty name", () => {
      const invalidCreate = {
        name: "",
        action_type: "test",
        action_description: "Test description",
        severity: "Info",
      }

      const result = activityLogCreateSchema.safeParse(invalidCreate)
      expect(result.success).toBe(false)
    })
  })

  describe("activityLogUpdateSchema", () => {
    it("should validate partial updates", () => {
      const validUpdate = {
        status: "FAILED",
        error_message: "Authentication failed",
        execution_time_ms: 150,
        notes: "Failed login attempt",
      }

      const result = activityLogUpdateSchema.safeParse(validUpdate)
      expect(result.success).toBe(true)
    })

    it("should allow empty update", () => {
      const result = activityLogUpdateSchema.safeParse({})
      expect(result.success).toBe(true)
    })
  })

  describe("activityLogFilterSchema", () => {
    it("should validate complete filter criteria", () => {
      const validFilter = {
        user_id: 123,
        action_types: ["USER_LOGIN", "USER_LOGOUT"],
        categories: ["Authentication", "Authorization"],
        severities: ["High", "Critical"],
        start_date: "2024-01-01T00:00:00Z",
        end_date: "2024-01-31T23:59:59Z",
        is_security_related: true,
        status: "SUCCESS",
        target_type: "User",
        request_ip: "***********",
      }

      const result = activityLogFilterSchema.safeParse(validFilter)
      expect(result.success).toBe(true)
    })

    it("should validate empty filter", () => {
      const result = activityLogFilterSchema.safeParse({})
      expect(result.success).toBe(true)
    })
  })
})

describe("Audit Trail Schemas", () => {
  describe("auditTrailBaseSchema", () => {
    it("should validate complete audit trail data", () => {
      const validTrail = {
        table_name: "users",
        record_id: 123,
        operation: "Update",
        user_id: 456,
        activity_log_id: 789,
        field_name: "email",
        old_value: "<EMAIL>",
        new_value: "<EMAIL>",
        change_reason: "User requested email change",
        change_context: {
          ip_address: "***********",
          user_agent: "Chrome/96.0",
        },
        is_sensitive: true,
        is_system_change: false,
        notes: "Email change verified",
      }

      const result = auditTrailBaseSchema.safeParse(validTrail)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.table_name).toBe("users")
        expect(result.data.operation).toBe("Update")
      }
    })

    it("should validate minimal audit trail data", () => {
      const minimalTrail = {
        table_name: "projects",
        record_id: 456,
        operation: "Critical",
      }

      const result = auditTrailBaseSchema.safeParse(minimalTrail)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.table_name).toBe("projects")
        expect(result.data.operation).toBe("Critical")
        expect(result.data.is_sensitive).toBe(false)
        expect(result.data.is_system_change).toBe(false)
      }
    })

    it("should trim table name", () => {
      const trailWithSpaces = {
        table_name: "  user_roles  ",
        record_id: 123,
        operation: "Delete",
      }

      const result = auditTrailBaseSchema.safeParse(trailWithSpaces)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.table_name).toBe("user_roles")
      }
    })
  })

  describe("auditTrailCreateSchema", () => {
    it("should validate audit trail creation", () => {
      const validCreate = {
        name: "User Email Update",
        table_name: "users",
        record_id: 123,
        operation: "Update",
        field_name: "email",
        old_value: "<EMAIL>",
        new_value: "<EMAIL>",
      }

      const result = auditTrailCreateSchema.safeParse(validCreate)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe("User Email Update")
        expect(result.data.operation).toBe("Update")
      }
    })

    it("should trim name field", () => {
      const createWithSpaces = {
        name: "  Trimmed Audit Name  ",
        table_name: "components",
        record_id: 456,
        operation: "Critical",
      }

      const result = auditTrailCreateSchema.safeParse(createWithSpaces)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe("Trimmed Audit Name")
      }
    })
  })
})

describe("Summary and History Schemas", () => {
  describe("auditSummarySchema", () => {
    it("should validate audit summary data", () => {
      const validSummary = {
        total_activities: 1500,
        total_data_changes: 850,
        security_events: 45,
        system_events: 200,
        user_actions: 1255,
        failed_operations: 23,
        top_action_types: [
          { action_type: "USER_LOGIN", count: 450 },
          { action_type: "DATA_UPDATE", count: 380 },
        ],
        top_users: [
          { user_id: 123, user_name: "John Doe", activity_count: 95 },
        ],
        activity_timeline: [
          { date: "2024-01-15", activity_count: 78 },
        ],
      }

      const result = auditSummarySchema.safeParse(validSummary)
      expect(result.success).toBe(true)
    })
  })

  describe("recordHistorySchema", () => {
    it("should validate record history data", () => {
      const validHistory = {
        table_name: "users",
        record_id: 123,
        total_changes: 5,
        first_change: "2024-01-01T10:00:00Z",
        last_change: "2024-01-15T14:30:00Z",
        changes: [
          {
            id: 1,
            name: "Email Update",
            activity_log_id: 456,
            user_id: 789,
            changed_at: "2024-01-15T14:30:00Z",
            table_name: "users",
            record_id: 123,
            operation: "Update",
            field_name: "email",
            old_value: "<EMAIL>",
            new_value: "<EMAIL>",
            change_reason: "User request",
            change_context: {},
            is_sensitive: true,
            is_system_change: false,
            notes: null,
            created_at: "2024-01-15T14:30:00Z",
            updated_at: "2024-01-15T14:30:00Z",
          },
        ],
        change_summary: { email: 2, name: 1, password: 1 },
      }

      const result = recordHistorySchema.safeParse(validHistory)
      expect(result.success).toBe(true)
    })
  })

  describe("userActivitySummarySchema", () => {
    it("should validate user activity summary", () => {
      const validSummary = {
        user_id: 123,
        total_activities: 150,
        recent_activities: [
          {
            id: 1,
            name: "Recent Login",
            user_id: 123,
            session_id: "sess_123",
            action_type: "USER_LOGIN",
            action_description: "User logged in",
            target_type: null,
            target_id: null,
            target_name: null,
            request_method: "POST",
            request_path: "/api/auth/login",
            request_ip: "***********",
            user_agent: "Chrome",
            status: "SUCCESS",
            severity: "Info",
            metadata: {},
            error_message: null,
            execution_time_ms: 200,
            category: "Auth",
            tags: ["login"],
            is_security_related: true,
            is_data_change: false,
            is_system_event: false,
            notes: null,
            created_at: "2024-01-15T10:00:00Z",
            updated_at: "2024-01-15T10:00:00Z",
          },
        ],
        top_actions: [
          { action_type: "USER_LOGIN", count: 50 },
        ],
        security_events: 12,
        data_changes: 25,
        activity_timeline: [
          { date: "2024-01-15", count: 8 },
        ],
      }

      const result = userActivitySummarySchema.safeParse(validSummary)
      expect(result.success).toBe(true)
    })
  })

  describe("UserActivityLogSchema", () => {
    it("should validate user activity log", () => {
      const validLog = {
        id: 1,
        user_id: 123,
        action: "login",
        resource: "authentication",
        ip_address: "***********",
        user_agent: "Chrome/96.0",
        timestamp: "2024-01-15T10:00:00Z",
        success: true,
        details: {
          method: "password",
          duration_ms: 150,
        },
      }

      const result = UserActivityLogSchema.safeParse(validLog)
      expect(result.success).toBe(true)
    })

    it("should validate log without optional fields", () => {
      const minimalLog = {
        id: 2,
        user_id: 456,
        action: "logout",
        timestamp: "2024-01-15T11:00:00Z",
        success: true,
      }

      const result = UserActivityLogSchema.safeParse(minimalLog)
      expect(result.success).toBe(true)
    })
  })
})