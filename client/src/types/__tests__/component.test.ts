/**
 * Unit tests for Component and ComponentType Zod schemas
 */
import { describe, it, expect } from "vitest"
import {
  ComponentCreateSchema,
  ComponentUpdateSchema,
  ComponentReadSchema,
  ComponentTypeCreateSchema,
  ComponentTypeUpdateSchema,
  ComponentSpecificationsSchema,
  ComponentDimensionsSchema,
} from "../component"

describe("Component Schemas", () => {
  describe("ComponentSpecificationsSchema", () => {
    it("should validate valid specifications", () => {
      const validSpec = {
        electrical: { voltage: "24V", current: "10A" },
        thermal: { operating_temp: "-20°C to +60°C" },
        standards_compliance: ["IEC-61131", "EN-60947-1"],
      }

      const result = ComponentSpecificationsSchema.safeParse(validSpec)
      expect(result.success).toBe(true)
    })

    it("should allow empty specifications", () => {
      const result = ComponentSpecificationsSchema.safeParse({})
      expect(result.success).toBe(true)
    })
  })

  describe("ComponentDimensionsSchema", () => {
    it("should validate valid dimensions", () => {
      const validDimensions = {
        length: 100,
        width: 50,
        height: 25,
        unit: "mm",
      }

      const result = ComponentDimensionsSchema.safeParse(validDimensions)
      expect(result.success).toBe(true)
    })

    it("should reject negative dimensions", () => {
      const invalidDimensions = {
        length: -10,
        width: 50,
      }

      const result = ComponentDimensionsSchema.safeParse(invalidDimensions)
      expect(result.success).toBe(false)
    })
  })

  describe("ComponentCreateSchema", () => {
    it("should validate valid component creation data", () => {
      const validComponent = {
        name: "Test Component",
        manufacturer: "Test Manufacturer",
        model_number: "TEST-001",
        component_type_id: 1,
        category_id: 1,
        currency: "EUR",
        is_active: true,
        is_preferred: false,
        stock_status: "active" as const,
        version: "1.0",
      }

      const result = ComponentCreateSchema.safeParse(validComponent)
      expect(result.success).toBe(true)
    })

    it("should reject component with invalid currency code", () => {
      const invalidComponent = {
        name: "Test Component",
        manufacturer: "Test Manufacturer",
        model_number: "TEST-001",
        component_type_id: 1,
        category_id: 1,
        currency: "INVALID", // Invalid currency code
      }

      const result = ComponentCreateSchema.safeParse(invalidComponent)
      expect(result.success).toBe(false)
    })

    it("should reject component with invalid stock status", () => {
      const invalidComponent = {
        name: "Test Component",
        manufacturer: "Test Manufacturer",
        model_number: "TEST-001",
        component_type_id: 1,
        category_id: 1,
        stock_status: "invalid_status",
      }

      const result = ComponentCreateSchema.safeParse(invalidComponent)
      expect(result.success).toBe(false)
    })

    it("should reject component with invalid version format", () => {
      const invalidComponent = {
        name: "Test Component",
        manufacturer: "Test Manufacturer",
        model_number: "TEST-001",
        component_type_id: 1,
        category_id: 1,
        version: "invalid-version",
      }

      const result = ComponentCreateSchema.safeParse(invalidComponent)
      expect(result.success).toBe(false)
    })

    it("should validate with specifications", () => {
      const componentWithSpecs = {
        name: "Test Component",
        manufacturer: "Test Manufacturer",
        model_number: "TEST-001",
        component_type_id: 1,
        category_id: 1,
        specifications: {
          electrical: { voltage: "24V" },
          thermal: { temp_range: "-20 to +70°C" },
        },
      }

      const result = ComponentCreateSchema.safeParse(componentWithSpecs)
      expect(result.success).toBe(true)
    })
  })

  describe("ComponentUpdateSchema", () => {
    it("should validate partial updates", () => {
      const partialUpdate = {
        name: "Updated Component Name",
        is_active: false,
      }

      const result = ComponentUpdateSchema.safeParse(partialUpdate)
      expect(result.success).toBe(true)
    })

    it("should allow empty update object", () => {
      const result = ComponentUpdateSchema.safeParse({})
      expect(result.success).toBe(true)
    })
  })

  describe("ComponentReadSchema", () => {
    it("should validate complete component read data", () => {
      const readData = {
        id: 1,
        name: "Test Component",
        manufacturer: "Test Manufacturer",
        model_number: "TEST-001",
        component_type_id: 1,
        category_id: 1,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        currency: "EUR",
        is_active: true,
        is_preferred: false,
        stock_status: "available",
        version: "1.0",
      }

      const result = ComponentReadSchema.safeParse(readData)
      expect(result.success).toBe(true)
    })
  })
})

describe("ComponentType Schemas", () => {
  describe("ComponentTypeCreateSchema", () => {
    it("should validate valid component type creation data", () => {
      const validType = {
        name: "Relay",
        description: "Electromagnetic relay for switching applications",
        category_id: 1,
        is_active: true,
        specifications_template: {
          electrical: { coil_voltage: "string", contact_current: "string" },
        },
      }

      const result = ComponentTypeCreateSchema.safeParse(validType)
      expect(result.success).toBe(true)
    })

    it("should reject component type with empty name", () => {
      const invalidType = {
        name: "",
        category_id: 1,
      }

      const result = ComponentTypeCreateSchema.safeParse(invalidType)
      expect(result.success).toBe(false)
    })

    it("should reject component type with invalid category_id", () => {
      const invalidType = {
        name: "Valid Name",
        category_id: 0, // Invalid, must be >= 1
      }

      const result = ComponentTypeCreateSchema.safeParse(invalidType)
      expect(result.success).toBe(false)
    })
  })

  describe("ComponentTypeUpdateSchema", () => {
    it("should validate partial updates", () => {
      const partialUpdate = {
        description: "Updated description",
        is_active: false,
      }

      const result = ComponentTypeUpdateSchema.safeParse(partialUpdate)
      expect(result.success).toBe(true)
    })

    it("should allow empty update object", () => {
      const result = ComponentTypeUpdateSchema.safeParse({})
      expect(result.success).toBe(true)
    })
  })
})