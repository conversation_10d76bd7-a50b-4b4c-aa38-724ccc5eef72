/**
 * Unit tests for User and UserPreference Zod schemas
 */
import { describe, it, expect } from "vitest"
import {
  UserBaseSchema,
  UserCreateSchema,
  UserUpdateSchema,
  UserReadSchema,
  UserSummarySchema,
  UserSessionSchema,
  UserPreferenceBaseSchema,
  UserPreferenceCreateSchema,
  UserPreferenceUpdateSchema,
  UserPreferenceReadSchema,
} from "../user"

describe("User Schemas", () => {
  describe("UserBaseSchema", () => {
    it("should validate valid user base data", () => {
      const validUser = {
        name: "<PERSON>",
        email: "<EMAIL>",
        is_superuser: false,
      }

      const result = UserBaseSchema.safeParse(validUser)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.email).toBe("<EMAIL>")
        expect(result.data.is_superuser).toBe(false)
      }
    })

    it("should apply default is_superuser value", () => {
      const userWithoutSuperuser = {
        name: "<PERSON>",
        email: "<EMAIL>",
      }

      const result = UserBaseSchema.safeParse(userWithoutSuperuser)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.is_superuser).toBe(false)
      }
    })

    it("should normalize email to lowercase and trim", () => {
      const userWithUppercaseEmail = {
        name: "Test User",
        email: "  <EMAIL>  ",
        is_superuser: true,
      }

      const result = UserBaseSchema.safeParse(userWithUppercaseEmail)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.email).toBe("<EMAIL>")
      }
    })

    it("should trim name field", () => {
      const userWithSpaces = {
        name: "  Trimmed Name  ",
        email: "<EMAIL>",
      }

      const result = UserBaseSchema.safeParse(userWithSpaces)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe("Trimmed Name")
      }
    })

    it("should reject name shorter than 3 characters", () => {
      const invalidUser = {
        name: "Jo", // Too short
        email: "<EMAIL>",
      }

      const result = UserBaseSchema.safeParse(invalidUser)
      expect(result.success).toBe(false)
    })

    it("should reject name longer than 50 characters", () => {
      const invalidUser = {
        name: "a".repeat(51), // Too long
        email: "<EMAIL>",
      }

      const result = UserBaseSchema.safeParse(invalidUser)
      expect(result.success).toBe(false)
    })

    it("should reject invalid email format", () => {
      const invalidUser = {
        name: "John Doe",
        email: "not-an-email",
      }

      const result = UserBaseSchema.safeParse(invalidUser)
      expect(result.success).toBe(false)
    })
  })

  describe("UserCreateSchema", () => {
    it("should validate user creation data", () => {
      const validCreate = {
        name: "New User",
        email: "<EMAIL>",
        password: "securePassword123",
        role: "user",
        is_active: true,
      }

      const result = UserCreateSchema.safeParse(validCreate)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.is_active).toBe(true)
      }
    })

    it("should apply default is_active value", () => {
      const createWithoutActive = {
        name: "Default User",
        email: "<EMAIL>",
        password: "password123",
      }

      const result = UserCreateSchema.safeParse(createWithoutActive)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.is_active).toBe(true)
      }
    })

    it("should reject password shorter than 8 characters", () => {
      const invalidCreate = {
        name: "Test User",
        email: "<EMAIL>",
        password: "short", // Too short
      }

      const result = UserCreateSchema.safeParse(invalidCreate)
      expect(result.success).toBe(false)
    })

    it("should validate optional role field", () => {
      const createWithRole = {
        name: "Admin User",
        email: "<EMAIL>",
        password: "adminPassword123",
        role: "administrator",
      }

      const result = UserCreateSchema.safeParse(createWithRole)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.role).toBe("administrator")
      }
    })
  })

  describe("UserUpdateSchema", () => {
    it("should validate partial user updates", () => {
      const validUpdate = {
        name: "Updated Name",
        email: "<EMAIL>",
      }

      const result = UserUpdateSchema.safeParse(validUpdate)
      expect(result.success).toBe(true)
    })

    it("should allow empty update object", () => {
      const result = UserUpdateSchema.safeParse({})
      expect(result.success).toBe(true)
    })

    it("should validate password update", () => {
      const passwordUpdate = {
        password: "newSecurePassword456",
      }

      const result = UserUpdateSchema.safeParse(passwordUpdate)
      expect(result.success).toBe(true)
    })

    it("should reject short password in update", () => {
      const invalidUpdate = {
        password: "short", // Too short
      }

      const result = UserUpdateSchema.safeParse(invalidUpdate)
      expect(result.success).toBe(false)
    })

    it("should normalize email in updates", () => {
      const updateWithUppercaseEmail = {
        email: "<EMAIL>",
      }

      const result = UserUpdateSchema.safeParse(updateWithUppercaseEmail)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.email).toBe("<EMAIL>")
      }
    })
  })

  describe("UserReadSchema", () => {
    it("should validate complete user read data", () => {
      const validRead = {
        id: 1,
        name: "John Doe",
        email: "<EMAIL>",
        is_superuser: false,
        is_active: true,
        is_admin: true,
        role: "administrator",
        last_login: "2024-01-15T10:30:00Z",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-15T10:30:00Z",
      }

      const result = UserReadSchema.safeParse(validRead)
      expect(result.success).toBe(true)
    })

    it("should validate user without optional fields", () => {
      const minimalRead = {
        id: 2,
        name: "Jane Smith",
        email: "<EMAIL>",
        is_superuser: false,
        is_active: true,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      }

      const result = UserReadSchema.safeParse(minimalRead)
      expect(result.success).toBe(true)
    })

    it("should handle null values for optional fields", () => {
      const readWithNulls = {
        id: 3,
        name: "Test User",
        email: "<EMAIL>",
        is_superuser: false,
        is_active: false,
        role: null,
        last_login: null,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      }

      const result = UserReadSchema.safeParse(readWithNulls)
      expect(result.success).toBe(true)
    })

    it("should require mandatory fields", () => {
      const incompleteRead = {
        name: "Incomplete User",
        email: "<EMAIL>",
        // Missing id, is_superuser, is_active, created_at, updated_at
      }

      const result = UserReadSchema.safeParse(incompleteRead)
      expect(result.success).toBe(false)
    })
  })

  describe("UserSummarySchema", () => {
    it("should validate user summary data", () => {
      const validSummary = {
        id: 1,
        name: "John Doe",
        email: "<EMAIL>",
        is_active: true,
        role: "user",
        last_login: "2024-01-15T10:30:00Z",
        created_at: "2024-01-01T00:00:00Z",
      }

      const result = UserSummarySchema.safeParse(validSummary)
      expect(result.success).toBe(true)
    })

    it("should validate summary without optional fields", () => {
      const minimalSummary = {
        id: 2,
        name: "Jane Smith",
        email: "<EMAIL>",
        is_active: false,
        created_at: "2024-01-01T00:00:00Z",
      }

      const result = UserSummarySchema.safeParse(minimalSummary)
      expect(result.success).toBe(true)
    })
  })

  describe("UserSessionSchema", () => {
    it("should validate complete session data", () => {
      const validSession = {
        id: "sess_abc123def456",
        user_id: 1,
        ip_address: "***********",
        user_agent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
        created_at: "2024-01-15T10:00:00Z",
        last_activity: "2024-01-15T11:30:00Z",
        expires_at: "2024-01-16T10:00:00Z",
        is_active: true,
      }

      const result = UserSessionSchema.safeParse(validSession)
      expect(result.success).toBe(true)
    })

    it("should validate session without optional fields", () => {
      const minimalSession = {
        id: "sess_minimal123",
        user_id: 2,
        created_at: "2024-01-15T10:00:00Z",
        last_activity: "2024-01-15T11:30:00Z",
        expires_at: "2024-01-16T10:00:00Z",
        is_active: false,
      }

      const result = UserSessionSchema.safeParse(minimalSession)
      expect(result.success).toBe(true)
    })
  })
})

describe("User Preference Schemas", () => {
  describe("UserPreferenceBaseSchema", () => {
    it("should validate complete preference data", () => {
      const validPrefs = {
        theme: "dark",
        language: "es",
        timezone: "America/New_York",
        date_format: "DD/MM/YYYY",
        time_format: "12h",
        units_system: "imperial",
        notifications_enabled: false,
        auto_save_interval: 600,
      }

      const result = UserPreferenceBaseSchema.safeParse(validPrefs)
      expect(result.success).toBe(true)
    })

    it("should apply default values", () => {
      const emptyPrefs = {}

      const result = UserPreferenceBaseSchema.safeParse(emptyPrefs)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.theme).toBe("light")
        expect(result.data.language).toBe("en")
        expect(result.data.timezone).toBe("UTC")
        expect(result.data.date_format).toBe("YYYY-MM-DD")
        expect(result.data.time_format).toBe("24h")
        expect(result.data.units_system).toBe("metric")
        expect(result.data.notifications_enabled).toBe(true)
        expect(result.data.auto_save_interval).toBe(300)
      }
    })

    it("should validate custom preference values", () => {
      const customPrefs = {
        theme: "custom-dark",
        language: "fr-CA",
        timezone: "Europe/Paris",
        auto_save_interval: 120,
      }

      const result = UserPreferenceBaseSchema.safeParse(customPrefs)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.theme).toBe("custom-dark")
        expect(result.data.language).toBe("fr-CA")
        expect(result.data.auto_save_interval).toBe(120)
      }
    })
  })

  describe("UserPreferenceCreateSchema", () => {
    it("should validate preference creation data", () => {
      const validCreate = {
        user_id: 1,
        theme: "dark",
        language: "es",
        notifications_enabled: false,
      }

      const result = UserPreferenceCreateSchema.safeParse(validCreate)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.user_id).toBe(1)
        expect(result.data.theme).toBe("dark")
        expect(result.data.timezone).toBe("UTC") // Default value
      }
    })

    it("should require user_id", () => {
      const invalidCreate = {
        theme: "dark",
        // Missing user_id
      }

      const result = UserPreferenceCreateSchema.safeParse(invalidCreate)
      expect(result.success).toBe(false)
    })

    it("should validate with minimal data", () => {
      const minimalCreate = {
        user_id: 2,
      }

      const result = UserPreferenceCreateSchema.safeParse(minimalCreate)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.user_id).toBe(2)
        expect(result.data.theme).toBe("light") // Default
      }
    })
  })

  describe("UserPreferenceUpdateSchema", () => {
    it("should validate partial preference updates", () => {
      const validUpdate = {
        theme: "dark",
        notifications_enabled: false,
        auto_save_interval: 900,
      }

      const result = UserPreferenceUpdateSchema.safeParse(validUpdate)
      expect(result.success).toBe(true)
    })

    it("should allow empty update object", () => {
      const result = UserPreferenceUpdateSchema.safeParse({})
      expect(result.success).toBe(true)
    })

    it("should validate single field updates", () => {
      const languageUpdate = {
        language: "ja",
      }

      const result = UserPreferenceUpdateSchema.safeParse(languageUpdate)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.language).toBe("ja")
      }
    })
  })

  describe("UserPreferenceReadSchema", () => {
    it("should validate complete preference read data", () => {
      const validRead = {
        id: 1,
        user_id: 1,
        theme: "dark",
        language: "en",
        timezone: "UTC",
        date_format: "YYYY-MM-DD",
        time_format: "24h",
        units_system: "metric",
        notifications_enabled: true,
        auto_save_interval: 300,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-15T10:30:00Z",
      }

      const result = UserPreferenceReadSchema.safeParse(validRead)
      expect(result.success).toBe(true)
    })

    it("should require id and user_id", () => {
      const incompleteRead = {
        theme: "light",
        language: "en",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        // Missing id and user_id
      }

      const result = UserPreferenceReadSchema.safeParse(incompleteRead)
      expect(result.success).toBe(false)
    })
  })
})