/**
 * Unit tests for Health Check Zod schemas
 */
import { describe, it, expect } from "vitest"
import {
  DatabaseHealthSchema,
  ServiceHealthSchema,
  SystemMetricsSchema,
  HealthCheckResponseSchema,
  SimpleHealthResponseSchema,
} from "../health"

describe("Health Check Schemas", () => {
  describe("DatabaseHealthSchema", () => {
    it("should validate valid database health data", () => {
      const validHealth = {
        status: "healthy",
        connection_responsive: true,
        connection_latency_ms: 15.5,
        pool_utilization: 45.2,
        pool_metrics: {
          active_connections: 5,
          idle_connections: 15,
          max_connections: 20,
        },
        health_score: 9,
        recommendations: ["Consider connection pooling optimization"],
        database_type: "postgresql",
        connection_error: null,
      }

      const result = DatabaseHealthSchema.safeParse(validHealth)
      expect(result.success).toBe(true)
    })

    it("should validate minimal database health data", () => {
      const minimalHealth = {
        status: "degraded",
        connection_responsive: false,
        pool_utilization: 80.0,
        pool_metrics: {},
        health_score: 3,
        database_type: "postgresql",
      }

      const result = DatabaseHealthSchema.safeParse(minimalHealth)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.recommendations).toEqual([])
      }
    })

    it("should reject invalid health score", () => {
      const invalidHealth = {
        status: "healthy",
        connection_responsive: true,
        pool_utilization: 45.2,
        pool_metrics: {},
        health_score: 15, // Invalid: exceeds max of 10
        database_type: "postgresql",
      }

      const result = DatabaseHealthSchema.safeParse(invalidHealth)
      expect(result.success).toBe(false)
    })

    it("should reject negative health score", () => {
      const invalidHealth = {
        status: "unhealthy",
        connection_responsive: false,
        pool_utilization: 95.0,
        pool_metrics: {},
        health_score: -1, // Invalid: below min of 0
        database_type: "postgresql",
      }

      const result = DatabaseHealthSchema.safeParse(invalidHealth)
      expect(result.success).toBe(false)
    })
  })

  describe("ServiceHealthSchema", () => {
    it("should validate complete service health data", () => {
      const validService = {
        name: "api-server",
        status: "healthy",
        version: "1.2.3",
        uptime_seconds: 86400,
        last_check: "2024-01-15T10:30:00Z",
        details: {
          memory_usage: "250MB",
          cpu_usage: "15%",
          active_requests: 42,
        },
      }

      const result = ServiceHealthSchema.safeParse(validService)
      expect(result.success).toBe(true)
    })

    it("should validate minimal service health data", () => {
      const minimalService = {
        name: "background-worker",
        status: "degraded",
        last_check: "2024-01-15T10:29:00Z",
      }

      const result = ServiceHealthSchema.safeParse(minimalService)
      expect(result.success).toBe(true)
    })

    it("should handle date parsing", () => {
      const serviceWithDate = {
        name: "scheduler",
        status: "healthy",
        last_check: new Date("2024-01-15T10:30:00Z"),
      }

      const result = ServiceHealthSchema.safeParse(serviceWithDate)
      expect(result.success).toBe(true)
    })

    it("should require mandatory fields", () => {
      const incompleteService = {
        status: "healthy",
        // Missing required name and last_check
      }

      const result = ServiceHealthSchema.safeParse(incompleteService)
      expect(result.success).toBe(false)
    })
  })

  describe("SystemMetricsSchema", () => {
    it("should validate complete system metrics", () => {
      const validMetrics = {
        memory_usage_mb: 512.5,
        cpu_usage_percent: 25.8,
        disk_usage_percent: 67.2,
        active_connections: 128,
        request_count_last_minute: 1543,
      }

      const result = SystemMetricsSchema.safeParse(validMetrics)
      expect(result.success).toBe(true)
    })

    it("should validate empty system metrics", () => {
      const emptyMetrics = {}

      const result = SystemMetricsSchema.safeParse(emptyMetrics)
      expect(result.success).toBe(true)
    })

    it("should validate partial system metrics", () => {
      const partialMetrics = {
        memory_usage_mb: 1024.0,
        active_connections: 64,
      }

      const result = SystemMetricsSchema.safeParse(partialMetrics)
      expect(result.success).toBe(true)
    })

    it("should handle null values", () => {
      const metricsWithNulls = {
        memory_usage_mb: null,
        cpu_usage_percent: 12.5,
        disk_usage_percent: null,
        active_connections: 32,
        request_count_last_minute: null,
      }

      const result = SystemMetricsSchema.safeParse(metricsWithNulls)
      expect(result.success).toBe(true)
    })
  })

  describe("HealthCheckResponseSchema", () => {
    it("should validate complete health check response", () => {
      const validResponse = {
        status: "healthy",
        timestamp: "2024-01-15T10:30:00Z",
        version: "2.1.0",
        environment: "production",
        uptime_seconds: 172800,
        database: {
          status: "healthy",
          connection_responsive: true,
          connection_latency_ms: 12.3,
          pool_utilization: 35.0,
          pool_metrics: { active: 7, idle: 13 },
          health_score: 10,
          recommendations: [],
          database_type: "postgresql",
          connection_error: null,
        },
        services: [
          {
            name: "api-server",
            status: "healthy",
            version: "2.1.0",
            uptime_seconds: 172800,
            last_check: "2024-01-15T10:30:00Z",
            details: null,
          },
        ],
        system_metrics: {
          memory_usage_mb: 768.0,
          cpu_usage_percent: 18.5,
          disk_usage_percent: 42.1,
          active_connections: 96,
          request_count_last_minute: 2847,
        },
        health_score: 9,
        critical_issues: [],
        warnings: ["High disk usage approaching threshold"],
        checks_performed: ["database", "services", "system_metrics"],
        response_time_ms: 45.2,
      }

      const result = HealthCheckResponseSchema.safeParse(validResponse)
      expect(result.success).toBe(true)
    })

    it("should validate minimal health check response", () => {
      const minimalResponse = {
        status: "degraded",
        timestamp: "2024-01-15T10:30:00Z",
        version: "2.1.0",
        environment: "development",
        uptime_seconds: 3600,
        database: {
          status: "degraded",
          connection_responsive: false,
          pool_utilization: 90.0,
          pool_metrics: {},
          health_score: 3,
          database_type: "postgresql",
        },
        health_score: 4,
      }

      const result = HealthCheckResponseSchema.safeParse(minimalResponse)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.services).toEqual([])
        expect(result.data.critical_issues).toEqual([])
        expect(result.data.warnings).toEqual([])
        expect(result.data.checks_performed).toEqual([])
      }
    })

    it("should reject invalid health score", () => {
      const invalidResponse = {
        status: "healthy",
        timestamp: "2024-01-15T10:30:00Z",
        version: "2.1.0",
        environment: "production",
        uptime_seconds: 3600,
        database: {
          status: "healthy",
          connection_responsive: true,
          pool_utilization: 30.0,
          pool_metrics: {},
          health_score: 8,
          database_type: "postgresql",
        },
        health_score: 12, // Invalid: exceeds max of 10
      }

      const result = HealthCheckResponseSchema.safeParse(invalidResponse)
      expect(result.success).toBe(false)
    })
  })

  describe("SimpleHealthResponseSchema", () => {
    it("should validate simple health response", () => {
      const validSimpleResponse = {
        status: "healthy",
        timestamp: "2024-01-15T10:30:00Z",
        version: "2.1.0",
        database_status: "connected",
        uptime_seconds: 86400,
      }

      const result = SimpleHealthResponseSchema.safeParse(validSimpleResponse)
      expect(result.success).toBe(true)
    })

    it("should validate unhealthy simple response", () => {
      const unhealthyResponse = {
        status: "unhealthy",
        timestamp: "2024-01-15T10:30:00Z",
        version: "2.1.0",
        database_status: "disconnected",
        uptime_seconds: 1200,
      }

      const result = SimpleHealthResponseSchema.safeParse(unhealthyResponse)
      expect(result.success).toBe(true)
    })

    it("should handle date parsing", () => {
      const responseWithDate = {
        status: "healthy",
        timestamp: new Date("2024-01-15T10:30:00Z"),
        version: "2.1.0",
        database_status: "connected",
        uptime_seconds: 7200,
      }

      const result = SimpleHealthResponseSchema.safeParse(responseWithDate)
      expect(result.success).toBe(true)
    })

    it("should require all mandatory fields", () => {
      const incompleteResponse = {
        status: "healthy",
        version: "2.1.0",
        // Missing timestamp, database_status, uptime_seconds
      }

      const result = SimpleHealthResponseSchema.safeParse(incompleteResponse)
      expect(result.success).toBe(false)
    })
  })
})