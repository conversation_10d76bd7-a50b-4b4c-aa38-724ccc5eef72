/**
 * Unit tests for Project Zod schemas
 */
import { describe, it, expect } from "vitest"
import {
  ProjectCreateSchema,
  ProjectUpdateSchema,
  ProjectReadSchema,
  ProjectSummarySchema,
  projectStatusSchema,
} from "@/types/api"

describe("Project Schemas", () => {
  describe("projectStatusSchema", () => {
    it("should validate all project status values", () => {
      const validStatuses = [
        "Draft",
        "Active",
        "On Hold",
        "Completed",
        "Cancelled",
        "Archived",
        "Planning",
        "Design In Progress",
        "Procurement In Progress",
        "Construction In Progress",
        "Commissioning",
      ]

      validStatuses.forEach((status) => {
        const result = projectStatusSchema.safeParse(status)
        expect(result.success).toBe(true)
      })
    })

    it("should reject invalid status values", () => {
      const invalidStatus = "INVALID_STATUS"
      const result = projectStatusSchema.safeParse(invalidStatus)
      expect(result.success).toBe(false)
    })
  })

  describe("ProjectCreateSchema", () => {
    it("should validate valid project creation data", () => {
      const validProject = {
        name: "New Electrical Project",
        description: "A comprehensive electrical design project",
        status: "Draft" as const,
        client: "ABC Manufacturing",
        location: "New York, NY",
        is_offline: false,
        project_number: "PRJ-2024-001",
      }

      const result = ProjectCreateSchema.safeParse(validProject)
      expect(result.success).toBe(true)
    })

    it("should validate minimal project data", () => {
      const minimalProject = {
        name: "Minimal Project",
      }

      const result = ProjectCreateSchema.safeParse(minimalProject)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.status).toBe("Draft")
        expect(result.data.is_offline).toBe(false)
      }
    })

    it("should reject project with empty name", () => {
      const invalidProject = {
        name: "",
      }

      const result = ProjectCreateSchema.safeParse(invalidProject)
      expect(result.success).toBe(false)
    })

    it("should reject project with name too long", () => {
      const invalidProject = {
        name: "a".repeat(256), // Exceeds 255 character limit
      }

      const result = ProjectCreateSchema.safeParse(invalidProject)
      expect(result.success).toBe(false)
    })

    it("should validate database URL with correct scheme", () => {
      const projectWithDb = {
        name: "Project with DB",
        database_url: "postgresql://user:pass@localhost:5432/dbname",
      }

      const result = ProjectCreateSchema.safeParse(projectWithDb)
      expect(result.success).toBe(true)
    })

    it("should reject invalid database URL scheme", () => {
      const projectWithInvalidDb = {
        name: "Project with Invalid DB",
        database_url: "mysql://user:pass@localhost:3306/dbname",
      }

      const result = ProjectCreateSchema.safeParse(projectWithInvalidDb)
      expect(result.success).toBe(false)
    })

    it("should trim and validate text fields", () => {
      const projectWithSpaces = {
        name: "  Trimmed Project Name  ",
        description: "  Trimmed description  ",
        client: "  Trimmed Client  ",
        location: "  Trimmed Location  ",
      }

      const result = ProjectCreateSchema.safeParse(projectWithSpaces)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe("Trimmed Project Name")
        expect(result.data.description).toBe("Trimmed description")
        expect(result.data.client).toBe("Trimmed Client")
        expect(result.data.location).toBe("Trimmed Location")
      }
    })
  })

  describe("ProjectUpdateSchema", () => {
    it("should validate partial updates", () => {
      const partialUpdate = {
        name: "Updated Project Name",
        status: "Active" as const,
        budget: 50000,
        currency: "USD",
      }

      const result = ProjectUpdateSchema.safeParse(partialUpdate)
      expect(result.success).toBe(true)
    })

    it("should allow empty update object", () => {
      const result = ProjectUpdateSchema.safeParse({})
      expect(result.success).toBe(true)
    })

    it("should validate date fields", () => {
      const updateWithDates = {
        start_date: "2024-01-01T00:00:00Z",
        end_date: "2024-12-31T23:59:59Z",
      }

      const result = ProjectUpdateSchema.safeParse(updateWithDates)
      expect(result.success).toBe(true)
    })

    it("should reject negative budget", () => {
      const invalidUpdate = {
        budget: -1000,
      }

      const result = ProjectUpdateSchema.safeParse(invalidUpdate)
      expect(result.success).toBe(false)
    })

    it("should validate currency field length", () => {
      const validUpdate = {
        currency: "USD",
      }

      const result = ProjectUpdateSchema.safeParse(validUpdate)
      expect(result.success).toBe(true)

      const invalidUpdate = {
        currency: "TOOLONGCURRENCY", // Exceeds 10 character limit
      }

      const invalidResult = ProjectUpdateSchema.safeParse(invalidUpdate)
      expect(invalidResult.success).toBe(false)
    })
  })

  describe("ProjectReadSchema", () => {
    it("should validate complete project read data", () => {
      const readData = {
        id: 1,
        name: "Test Project",
        description: "Test description",
        status: "Active" as const,
        client: "Test Client",
        location: "Test Location",
        is_offline: false,
        project_number: "PRJ-001",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        members: [],
      }

      const result = ProjectReadSchema.safeParse(readData)
      expect(result.success).toBe(true)
    })

    it("should validate project with members", () => {
      const projectWithMembers = {
        id: 1,
        name: "Test Project",
        description: "Test description",
        status: "Active" as const,
        client: "Test Client",
        location: "Test Location",
        is_offline: false,
        project_number: "PRJ-001",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        members: [
          { id: 1, name: "John Doe", role: "Lead Engineer" },
          { id: 2, name: "Jane Smith", role: "Designer" },
        ],
      }

      const result = ProjectReadSchema.safeParse(projectWithMembers)
      expect(result.success).toBe(true)
    })
  })

  describe("ProjectSummarySchema", () => {
    it("should validate project summary data", () => {
      const summaryData = {
        id: 1,
        name: "Summary Project",
        status: "Planning" as const,
        client_name: "Test Client",
        location: "Test Location",
        start_date: "2024-01-01T00:00:00Z",
        end_date: "2024-12-31T23:59:59Z",
        budget: 100000,
        currency: "EUR",
        owner_id: 1,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      }

      const result = ProjectSummarySchema.safeParse(summaryData)
      expect(result.success).toBe(true)
    })

    it("should validate minimal summary data", () => {
      const minimalSummary = {
        id: 1,
        name: "Minimal Summary",
        status: "Draft" as const,
        owner_id: 1,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      }

      const result = ProjectSummarySchema.safeParse(minimalSummary)
      expect(result.success).toBe(true)
    })
  })
})