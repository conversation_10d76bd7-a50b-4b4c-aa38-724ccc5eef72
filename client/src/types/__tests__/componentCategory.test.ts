/**
 * Unit tests for ComponentCategory Zod schemas
 */
import { describe, it, expect } from "vitest"
import {
  ComponentCategoryCreateSchema,
  ComponentCategoryUpdateSchema,
  ComponentCategoryReadSchema,
  ComponentCategoryTreeNodeSchema,
  ComponentCategorySearchSchema,
} from "../componentCategory"

describe("ComponentCategory Schemas", () => {
  describe("ComponentCategoryCreateSchema", () => {
    it("should validate valid category creation data", () => {
      const validCategory = {
        name: "Electrical Components",
        description: "Components for electrical systems",
        is_active: true,
      }

      const result = ComponentCategoryCreateSchema.safeParse(validCategory)
      expect(result.success).toBe(true)
    })

    it("should validate category with parent", () => {
      const categoryWithParent = {
        name: "Relays",
        description: "Electromagnetic relays",
        parent_category_id: 1,
        is_active: true,
      }

      const result = ComponentCategoryCreateSchema.safeParse(categoryWithParent)
      expect(result.success).toBe(true)
    })

    it("should reject category with empty name", () => {
      const invalidCategory = {
        name: "",
        description: "Valid description",
      }

      const result = ComponentCategoryCreateSchema.safeParse(invalidCategory)
      expect(result.success).toBe(false)
    })

    it("should reject category with invalid parent_category_id", () => {
      const invalidCategory = {
        name: "Valid Name",
        parent_category_id: 0, // Invalid, must be >= 1
      }

      const result = ComponentCategoryCreateSchema.safeParse(invalidCategory)
      expect(result.success).toBe(false)
    })

    it("should trim and validate name", () => {
      const categoryWithSpaces = {
        name: "  Trimmed Name  ",
        is_active: true,
      }

      const result = ComponentCategoryCreateSchema.safeParse(categoryWithSpaces)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.name).toBe("Trimmed Name")
      }
    })
  })

  describe("ComponentCategoryUpdateSchema", () => {
    it("should validate partial updates", () => {
      const partialUpdate = {
        description: "Updated description",
        is_active: false,
      }

      const result = ComponentCategoryUpdateSchema.safeParse(partialUpdate)
      expect(result.success).toBe(true)
    })

    it("should allow empty update object", () => {
      const result = ComponentCategoryUpdateSchema.safeParse({})
      expect(result.success).toBe(true)
    })

    it("should validate parent category change", () => {
      const parentUpdate = {
        parent_category_id: 2,
      }

      const result = ComponentCategoryUpdateSchema.safeParse(parentUpdate)
      expect(result.success).toBe(true)
    })
  })

  describe("ComponentCategoryReadSchema", () => {
    it("should validate complete category read data", () => {
      const readData = {
        id: 1,
        name: "Electrical Components",
        description: "Components for electrical systems",
        parent_category_id: null,
        is_active: true,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        full_path: "Electrical Components",
        level: 0,
        is_root_category: true,
        has_children: true,
        component_count: 10,
      }

      const result = ComponentCategoryReadSchema.safeParse(readData)
      expect(result.success).toBe(true)
    })

    it("should validate category with parent", () => {
      const readData = {
        id: 2,
        name: "Relays",
        description: "Electromagnetic relays",
        parent_category_id: 1,
        is_active: true,
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
        full_path: "Electrical Components > Relays",
        level: 1,
        is_root_category: false,
        has_children: false,
        component_count: 5,
      }

      const result = ComponentCategoryReadSchema.safeParse(readData)
      expect(result.success).toBe(true)
    })
  })

  describe("ComponentCategoryTreeNodeSchema", () => {
    it("should validate simple tree node", () => {
      const treeNode = {
        id: 1,
        name: "Root Category",
        description: "Root level category",
        is_active: true,
        level: 0,
        component_count: 5,
        children: [],
      }

      const result = ComponentCategoryTreeNodeSchema.safeParse(treeNode)
      expect(result.success).toBe(true)
    })

    it("should validate nested tree node", () => {
      const nestedTreeNode = {
        id: 1,
        name: "Root Category",
        description: "Root level category",
        is_active: true,
        level: 0,
        component_count: 5,
        children: [
          {
            id: 2,
            name: "Child Category",
            description: "Child category",
            is_active: true,
            level: 1,
            component_count: 3,
            children: [],
          },
        ],
      }

      const result = ComponentCategoryTreeNodeSchema.safeParse(nestedTreeNode)
      expect(result.success).toBe(true)
    })
  })

  describe("ComponentCategorySearchSchema", () => {
    it("should validate search parameters", () => {
      const searchParams = {
        search_term: "relay",
        parent_category_id: 1,
        is_active: true,
        include_children: false,
        min_component_count: 1,
        max_component_count: 100,
      }

      const result = ComponentCategorySearchSchema.safeParse(searchParams)
      expect(result.success).toBe(true)
    })

    it("should validate empty search", () => {
      const result = ComponentCategorySearchSchema.safeParse({})
      expect(result.success).toBe(true)
    })

    it("should validate with defaults", () => {
      const searchWithDefaults = {
        search_term: "components",
      }

      const result = ComponentCategorySearchSchema.safeParse(searchWithDefaults)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.include_children).toBe(false)
      }
    })

    it("should reject invalid component count ranges", () => {
      const invalidSearch = {
        min_component_count: -1,
      }

      const result = ComponentCategorySearchSchema.safeParse(invalidSearch)
      expect(result.success).toBe(false)
    })

    it("should reject invalid parent_category_id", () => {
      const invalidSearch = {
        parent_category_id: 0,
      }

      const result = ComponentCategorySearchSchema.safeParse(invalidSearch)
      expect(result.success).toBe(false)
    })
  })
})