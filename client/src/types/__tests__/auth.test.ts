/**
 * Unit tests for Authentication Zod schemas
 */
import { describe, it, expect } from "vitest"
import {
  LoginRequestSchema,
  LoginResponseSchema,
  LogoutResponseSchema,
  PasswordChangeRequestSchema,
  PasswordChangeResponseSchema,
  PasswordResetRequestSchema,
  PasswordResetConfirmSchema,
  PasswordResetResponseSchema,
  RegisterRequestSchema,
  RegisterResponseSchema,
} from "../auth"

describe("Authentication Schemas", () => {
  describe("LoginRequestSchema", () => {
    it("should validate valid login credentials", () => {
      const validLogin = {
        username: "<EMAIL>",
        password: "securePassword123",
      }

      const result = LoginRequestSchema.safeParse(validLogin)
      expect(result.success).toBe(true)
    })

    it("should validate username as string", () => {
      const loginWithUsername = {
        username: "johndo<PERSON>",
        password: "password123",
      }

      const result = LoginRequestSchema.safeParse(loginWithUsername)
      expect(result.success).toBe(true)
    })

    it("should require both username and password", () => {
      const incompleteLogin = {
        username: "<EMAIL>",
        // Missing password
      }

      const result = LoginRequestSchema.safeParse(incompleteLogin)
      expect(result.success).toBe(false)
    })
  })

  describe("LoginResponseSchema", () => {
    it("should validate complete login response", () => {
      const validResponse = {
        access_token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        token_type: "bearer",
        expires_in: 3600,
        user: {
          id: 1,
          name: "John Doe",
          email: "<EMAIL>",
          is_superuser: false,
          is_active: true,
          is_admin: false,
          role: "user",
          last_login: "2024-01-15T10:30:00Z",
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-15T10:30:00Z",
        },
      }

      const result = LoginResponseSchema.safeParse(validResponse)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.token_type).toBe("bearer")
      }
    })

    it("should apply default token_type", () => {
      const responseWithoutTokenType = {
        access_token: "token123",
        expires_in: 3600,
        user: {
          id: 1,
          name: "John Doe",
          email: "<EMAIL>",
          is_superuser: false,
          is_active: true,
          created_at: "2024-01-01T00:00:00Z",
          updated_at: "2024-01-15T10:30:00Z",
        },
      }

      const result = LoginResponseSchema.safeParse(responseWithoutTokenType)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.token_type).toBe("bearer")
      }
    })
  })

  describe("LogoutResponseSchema", () => {
    it("should validate logout response", () => {
      const validResponse = {
        message: "Successfully logged out",
        logged_out_at: "2024-01-15T11:00:00Z",
      }

      const result = LogoutResponseSchema.safeParse(validResponse)
      expect(result.success).toBe(true)
    })

    it("should apply default message", () => {
      const responseWithoutMessage = {
        logged_out_at: "2024-01-15T11:00:00Z",
      }

      const result = LogoutResponseSchema.safeParse(responseWithoutMessage)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.message).toBe("Successfully logged out")
      }
    })

    it("should handle date parsing", () => {
      const responseWithDate = {
        message: "Goodbye!",
        logged_out_at: new Date("2024-01-15T11:00:00Z"),
      }

      const result = LogoutResponseSchema.safeParse(responseWithDate)
      expect(result.success).toBe(true)
    })
  })

  describe("PasswordChangeRequestSchema", () => {
    it("should validate matching passwords", () => {
      const validRequest = {
        current_password: "oldPassword123",
        new_password: "newSecurePassword456",
        confirm_password: "newSecurePassword456",
      }

      const result = PasswordChangeRequestSchema.safeParse(validRequest)
      expect(result.success).toBe(true)
    })

    it("should reject mismatched passwords", () => {
      const invalidRequest = {
        current_password: "oldPassword123",
        new_password: "newSecurePassword456",
        confirm_password: "differentPassword789",
      }

      const result = PasswordChangeRequestSchema.safeParse(invalidRequest)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(["confirm_password"])
        expect(result.error.issues[0].message).toBe("New password and confirmation do not match")
      }
    })

    it("should enforce minimum password length", () => {
      const invalidRequest = {
        current_password: "old123",
        new_password: "short", // Too short
        confirm_password: "short",
      }

      const result = PasswordChangeRequestSchema.safeParse(invalidRequest)
      expect(result.success).toBe(false)
    })
  })

  describe("PasswordChangeResponseSchema", () => {
    it("should validate password change response", () => {
      const validResponse = {
        message: "Password changed successfully",
        changed_at: "2024-01-15T11:15:00Z",
      }

      const result = PasswordChangeResponseSchema.safeParse(validResponse)
      expect(result.success).toBe(true)
    })

    it("should apply default message", () => {
      const responseWithoutMessage = {
        changed_at: "2024-01-15T11:15:00Z",
      }

      const result = PasswordChangeResponseSchema.safeParse(responseWithoutMessage)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.message).toBe("Password changed successfully")
      }
    })
  })

  describe("PasswordResetRequestSchema", () => {
    it("should validate valid email", () => {
      const validRequest = {
        email: "<EMAIL>",
      }

      const result = PasswordResetRequestSchema.safeParse(validRequest)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.email).toBe("<EMAIL>")
      }
    })

    it("should normalize email to lowercase", () => {
      const requestWithUppercase = {
        email: "<EMAIL>",
      }

      const result = PasswordResetRequestSchema.safeParse(requestWithUppercase)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.email).toBe("<EMAIL>")
      }
    })

    it("should trim whitespace from email", () => {
      const requestWithSpaces = {
        email: "  <EMAIL>  ",
      }

      const result = PasswordResetRequestSchema.safeParse(requestWithSpaces)
      expect(result.success).toBe(true)
      if (result.success) {
        expect(result.data.email).toBe("<EMAIL>")
      }
    })

    it("should reject invalid email format", () => {
      const invalidRequest = {
        email: "not-an-email",
      }

      const result = PasswordResetRequestSchema.safeParse(invalidRequest)
      expect(result.success).toBe(false)
    })
  })

  describe("PasswordResetConfirmSchema", () => {
    it("should validate matching passwords with token", () => {
      const validRequest = {
        token: "reset_token_123",
        new_password: "newSecurePassword456",
        confirm_password: "newSecurePassword456",
      }

      const result = PasswordResetConfirmSchema.safeParse(validRequest)
      expect(result.success).toBe(true)
    })

    it("should reject mismatched passwords", () => {
      const invalidRequest = {
        token: "reset_token_123",
        new_password: "newSecurePassword456",
        confirm_password: "differentPassword789",
      }

      const result = PasswordResetConfirmSchema.safeParse(invalidRequest)
      expect(result.success).toBe(false)
      if (!result.success) {
        expect(result.error.issues[0].path).toEqual(["confirm_password"])
        expect(result.error.issues[0].message).toBe("New password and confirmation do not match")
      }
    })

    it("should enforce minimum password length", () => {
      const invalidRequest = {
        token: "reset_token_123",
        new_password: "short", // Too short
        confirm_password: "short",
      }

      const result = PasswordResetConfirmSchema.safeParse(invalidRequest)
      expect(result.success).toBe(false)
    })

    it("should require token", () => {
      const incompleteRequest = {
        new_password: "newSecurePassword456",
        confirm_password: "newSecurePassword456",
        // Missing token
      }

      const result = PasswordResetConfirmSchema.safeParse(incompleteRequest)
      expect(result.success).toBe(false)
    })
  })

  describe("PasswordResetResponseSchema", () => {
    it("should validate complete reset response", () => {
      const validResponse = {
        message: "Password reset link sent to your email",
        reset_token_sent: true,
        expires_at: "2024-01-15T12:00:00Z",
      }

      const result = PasswordResetResponseSchema.safeParse(validResponse)
      expect(result.success).toBe(true)
    })

    it("should validate response without expiration", () => {
      const responseWithoutExpiry = {
        message: "Password reset failed",
        reset_token_sent: false,
        expires_at: null,
      }

      const result = PasswordResetResponseSchema.safeParse(responseWithoutExpiry)
      expect(result.success).toBe(true)
    })
  })

  describe("RegisterRequestSchema", () => {
    it("should validate complete registration data", () => {
      const validRequest = {
        name: "John Doe",
        email: "<EMAIL>",
        password: "securePassword123",
        confirm_password: "securePassword123",
      }

      const result = RegisterRequestSchema.safeParse(validRequest)
      expect(result.success).toBe(true)
    })

    it("should require all fields", () => {
      const incompleteRequest = {
        name: "John Doe",
        email: "<EMAIL>",
        // Missing password and confirm_password
      }

      const result = RegisterRequestSchema.safeParse(incompleteRequest)
      expect(result.success).toBe(false)
    })

    it("should validate email format", () => {
      const invalidRequest = {
        name: "John Doe",
        email: "invalid-email",
        password: "securePassword123",
        confirm_password: "securePassword123",
      }

      const result = RegisterRequestSchema.safeParse(invalidRequest)
      expect(result.success).toBe(false)
    })
  })

  describe("RegisterResponseSchema", () => {
    it("should validate registration response", () => {
      const validResponse = {
        user: {
          id: 1,
          name: "John Doe",
          email: "<EMAIL>",
          is_superuser: false,
          is_active: true,
          created_at: "2024-01-15T12:00:00Z",
          updated_at: "2024-01-15T12:00:00Z",
        },
        message: "Account created successfully",
        created_at: "2024-01-15T12:00:00Z",
      }

      const result = RegisterResponseSchema.safeParse(validResponse)
      expect(result.success).toBe(true)
    })

    it("should require user object", () => {
      const incompleteResponse = {
        message: "Account created successfully",
        created_at: "2024-01-15T12:00:00Z",
        // Missing user
      }

      const result = RegisterResponseSchema.safeParse(incompleteResponse)
      expect(result.success).toBe(false)
    })
  })
})