/**
 * TypeScript definitions for User Role and Role Assignment Pydantic schemas.
 */
import { BaseSchema } from "./schemas/baseSchemas"
import { z } from "zod"

const permissionsValidation = z
  .string()
  .optional()
  .nullable()
  .refine(
    (val) => {
      if (val === null || val === undefined) return true
      try {
        JSON.parse(val)
        return true
      } catch {
        return false
      }
    },
    { message: "Permissions must be valid JSON" }
  )
//======================= # USER ROLE # =======================//
//========================= # BASE # =========================//

export const UserRoleBaseSchema = BaseSchema.extend({
  name: z.string().trim().min(1).max(100),
  description: z.string().trim().max(1000).optional().nullable(),
  is_system_role: z.boolean().default(false),
  is_active: z.boolean().default(true),
  permissions: permissionsValidation,
  parent_role_id: z.number().int().optional().nullable(),
  priority: z.number().int().min(0).max(100).default(0),
  notes: z.string().trim().max(500).optional().nullable(),
})

//========================= # CRUD # =========================//

export const UserRoleCreateSchema = UserRoleBaseSchema

export const UserRoleUpdateSchema = z.object({
  name: z.string().trim().min(1).max(100).optional(),
  description: z.string().trim().max(1000).optional().nullable(),
  is_active: z.boolean().optional(),
  permissions: permissionsValidation,
  parent_role_id: z.number().int().optional().nullable(),
  priority: z.number().int().min(0).max(100).optional(),
  notes: z.string().trim().max(500).optional().nullable(),
})

export const UserRoleResponseSchema = BaseSchema.extend({
  id: z.number().int(),
  name: z.string(),
  description: z.string().optional().nullable(),
  is_system_role: z.boolean(),
  is_active: z.boolean(),
  permissions: z.string().optional().nullable(),
  parent_role_id: z.number().int().optional().nullable(),
  priority: z.number().int(),
  notes: z.string().optional().nullable(),
  created_at: z.coerce.date(),
  updated_at: z.coerce.date(),
  is_deleted: z.boolean(),
  deleted_at: z.coerce.date().optional().nullable(),
  deleted_by_user_id: z.number().int().optional().nullable(),
})

export const UserRoleHierarchySchema: z.ZodType<UserRoleHierarchy> = z.lazy(
  () =>
    z.object({
      id: z.number().int(),
      name: z.string(),
      description: z.string().optional().nullable(),
      priority: z.number().int(),
      parent_role_id: z.number().int().optional().nullable(),
      child_roles: z.array(UserRoleHierarchySchema).default([]),
    })
)

export const UserRolePermissionsSchema = z.object({
  role_id: z.number().int(),
  role_name: z.string(),
  permissions: z.array(z.string()),
  inherited_permissions: z.array(z.string()).default([]),
  effective_permissions: z.array(z.string()).default([]),
})

export const UserRoleSummarySchema = z.object({
  user_id: z.number().int(),
  active_roles: z.array(UserRoleResponseSchema),
  expired_roles: z.array(UserRoleResponseSchema),
  inactive_roles: z.array(UserRoleResponseSchema),
  effective_permissions: z.array(z.string()),
})

//===================== # TYPE DEFINITIONS # =====================//

export type UserRole = z.infer<typeof UserRoleBaseSchema>
export type UserRoleCreate = z.infer<typeof UserRoleCreateSchema>
export type UserRoleUpdate = z.infer<typeof UserRoleUpdateSchema>
export type UserRoleResponse = z.infer<typeof UserRoleResponseSchema>

export type UserRolePermissions = z.infer<typeof UserRolePermissionsSchema>
export type UserRoleSummary = z.infer<typeof UserRoleSummarySchema>

export type UserRoleHierarchy = {
  id: number
  name: string
  description?: string | null
  priority: number
  parent_role_id?: number | null
  child_roles: UserRoleHierarchy[]
}

//================== # USER ROLE ASSIGNMENT # ==================//
//========================= # BASE # =========================//

const futureDateValidation = z.coerce.date()
  .optional()
  .nullable()
  .refine(
    (val) => val === null || val === undefined || val > new Date(),
    {
      message: "Expiration date must be in the future",
    }
  )

export const UserRoleAssignmentBaseSchema = z.object({
  user_id: z.number().int(),
  role_id: z.number().int(),
  assigned_by_user_id: z.number().int().optional().nullable(),
  expires_at: futureDateValidation,
  is_active: z.boolean().default(true),
  assignment_context: z.string().max(255).optional().nullable(),
  notes: z.string().max(500).optional().nullable(),
})

export const UserRoleAssignmentCreateSchema =
  UserRoleAssignmentBaseSchema.extend({
    name: z.string().trim().min(1).max(100),
  })

export const UserRoleAssignmentUpdateSchema = z.object({
  expires_at: futureDateValidation,
  is_active: z.boolean().optional(),
  assignment_context: z.string().max(255).optional(),
  notes: z.string().max(500).optional(),
})

export const UserRoleAssignmentResponseSchema = BaseSchema.extend({
  id: z.number().int(),
  name: z.string(),
  user_id: z.number().int(),
  role_id: z.number().int(),
  assigned_by_user_id: z.number().int().optional().nullable(),
  assigned_at: z.coerce.date(),
  expires_at: z.coerce.date().optional().nullable(),
  is_active: z.boolean(),
  assignment_context: z.string().optional().nullable(),
  notes: z.string().optional().nullable(),
  created_at: z.coerce.date(),
  updated_at: z.coerce.date(),
  is_deleted: z.boolean(),
  deleted_at: z.coerce.date().optional().nullable(),
  deleted_by_user_id: z.number().int().optional().nullable(),
  is_expired: z.boolean(),
})

//===================== # TYPE DEFINITIONS # =====================//

export type UserRoleAssignment = z.infer<typeof UserRoleAssignmentBaseSchema>
export type UserRoleAssignmentCreate = z.infer<
  typeof UserRoleAssignmentCreateSchema
>
export type UserRoleAssignmentUpdate = z.infer<
  typeof UserRoleAssignmentUpdateSchema
>
export type UserRoleAssignmentResponse = z.infer<
  typeof UserRoleAssignmentResponseSchema
>
