/**
 * Zod schemas and TypeScript definitions for Component and ComponentType based on Pydantic schemas.
 */
import { z } from "zod"

import {
  ComponentCategoryBaseSchema,
  ComponentCategoryReadSchema,
} from "./componentCategory"
import {
  BaseSchema,
  CreatePaginatedResponseSchema,
  TimestampMixinSchema,
} from "./schemas/baseSchemas"
import { componentStatusSchema } from "./schemas/enums/componentEnums"

// ========================= # COMPONENT SPECIFICATIONS # =========================

/**
 * Schema for component electrical specifications.
 */
export const ComponentSpecificationsSchema = z.object({
  electrical: z
    .record(z.string(), z.any())
    .optional()
    .describe("Electrical specifications (voltage, current, power, etc.)"),
  thermal: z
    .record(z.string(), z.any())
    .optional()
    .describe("Thermal specifications (operating temperature, etc.)"),
  mechanical: z
    .record(z.string(), z.any())
    .optional()
    .describe("Mechanical specifications (mounting, enclosure, etc.)"),
  standards_compliance: z
    .array(z.string())
    .optional()
    .describe("List of applicable standards (IEC, IEEE, EN, etc.)"),
  environmental: z
    .record(z.string(), z.any())
    .optional()
    .describe("Environmental specifications (IP rating, humidity, etc.)"),
})

/**
 * Schema for component physical dimensions.
 */
export const ComponentDimensionsSchema = z.object({
  length: z.number().min(0).optional().describe("Length in mm"),
  width: z.number().min(0).optional().describe("Width in mm"),
  height: z.number().min(0).optional().describe("Height in mm"),
  diameter: z
    .number()
    .min(0)
    .optional()
    .describe("Diameter in mm (for cylindrical components)"),
  unit: z.string().default("mm").describe("Unit of measurement"),
})

// ========================= # COMPONENT # =========================
// ========================= # BASE # =========================

export const ComponentBaseSchema = BaseSchema.extend({
  id: z.number().int().describe("Unique component identifier"),
  name: z.string().trim().min(1).max(200).describe("Component name"),
  manufacturer: z
    .string()
    .trim()
    .min(1)
    .max(100)
    .describe("Component manufacturer"),
  model_number: z
    .string()
    .trim()
    .min(1)
    .max(100)
    .describe("Manufacturer model/part number"),
  description: z
    .string()
    .trim()
    .max(1000)
    .optional()
    .nullable()
    .describe("Detailed component description"),
  component_type_id: z.number().int().min(1).describe("Component type ID"),
  category_id: z.number().int().min(1).describe("Component category ID"),
})

// ========================= # CRUD # =========================

export const ComponentCreateSchema = ComponentBaseSchema.extend({
  specifications: z
    .union([z.record(z.string(), z.any()), ComponentSpecificationsSchema])
    .optional()
    .describe("Component technical specifications"),
  unit_price: z.number().min(0).optional().describe("Component unit price"),
  currency: z
    .string()
    .length(3)
    .default("EUR")
    .describe("Price currency (ISO 4217)"),
  supplier: z
    .string()
    .trim()
    .max(100)
    .optional()
    .nullable()
    .describe("Primary supplier name"),
  part_number: z
    .string()
    .trim()
    .max(100)
    .optional()
    .nullable()
    .describe("Supplier part number"),
  weight_kg: z
    .number()
    .min(0)
    .optional()
    .describe("Component weight in kilograms"),
  dimensions: ComponentDimensionsSchema.optional().describe(
    "Physical dimensions"
  ),
  is_active: z.boolean().default(true).describe("Whether component is active"),
  is_preferred: z
    .boolean()
    .default(false)
    .describe("Whether component is preferred"),
  stock_status: componentStatusSchema
    .default("active")
    .describe("Stock availability status"),
  version: z
    .string()
    .regex(/^\d+\.\d+(\.\d+)?$/)
    .default("1.0")
    .describe("Component data version"),
  metadata: z
    .record(z.string(), z.any())
    .optional()
    .describe("Additional metadata"),
})

export const ComponentUpdateSchema = z.object({
  name: z.string().trim().min(1).max(200).optional().describe("Component name"),
  manufacturer: z
    .string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .describe("Component manufacturer"),
  model_number: z
    .string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .describe("Manufacturer model/part number"),
  description: z
    .string()
    .trim()
    .max(1000)
    .optional()
    .nullable()
    .describe("Detailed component description"),
  component_type_id: z
    .number()
    .int()
    .min(1)
    .optional()
    .describe("Component type ID"),
  category_id: z
    .number()
    .int()
    .min(1)
    .optional()
    .describe("Component category ID"),
  specifications: z
    .union([z.record(z.string(), z.any()), ComponentSpecificationsSchema])
    .optional()
    .describe("Component technical specifications"),
  unit_price: z.number().min(0).optional().describe("Component unit price"),
  currency: z
    .string()
    .length(3)
    .optional()
    .describe("Price currency (ISO 4217)"),
  supplier: z
    .string()
    .trim()
    .max(100)
    .optional()
    .nullable()
    .describe("Primary supplier name"),
  part_number: z
    .string()
    .trim()
    .max(100)
    .optional()
    .nullable()
    .describe("Supplier part number"),
  weight_kg: z
    .number()
    .min(0)
    .optional()
    .describe("Component weight in kilograms"),
  dimensions: ComponentDimensionsSchema.optional().describe(
    "Physical dimensions"
  ),
  is_active: z.boolean().optional().describe("Whether component is active"),
  is_preferred: z
    .boolean()
    .optional()
    .describe("Whether component is preferred"),
  stock_status: z
    .enum([
      "available",
      "limited",
      "out_of_stock",
      "discontinued",
      "obsolete",
      "special_order",
      "pre_order",
    ])
    .optional()
    .describe("Stock availability status"),
  version: z
    .string()
    .regex(/^\d+\.\d+(\.\d+)?$/)
    .optional()
    .describe("Component data version"),
  metadata: z
    .record(z.string(), z.any())
    .optional()
    .describe("Additional metadata"),
})

export const ComponentReadSchema = ComponentBaseSchema.extend(
  TimestampMixinSchema
).extend({
  category: ComponentCategoryReadSchema.describe("Component category entity"),
  specifications: z
    .record(z.string(), z.any())
    .optional()
    .describe("Component technical specifications"),
  unit_price: z.number().optional().describe("Component unit price"),
  currency: z.string().default("EUR").describe("Price currency"),
  supplier: z.string().optional().nullable().describe("Primary supplier name"),
  part_number: z
    .string()
    .optional()
    .nullable()
    .describe("Supplier part number"),
  weight_kg: z.number().optional().describe("Component weight in kilograms"),
  dimensions: ComponentDimensionsSchema.optional().describe(
    "Physical dimensions"
  ),
  is_active: z.boolean().default(true).describe("Whether component is active"),
  is_preferred: z
    .boolean()
    .default(false)
    .describe("Whether component is preferred"),
  stock_status: z
    .string()
    .default("available")
    .describe("Stock availability status"),
  version: z.string().default("1.0").describe("Component data version"),
  metadata: z
    .record(z.string(), z.any())
    .optional()
    .describe("Additional metadata"),
  full_name: z
    .string()
    .optional()
    .describe("Full component name (manufacturer + model)"),
  display_name: z.string().optional().describe("Display name for UI"),
})

// ========================= # OTHER # =========================

export const ComponentPaginatedResponseSchema =
  CreatePaginatedResponseSchema(ComponentReadSchema)

export const ComponentSummarySchema = BaseSchema.extend({
  id: z.number().int().describe("Unique component identifier"),
  name: z.string().describe("Component name"),
  manufacturer: z.string().describe("Component manufacturer"),
  model_number: z.string().describe("Manufacturer model/part number"),
  component_type_id: z.number().int().describe("Component type ID"),
  category_id: z.number().int().describe("Component category ID"),
  unit_price: z.number().optional().describe("Component unit price"),
  currency: z.string().default("EUR").describe("Price currency"),
  is_active: z.boolean().default(true).describe("Whether component is active"),
  is_preferred: z
    .boolean()
    .default(false)
    .describe("Whether component is preferred"),
  stock_status: z
    .string()
    .default("available")
    .describe("Stock availability status"),
})

export const ComponentSearchSchema = z.object({
  search_term: z
    .string()
    .trim()
    .max(200)
    .optional()
    .describe(
      "Search term for name, description, manufacturer, or part number"
    ),
  category_id: z
    .number()
    .int()
    .optional()
    .describe("Filter by component category"),
  component_type_id: z
    .number()
    .int()
    .optional()
    .describe("Filter by component type"),
  manufacturer: z
    .string()
    .trim()
    .max(100)
    .optional()
    .describe("Filter by manufacturer"),
  is_preferred: z.boolean().optional().describe("Filter by preferred status"),
  is_active: z
    .boolean()
    .default(true)
    .optional()
    .describe("Filter by active status"),
  min_price: z.number().min(0).optional().describe("Minimum price filter"),
  max_price: z.number().min(0).optional().describe("Maximum price filter"),
  currency: z
    .string()
    .length(3)
    .optional()
    .describe("Currency for price filters"),
  stock_status: z.string().optional().describe("Filter by stock status"),
  specifications: z
    .record(z.string(), z.any())
    .optional()
    .describe("Filter by technical specifications"),
})

export const ComponentAdvancedSearchSchema = ComponentSearchSchema.extend({
  sort_by: z.string().optional().describe("Sort field"),
  sort_order: z.enum(["asc", "desc"]).default("asc").describe("Sort order"),
  include_discontinued: z
    .boolean()
    .default(false)
    .describe("Include discontinued components"),
  date_range: z
    .object({
      start_date: z.string().optional().describe("Start date filter"),
      end_date: z.string().optional().describe("End date filter"),
    })
    .optional()
    .describe("Date range filter"),
  tags: z.array(z.string()).optional().describe("Filter by tags"),
})

export const ComponentAdvancedSearchResponseSchema = z.object({
  items: z.array(ComponentReadSchema).describe("Search results"),
  pagination: z
    .object({
      page: z.number().int().describe("Current page"),
      size: z.number().int().describe("Page size"),
      total: z.number().int().describe("Total items"),
      pages: z.number().int().describe("Total pages"),
    })
    .describe("Pagination info"),
  facets: z
    .object({
      categories: z
        .array(
          z.object({
            id: z.number().int(),
            name: z.string(),
            count: z.number().int(),
          })
        )
        .optional(),
      manufacturers: z
        .array(
          z.object({
            name: z.string(),
            count: z.number().int(),
          })
        )
        .optional(),
      types: z
        .array(
          z.object({
            id: z.number().int(),
            name: z.string(),
            count: z.number().int(),
          })
        )
        .optional(),
    })
    .optional()
    .describe("Search facets"),
  query_info: z
    .object({
      total_time_ms: z.number().optional(),
      suggestions: z.array(z.string()).optional(),
    })
    .optional()
    .describe("Query metadata"),
})

export const ComponentBulkCreateSchema = z.object({
  components: z
    .array(ComponentCreateSchema)
    .min(1)
    .describe("Components to create"),
  validate_only: z
    .boolean()
    .default(false)
    .describe("Only validate, don't create"),
  skip_duplicates: z
    .boolean()
    .default(false)
    .describe("Skip duplicate components"),
})

export const ComponentBulkUpdateSchema = z.object({
  components: z
    .array(
      ComponentUpdateSchema.extend({
        id: z.number().int().describe("Component ID to update"),
      })
    )
    .min(1)
    .describe("Components to update"),
  validate_only: z
    .boolean()
    .default(false)
    .describe("Only validate, don't update"),
})

export const ComponentValidationResultSchema = z.object({
  component_id: z
    .number()
    .int()
    .optional()
    .describe("Component ID if created/updated"),
  success: z.boolean().describe("Whether operation succeeded"),
  errors: z
    .array(
      z.object({
        field: z.string().describe("Field with error"),
        message: z.string().describe("Error message"),
        code: z.string().optional().describe("Error code"),
      })
    )
    .optional()
    .describe("Validation errors"),
  warnings: z
    .array(
      z.object({
        field: z.string().describe("Field with warning"),
        message: z.string().describe("Warning message"),
        code: z.string().optional().describe("Warning code"),
      })
    )
    .optional()
    .describe("Validation warnings"),
})

export const ComponentStatsSchema = z.object({
  total_components: z.number().int().describe("Total number of components"),
  active_components: z.number().int().describe("Number of active components"),
  preferred_components: z
    .number()
    .int()
    .describe("Number of preferred components"),
  by_category: z
    .array(
      z.object({
        category_id: z.number().int(),
        category_name: z.string(),
        count: z.number().int(),
      })
    )
    .describe("Components by category"),
  by_manufacturer: z
    .array(
      z.object({
        manufacturer: z.string(),
        count: z.number().int(),
      })
    )
    .describe("Components by manufacturer"),
  by_stock_status: z
    .array(
      z.object({
        status: z.string(),
        count: z.number().int(),
      })
    )
    .describe("Components by stock status"),
  price_stats: z
    .object({
      min_price: z.number().optional(),
      max_price: z.number().optional(),
      avg_price: z.number().optional(),
      currency: z.string().default("EUR"),
    })
    .optional()
    .describe("Price statistics"),
})

// ========================= # COMPONENT TYPE # =========================
// ========================= # BASE # =========================

export const ComponentTypeBaseSchema = BaseSchema.extend({
  name: z.string().trim().min(1).max(100).describe("Component type name"),
  description: z
    .string()
    .trim()
    .max(1000)
    .optional()
    .nullable()
    .describe("Detailed component type description"),
  category_id: z.number().int().min(1).describe("Component category ID"),
  is_active: z
    .boolean()
    .default(true)
    .describe("Whether component type is active in the system"),
  specifications_template: z
    .record(z.string(), z.any())
    .optional()
    .describe("JSON template for component specifications"),
  metadata: z
    .record(z.string(), z.any())
    .optional()
    .describe("Additional metadata for component type"),
})

// ========================= # CRUD # =========================

export const ComponentTypeCreateSchema = ComponentTypeBaseSchema

export const ComponentTypeUpdateSchema = z.object({
  name: z
    .string()
    .trim()
    .min(1)
    .max(100)
    .optional()
    .describe("Component type name"),
  description: z
    .string()
    .trim()
    .max(1000)
    .optional()
    .nullable()
    .describe("Detailed component type description"),
  category_id: z
    .number()
    .int()
    .min(1)
    .optional()
    .describe("Component category ID"),
  is_active: z
    .boolean()
    .optional()
    .describe("Whether component type is active in the system"),
  specifications_template: z
    .record(z.string(), z.any())
    .optional()
    .describe("JSON template for component specifications"),
  metadata: z
    .record(z.string(), z.any())
    .optional()
    .describe("Additional metadata for component type"),
})

export const ComponentTypeReadSchema = ComponentTypeBaseSchema.merge(
  TimestampMixinSchema
).extend({
  id: z.number().int().describe("Component type ID"),
  full_name: z.string().describe("Full name including category"),
  category_path: z.string().describe("Full category path"),
  component_count: z
    .number()
    .int()
    .describe("Number of components of this type"),
  has_specifications_template: z
    .boolean()
    .describe("Whether type has specifications template"),
  category_name: z.string().optional().describe("Category name"),
})

// ========================= # OTHER # =========================

export const ComponentTypePaginatedResponseSchema =
  CreatePaginatedResponseSchema(ComponentTypeReadSchema)

export const ComponentTypeSummarySchema = BaseSchema.extend({
  id: z.number().int().describe("Component type ID"),
  name: z.string().describe("Component type name"),
  description: z
    .string()
    .optional()
    .nullable()
    .describe("Component type description"),
  category_id: z.number().int().describe("Category ID"),
  category_name: z.string().optional().describe("Category name"),
  is_active: z.boolean().describe("Whether component type is active"),
  component_count: z.number().int().describe("Number of components"),
})

// ===================== # TYPE DEFINITIONS # =====================

// Component types
export type ComponentSpecifications = z.infer<
  typeof ComponentSpecificationsSchema
>
export type ComponentDimensions = z.infer<typeof ComponentDimensionsSchema>
export type Component = z.infer<typeof ComponentBaseSchema>
export type ComponentCreate = z.infer<typeof ComponentCreateSchema>
export type ComponentUpdate = z.infer<typeof ComponentUpdateSchema>
export type ComponentRead = z.infer<typeof ComponentReadSchema>
export type ComponentPaginatedResponse = z.infer<
  typeof ComponentPaginatedResponseSchema
>
export type ComponentSummary = z.infer<typeof ComponentSummarySchema>
export type ComponentSearch = z.infer<typeof ComponentSearchSchema>
export type ComponentAdvancedSearch = z.infer<
  typeof ComponentAdvancedSearchSchema
>
export type ComponentAdvancedSearchResponse = z.infer<
  typeof ComponentAdvancedSearchResponseSchema
>
export type ComponentBulkCreate = z.infer<typeof ComponentBulkCreateSchema>
export type ComponentBulkUpdate = z.infer<typeof ComponentBulkUpdateSchema>
export type ComponentValidationResult = z.infer<
  typeof ComponentValidationResultSchema
>
export type ComponentStats = z.infer<typeof ComponentStatsSchema>

// Component type types
export type ComponentType = z.infer<typeof ComponentTypeBaseSchema>
export type ComponentTypeCreate = z.infer<typeof ComponentTypeCreateSchema>
export type ComponentTypeUpdate = z.infer<typeof ComponentTypeUpdateSchema>
export type ComponentTypeRead = z.infer<typeof ComponentTypeReadSchema>
export type ComponentTypePaginatedResponse = z.infer<
  typeof ComponentTypePaginatedResponseSchema
>
export type ComponentTypeSummary = z.infer<typeof ComponentTypeSummarySchema>
