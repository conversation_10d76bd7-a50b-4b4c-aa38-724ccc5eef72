/**
 * Zod schemas and TypeScript definitions for User and User Preference based on Pydantic schemas.
 */
import {
  BaseSchema,
  CreatePaginatedResponseSchema,
  TimestampMixinSchema,
} from "./schemas/baseSchemas"
import { z } from "zod"

const emailValidation = z.string().trim().email().transform((v) => v.toLowerCase())
const nameValidation = z.string().trim().min(3).max(50)

//========================= # USER # =========================//
//========================= # BASE # =========================//

export const UserBaseSchema = BaseSchema.extend({
  name: nameValidation,
  email: emailValidation,
  is_superuser: z.boolean().default(false),
})

//========================= # CRUD # =========================//

export const UserCreateSchema = UserBaseSchema.extend({
  password: z.string().min(8, "Password must be at least 8 characters long"),
  role: z.string().optional(),
  is_active: z.boolean().default(true),
})

export const UserUpdateSchema = z.object({
  name: nameValidation.optional(),
  email: emailValidation.optional(),
  password: z
    .string()
    .min(8, "Password must be at least 8 characters long")
    .optional(),
  role: z.string().optional(),
  is_active: z.boolean().optional(),
})

export const UserReadSchema = UserBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int(),
  is_active: z.boolean(),
  role: z.string().optional().nullable(),
  last_login: z.coerce.date().optional().nullable(),
})

//======================== # OTHER # ========================//

export const UserPaginatedResponseSchema =
  CreatePaginatedResponseSchema(UserReadSchema)

export const UserSummarySchema = BaseSchema.extend({
  id: z.number().int(),
  name: z.string(),
  email: z.email(),
  is_active: z.boolean(),
  role: z.string().optional().nullable(),
  last_login: z.coerce.date().optional().nullable(),
  created_at: z.coerce.date(),
})

export const UserSessionSchema = BaseSchema.extend({
  id: z.string(),
  user_id: z.number().int(),
  ip_address: z.string().optional().nullable(),
  user_agent: z.string().optional().nullable(),
  created_at: z.coerce.date(),
  last_activity: z.coerce.date(),
  expires_at: z.coerce.date(),
  is_active: z.boolean(),
})

//===================== # TYPE DEFINITIONS # =====================//

export type User = z.infer<typeof UserBaseSchema>
export type UserCreate = z.infer<typeof UserCreateSchema>
export type UserUpdate = z.infer<typeof UserUpdateSchema>
export type UserRead = z.infer<typeof UserReadSchema>
export type UserPaginatedResponse = z.infer<typeof UserPaginatedResponseSchema>
export type UserSummary = z.infer<typeof UserSummarySchema>
export type UserSession = z.infer<typeof UserSessionSchema>

//==================== # USER PREFERENCE # ====================//
//========================= # BASE # =========================//

export const UserPreferenceBaseSchema = BaseSchema.extend({
  theme: z.string().default("light"),
  language: z.string().default("en"),
  timezone: z.string().default("UTC"),
  date_format: z.string().default("YYYY-MM-DD"),
  time_format: z.string().default("24h"),
  units_system: z.string().default("metric"),
  notifications_enabled: z.boolean().default(true),
  auto_save_interval: z.number().int().default(300),
})

//========================= # CRUD # =========================//

export const UserPreferenceCreateSchema = UserPreferenceBaseSchema.extend({
  user_id: z.number().int(),
})

export const UserPreferenceUpdateSchema = UserPreferenceBaseSchema.partial()

export const UserPreferenceReadSchema = UserPreferenceBaseSchema.merge(
  TimestampMixinSchema
).extend({
  id: z.number().int(),
  user_id: z.number().int(),
})

//===================== # TYPE DEFINITIONS # =====================//

export type UserPreference = z.infer<typeof UserPreferenceBaseSchema>
export type UserPreferenceCreate = z.infer<typeof UserPreferenceCreateSchema>
export type UserPreferenceUpdate = z.infer<typeof UserPreferenceUpdateSchema>
export type UserPreferenceRead = z.infer<typeof UserPreferenceReadSchema>
