/**
 * Zod schemas and TypeScript definitions for Project based on Pydantic schemas.
 */
import {
  BaseSchema,
  CreatePaginatedResponseSchema,
  TimestampMixinSchema,
} from "./schemas/baseSchemas"
import {
  projectStatusSchema
} from "./api"
import { z } from "zod"

// ========================= # PROJECT # =========================
// ========================= # BASE # =========================

export const ProjectBaseSchema = BaseSchema.extend({
  name: z.string().trim().min(1).max(255).describe("Project name"),
  description: z.string().trim().max(2000).optional().nullable().describe("Project description"),
  status: projectStatusSchema.default("Draft").describe("Project status"),
  client: z.string().trim().max(255).optional().nullable().describe("Client name"),
  location: z.string().trim().max(255).optional().nullable().describe("Project location"),
  is_offline: z.boolean().default(false).describe("Whether the project is offline-only"),
  database_url: z.string().trim().optional().nullable().describe("Optional local database URL for project-specific data storage")
    .refine((val) => {
      if (!val) return true
      return val.startsWith("postgresql://") || val.startsWith("postgresql+asyncpg://")
    }, {
      message: "Database URL must use supported schemes: postgresql://, postgresql+asyncpg://"
    }),
})

// ========================= # CRUD # =========================

export const ProjectCreateSchema = ProjectBaseSchema.extend({
  project_number: z.string().trim().max(100).optional().nullable().describe("Project number/identifier"),
})

export const ProjectUpdateSchema = z.object({
  name: z.string().trim().min(1).max(255).optional().describe("Project name"),
  description: z.string().trim().max(2000).optional().nullable().describe("Project description"),
  status: projectStatusSchema.optional().describe("Project status"),
  client_name: z.string().trim().max(255).optional().nullable().describe("Client name"),
  client_contact: z.string().trim().max(500).optional().nullable().describe("Client contact information"),
  location: z.string().trim().max(255).optional().nullable().describe("Project location"),
  start_date: z.coerce.date().optional().nullable().describe("Project start date"),
  end_date: z.coerce.date().optional().nullable().describe("Project end date"),
  budget: z.number().min(0).optional().nullable().describe("Project budget"),
  currency: z.string().trim().min(1).max(10).optional().nullable().describe("Budget currency"),
  is_offline: z.boolean().optional().describe("Whether the project is offline-only"),
  database_url: z.string().trim().optional().nullable().describe("Optional local database URL for project-specific data storage")
    .refine((val) => {
      if (!val) return true
      return val.startsWith("postgresql://") || val.startsWith("postgresql+asyncpg://")
    }, {
      message: "Database URL must use supported schemes: postgresql://, postgresql+asyncpg://"
    }),
})

export const ProjectReadSchema = ProjectBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int().describe("Unique identifier"),
  project_number: z.string().describe("Project number/identifier"),
  members: z.array(z.any()).default([]).describe("Project members"),
})

// ========================= # OTHER # =========================

export const ProjectPaginatedResponseSchema = CreatePaginatedResponseSchema(ProjectReadSchema)

export const ProjectSummarySchema = BaseSchema.extend({
  id: z.number().int().describe("Unique identifier"),
  name: z.string().describe("Project name"),
  status: projectStatusSchema.describe("Project status"),
  client_name: z.string().optional().nullable().describe("Client name"),
  location: z.string().optional().nullable().describe("Project location"),
  start_date: z.coerce.date().optional().nullable().describe("Project start date"),
  end_date: z.coerce.date().optional().nullable().describe("Project end date"),
  budget: z.number().optional().nullable().describe("Project budget"),
  currency: z.string().optional().nullable().describe("Budget currency"),
  owner_id: z.number().int().describe("Project owner user ID"),
  created_at: z.coerce.date().describe("Creation timestamp"),
  updated_at: z.coerce.date().describe("Last update timestamp"),
})

// ===================== # TYPE DEFINITIONS # =====================

export type ProjectStatus = z.infer<typeof projectStatusSchema>
export type Project = z.infer<typeof ProjectBaseSchema>
export type ProjectCreate = z.infer<typeof ProjectCreateSchema>
export type ProjectUpdate = z.infer<typeof ProjectUpdateSchema>
export type ProjectRead = z.infer<typeof ProjectReadSchema>
export type ProjectPaginatedResponse = z.infer<typeof ProjectPaginatedResponseSchema>
export type ProjectSummary = z.infer<typeof ProjectSummarySchema>