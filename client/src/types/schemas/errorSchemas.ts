/**
 * Base Zod schemas that mirror the server's Pydantic error schemas.
 *
 * This module provides base Zod objects and schemas used throughout the
 * client-side application for consistent error handling,
 * reflecting the structure of `server/src/core/schemas/errors.py`.
 */
import { z } from "zod"

/**
 * Schema for standardized error responses.
 * Provides a consistent structure for all API error responses
 * including error codes, messages, and contextual information.
 */
export const errorResponseSchema = z.object({
  code: z.string().describe("Unique application-specific error code."),
  detail: z.string().describe("A human-readable explanation of the error."),
  category: z
    .string()
    .describe(
      "Category of the error (e.g., ClientError, ServerError, Validation)."
    ),
  status_code: z
    .number()
    .int()
    .describe("HTTP status code equivalent (for UI/API compatibility)."),
  metadata: z
    .record(z.string(), z.any())
    .optional()
    .nullable()
    .describe("Additional context or debugging information."),
})

/**
 * Schema for detailed validation error information.
 * Provides specific details about validation failures including
 * field locations, error types, and descriptive messages.
 */
export const validationErrorDetailSchema = z.object({
  loc: z
    .array(z.union([z.string(), z.number()]))
    .describe("Location of the validation error in the input data."),
  msg: z.string().describe("Validation error message."),
  type: z.string().describe("Type of validation error."),
})

/**
 * Schema for validation error responses with detailed error information.
 * Extends the base error response schema to include detailed validation
 * error information for client-side error handling and display.
 */
export const validationErrorsResponseSchema = errorResponseSchema.extend({
  validation_details: z
    .array(validationErrorDetailSchema)
    .describe("Detailed list of validation errors."),
})

export type ErrorResponse = z.infer<typeof errorResponseSchema>
export type ValidationErrorResponse = z.infer<
  typeof validationErrorsResponseSchema
>
export type ValidationErrorDetail = z.infer<typeof validationErrorDetailSchema>
