/**
 * Base Zod schemas that mirror the server's Pydantic base schemas.
 *
 * This module provides base Zod objects and schemas used throughout the
 * client-side application for consistent validation and type safety,
 * reflecting the structure of `server/src/core/schemas/base_schemas.py`.
 */
import { z } from "zod"

/**
 * Base schema with strict validation to prevent extra fields.
 * Approximates Pydantic's `extra="forbid"`.
 * String trimming is applied via `.trim()` on individual string schemas.
 */
export const BaseSchema = z.object({}).strict()

/**
 * Mixin for timestamp fields, mirroring Pydantic's `TimestampMixin`.
 */
export const TimestampMixinSchema = z.object({
  created_at: z.iso.datetime().describe("Creation timestamp"),
  updated_at: z.iso
    .datetime()
    .nullable()
    .optional()
    .describe("Last update timestamp"),
})

/**
 * Base schema with soft delete functionality.
 */
export const SoftDeleteSchema = BaseSchema.extend({
  is_deleted: z
    .boolean()
    .default(false)
    .describe("Whether the record is soft deleted"),
  deleted_at: z.iso
    .datetime()
    .nullable()
    .optional()
    .describe("Soft deletion timestamp"),
})

/**
 * Schema for pagination parameters.
 * Mirrors Pydantic's `PaginationSchema`.
 */
export const PaginationSchema = z.object({
  page: z.number().int().min(1).default(1).describe("Page number (1-based)"),
  size: z.number().int().min(1).max(100).default(20).describe("Items per page"),
  total: z.number().int().optional().describe("Total number of items"),
  pages: z.number().int().optional().describe("Total number of pages"),
})

/**
 * Generic paginated response schema.
 * @param itemSchema The Zod schema for the items in the list.
 */
export const CreatePaginatedResponseSchema = <T extends z.ZodTypeAny>(
  itemSchema: T
) =>
  z.object({
    items: z.array(itemSchema).describe("List of items"),
    pagination: PaginationSchema.describe("Pagination information"),
  })

// Generic paginated response with z.any() for cases where item schema is unknown
export const PaginatedResponseSchema = CreatePaginatedResponseSchema(z.any())
