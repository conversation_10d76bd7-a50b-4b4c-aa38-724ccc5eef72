import z from "zod"

/**
 * Filter operators for querying and manipulating data.
 */
export const filterOperatorSchema = z.enum([
  "eq",
  "ne",
  "gt",
  "gte",
  "lt",
  "lte",
  "contains",
  "starts_with",
  "ends_with",
  "in",
  "not_in",
  "between",
  "fuzzy",
  "regex",
  "is_null",
  "is_not_null",
])

/**
 * Logical operators for filtering and querying data.
 */
export const logicalOperatorSchema = z.enum(["and", "or", "not"])

export type FilterOperatorEnum = z.infer<typeof filterOperatorSchema>
export type LogicalOperatorEnum = z.infer<typeof logicalOperatorSchema>