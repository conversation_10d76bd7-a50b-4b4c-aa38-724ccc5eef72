/**
 * enumeration types for various engineering calculations
 * performed by the Ultimate Electrical Designer application.
 * It classifies the types of analyses (e.g., heat loss, cable sizing, short circuit),
 * defines calculation statuses, and outlines optimization objectives.
 * These enums are fundamental for managing the computational workflows and
 * interpreting the results of the design process.
 */
import { z } from "zod"

/**
 * Defines the types of engineering calculations performed by the application.
 * These are the *functions* or *analyses* that the system can perform.
 */
export const calculationTypeSchema = z.enum([
  "Heat Loss Calculation",
  "Heat Tracing Design Calculation", // More specific than just "Heat Tracing"
  "Cable Sizing Calculation",
  "Voltage Drop Calculation",
  "Short Circuit Calculation",
  "Load Flow Analysis",
  "Power Requirement Estimation",
  "Earthing System Design Calculation", // New, critical for electrical
  "Lighting Calculation",
  "Power Factor Correction Calculation",
  "Motor Starting Analysis",
  "Protection Coordination Study",
  "Arc Flash Study", // New, very important safety calculation
  "Thermal Performance Analysis", // For enclosures, equipment
  "Optimization Calculation", // More generic optimization
  "Standards Compliance Check", // Not a calculation, but a validation type
])

/**
 * Current status of a calculation run.
 */
export const calculationStatusSchema = z.enum([
  "Pending",
  "Running",
  "Completed",
  "Failed",
  "Aborted", // New: user stopped it
  "Queued", // New: waiting to run
])

/**
 * Objectives for optimization calculations.
 */
export const optimizationObjectiveSchema = z.enum([
  "Minimize Cost",
  "Maximize Efficiency",
  "Minimize Weight",
  "Minimize Space",
  "Balanced Performance/Cost",
  "Minimize Energy Consumption",
  "Maximize Reliability",
])

export type CalculationTypeEnum = z.infer<typeof calculationTypeSchema>
export type CalculationStatusEnum = z.infer<typeof calculationStatusSchema>
export type OptimizationObjectiveEnum = z.infer<typeof optimizationObjectiveSchema>