import { z } from "zod";

/**
 * enumeration types pertaining to data input/output
 * operations, reporting, and data mapping within the Ultimate Electrical Designer
 * application. It encompasses classifications for file formats, data types,
 * import/parse statuses, mapping methodologies, and various report documents
 * and templates. These enums are crucial for facilitating seamless data exchange
 * and customizable documentation generation.
 */

/**
 * Common file formats for data import/export.
 */
export const fileFormatSchema = z.enum([
  "CSV",
  "Excel (XLSX)",
  "JSON",
  "XML",
  "PDF",
  "AutoCAD Drawing (DWG)",
  "Drawing Exchange Format (DXF)",
  "Industry Foundation Classes (IFC)", // For BIM
  "Standard for the Exchange of Product model data (STEP)", // For 3D models
  "EPLAN P8 Export", // Specific electrical CAD format
  "Revit Model", // BIM
  "SQL Script",
  "AutoCAD Plant 3D", // For piping data
  "SmartPlant P&ID", // For P&ID data
  "Text (TXT)",
  "Markdown (MD)",
  "LaTeX",
  "DXF (R12)", // Specific DXF version sometimes needed
]);
/**
 * Fundamental data types used in data mapping.
 */
export const mappingDataTypeSchema = z.enum([
  "String",
  "Integer",
  "Float",
  "Boolean",
  "Date",
  "DateTime",
  "List",
  "Dictionary",
  "Enum", // For mapping to internal enums
]);
/**
 * Specific domain entities or types for which data might be imported/exported or mapped.
 * This provides high-level context for data operations.
 */
export const domainDataTypeSchema = z.enum([
  "Project",
  "Component",
  "Circuit",
  "Bus",
  "Cable",
  "Load",
  "Report",
  "Specification",
  "Calculation Result",
  "Bill of Materials (BOM)",
  "Drawing",
  "Document",
  "User",
  "Equipment", // Keep for historical or high-level reference if distinct from component
  "Pipe",
  "Valve",
  "Tank",
  "Sensor",
  "Motor",
  "Heat Tracing System",
  "Wiring",
  "Site",
  "Location",
  "System", // Generic system grouping
]);
/**
 * Status of a data import operation.
 */
export const importStatusSchema = z.enum([
  "Pending",
  "In Progress",
  "Completed",
  "Failed",
  "Partially Completed",
  "Cancelled",
  "Validating", // New: during data validation phase
]);
/**
 * Status of parsing a data file.
 */
export const parseStatusSchema = z.enum([
  "Success",
  "Partial Success",
  "Failed",
  "Invalid Format",
  "Empty Data",
  "Data Structure Mismatch", // New
]);
/**
 * Types of data mapping operations.
 */
export const mappingTypeSchema = z.enum([
  "Direct Mapping",
  "Calculated Mapping",
  "Conditional Mapping",
  "Lookup Mapping",
  "Transformation Mapping",
  "Aggregation Mapping",
  "Default Value Mapping", // New: setting a default if source is missing
]);
/**
 * Specific types of reports or documents generated by the system.
 */
export const reportDocumentTypeSchema = z.enum([
  "Single Line Diagram",
  "Schematic Diagram",
  "Cable Schedule",
  "Load Schedule",
  "Bill of Materials (BOM)",
  "Specification Sheet",
  "Datasheet",
  "Calculation Report",
  "Executive Summary",
  "Technical Report",
  "Safety Report",
  "Project Plan",
  "Maintenance Schedule",
  "Commissioning Report",
  "Hazardous Area Classification Report",
  "Grounding Design Report",
  "Arc Flash Report",
  "Piping & Instrumentation Diagram (P&ID)", // If the tool supports mechanical elements
  "Isometric Drawing",
  "General Arrangement Drawing (GA)",
  "3D Model",
]);
/**
 * Categories for report or document templates.
 */
export const templateCategorySchema = z.enum([
  "Standard Template",
  "Detailed Template",
  "Summary Template",
  "Custom Template",
  "Client Specific Template",
  "Regulatory Template", // For compliance docs
]);
/**
 * Engines used for rendering documents or reports.
 */
export const renderingEngineSchema = z.enum([
  "Jinja2", // Python templating
  "Mustache", // Logic-less templating
  "Simple Text", // Basic string formatting
  "Markdown",
  "LaTeX",
  "HTML/CSS", // For web-based reports
  "PDF Engine", // Generic PDF generation engine
]);
/**
 * Status of a report generation process.
 */
export const reportStatusSchema = z.enum([
  "Pending",
  "Generating",
  "Completed",
  "Failed",
  "Cancelled",
  "Regenerating", // New
]);
/**
 * Priority level for report generation.
 */
export const reportPrioritySchema = z.enum([
  "Low",
  "Normal",
  "High",
  "Urgent",
]);
/**
 * Time periods for trending data.
 */
export const trendPeriodSchema = z.enum([
  "Hourly",
  "Daily",
  "Weekly",
  "Monthly",
  "Quarterly",
  "Yearly",
  "Custom Range", // For user-defined periods
]);

export type FileFormatEnum = z.infer<typeof fileFormatSchema>;
export type MappingDataTypeEnum = z.infer<typeof mappingDataTypeSchema>;
export type DomainDataTypeEnum = z.infer<typeof domainDataTypeSchema>;
export type ImportStatusEnum = z.infer<typeof importStatusSchema>;
export type ParseStatusEnum = z.infer<typeof parseStatusSchema>;
export type MappingTypeEnum = z.infer<typeof mappingTypeSchema>;
export type ReportDocumentTypeEnum = z.infer<typeof reportDocumentTypeSchema>;
export type TemplateCategoryEnum = z.infer<typeof templateCategorySchema>;
export type RenderingEngineEnum = z.infer<typeof renderingEngineSchema>;
export type ReportStatusEnum = z.infer<typeof reportStatusSchema>;
export type ReportPriorityEnum = z.infer<typeof reportPrioritySchema>;
export type TrendPeriodEnum = z.infer<typeof trendPeriodSchema>;
