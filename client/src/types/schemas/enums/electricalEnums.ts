import { z } from "zod";

/**
 * comprehensive enumeration types specific to electrical
 * engineering design within the Ultimate Electrical Designer application.
 * It serves as the single source of truth for classifying electrical components,
 * system elements, circuit characteristics, load types, and design criteria.
 * The enums herein are critical for accurate data modeling, calculation inputs,
 * and report generation in electrical design.
 */

export const componentFunctionalCategorySchema = z.enum([
  "Power Generation",
  "Power Transmission",
  "Power Distribution",
  "Process Control",
  "Safety Instrumented System (SIS)",
  "Fire & Gas System (F&G)",
  "Lighting System",
  "Communication Network",
  "Motor Control",
  "Measurement",
  "Alarming",
  "HVAC Control",
  "Heat Tracing System",
  "Data Acquisition",
  "Remote Monitoring",
  "Other System Function",
]);

export const electricalNodeTypeSchema = z.enum([
  "Bus",
  "Load Center",
  "Connection Point",
  "Source",
  "Panel",
  "Switchgear",
  "Transformer Node",
]);

export const cableInstallationMethodSchema = z.enum([
  "Cable Tray",
  "Conduit",
  "Direct Burial",
  "Duct Bank",
  "Open Air",
  "Cable Ladder",
  "Raceway",
  "Cable Trough",
  "Clips and Cleats",
  "Riser",
  "In-Wall",
]);

export const circuitTypeSchema = z.enum([
  "Power Distribution Circuit",
  "Motor Circuit",
  "Heating Circuit",
  "Lighting Circuit",
  "Control Circuit",
  "Instrumentation Circuit",
  "Communication Circuit",
  "Safety Instrumented Circuit",
  "Heat Tracing Circuit",
  "Emergency Power Circuit",
  "Essential Power Circuit",
  "Critical Power Circuit",
  "Auxiliary Circuit",
  "UPS Supplied Circuit",
  "DC Power Circuit",
  "Telecommunications Circuit",
]);

export const loadTypeSchema = z.enum([
  "Motor Load",
  "Heating Load",
  "Lighting Load",
  "Electronic Load",
  "Resistive Load",
  "Inductive Load",
  "Capacitive Load",
  "Mixed Load",
  "Welding Load",
  "Rectifier Load",
  "Transformer Load",
  "UPS Load",
  "HVAC Load",
  "Compressor Load",
  "Pump Load",
  "Other Load",
]);

export const loadCriticalitySchema = z.enum([
  "Critical",
  "Essential",
  "Important",
  "Normal",
  "Non-Essential",
]);

export const electricalCableTypeSchema = z.enum([
  "Power Cable",
  "Control Cable",
  "Instrumentation Cable",
  "Communication Cable",
  "Fiber Optic Cable",
  "Coaxial Cable",
  "Flexible Cable",
  "Bare Conductor",
]);

export const electricalInsulationTypeSchema = z.enum([
  "Polyvinyl Chloride (PVC)",
  "Cross-Linked Polyethylene (XLPE)",
  "Ethylene Propylene Rubber (EPR)",
  "Mineral Insulated (MI)",
  "Teflon (PTFE)",
  "Silicone Rubber",
]);

export const conductorMaterialSchema = z.enum([
  "Copper",
  "Aluminum",
]);

export const voltageLevelSchema = z.enum([
  "Extra Low Voltage (ELV)",
  "Low Voltage (LV)",
  "Medium Voltage (MV)",
  "High Voltage (HV)",
  "Extra High Voltage (EHV)",
]);

export const cableSelectionCriteriaSchema = z.enum([
  "Current Carrying Capacity (Ampacity)",
  "Voltage Drop",
  "Short Circuit Withstand",
  "Temperature Rating",
  "Insulation Type",
  "Conductor Material",
  "Installation Method",
  "Environmental Conditions",
  "Cost",
  "Length",
  "Mechanical Strength",
  "Flexibility",
]);

export const protectionDeviceTypeSchema = z.enum([
  "Moulded Case Circuit Breaker (MCCB)",
  "Miniature Circuit Breaker (MCB)",
  "Air Circuit Breaker (ACB)",
  "Vacuum Circuit Breaker (VCB)",
  "Gas Circuit Breaker (GCB)",
  "Thermal-Magnetic Circuit Breaker",
  "Electronic Trip Circuit Breaker",
  "Earth Leakage Circuit Breaker (ELCB)",
  "Fuse Link",
  "High Rupturing Capacity (HRC) Fuse",
  "Semiconductor Fuse",
  "Time-Delay Fuse",
  "Instantaneous Trip Fuse",
  "RCBO (Residual Current Breaker with Overcurrent protection)",
  "Arc Fault Circuit Interrupter (AFCI)",
  "Ground Fault Circuit Interrupter (GFCI)",
  "Surge Arrester",
]);

export const switchboardFunctionSchema = z.enum([
  "Main Distribution",
  "Sub Distribution",
  "Motor Control",
  "Lighting Control",
  "Power Factor Correction",
  "Critical Power Distribution",
  "Essential Power Distribution",
  "General Power",
  "Instrumentation Distribution",
  "Control System Panel",
  "Building Management System (BMS) Panel",
]);

export const feederTypeSchema = z.enum([
  "Main Feeder",
  "Sub Feeder",
  "Branch Circuit",
  "Critical Feeder",
  "Emergency Feeder",
  "Standby Feeder",
  "Motor Feeder",
  "HVAC Feeder",
  "Lighting Feeder",
  "Utility Supply Feeder",
  "Distribution Feeder",
  "Control Feeder",
  "Instrumentation Feeder",
]);

export type ComponentFunctionalCategoryEnum = z.infer<typeof componentFunctionalCategorySchema>;
export type ElectricalNodeTypeEnum = z.infer<typeof electricalNodeTypeSchema>;
export type CableInstallationMethodEnum = z.infer<typeof cableInstallationMethodSchema>;
export type CircuitTypeEnum = z.infer<typeof circuitTypeSchema>;
export type LoadTypeEnum = z.infer<typeof loadTypeSchema>;
export type LoadCriticalityEnum = z.infer<typeof loadCriticalitySchema>;
export type ElectricalCableTypeEnum = z.infer<typeof electricalCableTypeSchema>;
export type ElectricalInsulationTypeEnum = z.infer<typeof electricalInsulationTypeSchema>;
export type ConductorMaterialEnum = z.infer<typeof conductorMaterialSchema>;
export type VoltageLevelEnum = z.infer<typeof voltageLevelSchema>;
export type CableSelectionCriteriaEnum = z.infer<typeof cableSelectionCriteriaSchema>;
export type ProtectionDeviceTypeEnum = z.infer<typeof protectionDeviceTypeSchema>;
export type SwitchboardFunctionEnum = z.infer<typeof switchboardFunctionSchema>;
export type FeederTypeEnum = z.infer<typeof feederTypeSchema>;
