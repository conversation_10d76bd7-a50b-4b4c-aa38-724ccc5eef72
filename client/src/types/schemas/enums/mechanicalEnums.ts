/**
 * enumeration types related to mechanical elements and
 * their properties that are relevant for integrated electrical design and
 * project management within the Ultimate Electrical Designer application.
 * It includes classifications for piping materials, thermal insulation,
 * tank types, structural supports, and valve types, facilitating cross-disciplinary
 * data consistency and contextual understanding for electrical installations.
 */

import { z } from "zod";

/**
 * Common materials used for piping.
 */
export const pipeMaterialTypeSchema = z.enum([
  "Carbon Steel",
  "Stainless Steel",
  "Copper",
  "Polyvinyl Chloride (PVC)",
  "High-Density Polyethylene (HDPE)",
  "Fiber Reinforced Plastic (FRP)", // Fiberglass
  "Galvanized Steel",
  "Ductile Iron",
]);
export type PipeMaterialType = z.infer<typeof pipeMaterialTypeSchema>;

/**
 * Common materials used for thermal insulation of pipes and equipment.
 */
export const thermalInsulationTypeSchema = z.enum([
  "Mineral Wool",
  "Fiberglass", // Renamed from Glass Wool for common usage
  "Calcium Silicate",
  "Foamed Glass",
  "Polyurethane Foam",
  "Elastomeric Foam",
  "Aerogel", // High performance insulation
]);
export type ThermalInsulationType = z.infer<typeof thermalInsulationTypeSchema>;

/**
 * Common geometries or classifications for tanks.
 */
export const tankTypeSchema = z.enum([
  "Vertical Cylindrical Tank",
  "Horizontal Cylindrical Tank",
  "Spherical Tank",
  "Rectangular Tank",
  "Cone Bottom Tank",
  "Dished Head Tank",
  "Open Top Tank",
  "Floating Roof Tank", // For large oil tanks
]);
export type TankType = z.infer<typeof tankTypeSchema>;

/**
 * Types of structural supports for equipment or piping.
 */
export const supportTypeSchema = z.enum([
  "Concrete Slab",
  "Skid", // Pre-fabricated modular support
  "Pipe Rack",
  "Structural Steel Frame",
  "Legs", // For vessels, smaller equipment
  "Hangers", // For pipes from overhead structures
  "Saddle Support", // For horizontal vessels/pipes
  "Spring Support", // For piping accommodating thermal expansion
  "Vibration Isolator", // For pumps, compressors
]);
export type SupportType = z.infer<typeof supportTypeSchema>;

/**
 * Common accessories or fittings found on tanks.
 */
export const tankAccessoryTypeSchema = z.enum([
  "Manhole",
  "Handhole",
  "Nozzle",
  "Vent",
  "Gauge (Level, Pressure, Temp)",
  "Relief Valve",
  "Agitator / Mixer",
  "Ladder",
  "Platform",
  "Heating Coil", // For internal heating
]);
export type TankAccessoryType = z.infer<typeof tankAccessoryTypeSchema>;

/**
 * Standard pipe schedules defining wall thickness.
 */
export const pipeScheduleSchema = z.enum([
  "SCH 10",
  "SCH 20",
  "SCH 30",
  "SCH 40",
  "SCH 60",
  "SCH 80",
  "SCH 100",
  "SCH 120",
  "SCH 140",
  "SCH 160",
  "Standard Weight", // Equivalent to SCH 40 for certain sizes
  "Extra Strong", // Equivalent to SCH 80 for certain sizes
  "Double Extra Strong",
]);
export type PipeSchedule = z.infer<typeof pipeScheduleSchema>;

/**
 * Common types of valves encountered in piping systems.
 */
export const valveTypeSchema = z.enum([
  "Ball Valve",
  "Gate Valve",
  "Globe Valve",
  "Check Valve",
  "Butterfly Valve",
  "Plug Valve",
  "Diaphragm Valve",
  "Pinch Valve",
  "Pressure Relief Valve (PRV)",
  "Control Valve", // For modulating flow
  "Solenoid Valve", // Electrically actuated
]);
export type ValveType = z.infer<typeof valveTypeSchema>;

/**
 * Common classifications of soil.
 */
export const soilTypeSchema = z.enum([
  "Clay",
  "Sand",
  "Silt",
  "Gravel",
  "Loam",
  "Bedrock",
  "Filled Ground", // Man-made fill
]);
export type SoilType = z.infer<typeof soilTypeSchema>;