import { z } from "zod"

export const eventTypeSchema = z.enum([
  "Project Created",
  "Project Updated",
  "Project Deleted",
  "Component Added",
  "Component Updated",
  "Component Deleted",
  "Circuit Created",
  "Circuit Updated",
  "Circuit Deleted",
  "Calculation Initiated",
  "Calculation Completed",
  "Calculation Failed",
  "Report Generated",
  "Data Imported",
  "Data Exported",
  "User Login",
  "User Logout",
  "User Created",
  "User Updated",
  "User Deleted",
  "Permission Changed",
  "System Error",
  "Validation Triggered",
  "Validation Completed",
  "BOM Generated",
  "Purchase Order Issued",
  "Item Received",
])

export const entityTypeSchema = z.enum([
  "Project",
  "Component",
  "Circuit",
  "User",
  "Report",
  "Calculation",
  "Bill of Materials",
  "Task",
  "Milestone",
  "Document",
  "Supplier",
  "Client",
  "System",
])

export const projectStatusSchema = z.enum([
  "Draft",
  "Active",
  "On Hold",
  "Completed",
  "Cancelled",
  "Archived",
  "Planning",
  "Design In Progress",
  "Procurement In Progress",
  "Construction In Progress",
  "Commissioning",
])

export const userRoleSchema = z.enum([
  "Administrator",
  "Project Manager",
  "Lead Engineer",
  "Electrical Engineer",
  "Automation Engineer",
  "Instrumentation Engineer",
  "CAD Operator",
  "Viewer",
  "Guest",
  "Client",
  "Supplier",
])

export const bomStatusSchema = z.enum([
  "Draft",
  "Under Review",
  "Approved",
  "Released for Procurement",
  "Obsolete",
  "Revised",
])

export const bomItemStatusSchema = z.enum([
  "Active",
  "Inactive",
  "Discontinued",
  "Substitute Available",
  "Pending Approval",
  "Ordered",
  "Received",
  "Installed",
])

export const procurementStatusSchema = z.enum([
  "Not Ordered",
  "Requested (Internal)",
  "Quote Pending",
  "Quote Received",
  "Order Placed",
  "In Transit",
  "Partially Received",
  "Completely Received",
  "Cancelled",
  "On Hold",
])

export const taskPrioritySchema = z.enum(["Low", "Medium", "High", "Critical"])

export const taskStatusSchema = z.enum([
  "Not Started",
  "In Progress",
  "On Hold",
  "Completed",
  "Blocked",
  "Overdue",
  "Review Pending",
  "Approved",
])

export const milestoneStatusSchema = z.enum([
  "Planned",
  "On Track",
  "At Risk",
  "Delayed",
  "Completed",
  "Cancelled",
])

export type EventTypeEnum = z.infer<typeof eventTypeSchema>
export type EntityTypeEnum = z.infer<typeof entityTypeSchema>
export type ProjectStatusEnum = z.infer<typeof projectStatusSchema>
export type UserRoleEnum = z.infer<typeof userRoleSchema>
export type BomStatusEnum = z.infer<typeof bomStatusSchema>
export type BomItemStatusEnum = z.infer<typeof bomItemStatusSchema>
export type ProcurementStatusEnum = z.infer<typeof procurementStatusSchema>
export type TaskPriorityEnum = z.infer<typeof taskPrioritySchema>
export type TaskStatusEnum = z.infer<typeof taskStatusSchema>
export type MilestoneStatusEnum = z.infer<typeof milestoneStatusSchema>
