/**
 * enumeration types for internal system operations,
 * error handling, monitoring, and data validation within the
 * Ultimate Electrical Designer application. It covers classifications for
 * error severity and context, monitoring metrics, and detailed validation
 * statuses and types. These enums are vital for maintaining system stability,
 * diagnosing issues, and ensuring data integrity and design quality.
 */

import { z } from "zod";

export const auditOperationSchema = z.enum([
  "Critical", // Note: there appears to be an error in the original enum where INSERT = "Critical"
  "Update",
  "Delete",
  "Soft Delete",
  "Restore",
]);
export type AuditOperationEnum = z.infer<typeof auditOperationSchema>;

export const errorSeveritySchema = z.enum([
  "Critical",
  "High",
  "Medium",
  "Low",
  "Info",
]);
export type ErrorSeverityEnum = z.infer<typeof errorSeveritySchema>;

export const errorContextSchema = z.enum([
  "API",
  "Service Layer",
  "Repository",
  "Calculation",
  "Middleware",
  "Security",
  "Database",
  "Calculation Engine",
  "Data Import/Export",
  "Report Generation",
  "Authentication",
  "Authorization",
  "Configuration",
  "External Integration",
  "Background Job",
  "Validation",
]);
export type ErrorContextEnum = z.infer<typeof errorContextSchema>;

export const monitoringContextSchema = z.enum([
  "Operation Performance",
  "Function Performance",
  "Service Method Performance",
  "Utility Performance",
  "API Performance",
  "Database Performance",
  "Repository Performance",
  "Calculation Performance",
  "Batch Processing Performance",
  "Middleware Performance",
  "CPU Usage",
  "Memory Usage",
  "Disk Usage",
  "Network Throughput",
  "Calculation Resource Usage",
  "User Activity",
  "Data Storage",
  "Service Latency",
]);
export type MonitoringContextEnum = z.infer<typeof monitoringContextSchema>;

export const metricTypeSchema = z.enum([
  "Execution Time",
  "Memory Usage",
  "CPU Usage",
  "Request Count",
  "Error Rate",
  "Disk I/O",
  "Network Latency",
  "Concurrent Users",
  "Data Volume",
  "Throughput",
]);
export type MetricTypeEnum = z.infer<typeof metricTypeSchema>;

export const validationSeveritySchema = z.enum([
  "Error",
  "Warning",
  "Info",
  "Critical Error",
]);
export type ValidationSeverityEnum = z.infer<typeof validationSeveritySchema>;

export const validationTypeSchema = z.enum([
  "Required Field Check",
  "Data Type Validation",
  "Range Check",
  "Format Check",
  "Uniqueness Validation",
  "Consistency Check (Cross-Field)",
  "Reference Integrity Check",
  "Business Rule Validation",
  "Standards Compliance Validation",
  "Design Rule Validation",
  "Unit Consistency",
  "Circuit Breaker Selection Validation",
  "Cable Sizing Validation",
  "Harmonic Distortion Validation",
]);
export type ValidationTypeEnum = z.infer<typeof validationTypeSchema>;

export const validationResultSchema = z.enum([
  "Passed",
  "Failed",
  "Warning",
  "Blocked",
  "Skipped",
]);
export type ValidationResultEnum = z.infer<typeof validationResultSchema>;

export const securityLevelSchema = z.enum([
  "Basic",
  "Standard",
  "Strict",
  "Custom",
  "Public",
  "Internal",
  "Confidential",
  "Sensitive",
  "Top Secret",
]);
export type SecurityLevelEnum = z.infer<typeof securityLevelSchema>;

export const syncStatusSchema = z.enum([
  "Pending",
  "In Progress",
  "Completed",
  "Failed",
  "Cancelled",
  "Retry Required",
]);
export type SyncStatusEnum = z.infer<typeof syncStatusSchema>;

export const syncDirectionSchema = z.enum([
  "Local to Central",
  "Central to Local",
  "Bidirectional",
]);
export type SyncDirectionEnum = z.infer<typeof syncDirectionSchema>;

export const syncOperationSchema = z.enum([
  "Full Sync",
  "Incremental Sync",
  "Manual Sync",
  "Scheduled Sync",
  "Conflict Resolution",
]);
export type SyncOperationEnum = z.infer<typeof syncOperationSchema>;

export const connectionQualitySchema = z.enum([
  "Unknown",
  "Slow",
  "Moderate",
  "Fast",
  "Offline",
]);
export type ConnectionQualityEnum = z.infer<typeof connectionQualitySchema>;