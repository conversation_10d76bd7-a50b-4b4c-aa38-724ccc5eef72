/**
 * common, non-domain-specific enumeration types
 * that are broadly applicable across various aspects of an engineering project.
 * These enums provide fundamental classifications for environments, units of measure,
 * and other universal concepts, ensuring consistency and reusability throughout
 * the Ultimate Electrical Designer application.
 */

import z from "zod";

export const installationEnvironmentSchema = z.enum([
  "Indoor",
  "Outdoor",
  "Hazardous Area",
  "Underground",
  "Submerged",
])

export const unitOfMeasureSchema = z.enum([
  // Electrical Units
  "V", "A", "Ω", "W", "kW", "MW", "VA", "kVA", "MVA", "VAR", "kVAR", "Hz", "F", "H", "J", "kWh",
  // Thermal Units
  "°C", "°F", "K", "W/m", "BTU/h",
  // Length Units
  "m", "ft", "in", "mm", "cm", "km", "mi",
  // Mass Units
  "kg", "lb", "g", "t",
  // Time Units
  "s", "min", "h", "d", "yr",
  // Pressure Units
  "Pa", "kPa", "MPa", "bar", "psi",
  // Flow Units
  "m³/h", "ft³/h", "L/h", "gpm", "m³/s",
  // Area Units
  "m²", "ft²", "in²", "cm²",
  // Volume Units
  "m³", "ft³", "L", "gal",
  // Temperature Difference Units
  "Δ°C", "Δ°F", "ΔK",
  // Power Density Units
  "W/m²", "BTU/h·ft²",
  // Thermal Conductivity Units
  "W/m·K", "BTU/h·ft·°F",
  // Thermal Resistance Units
  "K/W", "h·ft²·°F/BTU",
  // Thermal Capacitance Units
  "J/K", "BTU/°F",
])

export type InstallationEnvironmentEnum = z.infer<typeof installationEnvironmentSchema>
export type UnitOfMeasureEnum = z.infer<typeof unitOfMeasureSchema>