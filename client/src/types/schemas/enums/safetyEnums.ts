/**
 * enumeration types related to hazardous area classifications.
 * It includes classifications for ATEX directives (Temperatures, Zones, Gas Groups, Protection Concepts).
 * These enums are essential for ensuring design adherence to international and local
 * regulatory requirements.
 */

import { z } from "zod";

/**
 * Temperature classes for hazardous area equipment as per IEC/NEC standards.
 */
export const atexTemperatureClassSchema = z.enum([
  "T1 (<450°C)",
  "T2 (<300°C)",
  "T3 (<200°C)",
  "T4 (<135°C)",
  "T5 (<100°C)",
  "T6 (<85°C)",
  "Not Applicable",
]);
export type ATEXTemperatureClassEnum = z.infer<typeof atexTemperatureClassSchema>;

/**
 * ATEX classifications for explosive atmospheres (gas/vapor/mist).
 */
export const atexZoneSchema = z.enum([
  "Zone 0 (Gas: continuous/long periods)",
  "Zone 1 (Gas: likely during normal operation)",
  "Zone 2 (Gas: unlikely/short periods)",
  "Zone 20 (Dust: continuous/long periods)",
  "Zone 21 (Dust: likely during normal operation)",
  "Zone 22 (Dust: unlikely/short periods)",
  "Non-Hazardous Area",
]);
export type ATEXZoneEnum = z.infer<typeof atexZoneSchema>;

/**
 * ATEX gas groups based on explosion characteristics.
 */
export const atexGasGroupSchema = z.enum([
  "Group IIA (e.g., Propane)",
  "Group IIB (e.g., Ethylene)",
  "Group IIC (e.g., Hydrogen, Acetylene)",
  "Not Applicable",
]);
export type ATEXGasGroupEnum = z.infer<typeof atexGasGroupSchema>;

/**
 * Common ATEX protection concepts for equipment.
 */
export const atexProtectionConceptSchema = z.enum([
  "Ex 'd' - Flameproof Enclosure",
  "Ex 'e' - Increased Safety",
  "Ex 'i' - Intrinsic Safety",
  "Ex 'p' - Pressurized Enclosure",
  "Ex 'm' - Encapsulation",
  "Ex 'o' - Oil Immersion",
  "Ex 'q' - Powder Filling",
  "Ex 'n' - Non-Sparking (Zone 2/22 only)",
  "Ex 't' - Protection by Enclosure (Dust)",
  "Not Applicable",
]);
export type ATEXProtectionConceptEnum = z.infer<typeof atexProtectionConceptSchema>;