/**
 * enumeration types related to engineering standards,
 * regulations, and compliance aspects relevant to electrical design.
 * It includes classifications for standards and compliance levels.
 * These enums are essential for ensuring design adherence to international and local
 * regulatory requirements.
 */

import { z } from "zod";

/**
 * Relevant engineering standards and codes.
 */
export const engineeringStandardSchema = z.enum([
  "IEC",
  "IEEE",
  "EN",
  "SFS",
]);
export type EngineeringStandardEnum = z.infer<typeof engineeringStandardSchema>;

/**
 * Status of compliance with a standard or regulation.
 */
export const complianceLevelSchema = z.enum([
  "Compliant",
  "Non-Compliant",
  "Warning (Partial Compliance / Deviation)",
  "Not Applicable",
  "Unknown",
  "Pending Review",
]);
export type ComplianceLevelEnum = z.infer<typeof complianceLevelSchema>;
