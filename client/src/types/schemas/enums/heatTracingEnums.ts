/**
 * enumeration types specifically tailored for heat tracing
 * system design within the Ultimate Electrical Designer application.
 * It covers classifications for heating methods, circuit applications,
 * sensor types, and heat tracing cable categories. These enums ensure precise
 * modeling and calculation of heat tracing requirements for pipelines and equipment.
 */

import { z } from "zod";

/**
 * Methods used for applying heat tracing cable to a pipe or equipment.
 */
export const heatingMethodTypeSchema = z.enum([
  "Single Trace", // Formerly "Parallel"
  "Spiral Trace",
  "Double Trace", // Two cables side-by-side
  "Multiple Trace", // More than two
  "Pad Heater", // For tanks/vessels
  "Panel Heater", // For specific surface areas
  "Grid Heater", // For large flat surfaces, floor heating etc.
  "Tank Heater", // Specific for tanks, could be pad, immersion etc.
]);
export type HeatingMethodType = z.infer<typeof heatingMethodTypeSchema>;

/**
 * Primary application or purpose of a heat tracing circuit.
 */
export const htCircuitApplicationTypeSchema = z.enum([
  "Freeze Protection",
  "Process Temperature Maintenance",
  "Anti-Condensation",
  "Snow Melting",
  "Hot Water Maintenance",
]);
export type HTCircuitApplicationType = z.infer<typeof htCircuitApplicationTypeSchema>;

/**
 * Types of sensors commonly used in heat tracing systems.
 */
export const htSensorTypeSchema = z.enum([
  "Resistance Temperature Detector (RTD)",
  "Thermocouple",
  "Ambient Temperature Sensor",
  "Line Temperature Sensor", // Sensor on the pipe/equipment being traced
  "Pipe Wall Sensor", // Specific for measuring pipe wall temp
  "Thermistor", // Another type of temp sensor
]);
export type HTSensorType = z.infer<typeof htSensorTypeSchema>;

/**
 * Broad categories of heat tracing cables based on their core technology.
 * This replaces `HeatTracingCableType` for better categorization,
 * and `ComponentType.HEAT_TRACING_CABLE` should be the specific component.
 */
export const heatTracingCableCategorySchema = z.enum([
  "Self-Regulating Cable",
  "Constant Wattage Cable",
  "Mineral Insulated (MI) Cable",
  "Skin Effect Current Tracing (SECT)", // System, not just a cable type but relevant
  "Series Resistance Cable", // Less common now, but historically relevant
]);
export type HeatTracingCableCategory = z.infer<typeof heatTracingCableCategorySchema>;