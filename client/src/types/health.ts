/**
 * Zod schemas and TypeScript definitions for Health Check based on Pydantic schemas.
 */
import { BaseSchema } from "./schemas/baseSchemas"
import { z } from "zod"

// ========================= # DATABASE HEALTH # =========================

export const DatabaseHealthSchema = z.object({
  status: z.string().describe("Database connection status"),
  connection_responsive: z.boolean().describe("Whether database responds to queries"),
  connection_latency_ms: z.number().optional().nullable().describe("Database query latency in milliseconds"),
  pool_utilization: z.number().describe("Connection pool utilization percentage"),
  pool_metrics: z.record(z.string(), z.any()).describe("Detailed connection pool metrics"),
  health_score: z.number().int().min(0).max(10).describe("Overall database health score (0-10)"),
  recommendations: z.array(z.string()).default([]).describe("Health improvement recommendations"),
  database_type: z.string().describe("Type of database (postgresql)"),
  connection_error: z.string().optional().nullable().describe("Connection error message if any"),
})

// ========================= # SERVICE HEALTH # =========================

export const ServiceHealthSchema = z.object({
  name: z.string().describe("Service name"),
  status: z.string().describe("Service status (healthy, degraded, unhealthy)"),
  version: z.string().optional().nullable().describe("Service version"),
  uptime_seconds: z.number().optional().nullable().describe("Service uptime in seconds"),
  last_check: z.coerce.date().describe("Last health check timestamp"),
  details: z.record(z.string(), z.any()).optional().nullable().describe("Additional service details"),
})

// ========================= # SYSTEM METRICS # =========================

export const SystemMetricsSchema = z.object({
  memory_usage_mb: z.number().optional().nullable().describe("Current memory usage in MB"),
  cpu_usage_percent: z.number().optional().nullable().describe("Current CPU usage percentage"),
  disk_usage_percent: z.number().optional().nullable().describe("Current disk usage percentage"),
  active_connections: z.number().int().optional().nullable().describe("Number of active connections"),
  request_count_last_minute: z.number().int().optional().nullable().describe("Requests processed in last minute"),
})

// ========================= # HEALTH CHECK RESPONSES # =========================

export const HealthCheckResponseSchema = BaseSchema.extend({
  status: z.string().describe("Overall system status (healthy, degraded, unhealthy)"),
  timestamp: z.coerce.date().describe("Health check timestamp"),
  version: z.string().describe("Application version"),
  environment: z.string().describe("Current environment (development, production, etc.)"),
  uptime_seconds: z.number().describe("Application uptime in seconds"),
  
  // Detailed health information
  database: DatabaseHealthSchema.describe("Database health status"),
  services: z.array(ServiceHealthSchema).default([]).describe("Individual service health status"),
  system_metrics: SystemMetricsSchema.optional().nullable().describe("System performance metrics"),
  
  // Overall health indicators
  health_score: z.number().int().min(0).max(10).describe("Overall system health score (0-10)"),
  critical_issues: z.array(z.string()).default([]).describe("Critical issues requiring attention"),
  warnings: z.array(z.string()).default([]).describe("Non-critical warnings"),
  
  // Additional context
  checks_performed: z.array(z.string()).default([]).describe("List of health checks performed"),
  response_time_ms: z.number().optional().nullable().describe("Health check response time in milliseconds"),
})

export const SimpleHealthResponseSchema = BaseSchema.extend({
  status: z.string().describe("System status (healthy, unhealthy)"),
  timestamp: z.coerce.date().describe("Health check timestamp"),
  version: z.string().describe("Application version"),
  database_status: z.string().describe("Database connection status"),
  uptime_seconds: z.number().describe("Application uptime in seconds"),
})

// ===================== # TYPE DEFINITIONS # =====================

export type DatabaseHealth = z.infer<typeof DatabaseHealthSchema>
export type ServiceHealth = z.infer<typeof ServiceHealthSchema>
export type SystemMetrics = z.infer<typeof SystemMetricsSchema>
export type HealthCheckResponse = z.infer<typeof HealthCheckResponseSchema>
export type SimpleHealthResponse = z.infer<typeof SimpleHealthResponseSchema>