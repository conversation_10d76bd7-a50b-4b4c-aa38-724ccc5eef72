/**
 * Typescript interfaces for API operations. 
 */
export interface ApiEndpoints {
  auth: {
    login: string
    logout: string
    token: string
    changePassword: string
  }
  users: {
    list: string
    create: string
    read: string
    update: string
    delete: string
    me: string
    summary: string
  }
  components: {
    list: string
    create: string
    read: string
    update: string
    delete: string
    search: string
    advancedSearch: string
    searchBySpecifications: string
    suggestions: string
    preferred: string
    stats: string
    categories: string
    types: string
    bulkCreate: string
    bulkUpdate: string
    bulkDelete: string
    selectiveBulkUpdate: string
  }
  health: {
    check: string
  }
}

export interface ApiClientConfig {
  baseURL: string
  timeout?: number
  headers?: Record<string, string>
}

export interface RequestOptions {
  headers?: Record<string, string>
  timeout?: number
  signal?: AbortSignal
}
