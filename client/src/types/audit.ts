import {
  BaseSchema,
  TimestampMixinSchema,
} from "./schemas/baseSchemas"
import {
  auditOperationSchema,
  errorSeveritySchema
} from "./api"
import { z } from "zod"

//===================== # ACTIVITY LOG # =====================//
//========================= # BASE # =========================//

export const activityLogBaseSchema = z.object({
  action_type: z
    .string()
    .min(1)
    .max(100)
    .describe("Type of action")
    .transform((val) => val.trim().toUpperCase()),
  action_description: z
    .string()
    .min(1)
    .max(1000)
    .describe("Description of the action")
    .transform((val) => val.trim()),
  user_id: z
    .number()
    .int()
    .optional()
    .nullable()
    .describe("ID of the user who performed the action"),
  session_id: z
    .string()
    .max(255)
    .optional()
    .nullable()
    .describe("Session identifier"),
  target_type: z
    .string()
    .max(100)
    .optional()
    .nullable()
    .describe("Type of target entity"),
  target_id: z
    .number()
    .int()
    .optional()
    .nullable()
    .describe("ID of target entity"),
  target_name: z
    .string()
    .max(255)
    .optional()
    .nullable()
    .describe("Name of target entity"),
  request_method: z
    .string()
    .max(10)
    .optional()
    .nullable()
    .describe("HTTP request method"),
  request_path: z
    .string()
    .max(500)
    .optional()
    .nullable()
    .describe("Request path"),
  request_ip: z.string().optional().nullable().describe("IP address"),
  user_agent: z.string().max(500).optional().nullable().describe("User agent"),
  status: z
    .string()
    .max(50)
    .default("SUCCESS")
    .describe("Status of the action"),
  severity: errorSeveritySchema,
  metadata: z
    .record(z.string(), z.any())
    .optional()
    .nullable()
    .describe("Additional metadata"),
  error_message: z
    .string()
    .max(1000)
    .optional()
    .nullable()
    .describe("Error message if failed"),
  execution_time_ms: z
    .number()
    .int()
    .nonnegative()
    .optional()
    .nullable()
    .describe("Execution time in milliseconds"),
  category: z
    .string()
    .max(100)
    .optional()
    .nullable()
    .describe("Category for grouping"),
  tags: z
    .array(z.string())
    .optional()
    .nullable()
    .describe("Tags for categorization")
    .transform((val) =>
      val ? Array.from(new Set(val.map((t) => t.trim()).filter(Boolean))) : null
    ),
  is_security_related: z
    .boolean()
    .default(false)
    .describe("Whether this is security-related"),
  is_data_change: z
    .boolean()
    .default(false)
    .describe("Whether this involves data changes"),
  is_system_event: z
    .boolean()
    .default(false)
    .describe("Whether this is a system event"),
  notes: z.string().max(500).optional().nullable().describe("Additional notes"),
})

//========================= # CRUD # =========================//

export const activityLogCreateSchema = activityLogBaseSchema.extend({
  name: z
    .string()
    .min(1)
    .max(100)
    .describe("Log entry name")
    .transform((val) => val.trim()),
})

export const activityLogUpdateSchema = z.object({
  status: z.string().max(50).optional(),
  error_message: z.string().max(1000).optional(),
  execution_time_ms: z.number().int().nonnegative().optional(),
  notes: z.string().max(500).optional(),
})

export const activityLogResponseSchema = BaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int(),
  name: z.string(),
  user_id: z.number().int().optional().nullable(),
  session_id: z.string().optional().nullable(),
  action_type: z.string(),
  action_description: z.string(),
  target_type: z.string().optional().nullable(),
  target_id: z.number().int().optional().nullable(),
  target_name: z.string().optional().nullable(),
  request_method: z.string().optional().nullable(),
  request_path: z.string().optional().nullable(),
  request_ip: z.string().optional().nullable(),
  user_agent: z.string().optional().nullable(),
  status: z.string(),
  severity: errorSeveritySchema,
  metadata: z.record(z.string(), z.any()).optional().nullable(),
  error_message: z.string().optional().nullable(),
  execution_time_ms: z.number().int().optional().nullable(),
  category: z.string().optional().nullable(),
  tags: z.array(z.string()).optional().nullable(),
  is_security_related: z.boolean(),
  is_data_change: z.boolean(),
  is_system_event: z.boolean(),
  notes: z.string().optional().nullable(),
})

//========================= # SEARCH & FILTER # =========================//

export const activityLogFilterSchema = z.object({
  user_id: z.number().int().optional(),
  action_types: z.array(z.string()).optional(),
  categories: z.array(z.string()).optional(),
  severities: z.array(errorSeveritySchema).optional(),
  start_date: z.coerce.date().optional(),
  end_date: z.coerce.date().optional(),
  is_security_related: z.boolean().optional(),
  is_data_change: z.boolean().optional(),
  is_system_event: z.boolean().optional(),
  status: z.string().optional(),
  target_type: z.string().optional(),
  target_id: z.number().int().optional(),
  request_ip: z.string().optional(),
})

//===================== # TYPE DEFINITIONS # =====================//

export type ActivityLog = z.infer<typeof activityLogBaseSchema>
export type ActivityLogCreate = z.infer<typeof activityLogCreateSchema>
export type ActivityLogUpdate = z.infer<typeof activityLogUpdateSchema>
export type ActivityLogResponse = z.infer<typeof activityLogResponseSchema>
export type ActivityLogFilter = z.infer<typeof activityLogFilterSchema>

//====================== # AUDIT TRAIL # ======================//
//========================= # BASE # =========================//

export const auditTrailBaseSchema = z.object({
  table_name: z
    .string()
    .min(1)
    .max(100)
    .describe("Name of the table")
    .transform((val) => val.trim()),
  record_id: z.number().int().describe("ID of the record"),
  operation: auditOperationSchema,
  user_id: z
    .number()
    .int()
    .optional()
    .nullable()
    .describe("ID of the user who made the change"),
  activity_log_id: z
    .number()
    .int()
    .optional()
    .nullable()
    .describe("ID of related activity log"),
  field_name: z
    .string()
    .max(100)
    .optional()
    .nullable()
    .describe("Name of the changed field"),
  old_value: z
    .string()
    .max(1000)
    .optional()
    .nullable()
    .describe("Previous value"),
  new_value: z.string().max(1000).optional().nullable().describe("New value"),
  change_reason: z
    .string()
    .max(255)
    .optional()
    .nullable()
    .describe("Reason for the change"),
  change_context: z
    .record(z.string(), z.any())
    .optional()
    .nullable()
    .describe("Additional context"),
  is_sensitive: z
    .boolean()
    .default(false)
    .describe("Whether the change involves sensitive data"),
  is_system_change: z
    .boolean()
    .default(false)
    .describe("Whether this is a system change"),
  notes: z.string().max(500).optional().nullable().describe("Additional notes"),
})

//========================= # CRUD # =========================//

export const auditTrailCreateSchema = auditTrailBaseSchema.extend({
  name: z
    .string()
    .min(1)
    .max(100)
    .describe("Audit trail entry name")
    .transform((val) => val.trim()),
})

export const auditTrailUpdateSchema = z.object({
  change_reason: z.string().max(255).optional(),
  change_context: z.record(z.string(), z.any()).optional(),
  is_sensitive: z.boolean().optional(),
  notes: z.string().max(500).optional(),
})

export const auditTrailResponseSchema = BaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int(),
  name: z.string(),
  activity_log_id: z.number().int().optional().nullable(),
  user_id: z.number().int().optional().nullable(),
  changed_at: z.coerce.date(),
  table_name: z.string(),
  record_id: z.number().int(),
  operation: z.string(),
  field_name: z.string().optional().nullable(),
  old_value: z.string().optional().nullable(),
  new_value: z.string().optional().nullable(),
  change_reason: z.string().optional().nullable(),
  change_context: z.record(z.string(), z.any()).optional().nullable(),
  is_sensitive: z.boolean(),
  is_system_change: z.boolean(),
  notes: z.string().optional().nullable(),
})

//========================= # SEARCH & FILTER # =========================//

export const auditTrailFilterSchema = z.object({
  table_name: z.string().optional(),
  record_id: z.number().int().optional(),
  user_id: z.number().int().optional(),
  operations: z.array(z.string()).optional(),
  field_name: z.string().optional(),
  start_date: z.coerce.date().optional(),
  end_date: z.coerce.date().optional(),
  is_sensitive: z.boolean().optional(),
  is_system_change: z.boolean().optional(),
  activity_log_id: z.number().int().optional(),
})

//===================== # TYPE DEFINITIONS # =====================//

export type AuditTrail = z.infer<typeof auditTrailBaseSchema>
export type AuditTrailCreate = z.infer<typeof auditTrailCreateSchema>
export type AuditTrailUpdate = z.infer<typeof auditTrailUpdateSchema>
export type AuditTrailResponse = z.infer<typeof auditTrailResponseSchema>
export type AuditTrailFilter = z.infer<typeof auditTrailFilterSchema>

//========================= # OTHER # =========================//

export const auditSummarySchema = z.object({
  total_activities: z.number().int(),
  total_data_changes: z.number().int(),
  security_events: z.number().int(),
  system_events: z.number().int(),
  user_actions: z.number().int(),
  failed_operations: z.number().int(),
  top_action_types: z.array(z.record(z.string(), z.any())),
  top_users: z.array(z.record(z.string(), z.any())),
  activity_timeline: z.array(z.record(z.string(), z.any())),
})

export const recordHistorySchema = z.object({
  table_name: z.string(),
  record_id: z.number().int(),
  total_changes: z.number().int(),
  first_change: z.coerce.date(),
  last_change: z.coerce.date(),
  changes: z.array(auditTrailResponseSchema),
  change_summary: z.record(z.string(), z.number().int()),
})

//===================== # TYPE DEFINITIONS # =====================//

export type AuditSummary = z.infer<typeof auditSummarySchema>
export type RecordHistory = z.infer<typeof recordHistorySchema>

//===================== # USER ACTIVITY # =====================//
//========================= # BASE # =========================//

export const UserActivityLogSchema = BaseSchema.extend({
  id: z.number().int(),
  user_id: z.number().int(),
  action: z.string(),
  resource: z.string().optional().nullable(),
  ip_address: z.string().optional().nullable(),
  user_agent: z.string().optional().nullable(),
  timestamp: z.coerce.date(),
  success: z.boolean(),
  details: z
    .record(z.string(), z.any())
    .optional()
    .nullable()
    .describe("Activity Log Details"),
})

//========================= # OTHER # =========================//

export const userActivitySummarySchema = z.object({
  user_id: z.number().int(),
  total_activities: z.number().int(),
  recent_activities: z.array(activityLogResponseSchema),
  top_actions: z.array(z.record(z.string(), z.any())),
  security_events: z.number().int(),
  data_changes: z.number().int(),
  activity_timeline: z.array(z.record(z.string(), z.any())),
})

//===================== # TYPE DEFINITIONS # =====================//

export type UserActivityLog = z.infer<typeof UserActivityLogSchema>
export type UserActivitySummary = z.infer<typeof userActivitySummarySchema>
