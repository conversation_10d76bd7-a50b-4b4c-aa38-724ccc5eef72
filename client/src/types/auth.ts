/**
 * TypeScript definitions for Authorizational operations.
 */
import { UserReadSchema } from "./user"
import { z } from "zod"

const emailValidation = z.string().trim().email().transform((v) => v.toLowerCase())

//=================== # PASSWORD CHANGE # ===================//

export const PasswordResetRequestSchema = z.object({
  email: emailValidation,
})

export const PasswordResetConfirmSchema = z
  .object({
    token: z.string(),
    new_password: z.string().min(8),
    confirm_password: z.string(),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "New password and confirmation do not match",
    path: ["confirm_password"],
  })

export const PasswordResetResponseSchema = z.object({
  message: z.string(),
  reset_token_sent: z.boolean(),
  expires_at: z.coerce.date().optional().nullable(),
})

export const PasswordChangeRequestSchema = z
  .object({
    current_password: z.string(),
    new_password: z.string().min(8),
    confirm_password: z.string(),
  })
  .refine((data) => data.new_password === data.confirm_password, {
    message: "New password and confirmation do not match",
    path: ["confirm_password"],
  })

export const PasswordChangeResponseSchema = z.object({
  message: z.string().default("Password changed successfully"),
  changed_at: z.coerce.date(),
})

//===================== # TYPE DEFINITIONS # =====================//

export type PasswordResetRequest = z.infer<typeof PasswordResetRequestSchema>
export type PasswordResetConfirm = z.infer<typeof PasswordResetConfirmSchema>
export type PasswordResetResponse = z.infer<typeof PasswordResetResponseSchema>
export type PasswordChangeRequest = z.infer<typeof PasswordChangeRequestSchema>
export type PasswordChangeResponse = z.infer<
  typeof PasswordChangeResponseSchema
>

//=================== # LOGIN & LOGOUT # ===================//

export const LoginRequestSchema = z.object({
  username: z.string(),
  password: z.string(),
})

export const LoginResponseSchema = z.object({
  access_token: z.string(),
  token_type: z.string().default("bearer"),
  expires_in: z.number().int(),
  user: UserReadSchema,
})

export const LogoutResponseSchema = z.object({
  message: z.string().default("Successfully logged out"),
  logged_out_at: z.coerce.date(),
})

//===================== # TYPE DEFINITIONS # =====================//

export type LoginRequest = z.infer<typeof LoginRequestSchema>
export type LoginResponse = z.infer<typeof LoginResponseSchema>
export type LogoutResponse = z.infer<typeof LogoutResponseSchema>

//=================== # REGISTRATION # ===================//

export const RegisterRequestSchema = z.object({
  name: z.string(),
  email: z.email(),
  password: z.string(),
  confirm_password: z.string(),
})

export const RegisterResponseSchema = z.object({
  user: UserReadSchema,
  message: z.string(),
  created_at: z.coerce.date(),
})

//===================== # TYPE DEFINITIONS # =====================//

export type RegisterRequest = z.infer<typeof RegisterRequestSchema>
export type RegisterResponse = z.infer<typeof RegisterResponseSchema>
