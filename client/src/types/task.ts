/**
 * Zod schemas and TypeScript definitions for Task Management based on Pydantic schemas.
 * 
 * This file provides 100% type safety alignment with the backend task management schemas,
 * ensuring strict compliance with the backend API contracts.
 */
import { z } from "zod"
import {
  BaseSchema,
  CreatePaginatedResponseSchema,
  TimestampMixinSchema,
} from "./schemas/baseSchemas"
import {
  taskPrioritySchema,
  taskStatusSchema,
} from "./schemas/enums/projectManagementEnums"

// ========================= # TASK ASSIGNMENT # =========================

export const TaskAssignmentBaseSchema = BaseSchema.extend({
  user_id: z.number().int().positive().describe("ID of the assigned user"),
  is_active: z.boolean().default(true).describe("Whether the assignment is active"),
})

export const TaskAssignmentCreateSchema = TaskAssignmentBaseSchema.extend({
  assigned_by_user_id: z.number().int().positive().optional().nullable().describe("ID of the user who made the assignment"),
})

export const TaskAssignmentReadSchema = TaskAssignmentBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int().positive().describe("Unique assignment identifier"),
  task_id: z.number().int().positive().describe("ID of the assigned task"),
  assigned_at: z.coerce.date().describe("When the assignment was made"),
  assigned_by_user_id: z.number().int().positive().optional().nullable().describe("ID of the user who made the assignment"),
})

// ========================= # TASK # =========================

export const TaskBaseSchema = BaseSchema.extend({
  title: z.string().trim().min(1).max(255).describe("Task title"),
  description: z.string().trim().max(2000).optional().nullable().describe("Detailed task description"),
  due_date: z.coerce.date().optional().nullable().describe("Optional due date for the task"),
  priority: taskPrioritySchema.default("Medium").describe("Task priority level"),
  status: taskStatusSchema.default("Not Started").describe("Current task status"),
})

export const TaskCreateSchema = TaskBaseSchema.extend({
  project_id: z.number().int().positive().describe("ID of the project this task belongs to"),
  assigned_user_ids: z.array(z.number().int().positive()).optional().nullable().describe("List of user IDs to assign to this task"),
})

export const TaskUpdateSchema = z.object({
  title: z.string().trim().min(1).max(255).optional().describe("Task title"),
  description: z.string().trim().max(2000).optional().nullable().describe("Detailed task description"),
  due_date: z.coerce.date().optional().nullable().describe("Optional due date for the task"),
  priority: taskPrioritySchema.optional().describe("Task priority level"),
  status: taskStatusSchema.optional().describe("Current task status"),
})

export const TaskReadSchema = TaskBaseSchema.merge(TimestampMixinSchema).extend({
  id: z.number().int().positive().describe("Unique task identifier"),
  task_id: z.string().uuid().describe("Unique UUID identifier for the task"),
  project_id: z.number().int().positive().describe("ID of the project this task belongs to"),
  assignments: z.array(TaskAssignmentReadSchema).default([]).describe("List of user assignments for this task"),
})

export const TaskSummarySchema = BaseSchema.extend({
  id: z.number().int().positive().describe("Unique task identifier"),
  task_id: z.string().uuid().describe("Unique UUID identifier for the task"),
  title: z.string().describe("Task title"),
  status: taskStatusSchema.describe("Current task status"),
  priority: taskPrioritySchema.describe("Task priority level"),
  due_date: z.coerce.date().optional().nullable().describe("Optional due date for the task"),
  project_id: z.number().int().positive().describe("ID of the project this task belongs to"),
  assigned_user_count: z.number().int().min(0).default(0).describe("Number of users assigned to this task"),
  created_at: z.coerce.date().describe("Creation timestamp"),
})

// ========================= # TASK OPERATIONS # =========================

export const TaskAssignmentRequestSchema = BaseSchema.extend({
  user_ids: z.array(z.number().int().positive()).min(1).describe("List of user IDs to assign"),
})

export const TaskUnassignmentRequestSchema = BaseSchema.extend({
  user_id: z.number().int().positive().describe("ID of the user to unassign"),
})

export const TaskStatusUpdateSchema = BaseSchema.extend({
  status: taskStatusSchema.describe("New task status"),
})

export const TaskStatisticsSchema = BaseSchema.extend({
  total_tasks: z.number().int().min(0).describe("Total number of tasks"),
  status_counts: z.record(z.string(), z.number().int().min(0)).describe("Count of tasks by status"),
  priority_counts: z.record(z.string(), z.number().int().min(0)).describe("Count of tasks by priority"),
  overdue_count: z.number().int().min(0).describe("Number of overdue tasks"),
})

export const TaskSearchRequestSchema = BaseSchema.extend({
  search_term: z.string().trim().min(1).max(255).describe("Search term"),
  project_id: z.number().int().positive().optional().nullable().describe("Optional project ID filter"),
})

// ========================= # RESPONSE SCHEMAS # =========================

export const TaskListResponseSchema = CreatePaginatedResponseSchema(TaskReadSchema)

// ========================= # TYPE EXPORTS # =========================

export type TaskAssignmentBase = z.infer<typeof TaskAssignmentBaseSchema>
export type TaskAssignmentCreate = z.infer<typeof TaskAssignmentCreateSchema>
export type TaskAssignmentRead = z.infer<typeof TaskAssignmentReadSchema>

export type TaskBase = z.infer<typeof TaskBaseSchema>
export type TaskCreate = z.infer<typeof TaskCreateSchema>
export type TaskUpdate = z.infer<typeof TaskUpdateSchema>
export type TaskRead = z.infer<typeof TaskReadSchema>
export type TaskSummary = z.infer<typeof TaskSummarySchema>

export type TaskAssignmentRequest = z.infer<typeof TaskAssignmentRequestSchema>
export type TaskUnassignmentRequest = z.infer<typeof TaskUnassignmentRequestSchema>
export type TaskStatusUpdate = z.infer<typeof TaskStatusUpdateSchema>
export type TaskStatistics = z.infer<typeof TaskStatisticsSchema>
export type TaskSearchRequest = z.infer<typeof TaskSearchRequestSchema>

export type TaskListResponse = z.infer<typeof TaskListResponseSchema>

// ========================= # CONVENIENCE TYPES # =========================

/**
 * Task with optional assignments for display purposes
 */
export type TaskWithAssignments = TaskRead & {
  assignedUsers?: Array<{
    id: number
    name: string
    email: string
  }>
}

/**
 * Task form data for creation/editing
 */
export type TaskFormData = Omit<TaskCreate, 'project_id'> & {
  project_id?: number
}

/**
 * Task filter options for listing
 */
export type TaskFilters = {
  status?: string[]
  priority?: string[]
  assigned_user_id?: number
  due_date_from?: Date
  due_date_to?: Date
  search?: string
}

/**
 * Task sort options
 */
export type TaskSortOptions = {
  field: 'title' | 'status' | 'priority' | 'due_date' | 'created_at' | 'updated_at'
  order: 'asc' | 'desc'
}
