"use client"

import * as React from "react"

import { LayoutProvider } from "@/hooks/useLayout"

import { SidebarProvider } from "@/components/ui/sidebar"
import { ActiveThemeProvider } from "@/components/active-theme"
import { ThemeProvider } from "@/components/theme-provider"

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider>
      <LayoutProvider>
        <ActiveThemeProvider>
          <SidebarProvider>{children}</SidebarProvider>
        </ActiveThemeProvider>
      </LayoutProvider>
    </ThemeProvider>
  )
}
