"use client"

/**
 * ComponentStats - Statistics dashboard for component management
 * Displays key metrics and analytics about components
 */
import {
  BarChart3,
  Building,
  DollarSign,
  Package,
  Star,
  Tag,
  TrendingUp,
} from "lucide-react"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

import { useComponentStats } from "@/modules/components/api/componentQueries"
import { formatPrice } from "@/modules/components/utils"

export interface ComponentStatsProps {
  className?: string
}

export function ComponentStats({ className = "" }: ComponentStatsProps) {
  const { data: stats, isLoading, error } = useComponentStats()

  if (isLoading) {
    return (
      <div
        className={`grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4 ${className}`}
      >
        {Array.from({ length: 8 }).map((_, index) => (
          <Card key={index}>
            <CardHeader className="pb-2">
              <div className="h-4 w-24 animate-pulse rounded bg-gray-200" />
            </CardHeader>
            <CardContent>
              <div className="mb-2 h-8 w-16 animate-pulse rounded bg-gray-200" />
              <div className="h-3 w-20 animate-pulse rounded bg-gray-200" />
            </CardContent>
          </Card>
        ))}
      </div>
    )
  }

  if (error || !stats) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="mb-4 text-red-500">
              <BarChart3 className="mx-auto h-12 w-12" />
            </div>
            <h3 className="mb-2 text-lg font-medium text-gray-900">
              Unable to load statistics
            </h3>
            <p className="text-gray-600">
              {error?.message ||
                "An error occurred while loading component statistics."}
            </p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const StatCard = ({
    title,
    value,
    icon: Icon,
    description,
    trend,
  }: {
    title: string
    value: string | number
    icon: any
    description?: string
    trend?: string
  }) => (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium text-gray-600">
          {title}
        </CardTitle>
        <Icon className="h-4 w-4 text-gray-400" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold text-gray-900">{value}</div>
        {description && (
          <p className="mt-1 text-xs text-gray-600">{description}</p>
        )}
        {trend && (
          <div className="mt-2 flex items-center">
            <TrendingUp className="mr-1 h-3 w-3 text-green-500" />
            <span className="text-xs text-green-600">{trend}</span>
          </div>
        )}
      </CardContent>
    </Card>
  )

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Overview Stats */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4">
        <StatCard
          title="Total Components"
          value={stats.total_components.toLocaleString("en-US")}
          icon={Package}
          description="All components in catalog"
        />

        <StatCard
          title="Active Components"
          value={stats.active_components.toLocaleString("en-US")}
          icon={Package}
          description={`${Math.round((stats.active_components / stats.total_components) * 100)}% of total`}
        />

        <StatCard
          title="Preferred Components"
          value={stats.preferred_components.toLocaleString("en-US")}
          icon={Star}
          description={`${Math.round((stats.preferred_components / stats.total_components) * 100)}% of total`}
        />

        <StatCard
          title="Inactive Components"
          value={stats.inactive_components.toLocaleString("en-US")}
          icon={Package}
          description={`${Math.round((stats.inactive_components / stats.total_components) * 100)}% of total`}
        />
      </div>

      {/* Price Statistics */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <StatCard
          title="Average Price"
          value={(() => {
            try {
              return formatPrice(stats.price_range.average, "EUR")
            } catch {
              return "N/A"
            }
          })()}
          icon={DollarSign}
          description="Average component price"
        />

        <StatCard
          title="Minimum Price"
          value={(() => {
            try {
              return formatPrice(stats.price_range.min, "EUR")
            } catch {
              return "N/A"
            }
          })()}
          icon={DollarSign}
          description="Lowest priced component"
        />

        <StatCard
          title="Maximum Price"
          value={(() => {
            try {
              return formatPrice(stats.price_range.max, "EUR")
            } catch {
              return "N/A"
            }
          })()}
          icon={DollarSign}
          description="Highest priced component"
        />
      </div>

      {/* Category and Type Breakdown */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Tag className="h-5 w-5" />
              Components by Category
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(stats.by_category)
                .sort(([, a], [, b]) => (b as number) - (a as number))
                .slice(0, 8)
                .map(([category, count]) => (
                  <div
                    key={category}
                    className="flex items-center justify-between"
                  >
                    <span className="mr-2 flex-1 truncate text-sm text-gray-700">
                      {category}
                    </span>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-20 rounded-full bg-gray-200">
                        <div
                          className="h-2 rounded-full bg-blue-500"
                          style={{
                            width: `${Math.min(((count as number) / Math.max(...Object.values(stats.by_category) as number[])) * 100, 100)}%`,
                          }}
                        />
                      </div>
                      <span className="w-8 text-right text-sm font-medium text-gray-900">
                        {count as number}
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Top Manufacturers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(stats.by_manufacturer)
                .sort(([, a], [, b]) => (b as number) - (a as number))
                .slice(0, 8)
                .map(([manufacturer, count]) => (
                  <div
                    key={manufacturer}
                    className="flex items-center justify-between"
                  >
                    <span className="mr-2 flex-1 truncate text-sm text-gray-700">
                      {manufacturer}
                    </span>
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-20 rounded-full bg-gray-200">
                        <div
                          className="h-2 rounded-full bg-green-500"
                          style={{
                            width: `${Math.min(((count as number) / Math.max(...Object.values(stats.by_manufacturer) as number[])) * 100, 100)}%`,
                          }}
                        />
                      </div>
                      <span className="w-8 text-right text-sm font-medium text-gray-900">
                        {count as number}
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
