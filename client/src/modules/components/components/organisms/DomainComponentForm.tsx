"use client"

/**
 * Domain-Integrated Component Form
 *
 * Enhanced form component that integrates domain validation,
 * business rules, and rich error handling with professional UI patterns.
 */
import React from "react"

import type { Component } from "@/modules/components/hooks"

import { Alert<PERSON>riangle, Info, Loader2, Save, X } from "lucide-react"

import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Checkbox } from "@/components/ui/checkbox"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"

import { useDomainComponentForm } from "@/modules/components/hooks/useDomainComponentForm"

export interface DomainComponentFormProps {
  component?: Component
  onSuccess?: (component: Component) => void
  onCancel?: () => void
  className?: string
}

export function DomainComponentForm({
  component,
  onSuccess,
  onCancel,
  className = "",
}: DomainComponentFormProps) {
  const {
    data,
    errors,
    warnings,
    isDirty,
    isValid,
    isSubmitting,
    validationResult,
    updateField,
    handleFieldBlur,
    handleSubmit,
    resetForm,
    getFieldError,
    getFieldWarning,
    isFieldTouched,
    mode,
  } = useDomainComponentForm({
    component,
    onSuccess,
    onError: (error) => {
      console.error("Form submission error:", error)
    },
  })

  const isEditing = mode === "edit"

  // Mock data for dropdowns (in real app, these would come from API/domain)
  const categories = [
    { id: 1, name: "Resistors", code: "RESISTOR" },
    { id: 2, name: "Capacitors", code: "CAPACITOR" },
    { id: 3, name: "Switches", code: "SWITCH" },
    { id: 4, name: "Connectors", code: "CONNECTOR" },
    { id: 5, name: "Sensors", code: "SENSOR" },
    { id: 6, name: "Relays", code: "RELAY" },
  ]

  const componentTypes = [
    { id: 1, name: "Resistor", code: "resistor" },
    { id: 2, name: "Capacitor", code: "capacitor" },
    { id: 3, name: "Switch", code: "switch" },
    { id: 4, name: "Connector", code: "connector" },
    { id: 5, name: "Sensor", code: "sensor" },
    { id: 6, name: "Relay", code: "relay" },
  ]

  const stockStatuses = [
    { value: "available", label: "Available" },
    { value: "limited", label: "Limited Stock" },
    { value: "out_of_stock", label: "Out of Stock" },
    { value: "discontinued", label: "Discontinued" },
    { value: "on_order", label: "On Order" },
  ]

  const currencies = [
    { value: "EUR", label: "EUR (€)" },
    { value: "USD", label: "USD ($)" },
    { value: "GBP", label: "GBP (£)" },
    { value: "JPY", label: "JPY (¥)" },
  ]

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle data-testid="domain-form-title">
          {isEditing ? "Edit Component" : "Add Component"}
        </CardTitle>

        {/* Validation Summary */}
        {validationResult && !validationResult.isValid && errors.length > 0 && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Please fix the following errors before submitting:
              <ul className="mt-2 list-inside list-disc">
                {errors.slice(0, 3).map((error, index) => (
                  <li key={index} className="text-sm">
                    {error.message}
                  </li>
                ))}
                {errors.length > 3 && (
                  <li className="text-sm">
                    ... and {errors.length - 3} more error
                    {errors.length - 3 !== 1 ? "s" : ""}
                  </li>
                )}
              </ul>
            </AlertDescription>
          </Alert>
        )}

        {/* Warnings */}
        {warnings.length > 0 && (
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <div className="flex flex-wrap gap-1">
                {warnings.map((warning, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="text-yellow-600"
                  >
                    {warning.message}
                  </Badge>
                ))}
              </div>
            </AlertDescription>
          </Alert>
        )}
      </CardHeader>

      <CardContent>
        <form
          onSubmit={handleSubmit}
          className="space-y-6"
          role="form"
          data-testid="domain-component-form"
        >
          {/* Basic Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Basic Information</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {/* Component Name */}
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm font-medium">
                  Component Name *
                </Label>
                <Input
                  id="name"
                  type="text"
                  value={data.name || ""}
                  onChange={(e) => updateField("name", e.target.value)}
                  onBlur={() => handleFieldBlur("name")}
                  className={getFieldError("name") ? "border-red-500" : ""}
                  placeholder="Enter component name"
                  aria-required="true"
                  aria-invalid={!!getFieldError("name")}
                  data-testid="domain-form-name"
                />
                {getFieldError("name") && (
                  <p
                    className="text-sm text-red-500"
                    data-testid="domain-error-name"
                  >
                    {getFieldError("name")}
                  </p>
                )}
                {getFieldWarning("name") && (
                  <p
                    className="text-sm text-yellow-600"
                    data-testid="domain-warning-name"
                  >
                    {getFieldWarning("name")}
                  </p>
                )}
              </div>

              {/* Manufacturer */}
              <div className="space-y-2">
                <Label htmlFor="manufacturer" className="text-sm font-medium">
                  Manufacturer *
                </Label>
                <Input
                  id="manufacturer"
                  type="text"
                  value={data.manufacturer || ""}
                  onChange={(e) => updateField("manufacturer", e.target.value)}
                  onBlur={() => handleFieldBlur("manufacturer")}
                  className={
                    getFieldError("manufacturer") ? "border-red-500" : ""
                  }
                  placeholder="Enter manufacturer"
                  aria-required="true"
                  data-testid="domain-form-manufacturer"
                />
                {getFieldError("manufacturer") && (
                  <p className="text-sm text-red-500">
                    {getFieldError("manufacturer")}
                  </p>
                )}
              </div>

              {/* Model Number */}
              <div className="space-y-2">
                <Label htmlFor="model_number" className="text-sm font-medium">
                  Model Number *
                </Label>
                <Input
                  id="model_number"
                  type="text"
                  value={data.model_number || ""}
                  onChange={(e) => updateField("model_number", e.target.value)}
                  onBlur={() => handleFieldBlur("model_number")}
                  className={
                    getFieldError("model_number") ? "border-red-500" : ""
                  }
                  placeholder="Enter model number"
                  aria-required="true"
                  data-testid="domain-form-model-number"
                />
                {getFieldError("model_number") && (
                  <p className="text-sm text-red-500">
                    {getFieldError("model_number")}
                  </p>
                )}
              </div>

              {/* Part Number */}
              <div className="space-y-2">
                <Label htmlFor="part_number" className="text-sm font-medium">
                  Part Number
                </Label>
                <Input
                  id="part_number"
                  type="text"
                  value={data.part_number || ""}
                  onChange={(e) => updateField("part_number", e.target.value)}
                  onBlur={() => handleFieldBlur("part_number")}
                  placeholder="Enter part number"
                  data-testid="domain-form-part-number"
                />
              </div>
            </div>

            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description" className="text-sm font-medium">
                Description
              </Label>
              <Textarea
                id="description"
                value={data.description || ""}
                onChange={(e) => updateField("description", e.target.value)}
                onBlur={() => handleFieldBlur("description")}
                placeholder="Enter component description"
                rows={3}
                data-testid="domain-form-description"
              />
            </div>
          </div>

          {/* Classification */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Classification</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {/* Category */}
              <div className="space-y-2">
                <Label htmlFor="category_id" className="text-sm font-medium">
                  Category
                </Label>
                <Select
                  value={data.category_id?.toString() || ""}
                  onValueChange={(value) =>
                    updateField("category_id", value ? Number(value) : null)
                  }
                >
                  <SelectTrigger data-testid="domain-form-category">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem
                        key={category.id}
                        value={category.id.toString()}
                      >
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Component Type */}
              <div className="space-y-2">
                <Label
                  htmlFor="component_type_id"
                  className="text-sm font-medium"
                >
                  Component Type
                </Label>
                <Select
                  value={data.component_type_id?.toString() || ""}
                  onValueChange={(value) =>
                    updateField(
                      "component_type_id",
                      value ? Number(value) : null
                    )
                  }
                >
                  <SelectTrigger data-testid="domain-form-type">
                    <SelectValue placeholder="Select component type" />
                  </SelectTrigger>
                  <SelectContent>
                    {componentTypes.map((type) => (
                      <SelectItem key={type.id} value={type.id.toString()}>
                        {type.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Pricing and Stock */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Pricing & Stock</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
              {/* Unit Price */}
              <div className="space-y-2">
                <Label htmlFor="unit_price" className="text-sm font-medium">
                  Unit Price
                </Label>
                <Input
                  id="unit_price"
                  type="number"
                  step="0.01"
                  min="0"
                  value={data.unit_price || ""}
                  onChange={(e) =>
                    updateField(
                      "unit_price",
                      e.target.value ? parseFloat(e.target.value) : null
                    )
                  }
                  onBlur={() => handleFieldBlur("unit_price")}
                  placeholder="0.00"
                  data-testid="domain-form-price"
                />
                {getFieldError("unit_price") && (
                  <p className="text-sm text-red-500">
                    {getFieldError("unit_price")}
                  </p>
                )}
              </div>

              {/* Currency */}
              <div className="space-y-2">
                <Label htmlFor="currency" className="text-sm font-medium">
                  Currency
                </Label>
                <Select
                  value={data.currency || "EUR"}
                  onValueChange={(value) => updateField("currency", value)}
                >
                  <SelectTrigger data-testid="domain-form-currency">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {currencies.map((currency) => (
                      <SelectItem key={currency.value} value={currency.value}>
                        {currency.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Stock Status */}
              <div className="space-y-2">
                <Label htmlFor="stock_status" className="text-sm font-medium">
                  Stock Status
                </Label>
                <Select
                  value={data.stock_status || "available"}
                  onValueChange={(value) => updateField("stock_status", value)}
                >
                  <SelectTrigger data-testid="domain-form-stock-status">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {stockStatuses.map((status) => (
                      <SelectItem key={status.value} value={status.value}>
                        {status.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Additional Details */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Additional Details</h3>

            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              {/* Supplier */}
              <div className="space-y-2">
                <Label htmlFor="supplier" className="text-sm font-medium">
                  Supplier
                </Label>
                <Input
                  id="supplier"
                  type="text"
                  value={data.supplier || ""}
                  onChange={(e) => updateField("supplier", e.target.value)}
                  placeholder="Enter supplier name"
                  data-testid="domain-form-supplier"
                />
              </div>

              {/* Weight */}
              <div className="space-y-2">
                <Label htmlFor="weight_kg" className="text-sm font-medium">
                  Weight (kg)
                </Label>
                <Input
                  id="weight_kg"
                  type="number"
                  step="0.001"
                  min="0"
                  value={data.weight_kg || ""}
                  onChange={(e) =>
                    updateField(
                      "weight_kg",
                      e.target.value ? parseFloat(e.target.value) : null
                    )
                  }
                  onBlur={() => handleFieldBlur("weight_kg")}
                  placeholder="0.000"
                  data-testid="domain-form-weight"
                />
                {getFieldError("weight_kg") && (
                  <p className="text-sm text-red-500">
                    {getFieldError("weight_kg")}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Status Options */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Status</h3>

            <div className="flex flex-col space-y-3">
              {/* Active Status */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_active"
                  checked={data.is_active ?? true}
                  onCheckedChange={(checked) =>
                    updateField("is_active", checked)
                  }
                  data-testid="domain-form-active"
                />
                <Label htmlFor="is_active" className="text-sm font-medium">
                  Active component
                </Label>
              </div>

              {/* Preferred Status */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="is_preferred"
                  checked={data.is_preferred ?? false}
                  onCheckedChange={(checked) =>
                    updateField("is_preferred", checked)
                  }
                  data-testid="domain-form-preferred"
                />
                <Label htmlFor="is_preferred" className="text-sm font-medium">
                  Preferred component
                </Label>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex items-center justify-between border-t pt-6">
            <div className="flex items-center space-x-2 text-sm text-gray-600">
              {isDirty && <span>• Unsaved changes</span>}
              {validationResult && (
                <Badge
                  variant={validationResult.isValid ? "success" : "destructive"}
                >
                  {validationResult.isValid
                    ? "Valid"
                    : `${errors.length} error${errors.length !== 1 ? "s" : ""}`}
                </Badge>
              )}
            </div>

            <div className="flex items-center space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={onCancel}
                disabled={isSubmitting}
                data-testid="domain-form-cancel"
              >
                <X className="mr-2 h-4 w-4" />
                Cancel
              </Button>

              <Button
                type="button"
                variant="outline"
                onClick={resetForm}
                disabled={isSubmitting || !isDirty}
                data-testid="domain-form-reset"
              >
                Reset
              </Button>

              <Button
                type="submit"
                disabled={!isValid || !isDirty || isSubmitting}
                data-testid="domain-form-submit"
              >
                {isSubmitting ? (
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Save className="mr-2 h-4 w-4" />
                )}
                {isSubmitting
                  ? "Saving..."
                  : isEditing
                    ? "Update Component"
                    : "Create Component"}
              </Button>
            </div>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
