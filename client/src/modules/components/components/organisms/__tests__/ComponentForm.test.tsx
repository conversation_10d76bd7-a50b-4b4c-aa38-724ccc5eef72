/**
 * ComponentForm Component Tests
 * Tests the ComponentForm component including form rendering, validation, submission workflows,
 * accessibility compliance, and integration with useComponentForm hook
 */

import {
  createMockComponent,
  mockComponent,
} from "@/test/factories/componentFactories"
import { renderWithProviders } from "@/test/utils"
import { act, fireEvent, screen, waitFor } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { ComponentForm } from "@/modules/components/components"

// Mock the API hooks
vi.mock("@/modules/components/api/componentQueries", () => ({
  useComponentCategories: vi.fn(() => ({
    data: [
      { id: 1, name: "Resistor", value: "1" },
      { id: 2, name: "Capacitor", value: "2" },
    ],
    isLoading: false,
  })),
  useComponentTypes: vi.fn(() => ({
    data: [
      { id: 1, name: "Fixed Resistor", value: "1" },
      { id: 2, name: "Variable Resistor", value: "2" },
    ],
    isLoading: false,
  })),
}))

// Mock the validation utility
vi.mock("@/modules/components/utils", () => ({
  validateComponent: vi.fn(() => ({
    isValid: true,
    errors: [],
  })),
  formatComponentName: vi.fn((name) => name),
  formatPrice: vi.fn((price) => price),
  formatWeight: vi.fn((weight) => weight),
  formatDimensions: vi.fn((dimensions) => dimensions),
  getComponentStatusColor: vi.fn(() => "green"),
  getComponentStatusText: vi.fn(() => "Active"),
  calculateTotalPrice: vi.fn(() => 0),
  isComponentAvailable: vi.fn(() => true),
  getComponentCategory: vi.fn(() => "RESISTOR"),
  normalizeSpecifications: vi.fn((specs) => specs),
}))

describe("ComponentForm", () => {
  const mockHandlers = {
    onSubmit: vi.fn(),
    onCancel: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("Rendering", () => {
    it("renders create form with correct title and fields", () => {
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      expect(screen.getByText("Add Component")).toBeInTheDocument()
      expect(screen.getByLabelText(/component name/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/manufacturer/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/model number/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/description/i)).toBeInTheDocument()
      expect(screen.getByLabelText(/category/i)).toBeInTheDocument()
    })

    it("renders edit form with correct title and populated fields", () => {
      renderWithProviders(
        <ComponentForm
          component={mockComponent}
          isEditing={true}
          {...mockHandlers}
        />
      )

      expect(screen.getByText("Edit Component")).toBeInTheDocument()
      expect(screen.getByDisplayValue(mockComponent.name)).toBeInTheDocument()
      expect(
        screen.getByDisplayValue(mockComponent.manufacturer)
      ).toBeInTheDocument()
      expect(
        screen.getByDisplayValue(mockComponent.model_number)
      ).toBeInTheDocument()
    })

    it("renders with loading state", () => {
      renderWithProviders(<ComponentForm isLoading={true} {...mockHandlers} />)

      const submitButton = screen.getByRole("button", { name: /create/i })
      expect(submitButton).toBeDisabled()
      expect(screen.getByTestId("loading-spinner")).toBeInTheDocument()
    })

    it("applies custom className", () => {
      const customClass = "custom-form-class"
      renderWithProviders(
        <ComponentForm className={customClass} {...mockHandlers} />
      )

      const formCard = screen.getByRole("form").closest(".custom-form-class")
      expect(formCard).toBeInTheDocument()
    })
  })

  describe("Form Interactions", () => {
    it("handles text input changes", async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)

      await act(async () => {
        await user.type(nameInput, "New Component Name")
      })

      expect(nameInput).toHaveValue("New Component Name")
    })

    it("handles select dropdown changes", async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const categorySelect = screen.getByLabelText(/category/i)

      // Verify the options are there
      expect(screen.getByText("Resistor")).toBeInTheDocument()
      expect(screen.getByText("Capacitor")).toBeInTheDocument()

      await act(async () => {
        await user.selectOptions(categorySelect, "1")
      })

      // Check if the option is selected
      expect(categorySelect).toHaveValue("1")
    })

    it("handles checkbox changes", async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const preferredCheckbox = screen.getByLabelText(/preferred/i)

      await act(async () => {
        await user.click(preferredCheckbox)
      })

      expect(preferredCheckbox).toBeChecked()
    })

    it("handles number input changes", async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const priceInput = screen.getByLabelText(/unit price/i)

      await act(async () => {
        await user.type(priceInput, "15.99")
      })

      expect(priceInput).toHaveValue(15.99)
    })
  })

  describe("Form Validation", () => {
    it("displays validation errors for required fields", async () => {
      const utils = await import("@/modules/components/utils")
      vi.mocked(utils.validateComponent).mockReturnValue({
        isValid: false,
        errors: [
          {
            field: "name",
            message: "Component name is required",
            code: "REQUIRED",
          },
          {
            field: "manufacturer",
            message: "Manufacturer is required",
            code: "REQUIRED",
          },
        ],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole("button", { name: /create/i })

      await act(async () => {
        fireEvent.click(submitButton)
      })

      expect(screen.getByText("Component name is required")).toBeInTheDocument()
      expect(screen.getByText("Manufacturer is required")).toBeInTheDocument()
    })

    it("clears field errors when field is modified", async () => {
      const user = userEvent.setup()
      const { validateComponent } = await import("@/modules/components/utils")

      // Initially return validation error
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [
          {
            field: "name",
            message: "Component name is required",
            code: "REQUIRED",
          },
        ],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      // Trigger validation error
      const submitButton = screen.getByRole("button", { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(
          screen.getByText("Component name is required")
        ).toBeInTheDocument()
      })

      // Clear error when field is modified
      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, "Valid Name")

      await waitFor(() => {
        expect(
          screen.queryByText("Component name is required")
        ).not.toBeInTheDocument()
      })
    })

    it("validates form on submission", async () => {
      const { validateComponent } = await import("@/modules/components/utils")
      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole("button", { name: /create/i })
      fireEvent.click(submitButton)

      expect(validateComponent).toHaveBeenCalled()
    })
  })

  describe("Form Submission", () => {
    it("submits form with valid data", async () => {
      const { validateComponent } = await import("@/modules/components/utils")
      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      // Fill form with valid data
      const nameInput = screen.getByLabelText(/component name/i)
      fireEvent.change(nameInput, { target: { value: "Test Component" } })

      const submitButton = screen.getByRole("button", { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockHandlers.onSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            name: "Test Component",
          })
        )
      })
    })

    it("does not submit form with invalid data", async () => {
      const { validateComponent } = await import("@/modules/components/utils")
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [{ field: "name", message: "Required", code: "REQUIRED" }],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole("button", { name: /create/i })
      fireEvent.click(submitButton)

      expect(mockHandlers.onSubmit).not.toHaveBeenCalled()
    })

    it("handles form cancellation", () => {
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const cancelButton = screen.getByRole("button", { name: /cancel/i })
      fireEvent.click(cancelButton)

      expect(mockHandlers.onCancel).toHaveBeenCalled()
    })

    it("enables submit button when form is not dirty (for validation)", () => {
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole("button", { name: /create/i })
      expect(submitButton).toBeEnabled()
    })

    it("enables submit button when form is dirty", async () => {
      const user = userEvent.setup()
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, "Test")

      const submitButton = screen.getByRole("button", { name: /create/i })
      expect(submitButton).not.toBeDisabled()
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA labels and attributes", () => {
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const form = screen.getByRole("form")
      expect(form).toBeInTheDocument()

      const nameInput = screen.getByLabelText(/component name/i)
      expect(nameInput).toHaveAttribute("aria-required", "true")

      const submitButton = screen.getByRole("button", { name: /create/i })
      expect(submitButton).toHaveAttribute("type", "submit")
    })

    it("associates error messages with form fields", async () => {
      const { validateComponent } = await import("@/modules/components/utils")
      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [
          {
            field: "name",
            message: "Component name is required",
            code: "REQUIRED",
          },
        ],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole("button", { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        const nameInput = screen.getByLabelText(/component name/i)
        const errorMessage = screen.getByText("Component name is required")

        expect(nameInput).toHaveAttribute("aria-describedby")
        expect(errorMessage).toHaveAttribute("id")
      })
    })

    it("supports keyboard navigation", async () => {
      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)
      const manufacturerInput = screen.getByLabelText(/manufacturer/i)

      await act(async () => {
        nameInput.focus()
      })
      expect(document.activeElement).toBe(nameInput)

      await act(async () => {
        // Simulate Tab key by focusing the next element
        manufacturerInput.focus()
      })
      expect(document.activeElement).toBe(manufacturerInput)
    })
  })

  describe("Edge Cases", () => {
    it("handles component with missing optional fields", () => {
      const minimalComponent = createMockComponent({
        description: undefined,
        unit_price: undefined,
        weight_kg: undefined,
      })

      renderWithProviders(
        <ComponentForm
          component={minimalComponent}
          isEditing={true}
          {...mockHandlers}
        />
      )

      expect(
        screen.getByDisplayValue(minimalComponent.name)
      ).toBeInTheDocument()
      expect(screen.getByLabelText(/description/i)).toHaveValue("")
    })

    it("handles form reset when component prop changes", async () => {
      const { rerender } = renderWithProviders(
        <ComponentForm
          component={mockComponent}
          isEditing={true}
          {...mockHandlers}
        />
      )

      expect(screen.getByDisplayValue(mockComponent.name)).toBeInTheDocument()

      const newComponent = createMockComponent({ name: "New Component Name" })

      await act(async () => {
        rerender(
          <ComponentForm
            component={newComponent}
            isEditing={true}
            {...mockHandlers}
          />
        )
      })

      expect(screen.getByDisplayValue("New Component Name")).toBeInTheDocument()
    })

    it("handles API loading states for categories and types", async () => {
      const queries = await import("@/modules/components/api/componentQueries")

      vi.mocked(queries.useComponentCategories).mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
        isError: false,
        isSuccess: false,
        refetch: vi.fn(),
        isFetching: true,
        status: "pending",
      } as any)

      vi.mocked(queries.useComponentTypes).mockReturnValue({
        data: [],
        isLoading: true,
        error: null,
        isError: false,
        isSuccess: false,
        refetch: vi.fn(),
        isFetching: true,
        status: "pending",
      } as any)

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const categorySelect = screen.getByLabelText(/category/i)
      expect(categorySelect).toBeInTheDocument()
      // Should show loading state or empty options
    })

    it("handles form submission with network errors", async () => {
      const { validateComponent } = await import("@/modules/components/utils")
      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      const onSubmitWithError = vi
        .fn()
        .mockRejectedValue(new Error("Network error"))

      renderWithProviders(
        <ComponentForm
          onSubmit={onSubmitWithError}
          onCancel={mockHandlers.onCancel}
        />
      )

      const nameInput = screen.getByLabelText(/component name/i)
      fireEvent.change(nameInput, { target: { value: "Test Component" } })

      const submitButton = screen.getByRole("button", { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(onSubmitWithError).toHaveBeenCalled()
      })
    })

    it("handles complex form data with specifications and dimensions", async () => {
      const user = userEvent.setup()
      const { validateComponent } = await import("@/modules/components/utils")
      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      // Fill in complex data
      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, "Complex Component")

      const submitButton = screen.getByRole("button", { name: /create/i })
      fireEvent.click(submitButton)

      await waitFor(() => {
        expect(mockHandlers.onSubmit).toHaveBeenCalledWith(
          expect.objectContaining({
            name: "Complex Component",
          })
        )
      })
    })
  })

  describe("Integration with useComponentForm Hook", () => {
    it("integrates properly with form hook for validation", async () => {
      const { validateComponent } = await import("@/modules/components/utils")

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      // Simulate form interaction that would trigger validation on blur
      const nameInput = screen.getByLabelText(/component name/i)

      await act(async () => {
        fireEvent.change(nameInput, { target: { value: "Test" } })
        fireEvent.blur(nameInput)
      })

      // Verify validation is called through blur event
      expect(validateComponent).toHaveBeenCalled()
    })

    it("handles form state synchronization", async () => {
      const user = userEvent.setup()

      renderWithProviders(
        <ComponentForm
          component={mockComponent}
          isEditing={true}
          {...mockHandlers}
        />
      )

      // Modify form data
      const nameInput = screen.getByLabelText(/component name/i)
      await user.clear(nameInput)
      await user.type(nameInput, "Modified Name")

      // Verify form reflects the changes
      expect(nameInput).toHaveValue("Modified Name")
    })

    it("handles form dirty state tracking", async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const submitButton = screen.getByRole("button", { name: /create/i })
      expect(submitButton).toBeEnabled() // Always enabled for validation

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, "Test")

      expect(submitButton).toBeEnabled() // Still enabled when dirty
    })
  })

  describe("Real-time Validation Feedback", () => {
    it("provides immediate validation feedback on field blur", async () => {
      const user = userEvent.setup()
      const { validateComponent } = await import("@/modules/components/utils")

      vi.mocked(validateComponent).mockReturnValue({
        isValid: false,
        errors: [
          { field: "name", message: "Name too short", code: "MIN_LENGTH" },
        ],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, "A")
      await user.tab() // Blur the field

      await waitFor(() => {
        expect(screen.getByText("Name too short")).toBeInTheDocument()
      })
    })

    it("shows validation success indicators", async () => {
      const user = userEvent.setup()
      const { validateComponent } = await import("@/modules/components/utils")

      vi.mocked(validateComponent).mockReturnValue({
        isValid: true,
        errors: [],
      })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)
      await user.type(nameInput, "Valid Component Name")

      // Check for visual success indicators (green border, checkmark, etc.)
      expect(nameInput).not.toHaveClass("border-red-500")
    })

    it("updates validation state as user types", async () => {
      const user = userEvent.setup()
      const { validateComponent } = await import("@/modules/components/utils")

      // First return error, then success
      vi.mocked(validateComponent)
        .mockReturnValueOnce({
          isValid: false,
          errors: [
            { field: "name", message: "Name too short", code: "MIN_LENGTH" },
          ],
        })
        .mockReturnValueOnce({
          isValid: true,
          errors: [],
        })

      renderWithProviders(<ComponentForm {...mockHandlers} />)

      const nameInput = screen.getByLabelText(/component name/i)

      // Type short name
      await user.type(nameInput, "A")
      await user.tab()

      await waitFor(() => {
        expect(screen.getByText("Name too short")).toBeInTheDocument()
      })

      // Type longer name
      await user.clear(nameInput)
      await user.type(nameInput, "Valid Component Name")

      await waitFor(() => {
        expect(screen.queryByText("Name too short")).not.toBeInTheDocument()
      })
    })
  })
})
