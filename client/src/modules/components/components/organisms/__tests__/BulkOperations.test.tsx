/**
 * BulkOperations Component Tests
 * Tests the BulkOperations component including selection workflows, bulk operations,
 * progress tracking, confirmation dialogs, error handling, and performance with large datasets
 */

import { renderWithProviders } from "@/test/utils"
import { fireEvent, screen, waitFor } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"

import {
  useBulkCreateComponents,
  useBulkDeleteComponents,
  useBulkUpdateComponents,
} from "@/modules/components/api"
import { BulkOperations } from "@/modules/components/components/organisms/index.ts"

// Mock the API mutation hooks
vi.mock("@/modules/components/api", () => ({
  useBulkCreateComponents: vi.fn(),
  useBulkUpdateComponents: vi.fn(),
  useBulkDeleteComponents: vi.fn(),
}))

describe("BulkOperations", () => {
  const mockMutations: Record<string, any> = {
    bulkCreate: {
      mutate: vi.fn(),
      mutateAsync: vi.fn(),
      isPending: false,
      isSuccess: false,
      isError: false,
      error: null,
      data: undefined,
      reset: vi.fn(),
    },
    bulkUpdate: {
      mutate: vi.fn(),
      mutateAsync: vi.fn(),
      isPending: false,
      isSuccess: false,
      isError: false,
      error: null,
      data: undefined,
      reset: vi.fn(),
    },
    bulkDelete: {
      mutate: vi.fn(),
      mutateAsync: vi.fn(),
      isPending: false,
      isSuccess: false,
      isError: false,
      error: null,
      data: undefined,
      reset: vi.fn(),
    },
  }

  const mockHandlers = {
    onSelectionClear: vi.fn(),
    onOperationComplete: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()

    // Reset mutation states to default
    mockMutations.bulkCreate.isPending = false
    mockMutations.bulkCreate.isSuccess = false
    mockMutations.bulkCreate.isError = false
    mockMutations.bulkCreate.error = null
    mockMutations.bulkCreate.data = undefined

    mockMutations.bulkUpdate.isPending = false
    mockMutations.bulkUpdate.isSuccess = false
    mockMutations.bulkUpdate.isError = false
    mockMutations.bulkUpdate.error = null
    mockMutations.bulkUpdate.data = undefined

    mockMutations.bulkDelete.isPending = false
    mockMutations.bulkDelete.isSuccess = false
    mockMutations.bulkDelete.isError = false
    mockMutations.bulkDelete.error = null
    mockMutations.bulkDelete.data = undefined

    vi.mocked(useBulkCreateComponents).mockReturnValue(mockMutations.bulkCreate)
    vi.mocked(useBulkUpdateComponents).mockReturnValue(mockMutations.bulkUpdate)
    vi.mocked(useBulkDeleteComponents).mockReturnValue(mockMutations.bulkDelete)
  })

  describe("Rendering", () => {
    it("renders bulk operations panel", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2, 3]} {...mockHandlers} />
      )

      expect(screen.getByText("Bulk Operations")).toBeInTheDocument()
      expect(screen.getByText("Mark as Preferred")).toBeInTheDocument()
      expect(screen.getByText("Deactivate")).toBeInTheDocument()
      expect(screen.getByText("Export Selected")).toBeInTheDocument()
      expect(screen.getByText("Delete Selected")).toBeInTheDocument()
    })

    it("shows selection summary when components are selected", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2, 3]} {...mockHandlers} />
      )

      expect(screen.getByText("3 components selected")).toBeInTheDocument()
      expect(screen.getByText("Clear Selection")).toBeInTheDocument()
    })

    it("handles singular vs plural text correctly", () => {
      const { rerender } = renderWithProviders(
        <BulkOperations selectedComponentIds={[1]} {...mockHandlers} />
      )

      expect(screen.getByText("1 component selected")).toBeInTheDocument()

      rerender(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      expect(screen.getByText("2 components selected")).toBeInTheDocument()
    })

    it("does not show selection summary when no components selected", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[]} {...mockHandlers} />
      )

      expect(screen.queryByText(/components selected/)).not.toBeInTheDocument()
      expect(screen.queryByText("Clear Selection")).not.toBeInTheDocument()
    })

    it("applies custom className", () => {
      const customClass = "custom-bulk-class"
      const { container } = renderWithProviders(
        <BulkOperations
          selectedComponentIds={[1, 2, 3]}
          className={customClass}
          {...mockHandlers}
        />
      )

      expect(container.firstChild).toHaveClass(customClass)
    })
  })

  describe("Component Selection Workflows", () => {
    it("disables operation buttons when no components selected", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[]} {...mockHandlers} />
      )

      expect(screen.getByText("Mark as Preferred")).toBeDisabled()
      expect(screen.getByText("Deactivate")).toBeDisabled()
      expect(screen.getByText("Export Selected")).toBeDisabled()
      expect(screen.getByText("Delete Selected")).toBeDisabled()
    })

    it("enables operation buttons when components are selected", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2, 3]} {...mockHandlers} />
      )

      expect(screen.getByText("Mark as Preferred")).not.toBeDisabled()
      expect(screen.getByText("Deactivate")).not.toBeDisabled()
      expect(screen.getByText("Export Selected")).not.toBeDisabled()
      expect(screen.getByText("Delete Selected")).not.toBeDisabled()
    })

    it("calls onSelectionClear when clear selection button is clicked", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2, 3]} {...mockHandlers} />
      )

      const clearButton = screen.getByText("Clear Selection")
      fireEvent.click(clearButton)

      expect(mockHandlers.onSelectionClear).toHaveBeenCalled()
    })

    it("handles large selection sets efficiently", () => {
      const largeSelection = Array.from({ length: 1000 }, (_, i) => i + 1)

      const startTime = performance.now()
      renderWithProviders(
        <BulkOperations
          selectedComponentIds={largeSelection}
          {...mockHandlers}
        />
      )
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(100) // Should render in under 100ms
      expect(screen.getByText("1,000 components selected")).toBeInTheDocument()
    })
  })

  describe("Bulk Operation Workflows", () => {
    it("performs bulk update to mark as preferred", async () => {
      mockMutations.bulkUpdate.mutateAsync.mockResolvedValue([
        { is_valid: true, component: { id: 1, name: "Component 1" } },
        { is_valid: true, component: { id: 2, name: "Component 2" } },
      ])

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      const markPreferredButton = screen.getByText("Mark as Preferred")
      fireEvent.click(markPreferredButton)

      await waitFor(() => {
        expect(mockMutations.bulkUpdate.mutateAsync).toHaveBeenCalledWith({
          component_ids: [1, 2],
          update_data: { is_preferred: true },
        })
      })

      expect(mockHandlers.onSelectionClear).toHaveBeenCalled()
      expect(mockHandlers.onOperationComplete).toHaveBeenCalled()
    })

    it("performs bulk update to deactivate components", async () => {
      mockMutations.bulkUpdate.mutateAsync.mockResolvedValue([
        { is_valid: true, component: { id: 1, name: "Component 1" } },
      ])

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1]} {...mockHandlers} />
      )

      const deactivateButton = screen.getByText("Deactivate")
      fireEvent.click(deactivateButton)

      await waitFor(() => {
        expect(mockMutations.bulkUpdate.mutateAsync).toHaveBeenCalledWith({
          component_ids: [1],
          update_data: { is_active: false },
        })
      })
    })

    it("handles export operation", () => {
      // Mock console.log to capture export data
      const consoleSpy = vi.spyOn(console, "log").mockImplementation(() => {})

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2, 3]} {...mockHandlers} />
      )

      const exportButton = screen.getByText("Export Selected")
      fireEvent.click(exportButton)

      expect(consoleSpy).toHaveBeenCalledWith(
        "Exporting components:",
        expect.objectContaining({
          component_ids: [1, 2, 3],
          format: "csv",
        })
      )

      consoleSpy.mockRestore()
    })

    it("shows confirmation dialog for delete operation", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      const deleteButton = screen.getByText("Delete Selected")
      fireEvent.click(deleteButton)

      expect(
        screen.getByRole("heading", { name: "Confirm Bulk Delete" })
      ).toBeInTheDocument()
      expect(
        screen.getByText(/Are you sure you want to delete 2 components\?/)
      ).toBeInTheDocument()
    })
  })

  describe("Progress Tracking and Loading States", () => {
    it("shows loading state during bulk update operation", () => {
      mockMutations.bulkUpdate.isPending = true

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      const markPreferredButton = screen.getByText("Mark as Preferred")
      expect(markPreferredButton).toBeDisabled()

      // Should show loading spinner
      const loadingSpinner = markPreferredButton.querySelector(".animate-spin")
      expect(loadingSpinner).toBeInTheDocument()
    })

    it("shows loading state during bulk delete operation", () => {
      mockMutations.bulkDelete.isPending = true

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      const deleteButton = screen.getByText("Delete Selected")
      expect(deleteButton).toBeDisabled()

      // Should show loading spinner
      const loadingSpinner = deleteButton.querySelector(".animate-spin")
      expect(loadingSpinner).toBeInTheDocument()
    })

    it("disables buttons during pending operations", () => {
      mockMutations.bulkUpdate.isPending = true
      mockMutations.bulkDelete.isPending = true

      // Update the mocks to return the modified states
      vi.mocked(useBulkUpdateComponents).mockReturnValue(
        mockMutations.bulkUpdate
      )
      vi.mocked(useBulkDeleteComponents).mockReturnValue(
        mockMutations.bulkDelete
      )

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      expect(screen.getByText("Mark as Preferred")).toBeDisabled()
      expect(screen.getByText("Deactivate")).toBeDisabled()
      expect(screen.getByText("Delete Selected")).toBeDisabled()
      // Export should still be enabled as it doesn't use mutations
      expect(screen.getByText("Export Selected")).not.toBeDisabled()
    })
  })

  describe("Confirmation Dialogs", () => {
    it("shows delete confirmation dialog with correct message", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2, 3]} {...mockHandlers} />
      )

      const deleteButton = screen.getByText("Delete Selected")
      fireEvent.click(deleteButton)

      expect(
        screen.getByRole("heading", { name: "Confirm Bulk Delete" })
      ).toBeInTheDocument()
      expect(
        screen.getByText(/Are you sure you want to delete 3 components\?/)
      ).toBeInTheDocument()
      expect(screen.getByText("Cancel")).toBeInTheDocument()
      expect(screen.getByText("Confirm")).toBeInTheDocument()
    })

    it("cancels delete operation when cancel is clicked", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      const deleteButton = screen.getByText("Delete Selected")
      fireEvent.click(deleteButton)

      const cancelButton = screen.getByText("Cancel")
      fireEvent.click(cancelButton)

      expect(
        screen.queryByRole("heading", { name: "Confirm Bulk Delete" })
      ).not.toBeInTheDocument()
      expect(mockMutations.bulkDelete.mutateAsync).not.toHaveBeenCalled()
    })

    it("proceeds with delete when confirm is clicked", async () => {
      mockMutations.bulkDelete.mutateAsync.mockResolvedValue({
        deleted_count: 2,
        errors: [],
      })

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      const deleteButton = screen.getByText("Delete Selected")
      fireEvent.click(deleteButton)

      const confirmButton = screen.getByText("Confirm")
      fireEvent.click(confirmButton)

      await waitFor(() => {
        expect(mockMutations.bulkDelete.mutateAsync).toHaveBeenCalledWith({
          componentIds: [1, 2],
          soft_delete: false,
        })
      })

      expect(mockHandlers.onSelectionClear).toHaveBeenCalled()
      expect(mockHandlers.onOperationComplete).toHaveBeenCalled()
    })

    it("has proper destructive styling for delete confirmation", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1]} {...mockHandlers} />
      )

      const deleteButton = screen.getByText("Delete Selected")
      fireEvent.click(deleteButton)

      const confirmButton = screen.getByText("Confirm")
      expect(confirmButton).toHaveClass("bg-destructive")
    })
  })

  describe("Error Handling", () => {
    it("handles bulk update errors gracefully", async () => {
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {})
      mockMutations.bulkUpdate.mutateAsync.mockRejectedValue(
        new Error("Update failed")
      )

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      const markPreferredButton = screen.getByText("Mark as Preferred")
      fireEvent.click(markPreferredButton)

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          "Bulk update failed:",
          expect.any(Error)
        )
      })

      consoleSpy.mockRestore()
    })

    it("handles bulk delete errors gracefully", async () => {
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {})
      mockMutations.bulkDelete.mutateAsync.mockRejectedValue(
        new Error("Delete failed")
      )

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1]} {...mockHandlers} />
      )

      const deleteButton = screen.getByText("Delete Selected")
      fireEvent.click(deleteButton)

      const confirmButton = screen.getByText("Confirm")
      fireEvent.click(confirmButton)

      await waitFor(() => {
        expect(consoleSpy).toHaveBeenCalledWith(
          "Bulk delete failed:",
          expect.any(Error)
        )
      })

      consoleSpy.mockRestore()
    })

    it("displays operation results with mixed success/failure", async () => {
      const mixedResults = [
        { is_valid: true, component: { id: 1, name: "Component 1" } },
        { is_valid: false, errors: ["Validation failed"], component: null },
        { is_valid: true, component: { id: 3, name: "Component 3" } },
      ]

      mockMutations.bulkUpdate.mutateAsync.mockResolvedValue(mixedResults)

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2, 3]} {...mockHandlers} />
      )

      const markPreferredButton = screen.getByText("Mark as Preferred")
      fireEvent.click(markPreferredButton)

      await waitFor(() => {
        expect(screen.getByText("Operation Results")).toBeInTheDocument()
        expect(
          screen.getByText("Successfully processed component Component 1")
        ).toBeInTheDocument()
        expect(
          screen.getByText("Failed to process: Validation failed")
        ).toBeInTheDocument()
        expect(
          screen.getByText("Successfully processed component Component 3")
        ).toBeInTheDocument()
      })
    })

    it("handles partial failures in bulk operations", async () => {
      const partialResults = [
        { is_valid: true, component: { id: 1, name: "Component 1" } },
        { is_valid: false, errors: ["Component not found"], component: null },
      ]

      mockMutations.bulkUpdate.mutateAsync.mockResolvedValue(partialResults)

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      const markPreferredButton = screen.getByText("Mark as Preferred")
      fireEvent.click(markPreferredButton)

      // Wait for the operation to complete
      await waitFor(() => {
        expect(mockMutations.bulkUpdate.mutateAsync).toHaveBeenCalled()
      })

      await waitFor(() => {
        expect(screen.getByText("Operation Results")).toBeInTheDocument()
      })

      // Check for success and error messages
      expect(
        screen.getByText("Successfully processed component Component 1")
      ).toBeInTheDocument()
      expect(
        screen.getByText("Failed to process: Component not found")
      ).toBeInTheDocument()
    })
  })

  describe("Performance with Large Datasets", () => {
    it("handles large selection sets without performance degradation", () => {
      const largeSelection = Array.from({ length: 5000 }, (_, i) => i + 1)

      const startTime = performance.now()
      renderWithProviders(
        <BulkOperations
          selectedComponentIds={largeSelection}
          {...mockHandlers}
        />
      )
      const endTime = performance.now()

      expect(endTime - startTime).toBeLessThan(200) // Should render in under 200ms
      expect(screen.getByText("5,000 components selected")).toBeInTheDocument()
    })

    it("efficiently processes bulk operations with large datasets", async () => {
      const largeSelection = Array.from({ length: 1000 }, (_, i) => i + 1)
      const largeResults = largeSelection.map((id) => ({
        is_valid: true,
        component: { id, name: `Component ${id}` },
      }))

      mockMutations.bulkUpdate.mutateAsync.mockResolvedValue(largeResults)

      renderWithProviders(
        <BulkOperations
          selectedComponentIds={largeSelection}
          {...mockHandlers}
        />
      )

      const startTime = performance.now()
      const markPreferredButton = screen.getByText("Mark as Preferred")
      fireEvent.click(markPreferredButton)

      await waitFor(() => {
        expect(mockMutations.bulkUpdate.mutateAsync).toHaveBeenCalled()
      })

      const endTime = performance.now()
      expect(endTime - startTime).toBeLessThan(2000) // Should complete in under 2 seconds
    })

    it("handles rapid selection changes efficiently", () => {
      const { rerender } = renderWithProviders(
        <BulkOperations selectedComponentIds={[]} {...mockHandlers} />
      )

      // Rapidly change selections
      for (let i = 1; i <= 100; i++) {
        const selection = Array.from({ length: i }, (_, idx) => idx + 1)
        rerender(
          <BulkOperations selectedComponentIds={selection} {...mockHandlers} />
        )
      }

      expect(screen.getByText("100 components selected")).toBeInTheDocument()
    })
  })

  describe("User Feedback and Status Updates", () => {
    it("provides clear visual feedback for operation states", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2, 3]} {...mockHandlers} />
      )

      // Check for selection indicator by looking for the blue icon in the selection summary
      const selectionIcon = screen
        .getByTestId("selection-count")
        .parentElement?.querySelector(".text-blue-500")
      expect(selectionIcon).toBeInTheDocument()

      // Check for operation buttons with proper styling
      const deleteButton = screen.getByText("Delete Selected")
      expect(deleteButton).toHaveClass("text-red-600")
    })

    it("shows appropriate icons for different operation types", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      // Check for specific icons in buttons
      const editButtons = screen
        .getAllByRole("button")
        .filter(
          (button) =>
            button.textContent?.includes("Mark as Preferred") ||
            button.textContent?.includes("Deactivate")
        )
      expect(editButtons.length).toBe(2)

      const exportButton = screen.getByText("Export Selected")
      expect(exportButton).toBeInTheDocument()

      const deleteButton = screen.getByText("Delete Selected")
      expect(deleteButton).toBeInTheDocument()
    })

    it("updates UI state after successful operations", async () => {
      mockMutations.bulkUpdate.mutateAsync.mockResolvedValue([
        { is_valid: true, component: { id: 1, name: "Component 1" } },
      ])

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1]} {...mockHandlers} />
      )

      const markPreferredButton = screen.getByText("Mark as Preferred")
      fireEvent.click(markPreferredButton)

      await waitFor(() => {
        expect(mockHandlers.onSelectionClear).toHaveBeenCalled()
        expect(mockHandlers.onOperationComplete).toHaveBeenCalled()
      })
    })

    it("maintains operation results visibility", async () => {
      const results = [
        { is_valid: true, component: { id: 1, name: "Test Component" } },
      ]

      mockMutations.bulkUpdate.mutateAsync.mockResolvedValue(results)

      renderWithProviders(
        <BulkOperations selectedComponentIds={[1]} {...mockHandlers} />
      )

      const markPreferredButton = screen.getByText("Mark as Preferred")
      fireEvent.click(markPreferredButton)

      await waitFor(() => {
        expect(screen.getByText("Operation Results")).toBeInTheDocument()
        expect(
          screen.getByText("Successfully processed component Test Component")
        ).toBeInTheDocument()
      })

      // Results should remain visible
      expect(screen.getByText("Operation Results")).toBeInTheDocument()
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA attributes and labels", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      const buttons = screen.getAllByRole("button")
      buttons.forEach((button) => {
        expect(button).toHaveAccessibleName()
      })
    })

    it("supports keyboard navigation", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1, 2]} {...mockHandlers} />
      )

      const markPreferredButton = screen.getByText("Mark as Preferred")
      const deactivateButton = screen.getByText("Deactivate")

      // Check that buttons are focusable
      markPreferredButton.focus()
      expect(document.activeElement).toBe(markPreferredButton)

      deactivateButton.focus()
      expect(document.activeElement).toBe(deactivateButton)
    })

    it("provides clear confirmation dialog accessibility", () => {
      renderWithProviders(
        <BulkOperations selectedComponentIds={[1]} {...mockHandlers} />
      )

      const deleteButton = screen.getByText("Delete Selected")
      fireEvent.click(deleteButton)

      const dialog = screen.getByRole("dialog")
      expect(dialog).toBeInTheDocument()

      const confirmButton = screen.getByText("Confirm")
      const cancelButton = screen.getByText("Cancel")

      expect(confirmButton).toHaveAccessibleName()
      expect(cancelButton).toHaveAccessibleName()
    })
  })

  describe("Integration with Missing Handlers", () => {
    it("handles missing onSelectionClear gracefully", () => {
      renderWithProviders(
        <BulkOperations
          selectedComponentIds={[1, 2]}
          onOperationComplete={mockHandlers.onOperationComplete}
        />
      )

      const clearButton = screen.getByText("Clear Selection")
      fireEvent.click(clearButton)

      // Should not throw error
      expect(clearButton).toBeInTheDocument()
    })

    it("handles missing onOperationComplete gracefully", async () => {
      mockMutations.bulkUpdate.mutateAsync.mockResolvedValue([
        { is_valid: true, component: { id: 1, name: "Component 1" } },
      ])

      renderWithProviders(
        <BulkOperations
          selectedComponentIds={[1]}
          onSelectionClear={mockHandlers.onSelectionClear}
        />
      )

      const markPreferredButton = screen.getByText("Mark as Preferred")
      fireEvent.click(markPreferredButton)

      await waitFor(() => {
        expect(mockMutations.bulkUpdate.mutateAsync).toHaveBeenCalled()
      })

      // Should not throw error
      expect(markPreferredButton).toBeInTheDocument()
    })
  })
})
