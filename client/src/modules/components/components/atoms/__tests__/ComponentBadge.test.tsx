/**
 * ComponentBadge Unit Tests
 * Comprehensive testing for the ComponentBadge atom component
 */

import type { ComponentRead } from "@/types/api"

import { render, screen } from "@testing-library/react"
import { axe, toHaveNoViolations } from "jest-axe"
import { describe, expect, it } from "vitest"

import {
  ComponentBadge,
  useComponentBadgeProps,
} from "../../../components/atoms/ComponentBadge"

// Extend Jest matchers
expect.extend(toHaveNoViolations)

// Mock component data
const mockComponent: Partial<ComponentRead> = {
  id: 1,
  name: "Test Component",
  is_active: true,
  is_preferred: false,
}

describe("ComponentBadge", () => {
  describe("Rendering", () => {
    it("renders with default props", () => {
      render(<ComponentBadge status="active" />)

      const badge = screen.getByRole("status")
      expect(badge).toBeInTheDocument()
      expect(badge).toHaveAttribute("aria-label", "Status: Active")
    })

    it("renders with custom label", () => {
      render(<ComponentBadge status="active" customLabel="Custom Status" />)

      const badge = screen.getByRole("status")
      expect(badge).toHaveAttribute("aria-label", "Status: Custom Status")
      expect(screen.getByText("Custom Status")).toBeInTheDocument()
    })

    it("renders with icon and label by default", () => {
      render(<ComponentBadge status="active" />)

      expect(screen.getByTestId("component-badge-icon")).toBeInTheDocument()
      expect(screen.getByTestId("component-badge-label")).toBeInTheDocument()
      expect(screen.getByText("Active")).toBeInTheDocument()
    })

    it("renders without icon when showIcon is false", () => {
      render(<ComponentBadge status="active" showIcon={false} />)

      expect(
        screen.queryByTestId("component-badge-icon")
      ).not.toBeInTheDocument()
      expect(screen.getByText("Active")).toBeInTheDocument()
    })

    it("renders without label when showLabel is false", () => {
      render(<ComponentBadge status="active" showLabel={false} />)

      expect(screen.getByTestId("component-badge-icon")).toBeInTheDocument()
      expect(screen.queryByText("Active")).not.toBeInTheDocument()
    })

    it("applies pulse animation when pulse prop is true", () => {
      render(<ComponentBadge status="active" pulse />)

      const badge = screen.getByRole("status")
      expect(badge).toHaveClass("animate-pulse")
    })
  })

  describe("Status Variants", () => {
    const statusTests = [
      { status: "active", expectedText: "Active", expectedIcon: "●" },
      { status: "inactive", expectedText: "Inactive", expectedIcon: "○" },
      { status: "preferred", expectedText: "Preferred", expectedIcon: "★" },
      { status: "available", expectedText: "Available", expectedIcon: "●" },
      { status: "limited", expectedText: "Limited Stock", expectedIcon: "◐" },
      {
        status: "out_of_stock",
        expectedText: "Out of Stock",
        expectedIcon: "○",
      },
      {
        status: "discontinued",
        expectedText: "Discontinued",
        expectedIcon: "✕",
      },
      { status: "on_order", expectedText: "On Order", expectedIcon: "◔" },
    ] as const

    statusTests.forEach(({ status, expectedText, expectedIcon }) => {
      it(`renders ${status} status correctly`, () => {
        render(<ComponentBadge status={status} />)

        expect(screen.getByText(expectedText)).toBeInTheDocument()
        expect(screen.getByText(expectedIcon)).toBeInTheDocument()
        expect(screen.getByRole("status")).toHaveAttribute(
          "aria-label",
          `Status: ${expectedText}`
        )
      })
    })
  })

  describe("Size Variants", () => {
    const sizeTests = [
      { size: "sm", expectedClass: "px-1.5 py-0.5 text-xs" },
      { size: "md", expectedClass: "px-2 py-1 text-sm" },
      { size: "lg", expectedClass: "px-3 py-1.5 text-base" },
    ] as const

    sizeTests.forEach(({ size, expectedClass }) => {
      it(`applies ${size} size classes correctly`, () => {
        render(<ComponentBadge status="active" size={size} />)

        const badge = screen.getByRole("status")
        // Check if the badge has the expected size classes
        expect(badge).toHaveClass(...expectedClass.split(" "))
      })
    })
  })

  describe("Variant Styles", () => {
    const variantTests = [
      { variant: "default", expectedClass: "border" },
      { variant: "solid", expectedClass: "border-transparent" },
      { variant: "outline", expectedClass: "bg-transparent border-2" },
    ] as const

    variantTests.forEach(({ variant, expectedClass }) => {
      it(`applies ${variant} variant classes correctly`, () => {
        render(<ComponentBadge status="active" variant={variant} />)

        const badge = screen.getByRole("status")
        expect(badge).toHaveClass(...expectedClass.split(" "))
      })
    })
  })

  describe("Custom Props", () => {
    it("applies custom className", () => {
      render(<ComponentBadge status="active" className="custom-class" />)

      const badge = screen.getByRole("status")
      expect(badge).toHaveClass("custom-class")
    })

    it("applies custom data-testid", () => {
      render(<ComponentBadge status="active" data-testid="custom-badge" />)

      expect(screen.getByTestId("custom-badge")).toBeInTheDocument()
    })

    it("forwards additional props", () => {
      render(<ComponentBadge status="active" id="custom-id" />)

      const badge = screen.getByRole("status")
      expect(badge).toHaveAttribute("id", "custom-id")
    })
  })

  describe("Convenience Components", () => {
    it("renders ActiveBadge correctly", () => {
      render(<ComponentBadge status="active" />)

      expect(screen.getByText("Active")).toBeInTheDocument()
    })

    it("renders PreferredBadge correctly", () => {
      render(<ComponentBadge status="preferred" />)

      expect(screen.getByText("Preferred")).toBeInTheDocument()
    })
  })

  describe("useComponentBadgeProps Hook", () => {
    it("returns inactive status for inactive component", () => {
      const component = { ...mockComponent, is_active: false }
      const props = useComponentBadgeProps(component)

      expect(props.status).toBe("inactive")
    })

    it("returns preferred status for preferred component", () => {
      const component = { ...mockComponent, is_preferred: true }
      const props = useComponentBadgeProps(component)

      expect(props.status).toBe("preferred")
    })

    it("returns stock status for active non-preferred component", () => {
      const component = { ...mockComponent, stock_status: "limited" }
      const props = useComponentBadgeProps(component)

      expect(props.status).toBe("limited")
    })

    it("returns active status as fallback", () => {
      const component = { ...mockComponent, stock_status: undefined }
      const props = useComponentBadgeProps(component)

      expect(props.status).toBe("active")
    })
  })

  describe("Accessibility", () => {
    it("meets WCAG accessibility standards", async () => {
      const { container } = render(<ComponentBadge status="active" />)
      const results = await axe(container)

      expect(results).toHaveNoViolations()
    })

    it("has proper ARIA attributes", () => {
      render(<ComponentBadge status="active" />)

      const badge = screen.getByRole("status")
      expect(badge).toHaveAttribute("role", "status")
      expect(badge).toHaveAttribute("aria-label", "Status: Active")
    })

    it("has proper semantic structure", () => {
      render(<ComponentBadge status="active" />)

      const icon = screen.getByTestId("component-badge-icon")
      const label = screen.getByTestId("component-badge-label")

      expect(icon).toHaveAttribute("aria-hidden", "true")
      expect(label).toBeInTheDocument()
    })
  })

  describe("Color Contrast", () => {
    it("applies correct color classes for different statuses", () => {
      const statusColorTests = [
        { status: "active", expectedColorClass: "bg-green-100 text-green-800" },
        { status: "inactive", expectedColorClass: "bg-gray-100 text-gray-600" },
        {
          status: "preferred",
          expectedColorClass: "bg-blue-100 text-blue-800",
        },
        {
          status: "out_of_stock",
          expectedColorClass: "bg-red-100 text-red-700",
        },
      ] as const

      statusColorTests.forEach(({ status, expectedColorClass }) => {
        const { unmount } = render(<ComponentBadge status={status} />)

        const badge = screen.getByRole("status")
        expect(badge).toHaveClass(...expectedColorClass.split(" "))

        unmount() // Clean up between iterations
      })
    })
  })

  describe("Error Handling", () => {
    it("handles missing status gracefully", () => {
      // @ts-expect-error Testing error case
      render(<ComponentBadge />)

      // Should still render without crashing
      expect(screen.getByRole("status")).toBeInTheDocument()
    })

    it("handles invalid status gracefully", () => {
      // @ts-expect-error Testing error case
      render(<ComponentBadge status="invalid" />)

      // Should still render without crashing
      expect(screen.getByRole("status")).toBeInTheDocument()
    })
  })

  describe("Performance", () => {
    it("renders quickly with many badges", () => {
      const startTime = performance.now()

      render(
        <div>
          {Array.from({ length: 100 }, (_, i) => (
            <ComponentBadge key={i} status="active" />
          ))}
        </div>
      )

      const endTime = performance.now()
      const renderTime = endTime - startTime

      // Should render 100 badges in less than 100ms
      expect(renderTime).toBeLessThan(100)
    })
  })
})
