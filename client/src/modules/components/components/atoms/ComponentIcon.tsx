/**
 * ComponentIcon Atom
 * Icon component for displaying component types with accessibility support
 */

import React from "react"

import type { VariantProps } from "class-variance-authority"

import { cva } from "class-variance-authority"
import {
  Battery,
  Bluetooth,
  Cable,
  CircuitBoard,
  Component,
  Cpu,
  Eye,
  Gauge,
  HardDrive,
  Lightbulb,
  MemoryStick,
  Microchip,
  Monitor,
  Plug,
  Power,
  Radio,
  RotateCcw,
  Settings,
  Shield,
  Smartphone,
  Thermometer,
  Volume2,
  Wifi,
  Wrench,
  Zap,
} from "lucide-react"

import { cn } from "@/lib/utils"

// Icon variants using CVA
const componentIconVariants = cva(
  "inline-flex items-center justify-center transition-colors",
  {
    variants: {
      size: {
        xs: "w-3 h-3",
        sm: "w-4 h-4",
        md: "w-5 h-5",
        lg: "w-6 h-6",
        xl: "w-8 h-8",
        "2xl": "w-10 h-10",
      },
      color: {
        default: "text-gray-600",
        primary: "text-blue-600",
        secondary: "text-gray-500",
        success: "text-green-600",
        warning: "text-yellow-600",
        danger: "text-red-600",
        muted: "text-gray-400",
      },
    },
    defaultVariants: {
      size: "md",
      color: "default",
    },
  }
)

// Component type to icon mapping
const componentTypeIcons = {
  // Electrical Components
  resistor: Gauge,
  capacitor: Battery,
  inductor: RotateCcw,
  diode: Zap,
  transistor: Microchip,
  ic: Cpu,
  microcontroller: CircuitBoard,
  processor: Cpu,
  memory: MemoryStick,
  storage: HardDrive,

  // Power Components
  battery: Battery,
  power_supply: Power,
  transformer: Power,
  regulator: Settings,
  converter: RotateCcw,
  inverter: RotateCcw,

  // Connectors & Cables
  connector: Plug,
  cable: Cable,
  wire: Cable,
  terminal: Plug,
  socket: Plug,

  // Switches & Controls
  switch: Power,
  button: Component,
  relay: Settings,
  contactor: Settings,
  breaker: Shield,
  fuse: Shield,

  // Sensors & Measurement
  sensor: Eye,
  thermometer: Thermometer,
  pressure_sensor: Gauge,
  flow_sensor: Gauge,
  level_sensor: Gauge,
  proximity_sensor: Eye,

  // Communication
  antenna: Radio,
  transceiver: Radio,
  modem: Wifi,
  router: Wifi,
  bluetooth: Bluetooth,
  wifi: Wifi,

  // Display & Interface
  display: Monitor,
  lcd: Monitor,
  led: Lightbulb,
  oled: Monitor,
  touchscreen: Smartphone,
  keypad: Component,

  // Audio & Sound
  speaker: Volume2,
  microphone: Volume2,
  buzzer: Volume2,
  amplifier: Volume2,

  // Mechanical
  motor: Settings,
  actuator: Wrench,
  valve: Settings,
  pump: RotateCcw,
  fan: RotateCcw,

  // Default fallback
  default: Component,
} as const

export type ComponentType = keyof typeof componentTypeIcons

export interface ComponentIconProps
  extends Omit<React.HTMLAttributes<HTMLDivElement>, "color">,
    VariantProps<typeof componentIconVariants> {
  type: ComponentType | string
  customIcon?: React.ComponentType<{ className?: string }>
  "data-testid"?: string
}

export const ComponentIcon = React.forwardRef<
  HTMLDivElement,
  ComponentIconProps
>(
  (
    {
      type,
      size,
      color,
      customIcon: CustomIcon,
      className,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    // Get the appropriate icon component
    const IconComponent =
      CustomIcon ||
      componentTypeIcons[type as ComponentType] ||
      componentTypeIcons.default

    return (
      <div
        ref={ref}
        className={cn(componentIconVariants({ size, color }), className)}
        role="img"
        aria-label={`${type} component icon`}
        data-testid={testId || `component-icon-${type}`}
        {...props}
      >
        <IconComponent className="h-full w-full" aria-hidden="true" />
      </div>
    )
  }
)

ComponentIcon.displayName = "ComponentIcon"

// Convenience components for common types
export const ResistorIcon = (props: Omit<ComponentIconProps, "type">) => (
  <ComponentIcon type="resistor" {...props} />
)

export const CapacitorIcon = (props: Omit<ComponentIconProps, "type">) => (
  <ComponentIcon type="capacitor" {...props} />
)

export const TransistorIcon = (props: Omit<ComponentIconProps, "type">) => (
  <ComponentIcon type="transistor" {...props} />
)

export const ICIcon = (props: Omit<ComponentIconProps, "type">) => (
  <ComponentIcon type="ic" {...props} />
)

export const SensorIcon = (props: Omit<ComponentIconProps, "type">) => (
  <ComponentIcon type="sensor" {...props} />
)

export const SwitchIcon = (props: Omit<ComponentIconProps, "type">) => (
  <ComponentIcon type="switch" {...props} />
)

export const ConnectorIcon = (props: Omit<ComponentIconProps, "type">) => (
  <ComponentIcon type="connector" {...props} />
)

// Hook for getting icon props from component data
export const useComponentIconProps = (component: {
  component_type?: string
  category?: string
}): ComponentIconProps => {
  // Try to match component type first, then category
  const type =
    component.component_type?.toLowerCase() ||
    component.category?.toLowerCase() ||
    "default"

  return { type }
}

// Helper function to get icon component by type
export const getComponentIcon = (
  type: string
): React.ComponentType<{ className?: string }> => {
  return componentTypeIcons[type as ComponentType] || componentTypeIcons.default
}

// Helper function to check if a type has a specific icon
export const hasComponentIcon = (type: string): boolean => {
  return type in componentTypeIcons
}

// Export available component types
export const availableComponentTypes = Object.keys(
  componentTypeIcons
) as ComponentType[]

// Export types for external use
export type ComponentIconSize = NonNullable<ComponentIconProps["size"]>
export type ComponentIconColor = NonNullable<ComponentIconProps["color"]>
