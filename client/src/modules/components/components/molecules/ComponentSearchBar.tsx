/**
 * ComponentSearchBar Molecule
 * Enhanced search component with suggestions, history, and accessibility
 */

import React, { useEffect, useRef, useState } from "react"

import {
  Clock,
  Filter,
  Loader2,
  Search,
  Settings,
  TrendingUp,
  X,
} from "lucide-react"

import { cn } from "@/lib/utils"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"

import { useComponentStore } from "@/modules/components/hooks/useComponentStore"
import { debounce } from "@/modules/components/utils/index"

export interface ComponentSearchBarProps {
  placeholder?: string
  showSuggestions?: boolean
  showHistory?: boolean
  showAdvancedToggle?: boolean
  className?: string
  onSearch?: (query: string) => void
  onAdvancedSearch?: () => void
  onClear?: () => void
  "data-testid"?: string
}

export const ComponentSearchBar = React.forwardRef<
  HTMLDivElement,
  ComponentSearchBarProps
>(
  (
    {
      placeholder = "Search components...",
      showSuggestions = true,
      showHistory = true,
      showAdvancedToggle = true,
      className,
      onSearch,
      onAdvancedSearch,
      onClear,
      "data-testid": testId,
      ...props
    },
    ref
  ) => {
    const {
      searchState,
      setSearchQuery,
      addToSearchHistory,
      clearSearchHistory,
      // toggleAdvancedSearch, // Unused variable
      // setSuggestions, // Unused variable
    } = useComponentStore()

    const [isOpen, setIsOpen] = useState(false)
    const [isLoading, setIsLoading] = useState(false)
    const [localQuery, setLocalQuery] = useState(searchState.query)
    const inputRef = useRef<HTMLInputElement>(null)

    // Debounced search function
    const debouncedSearch = debounce((query: string) => {
      setSearchQuery(query)
      if (onSearch) {
        onSearch(query)
      }
      if (query.trim()) {
        addToSearchHistory(query.trim())
      }
      setIsLoading(false)
    }, 300)

    // Handle input change
    const handleInputChange = (value: string) => {
      setLocalQuery(value)
      setIsLoading(true)
      debouncedSearch(value)
    }

    // Handle search submission
    const handleSubmit = (e: React.FormEvent) => {
      e.preventDefault()
      const query = localQuery.trim()
      if (query) {
        setSearchQuery(query)
        addToSearchHistory(query)
        if (onSearch) {
          onSearch(query)
        }
      }
      setIsOpen(false)
      inputRef.current?.blur()
    }

    // Handle clear
    const handleClear = () => {
      setLocalQuery("")
      setSearchQuery("")
      if (onClear) {
        onClear()
      }
      inputRef.current?.focus()
    }

    // Handle suggestion select
    const handleSuggestionSelect = (suggestion: string) => {
      setLocalQuery(suggestion)
      setSearchQuery(suggestion)
      addToSearchHistory(suggestion)
      if (onSearch) {
        onSearch(suggestion)
      }
      setIsOpen(false)
    }

    // Handle history item select
    const handleHistorySelect = (historyItem: string) => {
      setLocalQuery(historyItem)
      setSearchQuery(historyItem)
      if (onSearch) {
        onSearch(historyItem)
      }
      setIsOpen(false)
    }

    // Handle keyboard navigation
    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === "Escape") {
        setIsOpen(false)
        inputRef.current?.blur()
      } else if (e.key === "ArrowDown" && !isOpen) {
        setIsOpen(true)
      }
    }

    // Sync local query with store state
    useEffect(() => {
      setLocalQuery(searchState.query)
    }, [searchState.query])

    // Mock suggestions (in real app, these would come from API)
    const mockSuggestions = [
      "Siemens contactors",
      "ABB circuit breakers",
      "Schneider switches",
      "Phoenix connectors",
      "Weidmuller terminals",
    ]

    return (
      <div
        ref={ref}
        className={cn("relative w-full max-w-md", className)}
        data-testid={testId || "component-search-bar"}
        {...props}
      >
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <div className="relative">
              <form onSubmit={handleSubmit} className="relative">
                <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <Input
                  ref={inputRef}
                  type="text"
                  placeholder={placeholder}
                  value={localQuery}
                  onChange={(e) => handleInputChange(e.target.value)}
                  onKeyDown={handleKeyDown}
                  onFocus={() => setIsOpen(true)}
                  className="pr-20 pl-10"
                  aria-label="Search components"
                  aria-expanded={isOpen}
                  aria-haspopup="listbox"
                  data-testid={`${testId || "component-search-bar"}-input`}
                />

                <div className="absolute top-1/2 right-2 flex -translate-y-1/2 items-center gap-1">
                  {isLoading && (
                    <Loader2 className="h-4 w-4 animate-spin text-gray-400" />
                  )}

                  {localQuery && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={handleClear}
                      className="h-6 w-6 p-0 hover:bg-gray-100"
                      aria-label="Clear search"
                      data-testid={`${testId || "component-search-bar"}-clear`}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  )}

                  {showAdvancedToggle && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      onClick={onAdvancedSearch}
                      className={cn(
                        "h-6 w-6 p-0 hover:bg-gray-100",
                        searchState.is_advanced_mode &&
                          "bg-blue-100 text-blue-600"
                      )}
                      aria-label="Advanced search"
                      data-testid={`${testId || "component-search-bar"}-advanced`}
                    >
                      <Settings className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </form>
            </div>
          </PopoverTrigger>

          <PopoverContent
            className="w-[var(--radix-popover-trigger-width)] p-0"
            align="start"
            onOpenAutoFocus={(e) => e.preventDefault()}
          >
            <Command>
              <CommandList>
                {/* Recent searches */}
                {showHistory && searchState.search_history.length > 0 && (
                  <CommandGroup heading="Recent Searches">
                    {searchState.search_history
                      .slice(0, 5)
                      .map((item, index) => (
                        <CommandItem
                          key={`history-${index}`}
                          value={item}
                          onSelect={() => handleHistorySelect(item)}
                          className="flex items-center gap-2"
                        >
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span className="flex-1 truncate">{item}</span>
                        </CommandItem>
                      ))}

                    <CommandItem
                      onSelect={clearSearchHistory}
                      className="justify-center text-sm text-gray-500"
                    >
                      Clear history
                    </CommandItem>
                  </CommandGroup>
                )}

                {/* Suggestions */}
                {showSuggestions && (
                  <CommandGroup heading="Suggestions">
                    {mockSuggestions
                      .filter(
                        (suggestion) =>
                          suggestion
                            .toLowerCase()
                            .includes(localQuery.toLowerCase()) ||
                          localQuery.length === 0
                      )
                      .slice(0, 5)
                      .map((suggestion, index) => (
                        <CommandItem
                          key={`suggestion-${index}`}
                          value={suggestion}
                          onSelect={() => handleSuggestionSelect(suggestion)}
                          className="flex items-center gap-2"
                        >
                          <TrendingUp className="h-4 w-4 text-gray-400" />
                          <span className="flex-1 truncate">{suggestion}</span>
                        </CommandItem>
                      ))}
                  </CommandGroup>
                )}

                {/* No results */}
                {localQuery && (
                  <CommandEmpty>
                    <div className="py-6 text-center text-sm text-gray-500">
                      <p>No suggestions found</p>
                      <p className="mt-1 text-xs">
                        Press Enter to search for &quot;{localQuery}&quot;
                      </p>
                    </div>
                  </CommandEmpty>
                )}
              </CommandList>
            </Command>
          </PopoverContent>
        </Popover>

        {/* Active search indicator */}
        {searchState.query && (
          <div className="mt-2 flex items-center gap-2">
            <Badge variant="secondary" className="text-xs">
              <Search className="mr-1 h-3 w-3" />
              Searching: {searchState.query}
            </Badge>

            {searchState.is_advanced_mode && (
              <Badge variant="outline" className="text-xs">
                <Filter className="mr-1 h-3 w-3" />
                Advanced
              </Badge>
            )}
          </div>
        )}
      </div>
    )
  }
)

ComponentSearchBar.displayName = "ComponentSearchBar"

// Hook for search functionality
export const useComponentSearch = () => {
  const {
    searchState,
    setSearchQuery,
    addToSearchHistory,
    clearSearchHistory,
    toggleAdvancedSearch,
    setSuggestions,
  } = useComponentStore()

  const search = (query: string) => {
    setSearchQuery(query)
    if (query.trim()) {
      addToSearchHistory(query.trim())
    }
  }

  const clearSearch = () => {
    setSearchQuery("")
  }

  return {
    searchState,
    search,
    clearSearch,
    addToSearchHistory,
    clearSearchHistory,
    toggleAdvancedSearch,
    setSuggestions,
  }
}
