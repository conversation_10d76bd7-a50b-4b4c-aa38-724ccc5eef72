/**
 * ComponentCard Unit Tests
 * Tests the ComponentCard component with all props and interactions
 */

import React from "react"
import { createMockComponent } from "@/test/factories/componentFactories"
import { mockComponent, renderWithProviders } from "@/test/utils"
import { fireEvent, screen } from "@testing-library/react"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { ComponentCard } from "../ComponentCard"

// Mock UI components that might be causing issues
vi.mock("@/components/ui/dropdown-menu", () => ({
  DropdownMenu: ({ children }: any) => <div data-testid="dropdown-menu">{children}</div>,
  DropdownMenuTrigger: ({ children, asChild, ...props }: any) => 
    React.cloneElement(children, { ...props }),
  DropdownMenuContent: ({ children }: any) => <div data-testid="dropdown-content">{children}</div>,
  DropdownMenuItem: ({ children, onClick, ...props }: any) => (
    <div data-testid="dropdown-item" onClick={onClick} {...props}>{children}</div>
  ),
  DropdownMenuSeparator: () => <div data-testid="dropdown-separator" />,
}))

vi.mock("@/components/ui/tooltip", () => ({
  TooltipProvider: ({ children }: any) => children,
  Tooltip: ({ children }: any) => children,
  TooltipTrigger: ({ children, asChild, ...props }: any) => 
    React.cloneElement(children, { ...props }),
  TooltipContent: ({ children }: any) => <div data-testid="tooltip-content">{children}</div>,
}))

// Mock the atom components
vi.mock("@/modules/components/components/atoms", () => ({
  ComponentBadge: ({ children, ...props }: any) => (
    <span data-testid="component-badge-active" role="status" {...props}>
      <span data-testid="component-badge-icon">●</span>
      <span>Active</span>
    </span>
  ),
  useComponentBadgeProps: () => ({
    status: "active",
    size: "md",
    variant: "default"
  }),
  ComponentIcon: ({ ...props }: any) => (
    <div data-testid="component-icon-default" role="img" {...props}>
      <svg data-testid="component-icon-svg" />
    </div>
  ),
  useComponentIconProps: () => ({
    type: "default",
    color: "gray",
    size: "md"
  }),
}))

// Mock the utility functions
vi.mock("../../../utils", () => ({
  formatComponentName: vi.fn(
    (component) => component.display_name || component.name
  ),
  formatComponentDescription: vi.fn((description, maxLength) => 
    description ? description.substring(0, maxLength || 120) : ""
  ),
  formatPrice: vi.fn((price, currency) => `€${price}`),
  formatWeight: vi.fn((weight) => `${weight} kg`),
  formatDimensions: vi.fn(
    (dimensions) =>
      `${dimensions.length} × ${dimensions.width} × ${dimensions.height} mm`
  ),
  getComponentStatusColor: vi.fn(() => "text-green-600"),
  getComponentStatusText: vi.fn(() => "Available"),
}))

describe("ComponentCard", () => {
  const mockHandlers = {
    onSelect: vi.fn(),
    onEdit: vi.fn(),
    onDelete: vi.fn(),
    onView: vi.fn(),
    onTogglePreferred: vi.fn(),
    onCopy: vi.fn(),
    onExternalLink: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("Rendering", () => {
    it("renders component information correctly", () => {
      renderWithProviders(
        <ComponentCard component={mockComponent} {...mockHandlers} />
      )

      // Check for the formatted component name (display_name)
      expect(
        screen.getByText(mockComponent.display_name || mockComponent.name)
      ).toBeInTheDocument()
      
      // Check for manufacturer in the span
      expect(screen.getByText(mockComponent.manufacturer)).toBeInTheDocument()
      
      // Check for model number in the model line
      if (mockComponent.model_number) {
        expect(
          screen.getByText(`Model: ${mockComponent.model_number}`)
        ).toBeInTheDocument()
      }
    })

    it("renders with compact layout when compact prop is true", () => {
      renderWithProviders(
        <ComponentCard
          component={mockComponent}
          compact={true}
          {...mockHandlers}
        />
      )

      // In compact mode, some elements should have different styling
      const card = screen.getByRole("article")
      expect(card).toBeInTheDocument()
    })

    it("shows selected state when isSelected is true", () => {
      renderWithProviders(
        <ComponentCard
          component={mockComponent}
          isSelected={true}
          {...mockHandlers}
        />
      )

      const card = screen.getByRole("article")
      expect(card).toHaveClass("ring-2", "ring-blue-500")
    })

    it("hides actions when showActions is false", () => {
      renderWithProviders(
        <ComponentCard
          component={mockComponent}
          showActions={false}
          {...mockHandlers}
        />
      )

      // Check that action buttons are not present
      expect(
        screen.queryByTestId(`component-card-preferred`)
      ).not.toBeInTheDocument()
      expect(
        screen.queryByTestId(`component-card-actions`)
      ).not.toBeInTheDocument()
    })

    it("displays preferred star for preferred components", () => {
      const preferredComponent = createMockComponent({ is_preferred: true })

      renderWithProviders(
        <ComponentCard component={preferredComponent} {...mockHandlers} />
      )

      // Check that the preferred button shows the filled star (for preferred state)
      const preferredButton = screen.getByTestId(`component-card-preferred`)
      expect(preferredButton).toHaveAttribute("aria-label", "Remove from preferred")
    })

    it("displays inactive state for inactive components", () => {
      const inactiveComponent = createMockComponent({ is_active: false })

      renderWithProviders(
        <ComponentCard component={inactiveComponent} {...mockHandlers} />
      )

      // Check that the card has the opacity class for inactive state
      const card = screen.getByRole("article")
      expect(card).toHaveClass("opacity-60")
    })
  })

  describe("Interactions", () => {
    it("calls onView when card is clicked", () => {
      renderWithProviders(
        <ComponentCard component={mockComponent} {...mockHandlers} />
      )

      const card = screen.getByRole("article")
      fireEvent.click(card)

      expect(mockHandlers.onView).toHaveBeenCalledWith(mockComponent)
    })

    it("calls onEdit when edit menu item is clicked", async () => {
      renderWithProviders(
        <ComponentCard 
          component={mockComponent} 
          showActions={true}
          {...mockHandlers} 
        />
      )

      // Open the dropdown menu
      const actionsButton = screen.getByTestId(
        `component-card-actions`
      )
      fireEvent.click(actionsButton)

      // Click the edit menu item
      const editMenuItem = await screen.findByText("Edit")
      fireEvent.click(editMenuItem)

      expect(mockHandlers.onEdit).toHaveBeenCalledWith(mockComponent)
    })

    it("calls onDelete when delete menu item is clicked", async () => {
      renderWithProviders(
        <ComponentCard 
          component={mockComponent} 
          showActions={true} 
          {...mockHandlers} 
        />
      )

      // Open the dropdown menu
      const actionsButton = screen.getByTestId(
        `component-card-actions`
      )
      fireEvent.click(actionsButton)

      // Click the delete menu item
      const deleteMenuItem = await screen.findByText("Delete")
      fireEvent.click(deleteMenuItem)

      expect(mockHandlers.onDelete).toHaveBeenCalledWith(mockComponent)
    })

    it("calls onView when view menu item is clicked", async () => {
      renderWithProviders(
        <ComponentCard 
          component={mockComponent} 
          showActions={true} 
          {...mockHandlers} 
        />
      )

      // Open the dropdown menu
      const actionsButton = screen.getByTestId(
        `component-card-actions`
      )
      fireEvent.click(actionsButton)

      // Click the view menu item
      const viewMenuItem = await screen.findByText("View Details")
      fireEvent.click(viewMenuItem)

      expect(mockHandlers.onView).toHaveBeenCalledWith(mockComponent)
    })

    it("calls onTogglePreferred when preferred button is clicked", () => {
      renderWithProviders(
        <ComponentCard 
          component={mockComponent} 
          showActions={true} 
          {...mockHandlers} 
        />
      )

      const preferredButton = screen.getByTestId(
        `component-card-preferred`
      )
      fireEvent.click(preferredButton)

      expect(mockHandlers.onTogglePreferred).toHaveBeenCalledWith(mockComponent)
    })

    it("prevents event propagation when action buttons are clicked", () => {
      const onView = vi.fn()

      renderWithProviders(
        <ComponentCard
          component={mockComponent}
          showActions={true}
          {...mockHandlers}
          onView={onView}
        />
      )

      const preferredButton = screen.getByTestId(
        `component-card-preferred`
      )
      fireEvent.click(preferredButton)

      // onView should not be called when action button is clicked
      expect(onView).not.toHaveBeenCalled()
      expect(mockHandlers.onTogglePreferred).toHaveBeenCalledWith(mockComponent)
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA attributes", () => {
      renderWithProviders(
        <ComponentCard component={mockComponent} {...mockHandlers} />
      )

      const card = screen.getByRole("article")
      expect(card).toHaveAttribute(
        "aria-label",
        expect.stringContaining(
          mockComponent.display_name || mockComponent.name
        )
      )
    })

    it("supports keyboard navigation", () => {
      renderWithProviders(
        <ComponentCard component={mockComponent} {...mockHandlers} />
      )

      const card = screen.getByRole("article")
      expect(card).toHaveAttribute("tabIndex", "0")

      fireEvent.keyDown(card, { key: "Enter" })
      expect(mockHandlers.onView).toHaveBeenCalledWith(mockComponent)

      fireEvent.keyDown(card, { key: " " })
      expect(mockHandlers.onView).toHaveBeenCalledTimes(2)
    })

    it("has proper button labels for screen readers", () => {
      renderWithProviders(
        <ComponentCard 
          component={mockComponent} 
          showActions={true} 
          {...mockHandlers} 
        />
      )

      expect(
        screen.getByTestId(`component-card-preferred`)
      ).toHaveAttribute("aria-label")
      expect(
        screen.getByTestId(`component-card-actions`)
      ).toHaveAttribute("aria-label")
    })
  })

  describe("Edge Cases", () => {
    it("handles missing optional props gracefully", () => {
      renderWithProviders(<ComponentCard component={mockComponent} />)

      expect(
        screen.getByText(mockComponent.display_name || mockComponent.name)
      ).toBeInTheDocument()
    })

    it("handles component with missing optional fields", () => {
      const minimalComponent = createMockComponent({
        display_name: undefined,
        description: undefined,
        unit_price: undefined,
      })

      renderWithProviders(
        <ComponentCard component={minimalComponent} {...mockHandlers} />
      )

      expect(screen.getByText(minimalComponent.name)).toBeInTheDocument()
    })

    it("handles very long component names", () => {
      const longNameComponent = createMockComponent({
        display_name:
          "This is a very long component name that should be handled gracefully by the component card",
      })

      renderWithProviders(
        <ComponentCard component={longNameComponent} {...mockHandlers} />
      )

      expect(
        screen.getByText(
          longNameComponent.display_name || longNameComponent.name
        )
      ).toBeInTheDocument()
    })

    it("handles components with no price", () => {
      const noPriceComponent = createMockComponent({
        unit_price: null,
      })

      renderWithProviders(
        <ComponentCard component={noPriceComponent} {...mockHandlers} />
      )

      // Component name should still be displayed
      expect(
        screen.getByText(noPriceComponent.display_name || noPriceComponent.name)
      ).toBeInTheDocument()
      
      // Price section should not be present when unit_price is null
      expect(screen.queryByText(/\$|€|£/)).not.toBeInTheDocument()
    })
  })

  describe("Performance", () => {
    it("does not re-render unnecessarily", () => {
      const { rerender } = renderWithProviders(
        <ComponentCard component={mockComponent} {...mockHandlers} />
      )

      // Re-render with same props
      rerender(<ComponentCard component={mockComponent} {...mockHandlers} />)

      // Component should still be rendered correctly
      expect(
        screen.getByText(mockComponent.display_name || mockComponent.name)
      ).toBeInTheDocument()
    })
  })
})
