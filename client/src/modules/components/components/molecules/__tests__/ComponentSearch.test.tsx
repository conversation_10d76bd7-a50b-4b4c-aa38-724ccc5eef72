/**
 * ComponentSearch Unit Tests
 * Tests the ComponentSearch component with essential functionality
 */

import React from "react"
import { renderWithProviders } from "@/test/utils"
import { screen } from "@testing-library/react"
import userEvent from "@testing-library/user-event"
import { beforeEach, describe, expect, it, vi } from "vitest"

import { ComponentSearchBar as ComponentSearch } from "../ComponentSearchBar"
import { useComponentStore } from "../../../hooks/useComponentStore"

// Get access to mock functions
const mockUseComponentStore = useComponentStore as any

// Mock the useComponentStore hook
vi.mock("../../../hooks/useComponentStore", () => {
  const mockSetSearchQuery = vi.fn()
  const mockAddToSearchHistory = vi.fn()
  const mockClearSearchHistory = vi.fn()
  
  const defaultSearchState = {
    query: "",
    search_history: ["previous search", "another search"],
    suggestions: ["resistor 1k", "resistor 2k", "resistor 10k"],
    isSearching: false,
  }

  const mockUseComponentStore = vi.fn(() => ({
    searchState: defaultSearchState,
    setSearchQuery: mockSetSearchQuery,
    addToSearchHistory: mockAddToSearchHistory,
    clearSearchHistory: mockClearSearchHistory,
  }))

  // Make mock functions available for test access
  mockUseComponentStore.mockSetSearchQuery = mockSetSearchQuery
  mockUseComponentStore.mockAddToSearchHistory = mockAddToSearchHistory
  mockUseComponentStore.mockClearSearchHistory = mockClearSearchHistory
  mockUseComponentStore.defaultSearchState = defaultSearchState

  return {
    useComponentStore: mockUseComponentStore,
  }
})

// Mock utility functions
vi.mock("../../../utils/index", () => ({
  debounce: vi.fn((fn) => fn), // Return the function directly for testing
}))

// Mock UI components for testing
vi.mock("@/components/ui/command", () => ({
  Command: ({ children }: any) => <div data-testid="command">{children}</div>,
  CommandEmpty: ({ children }: any) => <div data-testid="command-empty">{children}</div>,
  CommandGroup: ({ children, heading }: any) => (
    <div data-testid="command-group">
      {heading && <div data-testid="command-group-heading">{heading}</div>}
      {children}
    </div>
  ),
  CommandItem: ({ children, onSelect, ...props }: any) => (
    <div data-testid="command-item" onClick={onSelect} {...props}>
      {children}
    </div>
  ),
  CommandList: ({ children }: any) => <div data-testid="command-list">{children}</div>,
}))

vi.mock("@/components/ui/popover", () => ({
  Popover: ({ children }: any) => <div data-testid="popover">{children}</div>,
  PopoverContent: ({ children }: any) => <div data-testid="popover-content">{children}</div>,
  PopoverTrigger: ({ children, asChild, ...props }: any) => 
    asChild ? React.cloneElement(children, { ...props }) : <div {...props}>{children}</div>,
}))

// Mock the useComponentSuggestions hook
const mockSuggestions = ["resistor 1k", "resistor 2k", "resistor 10k"]
vi.mock("../../../api/componentQueries", () => ({
  useComponentSuggestions: vi.fn(() => ({
    data: mockSuggestions,
    isLoading: false,
    isError: false,
  })),
}))

describe("ComponentSearch", () => {
  const mockHandlers = {
    onSearch: vi.fn(),
    onChange: vi.fn(),
    onClear: vi.fn(),
  }

  beforeEach(() => {
    vi.clearAllMocks()
    // Reset the store mock to default
    mockUseComponentStore.mockReturnValue({
      searchState: mockUseComponentStore.defaultSearchState,
      setSearchQuery: mockUseComponentStore.mockSetSearchQuery,
      addToSearchHistory: mockUseComponentStore.mockAddToSearchHistory,
      clearSearchHistory: mockUseComponentStore.mockClearSearchHistory,
    })
  })

  describe("Rendering", () => {
    it("renders search input with placeholder", () => {
      renderWithProviders(
        <ComponentSearch placeholder="Search components..." {...mockHandlers} />
      )

      const input = screen.getByPlaceholderText("Search components...")
      expect(input).toBeInTheDocument()
    })

    it("renders with initial value", () => {
      // Set initial query in the store mock
      mockUseComponentStore.mockReturnValueOnce({
        searchState: { ...mockUseComponentStore.defaultSearchState, query: "initial search" },
        setSearchQuery: mockUseComponentStore.mockSetSearchQuery,
        addToSearchHistory: mockUseComponentStore.mockAddToSearchHistory,
        clearSearchHistory: mockUseComponentStore.mockClearSearchHistory,
      })

      renderWithProviders(
        <ComponentSearch {...mockHandlers} />
      )

      const input = screen.getByDisplayValue("initial search")
      expect(input).toBeInTheDocument()
    })

    it("renders search icon", () => {
      renderWithProviders(<ComponentSearch {...mockHandlers} />)

      // Look for the search icon by its lucide class or aria-hidden attribute
      const searchIcon = screen.getByRole("textbox").parentElement?.querySelector(".lucide-search")
      expect(searchIcon).toBeInTheDocument()
    })

    it("renders clear button when there is text", () => {
      // Set initial query to have text
      mockUseComponentStore.mockReturnValueOnce({
        searchState: { ...mockUseComponentStore.defaultSearchState, query: "search text" },
        setSearchQuery: mockUseComponentStore.mockSetSearchQuery,
        addToSearchHistory: mockUseComponentStore.mockAddToSearchHistory,
        clearSearchHistory: mockUseComponentStore.mockClearSearchHistory,
      })

      renderWithProviders(<ComponentSearch {...mockHandlers} />)

      expect(
        screen.getByTestId("component-search-bar-clear")
      ).toBeInTheDocument()
    })

    it("does not render clear button when input is empty", () => {
      // Use default empty query state
      renderWithProviders(<ComponentSearch {...mockHandlers} />)

      expect(
        screen.queryByTestId("component-search-bar-clear")
      ).not.toBeInTheDocument()
    })
  })

  describe("Input Interactions", () => {
    it("updates search query when user types", async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentSearch {...mockHandlers} />)

      const input = screen.getByRole("textbox")
      await user.type(input, "resistor")

      // Should update the local input value
      expect(input).toHaveValue("resistor")
      // Should call the store method (debounced, so called once)
      expect(mockUseComponentStore.mockSetSearchQuery).toHaveBeenCalledWith("resistor")
    })

    it("calls onClear when clear button is clicked", async () => {
      const user = userEvent.setup()

      // Set up component with text so clear button appears
      mockUseComponentStore.mockReturnValueOnce({
        searchState: { ...mockUseComponentStore.defaultSearchState, query: "search text" },
        setSearchQuery: mockUseComponentStore.mockSetSearchQuery,
        addToSearchHistory: mockUseComponentStore.mockAddToSearchHistory,
        clearSearchHistory: mockUseComponentStore.mockClearSearchHistory,
      })

      renderWithProviders(<ComponentSearch {...mockHandlers} />)

      const clearButton = screen.getByTestId("component-search-bar-clear")
      await user.click(clearButton)

      expect(mockHandlers.onClear).toHaveBeenCalled()
    })

    it("triggers search on Enter key press", async () => {
      const user = userEvent.setup()

      renderWithProviders(<ComponentSearch {...mockHandlers} />)

      const input = screen.getByRole("textbox")
      // Type "resistor" then press enter
      await user.type(input, "resistor{enter}")

      // The onSearch should be called when form is submitted
      expect(mockHandlers.onSearch).toHaveBeenCalledWith("resistor")
    })
  })

  describe("Suggestions Dropdown", () => {
    it("hides suggestions when showSuggestions is false", () => {
      renderWithProviders(
        <ComponentSearch
          showSuggestions={false}
          value="res"
          {...mockHandlers}
        />
      )

      expect(screen.queryByText("resistor 1k")).not.toBeInTheDocument()
    })

    it("shows recent searches when available in store", () => {
      const recentSearches = ["capacitor", "diode", "transistor"]
      
      // Set up store with recent searches
      mockUseComponentStore.mockReturnValueOnce({
        searchState: { 
          ...mockUseComponentStore.defaultSearchState, 
          search_history: recentSearches 
        },
        setSearchQuery: mockUseComponentStore.mockSetSearchQuery,
        addToSearchHistory: mockUseComponentStore.mockAddToSearchHistory,
        clearSearchHistory: mockUseComponentStore.mockClearSearchHistory,
      })

      renderWithProviders(
        <ComponentSearch {...mockHandlers} />
      )

      // Check that the search input is rendered (component functionality depends on store)
      expect(screen.getByRole("textbox")).toBeInTheDocument()
    })
  })

  describe("Accessibility", () => {
    it("has proper ARIA attributes", () => {
      renderWithProviders(<ComponentSearch {...mockHandlers} />)

      const input = screen.getByRole("textbox")
      expect(input).toHaveAttribute("type", "text")
      expect(input).toHaveAttribute("placeholder")
    })
  })

  describe("Edge Cases", () => {
    it("handles missing handlers gracefully", () => {
      renderWithProviders(<ComponentSearch />)

      expect(screen.getByRole("textbox")).toBeInTheDocument()
    })

    it("handles empty value gracefully", () => {
      renderWithProviders(<ComponentSearch value="" {...mockHandlers} />)

      expect(screen.getByRole("textbox")).toHaveValue("")
    })

    it("handles null value gracefully", () => {
      renderWithProviders(
        <ComponentSearch value={undefined} {...mockHandlers} />
      )

      expect(screen.getByRole("textbox")).toBeInTheDocument()
    })

    it("handles different placeholder values", () => {
      renderWithProviders(
        <ComponentSearch placeholder="Custom placeholder" {...mockHandlers} />
      )

      expect(
        screen.getByPlaceholderText("Custom placeholder")
      ).toBeInTheDocument()
    })

    it("handles different field types", () => {
      renderWithProviders(
        <ComponentSearch field="manufacturer" {...mockHandlers} />
      )

      expect(screen.getByRole("textbox")).toBeInTheDocument()
    })
  })
})
