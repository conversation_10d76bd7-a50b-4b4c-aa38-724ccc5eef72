/**
 * Simplified Validation Utilities
 * Basic validation functions for component management
 */

import type { ComponentCreate, ComponentUpdate } from "../api"

// Validation result interface
export interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings?: ValidationError[]
}

export interface ValidationError {
  field: string
  message: string
  code?: string
  severity?: "error" | "warning" | "info"
}

// Basic component validation
export function validateComponent(
  data: unknown,
  _mode: "create" | "update" = "create"
): ValidationResult {
  const errors: ValidationError[] = []
  const warnings: ValidationError[] = []

  try {
    if (!data || typeof data !== "object") {
      errors.push({
        field: "root",
        message: "Invalid data provided",
        code: "INVALID_DATA"
      })
      return { isValid: false, errors, warnings }
    }

    const componentData = data as Partial<ComponentCreate | ComponentUpdate>

    // Basic required field validation
    if (!componentData.name || componentData.name.trim().length === 0) {
      errors.push({
        field: "name",
        message: "Component name is required",
        code: "REQUIRED_FIELD"
      })
    }

    if (!componentData.manufacturer || componentData.manufacturer.trim().length === 0) {
      errors.push({
        field: "manufacturer",
        message: "Manufacturer is required",
        code: "REQUIRED_FIELD"
      })
    }

    if (!componentData.model_number || componentData.model_number.trim().length === 0) {
      errors.push({
        field: "model_number",
        message: "Model number is required",
        code: "REQUIRED_FIELD"
      })
    }

    // Optional validations with warnings
    if (componentData.unit_price && componentData.unit_price < 0) {
      errors.push({
        field: "unit_price",
        message: "Unit price cannot be negative",
        code: "INVALID_VALUE"
      })
    }

    if (componentData.weight_kg && componentData.weight_kg < 0) {
      errors.push({
        field: "weight_kg",
        message: "Weight cannot be negative",
        code: "INVALID_VALUE"
      })
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }

  } catch (error: unknown) {
    return {
      isValid: false,
      errors: [{
        field: "root",
        message: `Validation error: ${String(error)}`,
        code: "VALIDATION_ERROR"
      }],
      warnings
    }
  }
}

// Form validation wrapper
export function validateComponentForm(
  data: Partial<ComponentCreate | ComponentUpdate>,
  _mode: "create" | "update" = "create"
): ValidationResult {
  return validateComponent(data, _mode)
}

// Field validation
export function validateField(
  value: unknown,
  field: string,
  rules: Record<string, unknown> = {}
): ValidationResult {
  const errors: ValidationError[] = []
  
  try {
    // Basic validation logic
    if (rules.required && (!value || String(value).trim().length === 0)) {
      errors.push({
        field,
        message: `${field} is required`,
        code: "REQUIRED_FIELD"
      })
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  } catch (error: unknown) {
    return {
      isValid: false,
      errors: [{
        field,
        message: `Field validation failed: ${String(error)}`,
        code: "VALIDATION_ERROR"
      }]
    }
  }
}

// Utility functions
export function formatValidationErrors(errors: ValidationError[]): string[] {
  return errors.map((error: ValidationError) => 
    typeof error === "object" && error.message ? error.message : String(error)
  )
}

export function hasValidationErrors(result: ValidationResult): boolean {
  return !result.isValid || result.errors.length > 0
}

export function getErrorsForField(result: ValidationResult, fieldName: string): ValidationError[] {
  return result.errors.filter(error => error.field === fieldName)
}

// Export default validation function
export { validateComponent as default }