/**
 * API Utilities
 * Functions for handling API requests and responses.
 */

// API error handling utilities
export class ComponentApiError extends <PERSON><PERSON>r {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = "ComponentApiError"
  }
}

// Parse API error response
export function parseApiError(error: any): ComponentApiError {
  if (error instanceof ComponentApiError) {
    return error
  }

  // Handle different error formats
  if (error?.response?.data) {
    const { data, status } = error.response
    return new ComponentApiError(
      data.detail || data.message || "An error occurred",
      status,
      data.error_code,
      data
    )
  }

  if (error?.detail) {
    return new ComponentApiError(
      error.detail,
      error.status || 500,
      error.error_code
    )
  }

  return new ComponentApiError(
    error?.message || "An unexpected error occurred",
    500
  )
}

// Format error message for display
export function formatErrorMessage(error: ComponentApiError | Error): string {
  if (error instanceof ComponentApiError) {
    switch (error.status) {
      case 400:
        return `Invalid request: ${error.message}`
      case 401:
        return "Authentication required. Please log in."
      case 403:
        return "You do not have permission to perform this action."
      case 404:
        return "The requested component was not found."
      case 409:
        return `Conflict: ${error.message}`
      case 422:
        return `Validation error: ${error.message}`
      case 500:
        return "A server error occurred. Please try again later."
      default:
        return error.message
    }
  }

  return error.message || "An unexpected error occurred"
}
