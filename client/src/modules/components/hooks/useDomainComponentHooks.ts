/**
 * Domain-Aware Component Hooks
 *
 * React hooks that provide clean interfaces for components to interact
 * with domain objects while maintaining separation of concerns.
 */

import { useCallback, useEffect, useMemo } from "react"

import type {
  Component,
  ComponentCatalog,
  ComponentCreateData,
  ComponentUpdateData,
  ValidationResult,
} from "../domain"

import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import {
  useComponentLoadingStates,
  useComponentOperationError,
  useDomainComponentCatalog,
  useDomainComponentStore,
  useLastValidationResult,
  useSelectedDomainComponents,
} from "./useDomainComponentStore"

// Query keys for React Query
const COMPONENT_QUERY_KEYS = {
  catalog: ["component-catalog"] as const,
  component: (id: number) => ["component", id] as const,
  search: (criteria: any) => ["component-search", criteria] as const,
  preferred: ["component-preferred"] as const,
  statistics: ["component-statistics"] as const,
}

/**
 * Hook for managing component catalog with domain objects
 */
export function useComponentCatalog(criteria: any = {}, pagination: any = {}) {
  const { loadCatalog, refreshCatalog, clearCatalog } =
    useDomainComponentStore()
  const catalog = useDomainComponentCatalog()
  const { isLoadingCatalog } = useComponentLoadingStates()
  const operationError = useComponentOperationError()

  // React Query for catalog data with domain integration
  const query = useQuery({
    queryKey: [...COMPONENT_QUERY_KEYS.catalog, criteria, pagination],
    queryFn: async () => {
      await loadCatalog(criteria, pagination)
      return catalog
    },
    enabled: true,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  const refresh = useCallback(async () => {
    await refreshCatalog()
    query.refetch()
  }, [refreshCatalog, query])

  const clear = useCallback(() => {
    clearCatalog()
  }, [clearCatalog])

  return {
    catalog,
    isLoading: isLoadingCatalog || query.isLoading,
    error: operationError || query.error,
    refresh,
    clear,
    refetch: query.refetch,
  }
}

/**
 * Hook for loading and managing individual components
 */
export function useComponent(id: number | null) {
  const { loadComponent, getFromCache } = useDomainComponentStore()
  const { isLoadingComponent } = useComponentLoadingStates()
  const operationError = useComponentOperationError()

  // Check cache first
  const cachedComponent = useMemo(() => {
    return id ? getFromCache(id) : null
  }, [id, getFromCache])

  const query = useQuery({
    queryKey: id ? COMPONENT_QUERY_KEYS.component(id) : ["component-null"],
    queryFn: async () => {
      if (!id) return null
      return await loadComponent(id)
    },
    enabled: !!id,
    initialData: cachedComponent,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  })

  return {
    component: query.data,
    isLoading: isLoadingComponent || query.isLoading,
    error: operationError || query.error,
    refetch: query.refetch,
  }
}

/**
 * Hook for creating components with domain validation
 */
export function useCreateComponent() {
  const { createComponent, validateComponent } = useDomainComponentStore()
  const { isSaving } = useComponentLoadingStates()
  const operationError = useComponentOperationError()
  const validationResult = useLastValidationResult()
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn: async (data: ComponentCreateData) => {
      return await createComponent(data)
    },
    onSuccess: (component) => {
      if (component) {
        // Invalidate and refetch catalog queries
        queryClient.invalidateQueries({
          queryKey: COMPONENT_QUERY_KEYS.catalog,
        })
        queryClient.invalidateQueries({
          queryKey: COMPONENT_QUERY_KEYS.statistics,
        })

        // Set the individual component query data
        queryClient.setQueryData(
          COMPONENT_QUERY_KEYS.component(component.id),
          component
        )
      }
    },
  })

  const create = useCallback(
    async (data: ComponentCreateData) => {
      return mutation.mutateAsync(data)
    },
    [mutation]
  )

  const validate = useCallback(
    (data: ComponentCreateData): ValidationResult => {
      return validateComponent(data)
    },
    [validateComponent]
  )

  return {
    create,
    validate,
    isCreating: isSaving || mutation.isPending,
    error: operationError || mutation.error,
    validationResult,
    reset: mutation.reset,
  }
}

/**
 * Hook for updating components with domain validation
 */
export function useUpdateComponent() {
  const { updateComponent, validateUpdate, getFromCache } =
    useDomainComponentStore()
  const { isSaving } = useComponentLoadingStates()
  const operationError = useComponentOperationError()
  const validationResult = useLastValidationResult()
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn: async ({
      id,
      data,
    }: {
      id: number
      data: ComponentUpdateData
    }) => {
      return await updateComponent(id, data)
    },
    onSuccess: (component) => {
      if (component) {
        // Invalidate and refetch catalog queries
        queryClient.invalidateQueries({
          queryKey: COMPONENT_QUERY_KEYS.catalog,
        })

        // Update the individual component query data
        queryClient.setQueryData(
          COMPONENT_QUERY_KEYS.component(component.id),
          component
        )
      }
    },
  })

  const update = useCallback(
    async (id: number, data: ComponentUpdateData) => {
      return mutation.mutateAsync({ id, data })
    },
    [mutation]
  )

  const validate = useCallback(
    (id: number, data: ComponentUpdateData): ValidationResult | null => {
      const component = getFromCache(id)
      if (!component) return null

      return validateUpdate(component, data)
    },
    [validateUpdate, getFromCache]
  )

  return {
    update,
    validate,
    isUpdating: isSaving || mutation.isPending,
    error: operationError || mutation.error,
    validationResult,
    reset: mutation.reset,
  }
}

/**
 * Hook for deleting components
 */
export function useDeleteComponent() {
  const { deleteComponent } = useDomainComponentStore()
  const { isSaving } = useComponentLoadingStates()
  const operationError = useComponentOperationError()
  const queryClient = useQueryClient()

  const mutation = useMutation({
    mutationFn: async (id: number) => {
      return await deleteComponent(id)
    },
    onSuccess: (success, id) => {
      if (success) {
        // Remove from individual component queries
        queryClient.removeQueries({
          queryKey: COMPONENT_QUERY_KEYS.component(id),
        })

        // Invalidate catalog queries
        queryClient.invalidateQueries({
          queryKey: COMPONENT_QUERY_KEYS.catalog,
        })
        queryClient.invalidateQueries({
          queryKey: COMPONENT_QUERY_KEYS.statistics,
        })
      }
    },
  })

  const deleteComp = useCallback(
    async (id: number) => {
      return mutation.mutateAsync(id)
    },
    [mutation]
  )

  return {
    delete: deleteComp,
    isDeleting: isSaving || mutation.isPending,
    error: operationError || mutation.error,
    reset: mutation.reset,
  }
}

/**
 * Hook for component search with domain objects
 */
export function useComponentSearch() {
  const { searchCatalog } = useDomainComponentStore()
  const catalog = useDomainComponentCatalog()

  const search = useCallback(
    (criteria: any): Component[] => {
      if (!catalog) return []
      return searchCatalog(criteria)
    },
    [catalog, searchCatalog]
  )

  return {
    search,
    hasCatalog: !!catalog,
  }
}

/**
 * Hook for preferred components
 */
export function usePreferredComponents() {
  const { getPreferredComponents } = useDomainComponentStore()
  const catalog = useDomainComponentCatalog()

  const query = useQuery({
    queryKey: COMPONENT_QUERY_KEYS.preferred,
    queryFn: () => {
      return getPreferredComponents()
    },
    enabled: !!catalog,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  return {
    preferredComponents: query.data || [],
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
  }
}

/**
 * Hook for catalog statistics
 */
export function useCatalogStatistics() {
  const { getCatalogStatistics } = useDomainComponentStore()
  const catalog = useDomainComponentCatalog()

  const query = useQuery({
    queryKey: COMPONENT_QUERY_KEYS.statistics,
    queryFn: () => {
      return getCatalogStatistics()
    },
    enabled: !!catalog,
    staleTime: 10 * 60 * 1000, // 10 minutes
  })

  return {
    statistics: query.data,
    isLoading: query.isLoading,
    error: query.error,
    refetch: query.refetch,
  }
}

/**
 * Hook for component selection with domain objects
 */
export function useComponentSelection() {
  const {
    selectDomainComponent,
    deselectDomainComponent,
    getSelectedDomainComponents,
    clearDomainSelection,
  } = useDomainComponentStore()

  const selectedComponents = useSelectedDomainComponents()

  const select = useCallback(
    (component: Component) => {
      selectDomainComponent(component)
    },
    [selectDomainComponent]
  )

  const deselect = useCallback(
    (component: Component) => {
      deselectDomainComponent(component)
    },
    [deselectDomainComponent]
  )

  const toggle = useCallback(
    (component: Component) => {
      const isSelected = selectedComponents.some((c) => c.id === component.id)
      if (isSelected) {
        deselect(component)
      } else {
        select(component)
      }
    },
    [selectedComponents, select, deselect]
  )

  const clear = useCallback(() => {
    clearDomainSelection()
  }, [clearDomainSelection])

  const isSelected = useCallback(
    (component: Component) => {
      return selectedComponents.some((c) => c.id === component.id)
    },
    [selectedComponents]
  )

  return {
    selectedComponents,
    select,
    deselect,
    toggle,
    clear,
    isSelected,
    count: selectedComponents.length,
  }
}

/**
 * Hook for bulk operations with domain objects
 */
export function useBulkOperations() {
  const { bulkCreateComponents, bulkUpdateComponents } =
    useDomainComponentStore()
  const { isSaving } = useComponentLoadingStates()
  const operationError = useComponentOperationError()
  const queryClient = useQueryClient()

  const bulkCreateMutation = useMutation({
    mutationFn: async (components: ComponentCreateData[]) => {
      return await bulkCreateComponents(components)
    },
    onSuccess: () => {
      // Invalidate catalog queries
      queryClient.invalidateQueries({ queryKey: COMPONENT_QUERY_KEYS.catalog })
      queryClient.invalidateQueries({
        queryKey: COMPONENT_QUERY_KEYS.statistics,
      })
    },
  })

  const bulkUpdateMutation = useMutation({
    mutationFn: async (
      updates: Array<{ component: Component; data: ComponentUpdateData }>
    ) => {
      return await bulkUpdateComponents(updates)
    },
    onSuccess: () => {
      // Invalidate catalog queries
      queryClient.invalidateQueries({ queryKey: COMPONENT_QUERY_KEYS.catalog })
    },
  })

  const bulkCreate = useCallback(
    async (components: ComponentCreateData[]) => {
      return bulkCreateMutation.mutateAsync(components)
    },
    [bulkCreateMutation]
  )

  const bulkUpdate = useCallback(
    async (
      updates: Array<{ component: Component; data: ComponentUpdateData }>
    ) => {
      return bulkUpdateMutation.mutateAsync(updates)
    },
    [bulkUpdateMutation]
  )

  return {
    bulkCreate,
    bulkUpdate,
    isProcessing:
      isSaving || bulkCreateMutation.isPending || bulkUpdateMutation.isPending,
    error:
      operationError || bulkCreateMutation.error || bulkUpdateMutation.error,
    resetCreate: bulkCreateMutation.reset,
    resetUpdate: bulkUpdateMutation.reset,
  }
}

/**
 * Hook for component comparison using domain services
 */
export function useComponentComparison() {
  const { validationService } = useDomainComponentStore()

  const compare = useCallback(
    (component1: Component, component2: Component) => {
      return validationService.compareComponents(component1, component2)
    },
    [validationService]
  )

  const findSimilar = useCallback(
    (component: Component, candidates: Component[]) => {
      return candidates
        .map((candidate) => ({
          component: candidate,
          similarity: validationService.compareComponents(component, candidate),
        }))
        .filter((result) => result.similarity.overallSimilarity > 0.7)
        .sort(
          (a, b) =>
            b.similarity.overallSimilarity - a.similarity.overallSimilarity
        )
        .map((result) => result.component)
    },
    [validationService]
  )

  return {
    compare,
    findSimilar,
  }
}

/**
 * Combined hook that provides all component operations
 */
export function useComponentOperations() {
  const catalog = useComponentCatalog()
  const create = useCreateComponent()
  const update = useUpdateComponent()
  const deleteOp = useDeleteComponent()
  const selection = useComponentSelection()
  const bulk = useBulkOperations()
  const search = useComponentSearch()
  const preferred = usePreferredComponents()
  const statistics = useCatalogStatistics()
  const comparison = useComponentComparison()

  return {
    // Catalog operations
    catalog: catalog.catalog,
    isLoadingCatalog: catalog.isLoading,
    refreshCatalog: catalog.refresh,
    clearCatalog: catalog.clear,

    // Individual component operations
    create: create.create,
    update: update.update,
    delete: deleteOp.delete,
    validate: create.validate,
    validateUpdate: update.validate,

    // Search operations
    search: search.search,
    preferredComponents: preferred.preferredComponents,

    // Selection operations
    selectedComponents: selection.selectedComponents,
    selectComponent: selection.select,
    deselectComponent: selection.deselect,
    toggleSelection: selection.toggle,
    clearSelection: selection.clear,
    isSelected: selection.isSelected,

    // Bulk operations
    bulkCreate: bulk.bulkCreate,
    bulkUpdate: bulk.bulkUpdate,

    // Comparison operations
    compareComponents: comparison.compare,
    findSimilarComponents: comparison.findSimilar,

    // Statistics
    statistics: statistics.statistics,

    // Loading states
    isProcessing:
      create.isCreating ||
      update.isUpdating ||
      deleteOp.isDeleting ||
      bulk.isProcessing,

    // Errors
    error: create.error || update.error || deleteOp.error || bulk.error,

    // Validation
    validationResult: create.validationResult || update.validationResult,
  }
}

/**
 * Hook for component form integration with domain validation
 */
export function useComponentForm(initialData?: Partial<ComponentCreateData>) {
  const { validateComponent } = useDomainComponentStore()
  const { useFormState, setFormErrors, updateFormField, resetForm } =
    useDomainComponentStore()

  const formState = useFormState()

  const validate = useCallback(
    (data: ComponentCreateData) => {
      const result = validateComponent(data)

      if (!result.isValid) {
        const formErrors: Record<string, string> = {}
        result.errors.forEach((error, index) => {
          formErrors[`error_${index}`] = error
        })
        setFormErrors(formErrors)
      } else {
        setFormErrors({})
      }

      return result
    },
    [validateComponent, setFormErrors]
  )

  const updateField = useCallback(
    (field: string, value: any) => {
      updateFormField(field as keyof ComponentCreateData, value)

      // Validate on change if form is dirty
      if (formState.is_dirty) {
        const currentData = {
          ...initialData,
          ...formState.data,
        } as ComponentCreateData
        validate(currentData)
      }
    },
    [updateFormField, formState, initialData, validate]
  )

  const reset = useCallback(() => {
    resetForm()
  }, [resetForm])

  return {
    formState,
    validate,
    updateField,
    reset,
    isValid: formState.is_valid,
    isDirty: formState.is_dirty,
    isSubmitting: formState.is_submitting,
    errors: formState.errors,
    touched: formState.touched,
  }
}
