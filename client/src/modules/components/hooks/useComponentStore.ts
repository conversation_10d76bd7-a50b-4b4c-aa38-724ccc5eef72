/**
 * Enhanced Component Store Hook
 * Comprehensive Zustand store with validation, persistence, and type safety
 */

// Types imported in main file
import type {
  BulkOperationState,
  ComponentDisplayOptions,
  ComponentFilterState,
  ComponentFormState,
  ComponentListState,
  ComponentSearchState,
} from "../types"

// Lodash pick function implementation
const pick = <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
  const result = {} as Pick<T, K>
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  return result
}
import { create } from "zustand"
import { devtools, persist, subscribeWithSelector } from "zustand/middleware"
import { immer } from "zustand/middleware/immer"

// Additional UI state types
interface FilterState {
  active_filters: Record<string, any>
  filter_count: number
  show_advanced_filters: boolean
}

interface SidebarState {
  is_open: boolean
  width: number
  collapsed: boolean
  active_section: "filters" | "search" | "stats"
}

interface ModalState {
  is_open: boolean
  type?: "create" | "edit" | "delete" | "bulk" | "import" | "export"
  data?: any
  loading: boolean
  error?: string
}

interface UserPreferences {
  display_options: ComponentDisplayOptions
  auto_save: boolean
  theme: "light" | "dark" | "system"
}

interface Notification {
  id: string
  type: "success" | "error" | "warning" | "info"
  title: string
  message: string
  timestamp: Date
  duration?: number
}

type ViewMode = "grid" | "list" | "table" | "cards"
type SortConfig = {
  field: string
  direction: "asc" | "desc"
}

// Default creator functions
const createDefaultListState = (): ComponentListState => ({
  filters: {
    search_term: "",
    component_category_id: null,
    component_type_id: null,
    manufacturer: "",
    is_preferred: null,
    is_active: null,
    min_price: null,
    max_price: null,
    currency: "EUR",
    stock_status: "",
  },
  sortBy: "name",
  sortOrder: "asc",
  page: 1,
  pageSize: 20,
  viewMode: "grid",
  selectedComponents: [],
})

const createDefaultSearchState = (): ComponentSearchState => ({
  query: "",
  field: "name",
  suggestions: [],
  isSearching: false,
  isAdvancedMode: false,
  searchHistory: [],
  recentSearches: [],
  savedSearches: [],
})

const createDefaultFilterState = (): FilterState => ({
  active_filters: {},
  filter_count: 0,
  show_advanced_filters: false,
})

const createDefaultUserPreferences = (): UserPreferences => ({
  display_options: {
    showImages: true,
    showSpecifications: true,
    showPricing: true,
    showPrices: true,
    showAvailability: true,
    compactMode: false,
  },
  auto_save: true,
  theme: "system",
})

// Enhanced store interface with comprehensive state management
interface ComponentStore {
  // Core State
  listState: ComponentListState
  searchState: ComponentSearchState
  filterState: FilterState
  bulkState: BulkOperationState
  formState: ComponentFormState
  sidebarState: SidebarState
  modalState: ModalState
  userPreferences: UserPreferences
  notifications: Notification[]
  
  // Computed Properties
  displayOptions: ComponentDisplayOptions

  // List Management Actions
  setListState: (state: Partial<ComponentListState>) => void
  updateFilters: (filters: Partial<ComponentFilterState>) => void
  clearFilters: () => void
  setViewMode: (mode: ViewMode) => void
  setSortConfig: (config: SortConfig) => void
  setPage: (page: number) => void
  setPageSize: (size: number) => void

  // Search Management Actions
  setSearchState: (state: Partial<ComponentSearchState>) => void
  setSearchQuery: (query: string) => void
  addToSearchHistory: (query: string) => void
  clearSearchHistory: () => void
  toggleAdvancedSearch: () => void
  setSuggestions: (suggestions: string[]) => void

  // Filter Management Actions
  setFilterState: (state: Partial<FilterState>) => void
  addFilter: (key: string, value: any) => void
  removeFilter: (key: string) => void
  clearAllFilters: () => void
  toggleAdvancedFilters: () => void

  // Bulk Operations Actions
  setBulkState: (state: Partial<BulkOperationState>) => void
  selectComponent: (id: number) => void
  deselectComponent: (id: number) => void
  selectAll: (ids: number[]) => void
  clearSelection: () => void
  setBulkOperation: (operation: BulkOperationState["operation"]) => void
  updateBulkProgress: (progress: number) => void

  // Form Management Actions
  setFormState: (state: Partial<ComponentFormState>) => void
  updateFormField: (field: keyof ComponentFormState["data"], value: any) => void
  setFormErrors: (errors: Record<string, string>) => void
  setFormTouched: (field: string, touched: boolean) => void
  resetForm: () => void
  setFormMode: (mode: ComponentFormState["mode"]) => void

  // UI State Actions
  setSidebarState: (state: Partial<SidebarState>) => void
  toggleSidebar: () => void
  setSidebarSection: (section: SidebarState["active_section"]) => void
  setModalState: (state: Partial<ModalState>) => void
  openModal: (type: ModalState["type"], data?: any) => void
  closeModal: () => void

  // User Preferences Actions
  setUserPreferences: (preferences: Partial<UserPreferences>) => void
  updateDisplayOptions: (options: Partial<ComponentDisplayOptions>) => void
  setDisplayOptions: (options: Partial<ComponentDisplayOptions>) => void
  toggleDisplayOption: (option: keyof ComponentDisplayOptions) => void

  // Notification Actions
  addNotification: (notification: Omit<Notification, "id">) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void

  // Computed Properties
  getActiveFilterCount: () => number
  getSelectedCount: () => number
  isComponentSelected: (id: number) => boolean
  hasActiveSearch: () => boolean
  getFilterSummary: () => string[]

  // Utility Actions
  reset: () => void
  resetToDefaults: () => void
}

// Default states
const defaultBulkState: BulkOperationState = {
  selectedIds: [],
  operation: null,
  isProcessing: false,
  progress: 0,
  results: [],
  errors: [],
}

const defaultFormState: ComponentFormState = {
  isEditing: false,
  isDirty: false,
  isSubmitting: false,
  errors: {},
  component: {},
}

const defaultSidebarState: SidebarState = {
  is_open: true,
  width: 300,
  collapsed: false,
  active_section: "filters",
}

const defaultModalState: ModalState = {
  is_open: false,
  type: undefined,
  data: undefined,
  loading: false,
  error: undefined,
}

// Enhanced store implementation with immer for immutable updates
export const useComponentStore = create<ComponentStore>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          // Initial State
          listState: createDefaultListState(),
          searchState: createDefaultSearchState(),
          filterState: createDefaultFilterState(),
          bulkState: defaultBulkState,
          formState: defaultFormState,
          sidebarState: defaultSidebarState,
          modalState: defaultModalState,
          userPreferences: createDefaultUserPreferences(),
          notifications: [],
          
          // Computed Properties  
          displayOptions: { ...createDefaultUserPreferences().display_options },

          // List Management Actions
          setListState: (state) =>
            set((draft) => {
              Object.assign(draft.listState, state)
            }),

          updateFilters: (filters) =>
            set((draft) => {
              Object.assign(draft.listState.filters, filters)
              draft.listState.page = 1 // Reset to first page
            }),

          clearFilters: () =>
            set((draft) => {
              draft.listState.filters = {
                search_term: "",
                component_category_id: null,
                component_type_id: null,
                manufacturer: "",
                is_preferred: null,
                is_active: null,
                min_price: null,
                max_price: null,
                currency: "EUR",
                stock_status: "",
              }
              draft.listState.page = 1
              draft.filterState.active_filters = {}
              draft.filterState.filter_count = 0
            }),

          setViewMode: (mode) =>
            set((draft) => {
              draft.listState.viewMode = mode
            }),

          setSortConfig: (config) =>
            set((draft) => {
              draft.listState.sortBy = config.field
              draft.listState.sortOrder = config.direction
            }),

          setPage: (page) =>
            set((draft) => {
              draft.listState.page = page
            }),

          setPageSize: (size) =>
            set((draft) => {
              draft.listState.pageSize = size
              draft.listState.page = 1 // Reset to first page
            }),

          // Search Management Actions
          setSearchState: (state) =>
            set((draft) => {
              Object.assign(draft.searchState, state)
            }),

          setSearchQuery: (query) =>
            set((draft) => {
              draft.searchState.query = query
              if (query && !draft.searchState.search_history.includes(query)) {
                draft.searchState.search_history.unshift(query)
                // Keep only last 10 searches
                if (draft.searchState.search_history.length > 10) {
                  draft.searchState.search_history =
                    draft.searchState.search_history.slice(0, 10)
                }
              }
            }),

          addToSearchHistory: (query) =>
            set((draft) => {
              if (query) {
                // Remove existing entry if it exists to move it to front
                const existingIndex = draft.searchState.recentSearches.findIndex(item => item.query === query)
                if (existingIndex > -1) {
                  draft.searchState.recentSearches.splice(existingIndex, 1)
                }
                
                const newEntry = {
                  query,
                  timestamp: new Date().toISOString()
                }
                draft.searchState.recentSearches.unshift(newEntry)
                
                // Also add to searchHistory (as string)
                const existingStringIndex = draft.searchState.searchHistory.indexOf(query)
                if (existingStringIndex > -1) {
                  draft.searchState.searchHistory.splice(existingStringIndex, 1)
                }
                draft.searchState.searchHistory.unshift(query)
                
                // Keep only last 10 searches
                if (draft.searchState.recentSearches.length > 10) {
                  draft.searchState.recentSearches = draft.searchState.recentSearches.slice(0, 10)
                }
                if (draft.searchState.searchHistory.length > 10) {
                  draft.searchState.searchHistory = draft.searchState.searchHistory.slice(0, 10)
                }
              }
            }),

          clearSearchHistory: () =>
            set((draft) => {
              draft.searchState.recentSearches = []
              draft.searchState.searchHistory = []
            }),

          toggleAdvancedSearch: () =>
            set((draft) => {
              draft.searchState.is_advanced_mode =
                !draft.searchState.is_advanced_mode
            }),

          setSuggestions: (suggestions) =>
            set((draft) => {
              draft.searchState.suggestions = suggestions
            }),

          // Filter Management Actions
          setFilterState: (state) =>
            set((draft) => {
              Object.assign(draft.filterState, state)
            }),

          addFilter: (key, value) =>
            set((draft) => {
              draft.filterState.active_filters[key] = value
              draft.filterState.filter_count = Object.keys(
                draft.filterState.active_filters
              ).length
            }),

          removeFilter: (key) =>
            set((draft) => {
              delete draft.filterState.active_filters[key]
              draft.filterState.filter_count = Object.keys(
                draft.filterState.active_filters
              ).length
            }),

          clearAllFilters: () =>
            set((draft) => {
              draft.filterState.active_filters = {}
              draft.filterState.filter_count = 0
            }),

          toggleAdvancedFilters: () =>
            set((draft) => {
              draft.filterState.show_advanced_filters =
                !draft.filterState.show_advanced_filters
            }),

          // Bulk Operations Actions
          setBulkState: (state) =>
            set((draft) => {
              Object.assign(draft.bulkState, state)
            }),

          selectComponent: (id) =>
            set((draft) => {
              // Validate ID - must be a positive number
              if (typeof id === 'number' && id > 0 && !draft.bulkState.selectedIds.includes(id)) {
                draft.bulkState.selectedIds.push(id)
              }
            }),

          deselectComponent: (id) =>
            set((draft) => {
              const index = draft.bulkState.selectedIds.indexOf(id)
              if (index > -1) {
                draft.bulkState.selectedIds.splice(index, 1)
              }
            }),

          selectAll: (ids) =>
            set((draft) => {
              draft.bulkState.selectedIds = [...ids]
            }),

          clearSelection: () =>
            set((draft) => {
              draft.bulkState.selectedIds = []
              draft.bulkState.operation = null
            }),

          setBulkOperation: (operation) =>
            set((draft) => {
              draft.bulkState.operation = operation
            }),

          updateBulkProgress: (progress) =>
            set((draft) => {
              draft.bulkState.progress = progress
            }),

          // Form Management Actions
          setFormState: (state) =>
            set((draft) => {
              Object.assign(draft.formState, state)
            }),

          updateFormField: (field, value: any) =>
            set((draft) => {
              draft.formState.component[field] = value
              draft.formState.isDirty = true
            }),

          setFormErrors: (errors: Record<string, string>) =>
            set((draft) => {
              draft.formState.errors = errors
            }),

          setFormTouched: (_field: string, touched: boolean) =>
            set((draft) => {
              // No touched state in current ComponentFormState
              draft.formState.isDirty = touched
            }),

          resetForm: () =>
            set((draft) => {
              draft.formState = { ...defaultFormState }
            }),

          setFormMode: (mode: boolean) =>
            set((draft) => {
              draft.formState.isEditing = mode
            }),

          // UI State Actions
          setSidebarState: (state) =>
            set((draft) => {
              Object.assign(draft.sidebarState, state)
            }),

          toggleSidebar: () =>
            set((draft) => {
              draft.sidebarState.is_open = !draft.sidebarState.is_open
            }),

          setSidebarSection: (section: SidebarState["active_section"]) =>
            set((draft) => {
              draft.sidebarState.active_section = section
            }),

          setModalState: (state) =>
            set((draft) => {
              Object.assign(draft.modalState, state)
            }),

          openModal: (type: ModalState["type"], data?: any) =>
            set((draft) => {
              draft.modalState.is_open = true
              draft.modalState.type = type
              draft.modalState.data = data
              draft.modalState.loading = false
              draft.modalState.error = undefined
            }),

          closeModal: () =>
            set((draft) => {
              draft.modalState = { ...defaultModalState }
            }),

          // User Preferences Actions
          setUserPreferences: (preferences) =>
            set((draft) => {
              Object.assign(draft.userPreferences, preferences)
            }),

          updateDisplayOptions: (options) =>
            set((draft) => {
              Object.assign(draft.userPreferences.display_options, options)
            }),

          setDisplayOptions: (options) =>
            set((draft) => {
              Object.assign(draft.userPreferences.display_options, options)
              Object.assign(draft.displayOptions, options)
            }),

          toggleDisplayOption: (option: keyof ComponentDisplayOptions) =>
            set((draft) => {
              const currentValue = draft.userPreferences.display_options[option]
              const newValue = !currentValue
              draft.userPreferences.display_options[option] = newValue
              draft.displayOptions[option] = newValue
            }),

          // Notification Actions
          addNotification: (notification: Omit<Notification, "id">) =>
            set((draft) => {
              const id = Date.now().toString()
              draft.notifications.push({ ...notification, id })
            }),

          removeNotification: (id: string) =>
            set((draft) => {
              const index = draft.notifications.findIndex((n: Notification) => n.id === id)
              if (index > -1) {
                draft.notifications.splice(index, 1)
              }
            }),

          clearNotifications: () =>
            set((draft) => {
              draft.notifications = []
            }),

          // Computed Properties
          getActiveFilterCount: () => {
            const state = get()
            const filters = state.listState.filters
            let count = 0
            
            // Count active filters (non-empty, non-null values)
            if (filters.search_term && filters.search_term.trim()) count++
            if (filters.component_category_id !== null) count++
            if (filters.component_type_id !== null) count++
            if (filters.manufacturer && filters.manufacturer.trim()) count++
            if (filters.is_preferred !== null) count++
            if (filters.is_active !== null) count++
            if (filters.min_price !== null) count++
            if (filters.max_price !== null) count++
            if (filters.stock_status && filters.stock_status.trim()) count++
            
            return count
          },

          getSelectedCount: () => {
            const state = get()
            return state.bulkState.selectedIds.length
          },

          isComponentSelected: (id: number) => {
            const state = get()
            return state.bulkState.selectedIds.includes(id)
          },

          hasActiveSearch: () => {
            const state = get()
            return state.searchState.query.length > 0
          },

          getFilterSummary: () => {
            const state = get()
            return Object.entries(state.filterState.active_filters).map(
              ([key, value]) => `${key}: ${value}`
            )
          },

          // Utility Actions
          reset: () =>
            set((draft) => {
              draft.listState = createDefaultListState()
              draft.searchState = createDefaultSearchState()
              draft.filterState = createDefaultFilterState()
              draft.bulkState = { ...defaultBulkState }
              draft.formState = { ...defaultFormState }
              draft.modalState = { ...defaultModalState }
              draft.userPreferences = createDefaultUserPreferences()
              draft.notifications = []
              draft.displayOptions = { ...createDefaultUserPreferences().display_options }
            }),

          resetToDefaults: () =>
            set((draft) => {
              draft.listState = createDefaultListState()
              draft.searchState = createDefaultSearchState()
              draft.filterState = createDefaultFilterState()
              draft.bulkState = { ...defaultBulkState }
              draft.formState = { ...defaultFormState }
              draft.sidebarState = { ...defaultSidebarState }
              draft.modalState = { ...defaultModalState }
              draft.userPreferences = createDefaultUserPreferences()
              draft.notifications = []
              draft.displayOptions = { ...createDefaultUserPreferences().display_options }
            }),
        }))
      ),
      {
        name: "component-store",
        partialize: (state) => ({
          // Only persist user preferences and some UI state
          userPreferences: state.userPreferences,
          sidebarState: {
            is_open: state.sidebarState.is_open,
            width: state.sidebarState.width,
            active_section: state.sidebarState.active_section,
          },
          searchState: pick(state.searchState, [
            "searchHistory",
            "savedSearches",
          ]),
          listState: {
            ...createDefaultListState(),
            viewMode: state.listState.viewMode,
            sortBy: state.listState.sortBy,
            sortOrder: state.listState.sortOrder,
            pageSize: state.listState.pageSize,
          },
        }),
        version: 1,
      }
    ),
    {
      name: "component-store",
      enabled: process.env.NODE_ENV === "development",
    }
  )
)

// Selector hooks for better performance and type safety
export const useComponentListState = () =>
  useComponentStore((state) => state.listState)
export const useComponentFilters = () =>
  useComponentStore((state) => state.filterState.active_filters)
export const useComponentViewMode = () =>
  useComponentStore((state) => state.listState.viewMode)
export const useComponentSelection = () =>
  useComponentStore((state) => state.bulkState.selectedIds)
export const useComponentDisplayOptions = () =>
  useComponentStore((state) => state.userPreferences.display_options)
export const useComponentFormState = () =>
  useComponentStore((state) => state.formState)
export const useComponentNotifications = () =>
  useComponentStore((state) => state.notifications)
