/**
 * Re-exports custom React Hooks specific to the 'components' domain.
 */

// Original hooks (preserved for backwards compatibility)
export { useComponentStore } from "./useComponentStore"

// Domain-aware hooks (recommended for new development)
export {
  useDomainComponentStore,
  useDomainComponentCatalog,
  useDomainComponentCache,
  useSelectedDomainComponents,
  useComponentValidationService,
  useLastValidationResult,
  useComponentOperationError,
  useComponentLoadingStates,
  useComponentListState,
  useComponentFilters,
  useComponentViewMode,
  useComponentSelection,
  useComponentDisplayOptions,
  useComponentFormState,
  useComponentNotifications,
} from "./useDomainComponentStore"

export * from "./useDomainComponentHooks"
export * from "./useDomainComponentForm"

// Re-export domain types for convenience
export type {
  Component,
  ComponentCreateData,
  ComponentUpdateData,
  ComponentCatalog,
  ValidationResult,
} from "../domain"
