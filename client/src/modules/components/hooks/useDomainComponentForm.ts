/**
 * Domain-Aware Component Form Hook
 *
 * Enhanced form management hook that integrates domain validation,
 * business rules, and rich error handling with existing form patterns.
 */

import { useCallback, useEffect, useState } from "react"

import type {
  Component,
  ComponentCreateData,
  ComponentUpdateData,
  ValidationResult,
} from "../domain"

import {
  useCreateComponent,
  useUpdateComponent,
} from "./useDomainComponentHooks"
import { useDomainComponentStore } from "./useDomainComponentStore"

/**
 * Form validation error with domain context
 */
export interface DomainFormError {
  field: string
  message: string
  code: string
  severity: "error" | "warning" | "info"
  suggestion?: string
}

/**
 * Enhanced form state with domain integration
 */
export interface DomainFormState {
  data: Partial<ComponentCreateData | ComponentUpdateData>
  errors: DomainFormError[]
  warnings: DomainFormError[]
  isDirty: boolean
  isSubmitting: boolean
  isValid: boolean
  validationResult: ValidationResult | null
  touchedFields: Set<string>
}

/**
 * Options for domain form hook
 */
export interface UseDomainComponentFormOptions {
  component?: Component
  mode?: "create" | "edit"
  validateOnChange?: boolean
  validateOnBlur?: boolean
  onSuccess?: (component: Component) => void
  onError?: (error: Error) => void
  onValidationChange?: (result: ValidationResult) => void
}

/**
 * Domain-aware form hook with comprehensive validation
 */
export function useDomainComponentForm(
  options: UseDomainComponentFormOptions = {}
) {
  const {
    component,
    mode = component ? "edit" : "create",
    validateOnChange = true,
    validateOnBlur = true,
    onSuccess,
    onError,
    onValidationChange,
  } = options

  const { validateComponent, validateUpdate } = useDomainComponentStore()
  const { create, isCreating, error: createError } = useCreateComponent()
  const { update, isUpdating, error: updateError } = useUpdateComponent()

  // Initialize form state
  const [formState, setFormState] = useState<DomainFormState>(() => ({
    data: component
      ? {
          name: component.name,
          manufacturer: component.manufacturer,
          model_number: component.model_number,
          description: component.description,
          component_type_id: component.component_type_id,
          category_id: component.category_id,
          unit_price: component.unit_price,
          currency: component.currency,
          supplier: component.supplier,
          part_number: component.part_number,
          weight_kg: component.weight_kg,
          specifications: component.specifications,
          dimensions: component.dimensions,
          is_active: component.is_active,
          is_preferred: component.is_preferred,
          stock_status: component.stock_status,
        }
      : {},
    errors: [],
    warnings: [],
    isDirty: false,
    isSubmitting: false,
    isValid: mode === "create" ? false : true,
    validationResult: null,
    touchedFields: new Set(),
  }))

  // Convert domain validation result to form errors
  const convertValidationResult = useCallback(
    (result: ValidationResult): DomainFormError[] => {
      const formErrors: DomainFormError[] = []

      result.errors.forEach((error, index) => {
        formErrors.push({
          field: `error_${index}`, // Generic field for now, could be enhanced to map specific fields
          message: error,
          code: `validation_error_${index}`,
          severity: "error",
        })
      })

      result.warnings.forEach((warning, index) => {
        formErrors.push({
          field: `warning_${index}`,
          message: warning,
          code: `validation_warning_${index}`,
          severity: "warning",
        })
      })

      return formErrors
    },
    []
  )

  // Perform domain validation
  const performValidation = useCallback(
    (
      data: Partial<ComponentCreateData | ComponentUpdateData>
    ): ValidationResult => {
      let result: ValidationResult

      if (mode === "create") {
        result = validateComponent(data as ComponentCreateData)
      } else if (component) {
        result = validateUpdate(component, data as ComponentUpdateData)
      } else {
        result = { isValid: false, errors: ["Invalid form mode"], warnings: [] }
      }

      // Notify about validation changes
      onValidationChange?.(result)

      return result
    },
    [mode, component, validateComponent, validateUpdate, onValidationChange]
  )

  // Update form field
  const updateField = useCallback(
    (field: string, value: any) => {
      setFormState((prev) => {
        const newData = { ...prev.data, [field]: value }
        const newTouchedFields = new Set(prev.touchedFields).add(field)

        let validationResult = prev.validationResult
        let errors = prev.errors
        let warnings = prev.warnings
        let isValid = prev.isValid

        // Validate on change if enabled
        if (validateOnChange) {
          validationResult = performValidation(newData)
          const formErrors = convertValidationResult(validationResult)
          errors = formErrors.filter((e) => e.severity === "error")
          warnings = formErrors.filter((e) => e.severity === "warning")
          isValid = validationResult.isValid
        }

        return {
          ...prev,
          data: newData,
          touchedFields: newTouchedFields,
          isDirty: true,
          validationResult,
          errors,
          warnings,
          isValid,
        }
      })
    },
    [validateOnChange, performValidation, convertValidationResult]
  )

  // Handle field blur
  const handleFieldBlur = useCallback(
    (field: string) => {
      if (!validateOnBlur) return

      setFormState((prev) => {
        const validationResult = performValidation(prev.data)
        const formErrors = convertValidationResult(validationResult)

        return {
          ...prev,
          validationResult,
          errors: formErrors.filter((e) => e.severity === "error"),
          warnings: formErrors.filter((e) => e.severity === "warning"),
          isValid: validationResult.isValid,
        }
      })
    },
    [validateOnBlur, performValidation, convertValidationResult]
  )

  // Validate entire form
  const validateForm = useCallback((): boolean => {
    const validationResult = performValidation(formState.data)
    const formErrors = convertValidationResult(validationResult)

    setFormState((prev) => ({
      ...prev,
      validationResult,
      errors: formErrors.filter((e) => e.severity === "error"),
      warnings: formErrors.filter((e) => e.severity === "warning"),
      isValid: validationResult.isValid,
    }))

    return validationResult.isValid
  }, [formState.data, performValidation, convertValidationResult])

  // Submit form
  const handleSubmit = useCallback(
    async (e?: React.FormEvent) => {
      e?.preventDefault()

      // Validate form first
      const isValid = validateForm()
      if (!isValid || !formState.isDirty) {
        return
      }

      setFormState((prev) => ({ ...prev, isSubmitting: true }))

      try {
        let result: Component | null = null

        if (mode === "create") {
          result = await create(formState.data as ComponentCreateData)
        } else if (component) {
          result = await update(
            component.id,
            formState.data as ComponentUpdateData
          )
        }

        if (result) {
          setFormState((prev) => ({
            ...prev,
            isSubmitting: false,
            isDirty: false,
          }))
          onSuccess?.(result)
        }
      } catch (error) {
        setFormState((prev) => ({ ...prev, isSubmitting: false }))
        onError?.(error as Error)
      }
    },
    [
      mode,
      component,
      formState.data,
      formState.isDirty,
      validateForm,
      create,
      update,
      onSuccess,
      onError,
    ]
  )

  // Reset form
  const resetForm = useCallback(() => {
    setFormState({
      data: component
        ? {
            name: component.name,
            manufacturer: component.manufacturer,
            model_number: component.model_number,
            description: component.description,
            component_type_id: component.component_type_id,
            category_id: component.category_id,
            unit_price: component.unit_price,
            currency: component.currency,
            supplier: component.supplier,
            part_number: component.part_number,
            weight_kg: component.weight_kg,
            specifications: component.specifications,
            dimensions: component.dimensions,
            is_active: component.is_active,
            is_preferred: component.is_preferred,
            stock_status: component.stock_status,
          }
        : {},
      errors: [],
      warnings: [],
      isDirty: false,
      isSubmitting: false,
      isValid: mode === "create" ? false : true,
      validationResult: null,
      touchedFields: new Set(),
    })
  }, [component, mode])

  // Get field-specific error
  const getFieldError = useCallback(
    (field: string): string | undefined => {
      return formState.errors.find(
        (error) =>
          error.field === field ||
          error.message.toLowerCase().includes(field.toLowerCase())
      )?.message
    },
    [formState.errors]
  )

  // Get field-specific warning
  const getFieldWarning = useCallback(
    (field: string): string | undefined => {
      return formState.warnings.find(
        (warning) =>
          warning.field === field ||
          warning.message.toLowerCase().includes(field.toLowerCase())
      )?.message
    },
    [formState.warnings]
  )

  // Check if field has been touched
  const isFieldTouched = useCallback(
    (field: string): boolean => {
      return formState.touchedFields.has(field)
    },
    [formState.touchedFields]
  )

  // Update form state when component prop changes
  useEffect(() => {
    if (component) {
      resetForm()
    }
  }, [component, resetForm])

  return {
    // Form state
    formState,
    data: formState.data,
    errors: formState.errors,
    warnings: formState.warnings,
    isDirty: formState.isDirty,
    isValid: formState.isValid,
    isSubmitting: formState.isSubmitting || isCreating || isUpdating,
    validationResult: formState.validationResult,

    // Form actions
    updateField,
    handleFieldBlur,
    validateForm,
    handleSubmit,
    resetForm,

    // Field helpers
    getFieldError,
    getFieldWarning,
    isFieldTouched,

    // Operation states
    error: createError || updateError,
    mode,
  }
}

/**
 * Simplified hook for quick form integration
 */
export function useSimpleDomainForm(component?: Component) {
  return useDomainComponentForm({
    component,
    validateOnChange: true,
    validateOnBlur: true,
  })
}
