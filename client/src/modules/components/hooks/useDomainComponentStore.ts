/**
 * Domain-Aware Component Store Hook
 *
 * Enhanced Zustand store that integrates with the domain layer,
 * maintaining references to domain objects and delegating business
 * logic to domain services and aggregates.
 */

// Existing schema imports for UI state
import type {
  BulkOperationState,
  ComponentDisplayOptions,
  ComponentFilterState,
  ComponentFormState,
  ComponentListState,
  ComponentSearchState,
} from "../types"
import type {
  ComponentCreateData,
  ComponentUpdateData,
  ValidationResult,
} from "../domain"

// Lodash pick function implementation
const pick = <T extends Record<string, any>, K extends keyof T>(obj: T, keys: K[]): Pick<T, K> => {
  const result = {} as Pick<T, K>
  keys.forEach(key => {
    if (key in obj) {
      result[key] = obj[key]
    }
  })
  return result
}
import { create } from "zustand"
import { devtools, persist, subscribeWithSelector } from "zustand/middleware"
import { immer } from "zustand/middleware/immer"

// Additional UI state types
interface FilterState {
  active_filters: Record<string, any>
  filter_count: number
  show_advanced_filters: boolean
}

interface SidebarState {
  is_open: boolean
  width: number
  collapsed: boolean
  active_section: "filters" | "search" | "stats"
}

interface ModalState {
  is_open: boolean
  type?: "create" | "edit" | "delete" | "bulk" | "import" | "export"
  data?: any
  loading: boolean
  error?: string
}

interface UserPreferences {
  display_options: ComponentDisplayOptions
  auto_save: boolean
  theme: "light" | "dark" | "system"
}

interface Notification {
  id: string
  type: "success" | "error" | "warning" | "info"
  title: string
  message: string
  timestamp: Date
  duration?: number
}

type ViewMode = "grid" | "list" | "table" | "cards"
type SortConfig = {
  field: string
  direction: "asc" | "desc"
}

// Default creator functions
const createDefaultListState = (): ComponentListState => ({
  filters: {},
  sortBy: "name",
  sortOrder: "asc",
  page: 1,
  pageSize: 20,
  viewMode: "grid",
  selectedComponents: [],
})

const createDefaultSearchState = (): ComponentSearchState => ({
  query: "",
  field: "name",
  suggestions: [],
  isSearching: false,
  isAdvancedMode: false,
  searchHistory: [],
  recentSearches: [],
  savedSearches: [],
})

const createDefaultFilterState = (): FilterState => ({
  active_filters: {},
  filter_count: 0,
  show_advanced_filters: false,
})

const createDefaultUserPreferences = (): UserPreferences => ({
  display_options: {
    showImages: true,
    showSpecifications: true,
    showPricing: true,
    showPrices: true,
    showAvailability: true,
    compactMode: false,
  },
  auto_save: true,
  theme: "system",
})

// API imports
import {
  domainComponentApi,
} from "@/modules/components/api"
import {
  Component,
  ComponentCatalog,
  ComponentValidationService,
} from "../domain"

// Domain imports

/**
 * Domain-specific state extensions
 */
interface DomainComponentState {
  // Domain Objects
  componentCatalog: ComponentCatalog | null
  validationService: ComponentValidationService

  // Loading States
  isLoadingCatalog: boolean
  isLoadingComponent: boolean
  isSaving: boolean

  // Domain Operations State
  lastValidationResult: ValidationResult | null
  lastOperationError: string | null

  // Component Data Cache
  componentCache: Map<number, Component>
  selectedComponents: Component[]
}

/**
 * Domain-specific actions
 */
interface DomainComponentActions {
  // Catalog Operations
  loadCatalog: (criteria?: any, pagination?: any) => Promise<void>
  refreshCatalog: () => Promise<void>
  clearCatalog: () => void

  // Component Operations
  loadComponent: (id: number) => Promise<Component | null>
  createComponent: (data: ComponentCreateData) => Promise<Component | null>
  updateComponent: (
    id: number,
    data: ComponentUpdateData
  ) => Promise<Component | null>
  deleteComponent: (id: number) => Promise<boolean>

  // Catalog-level Operations
  searchCatalog: (criteria: any) => Component[]
  getPreferredComponents: () => Component[]
  getCatalogStatistics: () => any

  // Validation Operations
  validateComponent: (data: ComponentCreateData) => ValidationResult
  validateUpdate: (
    component: Component,
    data: ComponentUpdateData
  ) => ValidationResult

  // Selection Operations with Domain Objects
  selectDomainComponent: (component: Component) => void
  deselectDomainComponent: (component: Component) => void
  getSelectedDomainComponents: () => Component[]
  clearDomainSelection: () => void

  // Bulk Operations with Domain Logic
  bulkCreateComponents: (
    components: ComponentCreateData[]
  ) => Promise<{ successful: Component[]; failed: any[] }>
  bulkUpdateComponents: (
    updates: Array<{ component: Component; data: ComponentUpdateData }>
  ) => Promise<{ successful: Component[]; failed: any[] }>

  // Utility Operations
  syncWithApi: () => Promise<void>
  clearCache: () => void
  getFromCache: (id: number) => Component | null
}

/**
 * Enhanced store interface combining existing UI state with domain integration
 */
interface DomainComponentStore
  extends ComponentStore,
    DomainComponentState,
    DomainComponentActions {}

// Missing type definition
type ComponentFilter = ComponentFilterState

// Import existing store interface (we'll extend it)
interface ComponentStore {
  // Core State
  listState: ComponentListState
  searchState: ComponentSearchState
  filterState: FilterState
  bulkState: BulkOperationState
  formState: ComponentFormState
  sidebarState: SidebarState
  modalState: ModalState
  userPreferences: UserPreferences
  notifications: Notification[]

  // All existing actions from the original store
  setListState: (state: Partial<ComponentListState>) => void
  updateFilters: (filters: Partial<ComponentFilter>) => void
  clearFilters: () => void
  setViewMode: (mode: ViewMode) => void
  setSortConfig: (config: SortConfig) => void
  setPage: (page: number) => void
  setPageSize: (size: number) => void
  setSearchState: (state: Partial<ComponentSearchState>) => void
  setSearchQuery: (query: string) => void
  addToSearchHistory: (query: string) => void
  clearSearchHistory: () => void
  toggleAdvancedSearch: () => void
  setSuggestions: (suggestions: string[]) => void
  setFilterState: (state: Partial<FilterState>) => void
  addFilter: (key: string, value: any) => void
  removeFilter: (key: string) => void
  clearAllFilters: () => void
  toggleAdvancedFilters: () => void
  setBulkState: (state: Partial<BulkOperationState>) => void
  selectComponent: (id: number) => void
  deselectComponent: (id: number) => void
  selectAll: (ids: number[]) => void
  clearSelection: () => void
  setBulkOperation: (operation: BulkOperationState["operation"]) => void
  updateBulkProgress: (progress: number) => void
  setFormState: (state: Partial<ComponentFormState>) => void
  updateFormField: (field: keyof ComponentFormState["component"], value: any) => void
  setFormErrors: (errors: Record<string, string>) => void
  setFormTouched: (field: string, touched: boolean) => void
  resetForm: () => void
  setFormMode: (mode: boolean) => void
  setSidebarState: (state: Partial<SidebarState>) => void
  toggleSidebar: () => void
  setSidebarSection: (section: SidebarState["active_section"]) => void
  setModalState: (state: Partial<ModalState>) => void
  openModal: (type: ModalState["type"], data?: any) => void
  closeModal: () => void
  setUserPreferences: (preferences: Partial<UserPreferences>) => void
  updateDisplayOptions: (options: Partial<ComponentDisplayOptions>) => void
  toggleDisplayOption: (option: keyof ComponentDisplayOptions) => void
  addNotification: (notification: Omit<Notification, "id">) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void
  getActiveFilterCount: () => number
  getSelectedCount: () => number
  isComponentSelected: (id: number) => boolean
  hasActiveSearch: () => boolean
  getFilterSummary: () => string[]
  reset: () => void
  resetToDefaults: () => void
}

// Default states for domain extensions
const defaultDomainState: DomainComponentState = {
  componentCatalog: null,
  validationService: new ComponentValidationService(),
  isLoadingCatalog: false,
  isLoadingComponent: false,
  isSaving: false,
  lastValidationResult: null,
  lastOperationError: null,
  componentCache: new Map(),
  selectedComponents: [],
}

// Default states from original store
const defaultBulkState: BulkOperationState = {
  selectedIds: [],
  operation: null,
  isProcessing: false,
  progress: 0,
  results: [],
  errors: [],
}

const defaultFormState: ComponentFormState = {
  isEditing: false,
  isDirty: false,
  isSubmitting: false,
  errors: {},
  component: {},
}

const defaultSidebarState: SidebarState = {
  is_open: true,
  width: 300,
  collapsed: false,
  active_section: "filters",
}

const defaultModalState: ModalState = {
  is_open: false,
  type: undefined,
  data: undefined,
  loading: false,
  error: undefined,
}

// Enhanced store implementation with domain integration
export const useDomainComponentStore = create<DomainComponentStore>()(
  devtools(
    persist(
      subscribeWithSelector(
        immer((set, get) => ({
          // Original UI State
          listState: createDefaultListState(),
          searchState: createDefaultSearchState(),
          filterState: createDefaultFilterState(),
          bulkState: defaultBulkState,
          formState: defaultFormState,
          sidebarState: defaultSidebarState,
          modalState: defaultModalState,
          userPreferences: createDefaultUserPreferences(),
          notifications: [],

          // Domain State
          ...defaultDomainState,

          // Original UI Actions (preserved from existing store)
          setListState: (state) =>
            set((draft) => {
              Object.assign(draft.listState, state)
            }),

          updateFilters: (filters) =>
            set((draft) => {
              Object.assign(draft.listState.filters, filters)
              draft.listState.page = 1
            }),

          clearFilters: () =>
            set((draft) => {
              draft.listState.filters = {}
              draft.listState.page = 1
              draft.filterState.active_filters = {}
              draft.filterState.filter_count = 0
            }),

          setViewMode: (mode) =>
            set((draft) => {
              draft.listState.viewMode = mode
            }),

          setSortConfig: (config) =>
            set((draft) => {
              draft.listState.sortBy = config.field
              draft.listState.sortOrder = config.direction
            }),

          setPage: (page) =>
            set((draft) => {
              draft.listState.page = page
            }),

          setPageSize: (size) =>
            set((draft) => {
              draft.listState.pageSize = size
              draft.listState.page = 1
            }),

          setSearchState: (state) =>
            set((draft) => {
              Object.assign(draft.searchState, state)
            }),

          setSearchQuery: (query) =>
            set((draft) => {
              draft.searchState.query = query
              if (query && !draft.searchState.searchHistory.includes(query)) {
                draft.searchState.searchHistory.unshift(query)
                if (draft.searchState.searchHistory.length > 10) {
                  draft.searchState.searchHistory =
                    draft.searchState.searchHistory.slice(0, 10)
                }
              }
            }),

          addToSearchHistory: (query) =>
            set((draft) => {
              if (query && !draft.searchState.searchHistory.includes(query)) {
                draft.searchState.searchHistory.unshift(query)
                if (draft.searchState.searchHistory.length > 10) {
                  draft.searchState.searchHistory =
                    draft.searchState.searchHistory.slice(0, 10)
                }
              }
            }),

          clearSearchHistory: () =>
            set((draft) => {
              draft.searchState.searchHistory = []
            }),

          toggleAdvancedSearch: () =>
            set((draft) => {
              draft.searchState.isAdvancedMode =
                !draft.searchState.isAdvancedMode
            }),

          setSuggestions: (suggestions) =>
            set((draft) => {
              draft.searchState.suggestions = suggestions
            }),

          setFilterState: (state) =>
            set((draft) => {
              Object.assign(draft.filterState, state)
            }),

          addFilter: (key, value) =>
            set((draft) => {
              draft.filterState.active_filters[key] = value
              draft.filterState.filter_count = Object.keys(
                draft.filterState.active_filters
              ).length
            }),

          removeFilter: (key) =>
            set((draft) => {
              delete draft.filterState.active_filters[key]
              draft.filterState.filter_count = Object.keys(
                draft.filterState.active_filters
              ).length
            }),

          clearAllFilters: () =>
            set((draft) => {
              draft.filterState.active_filters = {}
              draft.filterState.filter_count = 0
            }),

          toggleAdvancedFilters: () =>
            set((draft) => {
              draft.filterState.show_advanced_filters =
                !draft.filterState.show_advanced_filters
            }),

          setBulkState: (state) =>
            set((draft) => {
              Object.assign(draft.bulkState, state)
            }),

          selectComponent: (id) =>
            set((draft) => {
              if (!draft.bulkState.selectedIds.includes(id)) {
                draft.bulkState.selectedIds.push(id)
              }
            }),

          deselectComponent: (id) =>
            set((draft) => {
              const index = draft.bulkState.selectedIds.indexOf(id)
              if (index > -1) {
                draft.bulkState.selectedIds.splice(index, 1)
              }
            }),

          selectAll: (ids) =>
            set((draft) => {
              draft.bulkState.selectedIds = [...ids]
            }),

          clearSelection: () =>
            set((draft) => {
              draft.bulkState.selectedIds = []
              draft.bulkState.operation = null
            }),

          setBulkOperation: (operation) =>
            set((draft) => {
              draft.bulkState.operation = operation
            }),

          updateBulkProgress: (progress) =>
            set((draft) => {
              draft.bulkState.progress = progress
            }),

          setFormState: (state) =>
            set((draft) => {
              Object.assign(draft.formState, state)
            }),

          updateFormField: (field, value: any) =>
            set((draft) => {
              draft.formState.component[field] = value
              draft.formState.isDirty = true
            }),

          setFormErrors: (errors: Record<string, string>) =>
            set((draft) => {
              draft.formState.errors = errors
            }),

          setFormTouched: (_field: string, touched: boolean) =>
            set((draft) => {
              draft.formState.isDirty = touched
            }),

          resetForm: () =>
            set((draft) => {
              draft.formState = { ...defaultFormState }
            }),

          setFormMode: (mode: boolean) =>
            set((draft) => {
              draft.formState.isEditing = mode
            }),

          setSidebarState: (state) =>
            set((draft) => {
              Object.assign(draft.sidebarState, state)
            }),

          toggleSidebar: () =>
            set((draft) => {
              draft.sidebarState.is_open = !draft.sidebarState.is_open
            }),

          setSidebarSection: (section: SidebarState["active_section"]) =>
            set((draft) => {
              draft.sidebarState.active_section = section
            }),

          setModalState: (state) =>
            set((draft) => {
              Object.assign(draft.modalState, state)
            }),

          openModal: (type: ModalState["type"], data?: any) =>
            set((draft) => {
              draft.modalState.is_open = true
              draft.modalState.type = type
              draft.modalState.data = data
              draft.modalState.loading = false
              draft.modalState.error = undefined
            }),

          closeModal: () =>
            set((draft) => {
              draft.modalState = { ...defaultModalState }
            }),

          setUserPreferences: (preferences) =>
            set((draft) => {
              Object.assign(draft.userPreferences, preferences)
            }),

          updateDisplayOptions: (options) =>
            set((draft) => {
              Object.assign(draft.userPreferences.display_options, options)
            }),

          toggleDisplayOption: (option: keyof ComponentDisplayOptions) =>
            set((draft) => {
              draft.userPreferences.display_options[option] =
                !draft.userPreferences.display_options[option]
            }),

          addNotification: (notification: Omit<Notification, "id">) =>
            set((draft) => {
              const id = Date.now().toString()
              const timestamp = new Date()
              draft.notifications.push({ ...notification, id, timestamp })
            }),

          removeNotification: (id: string) =>
            set((draft) => {
              const index = draft.notifications.findIndex((n: Notification) => n.id === id)
              if (index > -1) {
                draft.notifications.splice(index, 1)
              }
            }),

          clearNotifications: () =>
            set((draft) => {
              draft.notifications = []
            }),

          // Original computed properties
          getActiveFilterCount: () => {
            const state = get()
            return state.filterState.filter_count
          },

          getSelectedCount: () => {
            const state = get()
            return state.bulkState.selectedIds.length
          },

          isComponentSelected: (id: number) => {
            const state = get()
            return state.bulkState.selectedIds.includes(id)
          },

          hasActiveSearch: () => {
            const state = get()
            return state.searchState.query.length > 0
          },

          getFilterSummary: () => {
            const state = get()
            return Object.entries(state.filterState.active_filters).map(
              ([key, value]) => `${key}: ${value}`
            )
          },

          reset: () =>
            set((draft) => {
              draft.listState = createDefaultListState()
              draft.searchState = createDefaultSearchState()
              draft.filterState = createDefaultFilterState()
              draft.bulkState = { ...defaultBulkState }
              draft.formState = { ...defaultFormState }
              draft.modalState = { ...defaultModalState }
              draft.notifications = []
            }),

          resetToDefaults: () =>
            set((draft) => {
              draft.listState = createDefaultListState()
              draft.searchState = createDefaultSearchState()
              draft.filterState = createDefaultFilterState()
              draft.bulkState = { ...defaultBulkState }
              draft.formState = { ...defaultFormState }
              draft.sidebarState = { ...defaultSidebarState }
              draft.modalState = { ...defaultModalState }
              draft.userPreferences = createDefaultUserPreferences()
              draft.notifications = []
            }),

          // NEW DOMAIN ACTIONS

          // Catalog Operations
          loadCatalog: async (criteria = {}, pagination = {}) => {
            set((draft) => {
              draft.isLoadingCatalog = true
              draft.lastOperationError = null
            })

            try {
              const result = await domainComponentApi.list(criteria, pagination)

              if (result.error) {
                set((draft) => {
                  draft.lastOperationError = result.error!.message
                  draft.isLoadingCatalog = false
                })
                return
              }

              if (result.data) {
                const catalog = ComponentCatalog.fromComponents(
                  result.data.components
                )

                set((draft) => {
                  draft.componentCatalog = catalog
                  draft.isLoadingCatalog = false

                  // Update cache
                  result.data!.components.forEach((component) => {
                    draft.componentCache.set(component.id, component)
                  })

                  // Update UI state - Note: ComponentListState doesn't have total_count or pagination object
                  // These would be managed separately in a pagination state
                })
              }
            } catch (error) {
              set((draft) => {
                draft.lastOperationError =
                  error instanceof Error ? error.message : "Unknown error"
                draft.isLoadingCatalog = false
              })
            }
          },

          refreshCatalog: async () => {
            const state = get()
            const criteria = state.filterState.active_filters
            const pagination = {
              page: state.listState.page,
              size: state.listState.pageSize,
            }
            await get().loadCatalog(criteria, pagination)
          },

          clearCatalog: () =>
            set((draft) => {
              draft.componentCatalog = null
              draft.componentCache.clear()
            }),

          // Component Operations
          loadComponent: async (id: number) => {
            set((draft) => {
              draft.isLoadingComponent = true
              draft.lastOperationError = null
            })

            try {
              // Check cache first
              const cached = get().componentCache.get(id)
              if (cached) {
                set((draft) => {
                  draft.isLoadingComponent = false
                })
                return cached
              }

              const result = await domainComponentApi.getById(id)

              if (result.error) {
                set((draft) => {
                  draft.lastOperationError = result.error!.message
                  draft.isLoadingComponent = false
                })
                return null
              }

              if (result.data) {
                set((draft) => {
                  draft.componentCache.set(id, result.data!)
                  draft.isLoadingComponent = false
                })
                return result.data
              }

              return null
            } catch (error) {
              set((draft) => {
                draft.lastOperationError =
                  error instanceof Error ? error.message : "Unknown error"
                draft.isLoadingComponent = false
              })
              return null
            }
          },

          createComponent: async (data: ComponentCreateData) => {
            set((draft) => {
              draft.isSaving = true
              draft.lastOperationError = null
              draft.lastValidationResult = null
            })

            try {
              const result = await domainComponentApi.create(data)

              if (result.error) {
                set((draft) => {
                  draft.lastOperationError = result.error!.message
                  draft.lastValidationResult = result.validation || null
                  draft.isSaving = false
                })
                return null
              }

              if (result.data) {
                set((draft) => {
                  // Add to cache
                  draft.componentCache.set(result.data!.id, result.data!)

                  // Add to catalog if it exists
                  if (draft.componentCatalog) {
                    try {
                      draft.componentCatalog.addExistingComponent(result.data!)
                    } catch {
                      // Component might already exist in catalog, ignore
                    }
                  }

                  draft.lastValidationResult = result.validation || null
                  draft.isSaving = false
                })

                // Add success notification
                get().addNotification({
                  type: "success",
                  title: "Component Created",
                  message: `${result.data.name} has been successfully created.`,
                  timestamp: new Date(),
                  duration: 5000,
                })

                return result.data
              }

              return null
            } catch (error) {
              set((draft) => {
                draft.lastOperationError =
                  error instanceof Error ? error.message : "Unknown error"
                draft.isSaving = false
              })
              return null
            }
          },

          updateComponent: async (id: number, data: ComponentUpdateData) => {
            set((draft) => {
              draft.isSaving = true
              draft.lastOperationError = null
              draft.lastValidationResult = null
            })

            try {
              const result = await domainComponentApi.update(id, data)

              if (result.error) {
                set((draft) => {
                  draft.lastOperationError = result.error!.message
                  draft.lastValidationResult = result.validation || null
                  draft.isSaving = false
                })
                return null
              }

              if (result.data) {
                set((draft) => {
                  // Update cache
                  draft.componentCache.set(id, result.data!)

                  // Update in catalog if it exists
                  if (
                    draft.componentCatalog &&
                    draft.componentCatalog.hasComponent(id)
                  ) {
                    draft.componentCatalog.updateComponent(id, data)
                  }

                  draft.lastValidationResult = result.validation || null
                  draft.isSaving = false
                })

                // Add success notification
                get().addNotification({
                  type: "success",
                  title: "Component Updated",
                  message: `${result.data.name} has been successfully updated.`,
                  timestamp: new Date(),
                  duration: 5000,
                })

                return result.data
              }

              return null
            } catch (error) {
              set((draft) => {
                draft.lastOperationError =
                  error instanceof Error ? error.message : "Unknown error"
                draft.isSaving = false
              })
              return null
            }
          },

          deleteComponent: async (id: number) => {
            set((draft) => {
              draft.isSaving = true
              draft.lastOperationError = null
            })

            try {
              const result = await domainComponentApi.delete(id)

              if (result.error) {
                set((draft) => {
                  draft.lastOperationError = result.error!.message
                  draft.isSaving = false
                })
                return false
              }

              set((draft) => {
                // Remove from cache
                draft.componentCache.delete(id)

                // Remove from catalog if it exists
                if (
                  draft.componentCatalog &&
                  draft.componentCatalog.hasComponent(id)
                ) {
                  draft.componentCatalog.removeComponent(id)
                }

                // Remove from selection
                const index = draft.bulkState.selectedIds.indexOf(id)
                if (index > -1) {
                  draft.bulkState.selectedIds.splice(index, 1)
                }

                draft.isSaving = false
              })

              // Add success notification
              get().addNotification({
                type: "success",
                title: "Component Deleted",
                message: "Component has been successfully deleted.",
                timestamp: new Date(),
                duration: 5000,
              })

              return true
            } catch (error) {
              set((draft) => {
                draft.lastOperationError =
                  error instanceof Error ? error.message : "Unknown error"
                draft.isSaving = false
              })
              return false
            }
          },

          // Catalog-level Operations
          searchCatalog: (criteria: any) => {
            const state = get()
            if (!state.componentCatalog) return []

            return state.componentCatalog.searchComponents(criteria)
          },

          getPreferredComponents: () => {
            const state = get()
            if (!state.componentCatalog) return []

            return state.componentCatalog.searchComponents({
              isPreferred: true,
            })
          },

          getCatalogStatistics: () => {
            const state = get()
            if (!state.componentCatalog) return null

            return state.componentCatalog.getStatistics()
          },

          // Validation Operations
          validateComponent: (data: ComponentCreateData) => {
            const state = get()
            const result =
              state.validationService.validateComponentCreation(data)

            set((draft) => {
              draft.lastValidationResult = result
            })

            return result
          },

          validateUpdate: (component: Component, data: ComponentUpdateData) => {
            const state = get()
            const result = state.validationService.validateComponentUpdate(
              component,
              data
            )

            set((draft) => {
              draft.lastValidationResult = result
            })

            return result
          },

          // Selection Operations with Domain Objects
          selectDomainComponent: (component: Component) =>
            set((draft) => {
              if (
                !draft.selectedComponents.find((c: Component) => c.id === component.id)
              ) {
                draft.selectedComponents.push(component)
                if (!draft.bulkState.selectedIds.includes(component.id)) {
                  draft.bulkState.selectedIds.push(component.id)
                }
              }
            }),

          deselectDomainComponent: (component: Component) =>
            set((draft) => {
              const index = draft.selectedComponents.findIndex(
                (c: Component) => c.id === component.id
              )
              if (index > -1) {
                draft.selectedComponents.splice(index, 1)
              }

              const idIndex = draft.bulkState.selectedIds.indexOf(component.id)
              if (idIndex > -1) {
                draft.bulkState.selectedIds.splice(idIndex, 1)
              }
            }),

          getSelectedDomainComponents: () => {
            return get().selectedComponents
          },

          clearDomainSelection: () =>
            set((draft) => {
              draft.selectedComponents = []
              draft.bulkState.selectedIds = []
              draft.bulkState.operation = null
            }),

          // Bulk Operations with Domain Logic
          bulkCreateComponents: async (components: ComponentCreateData[]) => {
            set((draft) => {
              draft.bulkState.isProcessing = true
              draft.bulkState.progress = 0
              draft.lastOperationError = null
            })

            try {
              const result = await domainComponentApi.bulkCreate(components)

              if (result.error) {
                set((draft) => {
                  draft.lastOperationError = result.error!.message
                  draft.bulkState.isProcessing = false
                })
                return { successful: [], failed: [] }
              }

              if (result.data) {
                set((draft) => {
                  // Add successful components to cache
                  result.data!.successful.forEach((component) => {
                    draft.componentCache.set(component.id, component)
                  })

                  // Update catalog
                  if (draft.componentCatalog) {
                    result.data!.successful.forEach((component) => {
                      try {
                        draft.componentCatalog!.addExistingComponent(component)
                      } catch {
                        // Component might already exist, ignore
                      }
                    })
                  }

                  draft.bulkState.isProcessing = false
                  draft.bulkState.progress = 100
                  draft.bulkState.results = result.data!.successful
                  draft.bulkState.errors = result.data!.failed
                })

                // Add notification
                get().addNotification({
                  type: result.data.failed.length > 0 ? "warning" : "success",
                  title: "Bulk Create Complete",
                  message: `${result.data.successful.length} components created, ${result.data.failed.length} failed.`,
                  timestamp: new Date(),
                  duration: 5000,
                })

                return {
                  successful: result.data.successful,
                  failed: result.data.failed,
                }
              }

              return { successful: [], failed: [] }
            } catch (error) {
              set((draft) => {
                draft.lastOperationError =
                  error instanceof Error ? error.message : "Unknown error"
                draft.bulkState.isProcessing = false
              })
              return { successful: [], failed: [] }
            }
          },

          bulkUpdateComponents: async (
            updates: Array<{ component: Component; data: ComponentUpdateData }>
          ) => {
            set((draft) => {
              draft.bulkState.isProcessing = true
              draft.bulkState.progress = 0
              draft.lastOperationError = null
            })

            const successful: Component[] = []
            const failed: any[] = []

            try {
              for (let i = 0; i < updates.length; i++) {
                const { component, data } = updates[i]

                try {
                  const updatedComponent = await get().updateComponent(
                    component.id,
                    data
                  )
                  if (updatedComponent) {
                    successful.push(updatedComponent)
                  } else {
                    failed.push({ component, error: "Update failed" })
                  }
                } catch (error) {
                  failed.push({
                    component,
                    error:
                      error instanceof Error ? error.message : "Unknown error",
                  })
                }

                // Update progress
                set((draft) => {
                  draft.bulkState.progress = Math.round(
                    ((i + 1) / updates.length) * 100
                  )
                })
              }

              set((draft) => {
                draft.bulkState.isProcessing = false
                draft.bulkState.results = successful
                draft.bulkState.errors = failed
              })

              // Add notification
              get().addNotification({
                type: failed.length > 0 ? "warning" : "success",
                title: "Bulk Update Complete",
                message: `${successful.length} components updated, ${failed.length} failed.`,
                timestamp: new Date(),
                duration: 5000,
              })

              return { successful, failed }
            } catch (error) {
              set((draft) => {
                draft.lastOperationError =
                  error instanceof Error ? error.message : "Unknown error"
                draft.bulkState.isProcessing = false
              })
              return { successful, failed }
            }
          },

          // Utility Operations
          syncWithApi: async () => {
            await get().refreshCatalog()
          },

          clearCache: () =>
            set((draft) => {
              draft.componentCache.clear()
            }),

          getFromCache: (id: number) => {
            return get().componentCache.get(id) || null
          },
        }))
      ),
      {
        name: "domain-component-store",
        partialize: (state) => ({
          // Only persist user preferences and some UI state
          userPreferences: state.userPreferences,
          sidebarState: {
            is_open: state.sidebarState.is_open,
            width: state.sidebarState.width,
            active_section: state.sidebarState.active_section,
          },
          searchState: pick(state.searchState, [
            "searchHistory",
            "savedSearches",
          ]),
          listState: {
            ...createDefaultListState(),
            viewMode: state.listState.viewMode,
            sortBy: state.listState.sortBy,
            sortOrder: state.listState.sortOrder,
            pageSize: state.listState.pageSize,
          },
        }),
        version: 2, // Increment version due to new domain state
      }
    ),
    {
      name: "domain-component-store",
      enabled: process.env.NODE_ENV === "development",
    }
  )
)

// Enhanced selector hooks for domain objects
export const useDomainComponentCatalog = () =>
  useDomainComponentStore((state) => state.componentCatalog)
export const useDomainComponentCache = () =>
  useDomainComponentStore((state) => state.componentCache)
export const useSelectedDomainComponents = () =>
  useDomainComponentStore((state) => state.selectedComponents)
export const useComponentValidationService = () =>
  useDomainComponentStore((state) => state.validationService)
export const useLastValidationResult = () =>
  useDomainComponentStore((state) => state.lastValidationResult)
export const useComponentOperationError = () =>
  useDomainComponentStore((state) => state.lastOperationError)
export const useComponentLoadingStates = () =>
  useDomainComponentStore((state) => ({
    isLoadingCatalog: state.isLoadingCatalog,
    isLoadingComponent: state.isLoadingComponent,
    isSaving: state.isSaving,
  }))

// Original selector hooks (preserved for backwards compatibility)
export const useComponentListState = () =>
  useDomainComponentStore((state) => state.listState)
export const useComponentFilters = () =>
  useDomainComponentStore((state) => state.filterState.active_filters)
export const useComponentViewMode = () =>
  useDomainComponentStore((state) => state.listState.viewMode)
export const useComponentSelection = () =>
  useDomainComponentStore((state) => state.bulkState.selectedIds)
export const useComponentDisplayOptions = () =>
  useDomainComponentStore((state) => state.userPreferences.display_options)
export const useComponentFormState = () =>
  useDomainComponentStore((state) => state.formState)
export const useComponentNotifications = () =>
  useDomainComponentStore((state) => state.notifications)
