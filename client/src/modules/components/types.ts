// Import types for use in interfaces
import type {
  ComponentAdvancedSearch,
  ComponentCreate,
  ComponentSearch,
  ComponentUpdate,
  ComponentValidationResult,
} from "@/types/api"

/**
 * Component management domain-specific types
 * Re-exports from the main API types and adds domain-specific extensions
 */

// Re-export component-related types from the main API types
export type {
  ComponentAdvancedSearch,
  ComponentAdvancedSearchResponse,
  Component,
  ComponentBulkCreate,
  ComponentBulkUpdate,
  ComponentCreate,
  ComponentDimensions,
  ComponentPaginatedResponse,
  ComponentRead,
  ComponentSearch,
  ComponentStats,
  ComponentSummary,
  ComponentUpdate,
  ComponentValidationResult,
} from "@/types/api"

// Add alias for compatibility
export type ComponentFilter = ComponentFilterState
export type RangeFilter = {
  min?: number
  max?: number
}

// Define missing types locally
export interface AdvancedFilter {
  field: string
  operator: "eq" | "ne" | "gt" | "gte" | "lt" | "lte" | "in" | "contains"
  value: unknown
}

export interface RangeFilter {
  min?: number
  max?: number
}

export interface SpecificationFilter {
  key: string
  value: unknown
  operator?: "eq" | "contains" | "exists"
}

export interface ComponentCategoryEntity {
  id: number
  name: string
  description?: string
}

export interface ComponentTypeEntity {
  id: number
  name: string
  description?: string
  category_id?: number
}

export interface ComponentSearchResult {
  id: number
  name: string
  manufacturer: string
  model_number: string
  match_score: number
}

export interface ComponentSpecifications {
  electrical?: Record<string, unknown>
  thermal?: Record<string, unknown>
  mechanical?: Record<string, unknown>
  environmental?: Record<string, unknown>
  standards_compliance?: string[]
}

// Domain-specific UI state types
export interface ComponentFilterState {
  search_term?: string
  component_category_id?: number | null
  component_type_id?: number | null
  manufacturer?: string
  is_preferred?: boolean | null
  is_active?: boolean | null
  min_price?: number | null
  max_price?: number | null
  currency?: string
  stock_status?: string
}

export interface ComponentListState {
  filters: ComponentFilterState
  sortBy: string
  sortOrder: "asc" | "desc"
  page: number
  pageSize: number
  viewMode: "grid" | "list" | "table" | "cards"
  selectedComponents: number[]
}

export interface ComponentFormState {
  isEditing: boolean
  isDirty: boolean
  isSubmitting: boolean
  errors: Record<string, string>
  component: Partial<ComponentCreate | ComponentUpdate>
}

export interface ComponentSearchState {
  query: string
  field: string
  suggestions: string[]
  isSearching: boolean
  isAdvancedMode: boolean
  searchHistory: SearchHistoryEntry[]
  recentSearches: SearchHistoryEntry[]
  savedSearches: Array<{
    id: string
    name: string
    search: ComponentSearch | ComponentAdvancedSearch
    created_at: string
  }>
}

// UI-specific component display types
export interface ComponentDisplayOptions {
  showImages: boolean
  showSpecifications: boolean
  showPricing: boolean
  showPrices: boolean
  showAvailability: boolean
  compactMode: boolean
}

// Bulk operation UI types
export interface BulkOperationState {
  selectedIds: number[]
  operation: "update" | "delete" | "export" | null
  isProcessing: boolean
  progress: number
  results: ComponentValidationResult[]
  errors: string[]
}

// Component category and type summary for dropdowns
export interface ComponentCategorySummary {
  id: number
  name: string
  description?: string
  component_count: number
}

export interface ComponentTypeSummary {
  id: number
  name: string
  description?: string
  category_id?: number
  component_count: number
}

// Search suggestion types
export interface SearchHistoryEntry {
  query: string
  timestamp: string
}

export interface SearchSuggestion {
  value: string
  type: "name" | "manufacturer" | "part_number" | "model"
  count?: number
}

// Component validation types for forms
export interface ComponentValidationRules {
  name: {
    required: boolean
    minLength: number
    maxLength: number
  }
  manufacturer: {
    required: boolean
    minLength: number
    maxLength: number
  }
  model_number: {
    required: boolean
    minLength: number
    maxLength: number
  }
  part_number: {
    required: boolean
    minLength: number
    maxLength: number
  }
  description: {
    maxLength: number
  }
  unit_price: {
    min: number
  }
  weight_kg: {
    min: number
  }
}

// Export validation rules constant
export const COMPONENT_VALIDATION_RULES: ComponentValidationRules = {
  name: {
    required: true,
    minLength: 3,
    maxLength: 200,
  },
  manufacturer: {
    required: true,
    minLength: 2,
    maxLength: 100,
  },
  model_number: {
    required: true,
    minLength: 1,
    maxLength: 100,
  },
  part_number: {
    required: true,
    minLength: 1,
    maxLength: 100,
  },
  description: {
    maxLength: 1000,
  },
  unit_price: {
    min: 0,
  },
  weight_kg: {
    min: 0,
  },
}

// Component status types
export type ComponentStatus = "active" | "inactive" | "discontinued" | "pending"
export type StockStatus =
  | "available"
  | "low_stock"
  | "out_of_stock"
  | "discontinued"
  | "on_order"

// Component import/export types
export interface ComponentImportData {
  file: File
  format: "csv" | "xlsx" | "json"
  mapping: Record<string, string>
  validateOnly: boolean
}

export interface ComponentExportOptions {
  format: "csv" | "xlsx" | "json" | "pdf"
  includeSpecifications: boolean
  includeImages: boolean
  filters?: ComponentFilterState
  selectedIds?: number[]
}
