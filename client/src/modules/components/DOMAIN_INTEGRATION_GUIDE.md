# Domain Integration Guide - Components Module

## Overview

This guide demonstrates how Domain-Driven Design (DDD) tactical patterns have been successfully integrated into the Components module, serving as a reference implementation for other modules in the Ultimate Electrical Designer platform.

## Architecture Overview

### Domain Layer Structure

```
src/modules/components/domain/
├── entities/               # Domain entities with business logic
│   └── Component.ts       # Core component entity
├── value-objects/         # Immutable value objects
│   ├── ComponentStatus.ts # Status value object
│   ├── Specifications.ts  # Technical specifications
│   └── PriceValue.ts     # Price with currency
├── aggregates/           # Domain aggregates
│   └── ComponentCatalog.ts # Component collection management
├── domain-services/      # Domain services for complex logic
│   └── ComponentValidationService.ts # Business rule validation
├── events/              # Domain events (future implementation)
├── repositories/        # Repository interfaces
│   ├── IComponentRepository.ts
│   └── IComponentCatalogRepository.ts
└── specifications/      # Business rule specifications
    └── ComponentSpecifications.ts
```

## Core Domain Concepts

### 1. Domain Entities

#### Component Entity
The `Component` entity encapsulates all business logic and invariants for electrical components:

```typescript
// src/modules/components/domain/entities/Component.ts
export class Component {
  private constructor(
    public readonly id: number,
    private _name: string,
    private _manufacturer: string,
    private _modelNumber: string,
    private _specifications: Specifications,
    private _price: PriceValue,
    private _status: ComponentStatus,
    public readonly version: string = '1.0.0',
    public readonly createdAt: string,
    public readonly updatedAt: string
  ) {
    this.validateInvariants()
  }

  // Business methods
  public updateSpecifications(specs: Partial<SpecificationsData>): Component {
    const updatedSpecs = this._specifications.update(specs)
    return new Component(
      this.id,
      this._name,
      this._manufacturer,
      this._modelNumber,
      updatedSpecs,
      this._price,
      this._status,
      this.version,
      this.createdAt,
      new Date().toISOString()
    )
  }

  public markAsPreferred(): Component {
    const newStatus = this._status.markAsPreferred()
    return this.withStatus(newStatus)
  }

  // Domain invariants
  private validateInvariants(): void {
    this.validateName()
    this.validatePrice()
    this.validateSpecifications()
  }

  // Business queries
  public isAvailable(): boolean {
    return this._status.isAvailable()
  }

  public isActive(): boolean {
    return this._status.isActive()
  }
}
```

**Key Features:**
- **Immutability**: All updates create new instances
- **Business Logic**: Domain-specific methods like `markAsPreferred()`
- **Invariant Protection**: Automatic validation on construction
- **Rich Queries**: Business-meaningful query methods

### 2. Value Objects

#### ComponentStatus Value Object
Encapsulates component status with business rules:

```typescript
// src/modules/components/domain/value-objects/ComponentStatus.ts
export class ComponentStatus {
  private constructor(
    private readonly isActive: boolean,
    private readonly isPreferred: boolean,
    private readonly stockStatus: StockStatus
  ) {}

  public static create(data: ComponentStatusData): ComponentStatus {
    return new ComponentStatus(
      data.isActive ?? true,
      data.isPreferred ?? false,
      data.stockStatus ?? 'available'
    )
  }

  public markAsPreferred(): ComponentStatus {
    return new ComponentStatus(this.isActive, true, this.stockStatus)
  }

  public isAvailable(): boolean {
    return this.isActive && this.stockStatus !== 'out_of_stock' && this.stockStatus !== 'discontinued'
  }

  // Value object equality
  public equals(other: ComponentStatus): boolean {
    return this.isActive === other.isActive &&
           this.isPreferred === other.isPreferred &&
           this.stockStatus === other.stockStatus
  }
}
```

### 3. Domain Aggregates

#### ComponentCatalog Aggregate
Manages collections of components with business rules:

```typescript
// src/modules/components/domain/aggregates/ComponentCatalog.ts
export class ComponentCatalog {
  private constructor(
    private readonly components: Map<number, Component> = new Map(),
    private readonly metadata: CatalogMetadata
  ) {}

  public addComponent(component: Component): ComponentCatalog {
    // Business rule: No duplicate part numbers
    const existingWithSamePartNumber = Array.from(this.components.values())
      .find(c => c.partNumber === component.partNumber)
    
    if (existingWithSamePartNumber) {
      throw new DomainError('Component with this part number already exists')
    }

    const newComponents = new Map(this.components)
    newComponents.set(component.id, component)
    
    return new ComponentCatalog(newComponents, this.metadata)
  }

  public getPreferredComponents(): Component[] {
    return Array.from(this.components.values())
      .filter(component => component.isPreferred())
  }

  public searchComponents(query: ComponentSearchQuery): Component[] {
    return Array.from(this.components.values())
      .filter(component => this.matchesQuery(component, query))
  }
}
```

## Integration Patterns

### 1. React Hook Integration

#### Domain-Aware Hooks
```typescript
// src/modules/components/hooks/useDomainComponentHooks.ts
export function useComponentOperations() {
  const { catalog, isLoading, error } = useDomainComponentStore()
  
  const createComponent = useMutation({
    mutationFn: async (data: ComponentCreateData) => {
      // Create domain entity first for validation
      const component = Component.create(data)
      
      // Convert to API format through adapter
      const apiData = ComponentAdapter.toApiFormat(component)
      
      // Make API call
      return await componentApi.create(apiData)
    },
    onSuccess: (result) => {
      // Convert API response back to domain entity
      const domainComponent = ComponentAdapter.toDomainEntity(result)
      
      // Update domain store
      addComponentToCatalog(domainComponent)
    }
  })

  return {
    catalog,
    isLoading,
    error,
    createComponent: createComponent.mutate,
    isCreating: createComponent.isPending
  }
}
```

#### Domain Form Integration
```typescript
// src/modules/components/hooks/useDomainComponentForm.ts
export function useDomainComponentForm(options: UseDomainComponentFormOptions = {}) {
  const { validateComponent, validateUpdate } = useDomainComponentStore()
  const [data, setData] = useState<ComponentFormData>(getInitialData(options.component))
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null)

  const performValidation = useCallback((formData: ComponentFormData): ValidationResult => {
    try {
      if (mode === 'create') {
        // Validate through domain entity creation
        Component.create(formData)
        return { isValid: true, errors: [], warnings: [] }
      } else if (component) {
        // Validate through domain entity update
        component.updateWith(formData)
        return { isValid: true, errors: [], warnings: [] }
      }
    } catch (error) {
      if (error instanceof ValidationError) {
        return {
          isValid: false,
          errors: error.errors,
          warnings: error.warnings
        }
      }
      throw error
    }
    
    return { isValid: false, errors: ['Unknown validation error'], warnings: [] }
  }, [mode, component])

  return {
    data,
    validationResult,
    updateField: (field: string, value: any) => {
      const newData = { ...data, [field]: value }
      setData(newData)
      
      // Real-time validation through domain layer
      const validation = performValidation(newData)
      setValidationResult(validation)
    },
    handleSubmit: async (e: React.FormEvent) => {
      e.preventDefault()
      const validation = performValidation(data)
      
      if (validation.isValid) {
        if (mode === 'create') {
          await createComponent.mutateAsync(data)
        } else if (component) {
          await updateComponent.mutateAsync({ id: component.id, ...data })
        }
      }
    }
  }
}
```

### 2. State Management Integration

#### Zustand Store with Domain Logic
```typescript
// src/modules/components/hooks/useDomainComponentStore.ts
interface DomainComponentStore {
  catalog: ComponentCatalog | null
  selectedComponents: Component[]
  
  // Domain operations
  loadCatalog: () => Promise<void>
  addComponent: (component: Component) => void
  updateComponent: (id: number, updates: ComponentUpdateData) => void
  validateComponent: (data: ComponentCreateData) => ValidationResult
  validateUpdate: (component: Component, updates: ComponentUpdateData) => ValidationResult
}

export const useDomainComponentStore = create<DomainComponentStore>((set, get) => ({
  catalog: null,
  selectedComponents: [],

  loadCatalog: async () => {
    const apiComponents = await componentApi.getAll()
    const domainComponents = apiComponents.map(ComponentAdapter.toDomainEntity)
    const catalog = ComponentCatalog.fromComponents(domainComponents)
    
    set({ catalog })
  },

  addComponent: (component) => {
    const { catalog } = get()
    if (catalog) {
      const updatedCatalog = catalog.addComponent(component)
      set({ catalog: updatedCatalog })
    }
  },

  validateComponent: (data) => {
    try {
      Component.create(data) // Domain validation
      return { isValid: true, errors: [], warnings: [] }
    } catch (error) {
      if (error instanceof ValidationError) {
        return { isValid: false, errors: error.errors, warnings: error.warnings }
      }
      throw error
    }
  }
}))
```

### 3. Component Integration

#### Domain-Aware Form Component
```typescript
// src/modules/components/components/organisms/DomainComponentForm.tsx
export function DomainComponentForm({ component, onSuccess }: DomainComponentFormProps) {
  const {
    data,
    errors,
    warnings,
    isValid,
    isDirty,
    validationResult,
    updateField,
    handleSubmit
  } = useDomainComponentForm({ component, onSuccess })

  return (
    <Card>
      {/* Validation Summary from Domain */}
      {validationResult && !validationResult.isValid && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Please fix the following errors before submitting:
            <ul className="mt-2 list-inside list-disc">
              {errors.slice(0, 3).map((error, index) => (
                <li key={index}>{error.message}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {/* Domain Warnings */}
      {warnings.length > 0 && (
        <Alert>
          <Info className="h-4 w-4" />
          <AlertDescription>
            {warnings.map((warning, index) => (
              <Badge key={index} variant="outline" className="text-yellow-600">
                {warning.message}
              </Badge>
            ))}
          </AlertDescription>
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        {/* Form fields with domain validation */}
        <Input
          value={data.name || ''}
          onChange={(e) => updateField('name', e.target.value)}
          className={getFieldError('name') ? 'border-red-500' : ''}
        />
        
        <Button 
          type="submit" 
          disabled={!isValid || !isDirty}
        >
          {component ? 'Update Component' : 'Create Component'}
        </Button>
      </form>
    </Card>
  )
}
```

## API Integration

### Domain Adapter Pattern
```typescript
// src/modules/components/api/ComponentAdapter.ts
export class ComponentAdapter {
  public static toDomainEntity(apiComponent: ComponentRead): Component {
    return Component.create({
      id: apiComponent.id,
      name: apiComponent.name,
      manufacturer: apiComponent.manufacturer,
      modelNumber: apiComponent.model_number,
      specifications: {
        partNumber: apiComponent.part_number,
        description: apiComponent.description,
        weightKg: apiComponent.weight_kg
      },
      price: {
        amount: apiComponent.unit_price,
        currency: apiComponent.currency
      },
      status: {
        isActive: apiComponent.is_active,
        isPreferred: apiComponent.is_preferred,
        stockStatus: apiComponent.stock_status
      },
      categoryId: apiComponent.category_id,
      componentTypeId: apiComponent.component_type_id,
      supplier: apiComponent.supplier,
      version: apiComponent.version || '1.0.0',
      createdAt: apiComponent.created_at,
      updatedAt: apiComponent.updated_at
    })
  }

  public static toApiFormat(component: Component): ComponentCreate {
    return {
      name: component.name,
      manufacturer: component.manufacturer,
      model_number: component.modelNumber,
      part_number: component.partNumber,
      description: component.description,
      unit_price: component.price,
      currency: component.currency,
      weight_kg: component.weightKg,
      category_id: component.categoryId,
      component_type_id: component.componentTypeId,
      supplier: component.supplier,
      is_active: component.isActive(),
      is_preferred: component.isPreferred(),
      stock_status: component.stockStatus
    }
  }
}
```

## Testing Strategies

### Unit Testing Domain Logic
```typescript
// src/modules/components/domain/entities/__tests__/Component.test.ts
describe('Component Domain Entity', () => {
  describe('Business Logic', () => {
    test('should mark component as preferred', () => {
      const component = Component.create(validComponentData)
      const preferredComponent = component.markAsPreferred()
      
      expect(preferredComponent.isPreferred()).toBe(true)
      expect(preferredComponent.id).toBe(component.id) // Same identity
      expect(preferredComponent).not.toBe(component) // Different instance
    })

    test('should validate business invariants', () => {
      expect(() => Component.create({
        ...validComponentData,
        name: '' // Invalid: empty name
      })).toThrow(ValidationError)
    })

    test('should update specifications immutably', () => {
      const component = Component.create(validComponentData)
      const updatedComponent = component.updateSpecifications({
        description: 'Updated description'
      })
      
      expect(updatedComponent.description).toBe('Updated description')
      expect(component.description).not.toBe('Updated description')
    })
  })
})
```

### Integration Testing
```typescript
// src/modules/components/hooks/__tests__/useDomainComponentForm.test.tsx
describe('useDomainComponentForm', () => {
  test('should validate through domain layer', () => {
    const { result } = renderHook(() => useDomainComponentForm())
    
    act(() => {
      result.current.updateField('name', '') // Invalid
    })
    
    expect(result.current.validationResult?.isValid).toBe(false)
    expect(result.current.errors).toContainEqual(
      expect.objectContaining({ field: 'name', message: expect.stringContaining('required') })
    )
  })

  test('should create component through domain layer', async () => {
    const mockOnSuccess = vi.fn()
    const { result } = renderHook(() => useDomainComponentForm({ onSuccess: mockOnSuccess }))
    
    // Fill valid form data
    act(() => {
      Object.entries(validComponentData).forEach(([field, value]) => {
        result.current.updateField(field, value)
      })
    })
    
    // Submit form
    await act(async () => {
      await result.current.handleSubmit(mockEvent)
    })
    
    expect(mockOnSuccess).toHaveBeenCalledWith(
      expect.objectContaining({ name: validComponentData.name })
    )
  })
})
```

### E2E Testing with Domain Validation
```typescript
// tests/e2e/components/domain-integrated-components.spec.ts
test('should validate domain rules in browser', async ({ page }) => {
  await domainPage.openDomainCreateForm()
  
  // Test domain validation in real browser environment
  await domainPage.fillDomainForm({ name: '', manufacturer: '' })
  
  // Should show domain-generated validation errors
  await expect(domainPage.getDomainValidationSummary()).toBeVisible()
  await expect(domainPage.getDomainFieldError('name')).toContainText(/required/i)
  
  // Submit should be disabled due to domain validation
  await expect(page.locator('[data-testid="domain-form-submit"]')).toBeDisabled()
})
```

## Migration Guide for Other Modules

### Step 1: Analyze Domain Concepts
1. Identify core entities (e.g., Project, User, Circuit)
2. Extract value objects (e.g., ProjectStatus, UserRole, Voltage)
3. Define aggregates (e.g., ProjectPortfolio, UserManagement)
4. Identify domain services (e.g., ProjectValidationService)

### Step 2: Create Domain Layer
```bash
# Create domain directory structure
mkdir -p src/modules/{module-name}/domain/{entities,value-objects,aggregates,domain-services,repositories}

# Copy and adapt domain templates from components module
cp -r src/modules/components/domain/entities/Component.ts src/modules/{module-name}/domain/entities/{Entity}.ts
```

### Step 3: Integrate with React Layer
1. Create domain-aware hooks following `useDomainComponentHooks.ts` pattern
2. Update stores to use domain entities
3. Create domain-integrated forms
4. Add adapters for API integration

### Step 4: Testing Integration
1. Unit test domain logic
2. Integration test hooks and components  
3. E2E test complete workflows
4. Performance audit

## Best Practices

### 1. Domain Modeling
- **Keep entities focused**: Each entity should have a single clear purpose
- **Protect invariants**: Always validate business rules in constructors
- **Use immutability**: Create new instances for all state changes
- **Rich behavior**: Add domain-meaningful methods, not just getters/setters

### 2. Integration Patterns
- **Adapter pattern**: Use adapters to convert between API and domain formats
- **Repository interfaces**: Define domain contracts, implement in infrastructure
- **Domain services**: Extract complex business logic that doesn't belong to entities
- **Event-driven updates**: Use domain events for loose coupling (future enhancement)

### 3. Performance Considerations
- **Lazy loading**: Load domain services and complex validations on-demand
- **Memoization**: Cache expensive domain operations
- **Batch operations**: Process multiple domain operations together
- **Virtualization**: Use virtual scrolling for large domain collections

### 4. Testing Strategy
- **Test domain logic first**: Unit test all business rules
- **Integration testing**: Verify domain-React integration
- **E2E validation**: Test complete workflows including domain validation
- **Performance testing**: Monitor domain operation performance

## Common Pitfalls and Solutions

### 1. Over-Engineering
**Problem**: Creating too many abstractions for simple operations
**Solution**: Start simple, add complexity only when business rules demand it

### 2. Performance Issues
**Problem**: Domain operations causing UI lag
**Solution**: Use debouncing, memoization, and lazy loading

### 3. State Synchronization
**Problem**: Domain state getting out of sync with UI state
**Solution**: Single source of truth through domain store, proper error boundaries

### 4. Testing Complexity
**Problem**: Domain integration making tests complex
**Solution**: Test domain logic separately, mock domain layer in UI tests

## Conclusion

The domain-driven architecture in the Components module provides:

- ✅ **Business Logic Encapsulation**: All rules centralized in domain layer
- ✅ **Type Safety**: Full TypeScript integration with domain models
- ✅ **Testability**: Clear separation enables focused testing
- ✅ **Maintainability**: Business logic changes isolated to domain layer
- ✅ **Scalability**: Pattern scales to complex business requirements

This implementation serves as the foundation for expanding domain-driven architecture across the entire Ultimate Electrical Designer platform.

---

**For Questions**: Refer to the Components module implementation or create GitHub issues for clarification.
**Next Steps**: Apply these patterns to Projects and Dashboard modules using the migration guide above.