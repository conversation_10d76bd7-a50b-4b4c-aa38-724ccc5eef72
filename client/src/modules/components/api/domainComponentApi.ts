/**
 * Domain-Aware Component API Service
 *
 * This service provides a domain-centric interface to the component API,
 * automatically converting between API responses and domain objects.
 */

import type { RequestOptions } from "@/types/api"
import type {
  ComponentCreateData,
  ComponentUpdateData,
  ValidationResult,
} from "@/modules/components/domain"

import {
  Component,
  ComponentCatalog,
  ComponentValidationService,
} from "@/modules/components/domain"

import { componentApi } from "@/modules/components/api"
import {
  apiAdvancedSearchToDomain,
  apiComponentToDomain,
  apiPaginatedResponseToDomain,
  DomainApiError,
  domainComponentToApiCreate,
  domainSearchToApiParams,
  domainUpdateToApiUpdate,
  handleApiResponseWithDomain,
} from "@/modules/components/api"

/**
 * Domain-aware validation using ComponentValidationService
 */
export function validateComponentData(
  componentData: ComponentCreateData,
  validationService?: ComponentValidationService
): ValidationResult {
  const service = validationService || new ComponentValidationService()
  return service.validateComponentCreation(componentData)
}

/**
 * Domain search criteria interface
 */
interface DomainSearchCriteria {
  namePattern?: string
  manufacturer?: string
  componentTypeId?: number
  categoryId?: number
  isPreferred?: boolean
  stockStatus?: string
  isActive?: boolean
  priceRange?: {
    min?: number
    max?: number
    currency?: string
  }
}

/**
 * Domain pagination options
 */
interface DomainPaginationOptions {
  page?: number
  size?: number
}

/**
 * Domain component API service
 */
export class DomainComponentApiService {
  private validationService: ComponentValidationService

  constructor(validationService?: ComponentValidationService) {
    this.validationService =
      validationService || new ComponentValidationService()
  }

  // Basic CRUD Operations

  /**
   * Get component by ID
   */
  async getById(
    id: number,
    options?: RequestOptions
  ): Promise<{ data?: Component; error?: DomainApiError }> {
    return handleApiResponseWithDomain(
      componentApi.getById(id, options),
      apiComponentToDomain,
      "search"
    )
  }

  /**
   * List components with domain conversion
   */
  async list(
    criteria: DomainSearchCriteria = {},
    pagination: DomainPaginationOptions = {},
    options?: RequestOptions
  ): Promise<{
    data?: {
      components: Component[]
      pagination: {
        totalCount: number
        page: number
        size: number
        hasMore: boolean
      }
    }
    error?: DomainApiError
  }> {
    const apiParams = domainSearchToApiParams(criteria)

    return handleApiResponseWithDomain(
      componentApi.list(
        {
          ...apiParams,
          page: pagination.page,
          size: pagination.size,
        },
        options
      ),
      apiPaginatedResponseToDomain,
      "search"
    )
  }

  /**
   * Create component with domain validation
   */
  async create(
    componentData: ComponentCreateData,
    options?: RequestOptions
  ): Promise<{
    data?: Component
    error?: DomainApiError
    validation?: ValidationResult
  }> {
    // Domain validation before API call
    const validation =
      this.validationService.validateComponentCreation(componentData)

    if (!validation.isValid) {
      return {
        error: new DomainApiError(
          `Validation failed: ${validation.errors.join(", ")}`,
          validation,
          "validation"
        ),
        validation,
      }
    }

    // Convert to API format and create
    const component = Component.create(componentData)
    const apiData = domainComponentToApiCreate(component)

    const result = await handleApiResponseWithDomain(
      componentApi.create(apiData, options),
      apiComponentToDomain,
      "create"
    )

    return {
      ...result,
      validation,
    }
  }

  /**
   * Update component with domain validation
   */
  async update(
    id: number,
    updateData: ComponentUpdateData,
    options?: RequestOptions
  ): Promise<{
    data?: Component
    error?: DomainApiError
    validation?: ValidationResult
  }> {
    // First, get the existing component for validation context
    const existingResult = await this.getById(id, options)
    if (existingResult.error || !existingResult.data) {
      return {
        error:
          existingResult.error ||
          new DomainApiError("Component not found", null, "update"),
      }
    }

    // Domain validation
    const validation = this.validationService.validateComponentUpdate(
      existingResult.data,
      updateData
    )

    if (!validation.isValid) {
      return {
        error: new DomainApiError(
          `Validation failed: ${validation.errors.join(", ")}`,
          validation,
          "validation"
        ),
        validation,
      }
    }

    // Convert to API format and update
    const apiUpdateData = domainUpdateToApiUpdate(updateData)

    const result = await handleApiResponseWithDomain(
      componentApi.update(id, apiUpdateData, options),
      apiComponentToDomain,
      "update"
    )

    return {
      ...result,
      validation,
    }
  }

  /**
   * Delete component
   */
  async delete(
    id: number,
    options?: RequestOptions
  ): Promise<{ error?: DomainApiError }> {
    try {
      const response = await componentApi.delete(id, options)

      if (response.error) {
        return {
          error: DomainApiError.fromApiError(response.error, "delete"),
        }
      }

      return {}
    } catch (error) {
      return {
        error: DomainApiError.fromApiError(error, "delete"),
      }
    }
  }

  // Advanced Search Operations

  /**
   * Search components with domain criteria
   */
  async search(
    criteria: DomainSearchCriteria,
    pagination: DomainPaginationOptions = {},
    options?: RequestOptions
  ): Promise<{
    data?: {
      components: Component[]
      pagination: {
        totalCount: number
        page: number
        size: number
        hasMore: boolean
      }
    }
    error?: DomainApiError
  }> {
    const apiParams = domainSearchToApiParams(criteria)

    return handleApiResponseWithDomain(
      componentApi.search(apiParams, pagination, options),
      apiPaginatedResponseToDomain,
      "search"
    )
  }

  /**
   * Advanced search returning ComponentCatalog
   */
  async advancedSearch(
    searchQuery: string,
    filters: DomainSearchCriteria = {},
    pagination: DomainPaginationOptions = {},
    options?: RequestOptions
  ): Promise<{
    data?: {
      catalog: ComponentCatalog
      metadata: {
        totalCount: number
        searchScore: number
        suggestions: string[]
      }
    }
    error?: DomainApiError
  }> {
    const apiParams = domainSearchToApiParams(filters)

    return handleApiResponseWithDomain(
      componentApi.advancedSearch(
        {
          query: searchQuery,
          filters: apiParams,
        },
        pagination,
        options
      ),
      apiAdvancedSearchToDomain,
      "search"
    )
  }

  /**
   * Get preferred components as ComponentCatalog
   */
  async getPreferredCatalog(
    pagination: DomainPaginationOptions = {},
    options?: RequestOptions
  ): Promise<{
    data?: ComponentCatalog
    error?: DomainApiError
  }> {
    return handleApiResponseWithDomain(
      componentApi.getPreferred(
        {
          skip: pagination.page
            ? (pagination.page - 1) * (pagination.size || 10)
            : 0,
          limit: pagination.size || 10,
        },
        options
      ),
      (apiComponents) => {
        const components = apiComponents.map((apiComponent) =>
          apiComponentToDomain(apiComponent as any)
        )
        return ComponentCatalog.fromComponents(components)
      },
      "search"
    )
  }

  // Bulk Operations

  /**
   * Bulk create components with domain validation
   */
  async bulkCreate(
    componentsData: ComponentCreateData[],
    options?: RequestOptions
  ): Promise<{
    data?: {
      successful: Component[]
      failed: Array<{
        data: ComponentCreateData
        error: string
        validation?: ValidationResult
      }>
      statistics: {
        total: number
        successful: number
        failed: number
      }
    }
    error?: DomainApiError
  }> {
    // Domain validation for all components
    const validationResults = componentsData.map((data) => ({
      data,
      validation: this.validationService.validateComponentCreation(data),
    }))

    const validComponents = validationResults.filter(
      (r) => r.validation.isValid
    )
    const invalidComponents = validationResults.filter(
      (r) => !r.validation.isValid
    )

    if (validComponents.length === 0) {
      return {
        data: {
          successful: [],
          failed: invalidComponents.map((r) => ({
            data: r.data,
            error: `Validation failed: ${r.validation.errors.join(", ")}`,
            validation: r.validation,
          })),
          statistics: {
            total: componentsData.length,
            successful: 0,
            failed: componentsData.length,
          },
        },
      }
    }

    // Convert valid components to API format
    const apiComponents = validComponents.map((r) => {
      const component = Component.create(r.data)
      return domainComponentToApiCreate(component)
    })

    try {
      const response = await componentApi.bulkCreate(
        {
          components: apiComponents,
          validate: true,
        },
        options
      )

      if (response.error) {
        return {
          error: DomainApiError.fromApiError(response.error, "create"),
        }
      }

      if (response.data) {
        const successful = response.data
          .filter((r) => r.is_valid && r.component)
          .map((r) => apiComponentToDomain(r.component!))

        const apiFailed = response.data
          .filter((r) => !r.is_valid)
          .map((r, index) => ({
            data: validComponents[index].data,
            error: r.errors?.join(", ") || "Unknown error",
          }))

        return {
          data: {
            successful,
            failed: [
              ...apiFailed,
              ...invalidComponents.map((r) => ({
                data: r.data,
                error: `Validation failed: ${r.validation.errors.join(", ")}`,
                validation: r.validation,
              })),
            ],
            statistics: {
              total: componentsData.length,
              successful: successful.length,
              failed: apiFailed.length + invalidComponents.length,
            },
          },
        }
      }

      return {
        error: new DomainApiError(
          "No data received from bulk create",
          null,
          "create"
        ),
      }
    } catch (error) {
      return {
        error: DomainApiError.fromApiError(error, "create"),
      }
    }
  }

  // Utility Methods

  /**
   * Validate component data without API call
   */
  validateComponent(componentData: ComponentCreateData): ValidationResult {
    return this.validationService.validateComponentCreation(componentData)
  }

  /**
   * Compare two components
   */
  compareComponents(component1: Component, component2: Component) {
    return this.validationService.compareComponents(component1, component2)
  }

  /**
   * Get component statistics
   */
  async getStatistics(options?: RequestOptions) {
    return componentApi.getStats(options)
  }

  /**
   * Get search suggestions
   */
  async getSuggestions(
    query: string,
    field: string = "name",
    limit: number = 10,
    options?: RequestOptions
  ) {
    return componentApi.getSuggestions(query, field, limit, options)
  }
}

// Default instance
export const domainComponentApi = new DomainComponentApiService()
