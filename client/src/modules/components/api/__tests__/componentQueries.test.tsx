/**
 * Component React Query Hooks Tests
 * Tests the React Query hooks for component data fetching
 */

import React from "react"

import {
  mockApiErrorResponse,
  mockApiSuccess,
  mockComponent,
  mockComponentPaginatedResponse,
  mockComponentStats,
} from "@/test/utils"
import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { renderHook, waitFor } from "@testing-library/react" // Removed unused 'act' import
import { beforeEach, describe, expect, it, vi } from "vitest"

import {
  useComponent,
  useComponentAdvancedSearch,
  useComponents,
  useComponentSearch,
  useComponentStats,
  useComponentSuggestions,
} from "../componentQueries"

// Mock the API client
vi.mock("../../api/componentApi", () => ({
  componentApi: {
    list: vi.fn(),
    getById: vi.fn(),
    search: vi.fn(),
    advancedSearch: vi.fn(),
    getStats: vi.fn(),
    getSuggestions: vi.fn(),
  },
}))

const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        staleTime: 0,
        gcTime: 0,
      },
    },
  })

  const TestWrapper = ({ children }: { children: React.ReactNode }) => (
    <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
  )
  TestWrapper.displayName = 'TestWrapper'
  return TestWrapper
}

describe("Component Query Hooks", () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe("useComponents", () => {
    it("fetches components successfully", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.list).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const { result } = renderHook(() => useComponents(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockComponentPaginatedResponse)
      expect(componentApi.list).toHaveBeenCalledWith({})
    })

    it("fetches components with parameters", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.list).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const params = {
        page: 2,
        size: 20,
        search_term: "resistor",
        category: "RESISTOR",
      }

      const { result } = renderHook(() => useComponents(params), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(componentApi.list).toHaveBeenCalledWith(params)
    })

    it("handles API errors", async () => {
      const { componentApi } = await import("../componentApi")
      const error = mockApiErrorResponse("Failed to fetch components")
      vi.mocked(componentApi.list).mockResolvedValue(error)

      const { result } = renderHook(() => useComponents(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error).toBeInstanceOf(Error)
      expect(result.current.error?.message).toBe("Failed to fetch components")
    })

    it("uses correct query key", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.list).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const params = { page: 1, category: "RESISTOR" }

      renderHook(() => useComponents(params), { wrapper: createWrapper() })

      // Query key should include parameters for proper caching
      expect(componentApi.list).toHaveBeenCalledWith(params)
    })

    it("respects staleTime configuration", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.list).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const { result } = renderHook(
        () => useComponents({}, { staleTime: 10 * 60 * 1000 }),
        {
          wrapper: createWrapper(),
        }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // Should not refetch immediately due to staleTime
      expect(componentApi.list).toHaveBeenCalledTimes(1)
    })
  })

  describe("useComponent", () => {
    it("fetches single component successfully", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.getById).mockResolvedValue(
        mockApiSuccess(mockComponent)
      )

      const { result } = renderHook(() => useComponent(1), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockComponent)
      expect(componentApi.getById).toHaveBeenCalledWith(1)
    })

    it("handles component not found", async () => {
      const { componentApi } = await import("../componentApi")
      const error = mockApiErrorResponse(
        "Component not found",
        "COMPONENT_NOT_FOUND"
      )
      vi.mocked(componentApi.getById).mockResolvedValue(error)

      const { result } = renderHook(() => useComponent(999), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.message).toBe("Component not found")
    })

    it("does not fetch when ID is undefined", async () => {
      const { componentApi } = await import("../componentApi")

      renderHook(() => useComponent(undefined as any), {
        wrapper: createWrapper(),
      })

      expect(componentApi.getById).not.toHaveBeenCalled()
    })

    it("enables/disables query based on ID", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.getById).mockResolvedValue(
        mockApiSuccess(mockComponent)
      )

      const { result, rerender } = renderHook(({ id }) => useComponent(id), {
        wrapper: createWrapper(),
        initialProps: { id: undefined as any },
      })

      expect(result.current.isFetching).toBe(false)

      rerender({ id: 1 })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(componentApi.getById).toHaveBeenCalledWith(1)
    })
  })

  describe("useComponentSearch", () => {
    it("searches components successfully", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.search).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const searchParams = {
        search_term: "resistor",
      }

      const { result } = renderHook(() => useComponentSearch(searchParams), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockComponentPaginatedResponse)
      expect(componentApi.search).toHaveBeenCalledWith(searchParams, {})
    })

    it("searches with pagination", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.search).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const searchParams = {
        search_term: "resistor",
      }
      const pagination = { page: 2, size: 20 }

      const { result } = renderHook(
        () => useComponentSearch(searchParams, pagination),
        {
          wrapper: createWrapper(),
        }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(componentApi.search).toHaveBeenCalledWith(searchParams, pagination)
    })

    it("handles empty search results", async () => {
      const { componentApi } = await import("../componentApi")
      const emptyResponse = {
        ...mockComponentPaginatedResponse,
        items: [],
        total: 0,
      }
      vi.mocked(componentApi.search).mockResolvedValue(
        mockApiSuccess(emptyResponse)
      )

      const searchParams = {
        search_term: "nonexistent",
      }

      const { result } = renderHook(() => useComponentSearch(searchParams), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data?.items).toEqual([])
      expect(result.current.data?.total).toBe(0)
    })

    it("does not search with empty query", async () => {
      const { componentApi } = await import("../componentApi")

      const searchParams = {
        search_term: "",
      }

      renderHook(() => useComponentSearch(searchParams), {
        wrapper: createWrapper(),
      })

      expect(componentApi.search).not.toHaveBeenCalled()
    })
  })

  describe("useComponentAdvancedSearch", () => {
    it("performs advanced search successfully", async () => {
      const { componentApi } = await import("../componentApi")
      const mockAdvancedSearchResponse = {
        items: mockComponentPaginatedResponse.items.map((component) => ({
          component,
          score: 1.0,
          highlights: {},
        })),
        pagination: {
          page: mockComponentPaginatedResponse.page,
          size: mockComponentPaginatedResponse.size,
          total: mockComponentPaginatedResponse.total,
          pages: mockComponentPaginatedResponse.pages,
        },
      }
      vi.mocked(componentApi.advancedSearch).mockResolvedValue(
        mockApiSuccess(mockAdvancedSearchResponse)
      )

      const searchParams = {
        search_term: "resistor",
        basic_filters: [
          { field: "category", operator: "eq", value: "RESISTOR" },
          { field: "manufacturer", operator: "eq", value: "Test Electronics" },
        ],
        specification_filters: [
          {
            field: "resistance",
            operator: "eq",
            value: "1000",
            path: "specifications.resistance",
            data_type: "string",
          },
        ],
      }

      const { result } = renderHook(
        () => useComponentAdvancedSearch(searchParams),
        {
          wrapper: createWrapper(),
        }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockAdvancedSearchResponse)
      expect(componentApi.advancedSearch).toHaveBeenCalledWith(searchParams, {})
    })

    it("handles complex filter combinations", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.advancedSearch).mockResolvedValue(
        mockApiSuccess({
          items: mockComponentPaginatedResponse.items.map((component: any) => ({
            component,
            score: 1.0,
            highlights: {},
          })),
          pagination: {
            page: mockComponentPaginatedResponse.page,
            size: mockComponentPaginatedResponse.size,
            total: mockComponentPaginatedResponse.total,
            pages: mockComponentPaginatedResponse.pages,
          },
        })
      )

      const complexParams = {
        search_term: "resistor",
        basic_filters: [
          { field: "category", operator: "eq", value: "RESISTOR" },
          { field: "is_preferred", operator: "eq", value: true },
        ],
        specification_filters: [
          {
            field: "resistance",
            operator: "gte",
            value: "1000",
            path: "specifications.resistance",
            data_type: "string",
          },
          {
            field: "tolerance",
            operator: "lte",
            value: "1%",
            path: "specifications.tolerance",
            data_type: "string",
          },
        ],
        range_filters: [{ field: "price", min: 0.1, max: 1.0 }],
      }

      const { result } = renderHook(
        () => useComponentAdvancedSearch(complexParams),
        {
          wrapper: createWrapper(),
        }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(componentApi.advancedSearch).toHaveBeenCalledWith(
        complexParams,
        {}
      )
    })
  })

  describe("useComponentStats", () => {
    it("fetches component statistics successfully", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.getStats).mockResolvedValue(
        mockApiSuccess(mockComponentStats)
      )

      const { result } = renderHook(() => useComponentStats(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockComponentStats)
      expect(componentApi.getStats).toHaveBeenCalled()
    })

    it("handles stats fetch errors", async () => {
      const { componentApi } = await import("../componentApi")
      const error = {
        data: undefined,
        error: {
          detail: "Failed to fetch stats",
          error_code: "FETCH_ERROR",
          timestamp: "2024-01-01T00:00:00Z",
        },
        status: 500,
      }
      vi.mocked(componentApi.getStats).mockResolvedValue(error)

      const { result } = renderHook(() => useComponentStats(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.message).toBe("Failed to fetch stats")
    })

    it("caches stats data appropriately", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.getStats).mockResolvedValue(
        mockApiSuccess(mockComponentStats)
      )

      const wrapper = createWrapper()

      const { result: result1 } = renderHook(() => useComponentStats(), {
        wrapper,
      })

      await waitFor(() => {
        expect(result1.current.isSuccess).toBe(true)
      })

      const { result: result2 } = renderHook(() => useComponentStats(), {
        wrapper,
      })

      await waitFor(() => {
        expect(result2.current.isSuccess).toBe(true)
      })

      // Should use cached data
      expect(componentApi.getStats).toHaveBeenCalledTimes(1)
    })
  })

  describe("useComponentSuggestions", () => {
    it("fetches suggestions successfully", async () => {
      const { componentApi } = await import("../componentApi")
      const mockSuggestions = ["resistor 1k", "resistor 2k", "resistor 10k"]
      vi.mocked(componentApi.getSuggestions).mockResolvedValue(
        mockApiSuccess(mockSuggestions)
      )

      const { result } = renderHook(
        () => useComponentSuggestions("res", "name"),
        {
          wrapper: createWrapper(),
        }
      )

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(result.current.data).toEqual(mockSuggestions)
      expect(componentApi.getSuggestions).toHaveBeenCalledWith(
        "res",
        "name",
        10
      )
    })

    it("does not fetch suggestions for empty query", async () => {
      const { componentApi } = await import("../componentApi")

      renderHook(() => useComponentSuggestions("", "name"), {
        wrapper: createWrapper(),
      })

      expect(componentApi.getSuggestions).not.toHaveBeenCalled()
    })

    it("debounces suggestion requests", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.getSuggestions).mockResolvedValue(
        mockApiSuccess([])
      )

      const { result, rerender } = renderHook(
        ({ query }) => useComponentSuggestions(query, "name"),
        {
          wrapper: createWrapper(),
          initialProps: { query: "r" },
        }
      )

      // The hook should not fetch for the first query immediately
      expect(componentApi.getSuggestions).not.toHaveBeenCalled()

      rerender({ query: "re" })
      rerender({ query: "res" })

      // Wait for debounce to complete and query to execute
      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // Should have been called once with the final query
      expect(componentApi.getSuggestions).toHaveBeenCalledWith(
        "res",
        "name",
        10
      )
    })
  })

  describe("Query Invalidation and Refetching", () => {
    it("supports manual refetch", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.list).mockResolvedValue(
        mockApiSuccess(mockComponentPaginatedResponse)
      )

      const { result } = renderHook(() => useComponents(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      expect(componentApi.list).toHaveBeenCalledTimes(1)

      // Test that refetch method exists and is a function
      expect(typeof result.current.refetch).toBe("function")

      // Call refetch and wait for it to complete
      await result.current.refetch()

      await waitFor(() => {
        expect(componentApi.list).toHaveBeenCalledTimes(2)
      })
    })

    it("handles refetch errors gracefully", async () => {
      const { componentApi } = await import("../componentApi")
      vi.mocked(componentApi.list)
        .mockResolvedValueOnce(mockApiSuccess(mockComponentPaginatedResponse))
        .mockResolvedValueOnce({
          data: undefined,
          error: {
            detail: "Refetch failed",
            error_code: "REFETCH_ERROR",
            timestamp: "2024-01-01T00:00:00Z",
          },
          status: 500,
        })

      const { result } = renderHook(() => useComponents(), {
        wrapper: createWrapper(),
      })

      await waitFor(() => {
        expect(result.current.isSuccess).toBe(true)
      })

      // The refetch should trigger error handling
      await result.current.refetch()

      await waitFor(() => {
        expect(result.current.isError).toBe(true)
      })

      expect(result.current.error?.message).toBe("Refetch failed")
    })
  })
})
