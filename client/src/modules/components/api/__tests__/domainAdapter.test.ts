/**
 * Domain Adapter Tests
 *
 * Tests for the domain/API conversion utilities
 */

import { describe, expect, it } from "vitest"
import type { ComponentRead } from "@/types/api"
import { Component, type ComponentCreateData } from "../../domain"
import {
  apiComponentToDomain,
  DomainApiError,
  domainComponentToApiCreate,
  domainUpdateToApiUpdate,
} from "../domainAdapter"

describe("Domain Adapter", () => {
  describe("apiComponentToDomain", () => {
    it("should convert API component to domain component", () => {
      const apiComponent: ComponentRead = {
        id: 1,
        name: "Test Resistor",
        manufacturer: "TestCorp",
        model_number: "R100",
        description: "A test resistor",
        component_type_id: 1,
        category_id: 1,
        specifications: {
          electrical: {
            resistance: "100Ω",
            tolerance: "5%",
          },
        },
        unit_price: 1.5,
        currency: "EUR",
        supplier: "Test Supplier",
        part_number: "TC-R100",
        weight_kg: null,
        dimensions: null,
        is_preferred: false,
        stock_status: "available",
        version: "1.0.0",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      }

      const domainComponent = apiComponentToDomain(apiComponent)

      expect(domainComponent.id).toBe(1)
      expect(domainComponent.name).toBe("Test Resistor")
      expect(domainComponent.manufacturer).toBe("TestCorp")
      expect(domainComponent.hasSpecifications()).toBe(true)
      expect(domainComponent.hasPricing()).toBe(true)
      expect(domainComponent.isAvailable()).toBe(true)
    })

    it("should handle minimal API component data", () => {
      const apiComponent: ComponentRead = {
        id: 2,
        name: "Minimal Component",
        manufacturer: "MinimalCorp",
        model_number: "MIN001",
        component_type_id: 1,
        category_id: 1,
        is_preferred: false,
        stock_status: "limited",
        version: "1.0.0",
        created_at: "2024-01-01T00:00:00Z",
        updated_at: "2024-01-01T00:00:00Z",
      }

      const domainComponent = apiComponentToDomain(apiComponent)

      expect(domainComponent.id).toBe(2)
      expect(domainComponent.name).toBe("Minimal Component")
      expect(domainComponent.hasSpecifications()).toBe(false)
      expect(domainComponent.hasPricing()).toBe(false)
    })
  })

  describe("domainComponentToApiCreate", () => {
    it("should convert domain component to API create data", () => {
      const componentData: ComponentCreateData = {
        name: "Domain Test Component",
        manufacturer: "DomainCorp",
        model_number: "DOM001",
        description: "A domain test component",
        component_type_id: 1,
        category_id: 1,
        specifications: {
          electrical: {
            voltage: "5V",
            current: "1A",
          },
        },
        unit_price: 10.0,
        currency: "EUR",
        supplier: "Domain Supplier",
        part_number: "DC-DOM001",
        is_preferred: false,
        stock_status: "available",
        version: "1.0.0",
      }

      const domainComponent = Component.create(componentData)
      const apiCreateData = domainComponentToApiCreate(domainComponent)

      expect(apiCreateData.name).toBe("Domain Test Component")
      expect(apiCreateData.manufacturer).toBe("DomainCorp")
      expect(apiCreateData.model_number).toBe("DOM001")
      expect(apiCreateData.specifications).toBeDefined()
      expect(apiCreateData.unit_price).toBe(10.0)
      expect(apiCreateData.currency).toBe("EUR")
    })

    it("should handle component without specifications and pricing", () => {
      const componentData: ComponentCreateData = {
        name: "Simple Component",
        manufacturer: "SimpleCorp",
        model_number: "SIM001",
        component_type_id: 1,
        category_id: 1,
        is_preferred: false,
        stock_status: "available",
        version: "1.0.0",
      }

      const domainComponent = Component.create(componentData)
      const apiCreateData = domainComponentToApiCreate(domainComponent)

      expect(apiCreateData.name).toBe("Simple Component")
      expect(apiCreateData.specifications).toBeUndefined()
      expect(apiCreateData.unit_price).toBeUndefined()
      expect(apiCreateData.currency).toBeUndefined()
    })
  })

  describe("domainUpdateToApiUpdate", () => {
    it("should convert domain update data to API update data", () => {
      const updateData = {
        name: "Updated Name",
        unit_price: 15.0,
        stock_status: "limited",
      }

      const apiUpdateData = domainUpdateToApiUpdate(updateData)

      expect(apiUpdateData.name).toBe("Updated Name")
      expect(apiUpdateData.unit_price).toBe(15.0)
      expect(apiUpdateData.stock_status).toBe("limited")
      expect(apiUpdateData.manufacturer).toBeUndefined()
    })

    it("should handle empty update data", () => {
      const updateData = {}
      const apiUpdateData = domainUpdateToApiUpdate(updateData)

      expect(Object.keys(apiUpdateData)).toHaveLength(0)
    })
  })

  describe("DomainApiError", () => {
    it("should create domain API error with context", () => {
      const error = new DomainApiError(
        "Test error",
        { code: "TEST_ERROR" },
        "create"
      )

      expect(error.message).toBe("Test error")
      expect(error.context).toBe("create")
      expect(error.originalError.code).toBe("TEST_ERROR")
      expect(error.name).toBe("DomainApiError")
    })

    it("should create error from API error", () => {
      const apiError = {
        message: "API Error",
        status: 400,
        details: { field: "name" },
      }

      const domainError = DomainApiError.fromApiError(apiError, "validation")

      expect(domainError.message).toBe("API Error")
      expect(domainError.context).toBe("validation")
      expect(domainError.originalError).toBe(apiError)
    })
  })
})
