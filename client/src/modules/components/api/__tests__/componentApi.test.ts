/**
 * Component API Client Tests
 * Tests the low-level API functions for component management
 */

import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"
import {
  mockApiError,
  mockApiErrorResponse,
  mockApiSuccess,
  mockComponent,
  mockComponentCreate,
  mockComponentPaginatedResponse,
  mockComponentUpdate,
  mockFetch,
  mockFetchError,
} from "@/test/utils"
import { componentApi } from "@/modules/components/api"

const API_BASE_URL =
  process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:8000"
const API_VERSION = "/api/v1"
const buildUrl = (endpoint: string): string =>
  `${API_BASE_URL}${API_VERSION}${endpoint}`

describe("componentApi", () => {
  beforeEach(() => {
    vi.spyOn(global, "fetch")
  })

  afterEach(() => {
    vi.restoreAllMocks()
  })

  describe("list", () => {
    it("fetches components with default parameters", async () => {
      global.fetch = mockFetch(mockComponentPaginatedResponse)

      const result = await componentApi.list()

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/?"),
        expect.any(Object)
      )
      expect(result).toEqual(mockApiSuccess(mockComponentPaginatedResponse))
    })

    it("fetches components with pagination parameters", async () => {
      global.fetch = mockFetch(mockComponentPaginatedResponse)

      await componentApi.list({ page: 2, size: 20 })

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/?page=2&size=20"),
        expect.any(Object)
      )
    })

    it("fetches components with filter parameters", async () => {
      global.fetch = mockFetch(mockComponentPaginatedResponse)
      const params = {
        search_term: "resistor",
        category: "RESISTOR",
        manufacturer: "Test Electronics",
        is_preferred: true,
        is_active: true,
      }
      await componentApi.list(params)

      const expectedUrl = buildUrl(
        `/components/?${new URLSearchParams(params as any).toString()}`
      )
      expect(fetch).toHaveBeenCalledWith(expectedUrl, expect.any(Object))
    })

    it("handles undefined and null parameters", async () => {
      global.fetch = mockFetch(mockComponentPaginatedResponse)
      await componentApi.list({
        page: undefined,
        search_term: undefined,
        is_preferred: false,
      })

      const calledUrl = (fetch as any).mock.calls[0][0]
      expect(calledUrl).not.toContain("page=")
      expect(calledUrl).not.toContain("search_term=")
      expect(calledUrl).toContain("is_preferred=false")
    })

    it("handles API errors", async () => {
      const error = mockApiError("Failed to fetch components")
      global.fetch = mockFetchError(error, 400)

      const result = await componentApi.list()

      expect(result).toEqual(mockApiErrorResponse("Failed to fetch components"))
    })
  })

  describe("getById", () => {
    it("fetches component by ID", async () => {
      global.fetch = mockFetch(mockComponent)

      const result = await componentApi.getById(1)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/1"),
        expect.any(Object)
      )
      expect(result).toEqual(mockApiSuccess(mockComponent))
    })

    it("handles non-existent component", async () => {
      const error = mockApiError("Component not found", "COMPONENT_NOT_FOUND")
      global.fetch = mockFetchError(error, 404)

      const result = await componentApi.getById(999)

      expect(result).toEqual({
        ...mockApiErrorResponse("Component not found", "COMPONENT_NOT_FOUND"),
        status: 404,
      })
    })
  })

  describe("create", () => {
    it("creates a new component", async () => {
      global.fetch = mockFetch(mockComponent)

      const result = await componentApi.create(mockComponentCreate)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(mockComponentCreate),
        })
      )
      expect(result).toEqual(mockApiSuccess(mockComponent))
    })

    it("handles validation errors", async () => {
      const error = mockApiError("Validation failed", "VALIDATION_ERROR")
      global.fetch = mockFetchError(error, 422)

      const result = await componentApi.create(mockComponentCreate)

      expect(result).toEqual({
        ...mockApiErrorResponse("Validation failed", "VALIDATION_ERROR"),
        status: 422,
      })
    })

    it("handles network errors", async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error("Network error"))
      const result = await componentApi.create(mockComponentCreate)
      expect(result.error?.detail).toBe("Network error")
      expect(result.status).toBe(0)
    })
  })

  describe("update", () => {
    it("updates an existing component", async () => {
      const updatedComponent = { ...mockComponent, ...mockComponentUpdate }
      global.fetch = mockFetch(updatedComponent)

      const result = await componentApi.update(1, mockComponentUpdate)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/1"),
        expect.objectContaining({
          method: "PUT",
          body: JSON.stringify(mockComponentUpdate),
        })
      )
      expect(result).toEqual(mockApiSuccess(updatedComponent))
    })

    it("handles partial updates", async () => {
      const partialUpdate = { name: "Updated Name" }
      const updatedComponent = { ...mockComponent, name: "Updated Name" }
      global.fetch = mockFetch(updatedComponent)

      const result = await componentApi.update(1, partialUpdate)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/1"),
        expect.objectContaining({
          method: "PUT",
          body: JSON.stringify(partialUpdate),
        })
      )
      expect(result).toEqual(mockApiSuccess(updatedComponent))
    })

    it("handles non-existent component", async () => {
      const error = mockApiError("Component not found", "COMPONENT_NOT_FOUND")
      global.fetch = mockFetchError(error, 404)

      const result = await componentApi.update(999, mockComponentUpdate)

      expect(result).toEqual({
        ...mockApiErrorResponse("Component not found", "COMPONENT_NOT_FOUND"),
        status: 404,
      })
    })
  })

  describe("delete", () => {
    it("deletes a component", async () => {
      global.fetch = mockFetch(null, 204)

      const result = await componentApi.delete(1)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/1"),
        expect.objectContaining({
          method: "DELETE",
        })
      )
      expect(result.status).toBe(204)
      expect(result.data).toBe(null)
    })

    it("handles non-existent component", async () => {
      const error = mockApiError("Component not found", "COMPONENT_NOT_FOUND")
      global.fetch = mockFetchError(error, 404)

      const result = await componentApi.delete(999)

      expect(result).toEqual({
        ...mockApiErrorResponse("Component not found", "COMPONENT_NOT_FOUND"),
        status: 404,
      })
    })

    it("handles deletion conflicts", async () => {
      const error = mockApiError("Component is in use", "COMPONENT_IN_USE")
      global.fetch = mockFetchError(error, 409)

      const result = await componentApi.delete(1)

      expect(result).toEqual({
        ...mockApiErrorResponse("Component is in use", "COMPONENT_IN_USE"),
        status: 409,
      })
    })
  })

  describe("search", () => {
    it("searches components with basic parameters", async () => {
      const searchParams = { search_term: "resistor" }
      global.fetch = mockFetch(mockComponentPaginatedResponse)

      const result = await componentApi.search(searchParams)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/search?"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(searchParams),
        })
      )
      expect(result).toEqual(mockApiSuccess(mockComponentPaginatedResponse))
    })

    it("searches components with pagination", async () => {
      const searchParams = { search_term: "resistor" }
      global.fetch = mockFetch(mockComponentPaginatedResponse)

      await componentApi.search(searchParams, { page: 2, size: 20 })

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/search?page=2&size=20"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(searchParams),
        })
      )
    })

    it("handles empty search results", async () => {
      const emptyResponse = {
        ...mockComponentPaginatedResponse,
        items: [],
        total: 0,
      }
      global.fetch = mockFetch(emptyResponse)

      const result = await componentApi.search({ search_term: "nonexistent" })

      expect(result).toEqual(mockApiSuccess(emptyResponse))
    })
  })

  describe("advancedSearch", () => {
    it("performs advanced search with filters", async () => {
      const advancedParams = {
        basic_filters: [
          { field: "category", operator: "eq", value: "RESISTOR" },
        ],
      }
      global.fetch = mockFetch(mockComponentPaginatedResponse)

      const result = await componentApi.advancedSearch(advancedParams)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/search/advanced?"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(advancedParams),
        })
      )
      expect(result).toEqual(mockApiSuccess(mockComponentPaginatedResponse))
    })

    it("handles complex specification filters", async () => {
      const complexParams = {
        specification_filters: [
          {
            path: "electrical.resistance",
            operator: "gte",
            value: "1000",
            data_type: "number",
          },
        ],
      }
      global.fetch = mockFetch(mockComponentPaginatedResponse)

      await componentApi.advancedSearch(complexParams)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/search/advanced?"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(complexParams),
        })
      )
    })
  })

  describe("bulkCreate", () => {
    it("creates multiple components", async () => {
      const bulkData = {
        components: [mockComponentCreate],
      }
      const bulkResponse = [
        {
          is_valid: true,
          component: mockComponent,
          errors: null,
          warnings: null,
        },
      ]
      global.fetch = mockFetch(bulkResponse)

      const result = await componentApi.bulkCreate(bulkData)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/bulk/create"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(bulkData),
        })
      )
      expect(result).toEqual(mockApiSuccess(bulkResponse))
    })

    it("validates components without creating", async () => {
      const bulkData = {
        components: [mockComponentCreate],
        validate_only: true,
      }
      const validationResponse = [
        {
          is_valid: true,
          component: mockComponent,
          errors: null,
          warnings: null,
        },
      ]
      global.fetch = mockFetch(validationResponse)

      const result = await componentApi.bulkCreate(bulkData)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/bulk/create"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(bulkData),
        })
      )
      expect(result).toEqual(mockApiSuccess(validationResponse))
    })
  })

  describe("bulkUpdate", () => {
    it("updates multiple components", async () => {
      const bulkData = {
        component_ids: [1, 2],
        update_data: { name: "Updated Components" },
      }
      const bulkResponse = [
        {
          is_valid: true,
          component: mockComponent,
          errors: null,
          warnings: null,
        },
      ]
      global.fetch = mockFetch(bulkResponse)

      const result = await componentApi.bulkUpdate(bulkData)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/bulk/update"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(bulkData),
        })
      )
      expect(result).toEqual(mockApiSuccess(bulkResponse))
    })
  })

  describe("getStats", () => {
    it("fetches component statistics", async () => {
      const stats = { total_components: 150 }
      global.fetch = mockFetch(stats)

      const result = await componentApi.getStats()

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/stats"),
        expect.any(Object)
      )
      expect(result).toEqual(mockApiSuccess(stats))
    })
  })

  describe("Error Handling", () => {
    it("handles network timeouts", async () => {
      global.fetch = vi.fn().mockRejectedValue(new Error("Request timeout"))
      const result = await componentApi.list()
      expect(result.error?.detail).toBe("Request timeout")
      expect(result.status).toBe(0)
    })

    it("handles malformed responses", async () => {
      // This mock simulates a non-JSON response
      global.fetch = vi.fn().mockResolvedValue({
        ok: true,
        status: 200,
        json: () => Promise.reject(new Error("invalid json")),
      })
      const result = await componentApi.list()
      expect(result.error?.detail).toBe("Failed to parse JSON response")
    })

    it("handles server errors", async () => {
      const serverError = mockApiError(
        "Internal server error",
        "INTERNAL_ERROR"
      )
      global.fetch = mockFetchError(serverError, 500)

      const result = await componentApi.list()

      expect(result).toEqual({
        ...mockApiErrorResponse("Internal server error", "INTERNAL_ERROR"),
        status: 500,
      })
    })
  })

  describe("Request Options", () => {
    it("passes through request options", async () => {
      global.fetch = mockFetch(mockComponent)
      const options = {
        timeout: 10000,
        headers: { "Custom-Header": "value" },
      }
      await componentApi.create(mockComponentCreate, options)

      expect(fetch).toHaveBeenCalledWith(
        buildUrl("/components/"),
        expect.objectContaining({
          method: "POST",
          body: JSON.stringify(mockComponentCreate),
          headers: expect.objectContaining({ "Custom-Header": "value" }),
        })
      )
    })
  })
})
