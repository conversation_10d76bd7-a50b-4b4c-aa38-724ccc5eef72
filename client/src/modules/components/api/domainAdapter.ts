/**
 * Domain Adapter for Component API
 *
 * This adapter bridges the gap between the API layer and the domain layer,
 * converting API responses to domain objects and vice versa.
 */

import type {
  ComponentAdvancedSearchResponse,
  ComponentCreate,
  ComponentPaginatedResponse,
  ComponentRead,
  ComponentUpdate,
} from "@/modules/components/api"
import type {
  ComponentCreateData,
  ComponentUpdateData,
  ValidationResult,
} from "@/modules/components/domain"

import {
  Component,
  ComponentCatalog,
  ComponentValidationService,
} from "@/modules/components/domain"

/**
 * Converts API ComponentRead to domain Component entity
 */
export function apiComponentToDomain(apiComponent: ComponentRead): Component {
  const componentData: ComponentCreateData = {
    name: apiComponent.name,
    manufacturer: apiComponent.manufacturer,
    model_number: apiComponent.model_number,
    description: apiComponent.description || undefined,
    component_type_id: apiComponent.component_type_id,
    category_id: apiComponent.category_id,
    specifications: apiComponent.specifications || {},
    unit_price: apiComponent.unit_price || undefined,
    currency: apiComponent.currency || "EUR",
    supplier: apiComponent.supplier || undefined,
    part_number: apiComponent.part_number || undefined,
    weight_kg: apiComponent.weight_kg || undefined,
    dimensions: apiComponent.dimensions || undefined,
    is_preferred: apiComponent.is_preferred || false,
    stock_status: apiComponent.stock_status || "available",
    version: apiComponent.version || "1.0.0",
  }

  // Create component with proper ID
  const component = Component.create(componentData)

  // Set the ID from API (this would normally be done by the repository)
  // Note: This is a workaround for the fact that our domain entity sets ID to 0
  Object.defineProperty(component, "_id", {
    value: apiComponent.id,
    writable: false,
    configurable: true,
  })

  return component
}

/**
 * Converts domain Component entity to API ComponentCreate
 */
export function domainComponentToApiCreate(
  component: Component
): ComponentCreate {
  return {
    name: component.name,
    manufacturer: component.manufacturer,
    model_number: component.model_number,
    description: component.description || undefined,
    component_type_id: component.component_type_id,
    category_id: component.category_id,
    specifications: component.hasSpecifications()
      ? component.specifications
      : undefined,
    unit_price: component.hasPricing() && component.unit_price !== null ? component.unit_price : undefined,
    currency: component.hasPricing() && component.currency ? component.currency : undefined,
    supplier: component.supplier || undefined,
    part_number: component.part_number || undefined,
    weight_kg: component.weight_kg || undefined,
    dimensions: component.dimensions || undefined,
    is_active: component.is_active,
    is_preferred: component.is_preferred,
    stock_status: component.stock_status as 'available' | 'limited' | 'out_of_stock' | 'discontinued' | 'obsolete' | 'special_order' | 'pre_order',
    version: component.version,
  }
}

/**
 * Converts domain ComponentUpdateData to API ComponentUpdate
 */
export function domainUpdateToApiUpdate(
  updateData: ComponentUpdateData
): ComponentUpdate {
  const apiUpdate: ComponentUpdate = {}

  if (updateData.name !== undefined) apiUpdate.name = updateData.name
  if (updateData.manufacturer !== undefined)
    apiUpdate.manufacturer = updateData.manufacturer
  if (updateData.model_number !== undefined)
    apiUpdate.model_number = updateData.model_number
  if (updateData.description !== undefined)
    apiUpdate.description = updateData.description
  if (updateData.component_type_id !== undefined)
    apiUpdate.component_type_id = updateData.component_type_id
  if (updateData.category_id !== undefined)
    apiUpdate.category_id = updateData.category_id
  if (updateData.specifications !== undefined)
    apiUpdate.specifications = updateData.specifications
  if (updateData.unit_price !== undefined)
    apiUpdate.unit_price = updateData.unit_price
  if (updateData.currency !== undefined)
    apiUpdate.currency = updateData.currency
  if (updateData.supplier !== undefined)
    apiUpdate.supplier = updateData.supplier
  if (updateData.part_number !== undefined)
    apiUpdate.part_number = updateData.part_number
  if (updateData.weight_kg !== undefined)
    apiUpdate.weight_kg = updateData.weight_kg
  if (updateData.dimensions !== undefined)
    apiUpdate.dimensions = updateData.dimensions
  if (updateData.is_preferred !== undefined)
    apiUpdate.is_preferred = updateData.is_preferred
  if (updateData.stock_status !== undefined)
    apiUpdate.stock_status = updateData.stock_status as 'available' | 'limited' | 'out_of_stock' | 'discontinued' | 'obsolete' | 'special_order' | 'pre_order'
  if (updateData.version !== undefined) apiUpdate.version = updateData.version

  return apiUpdate
}

/**
 * Converts API paginated response to domain components array with metadata
 */
export function apiPaginatedResponseToDomain(
  apiResponse: ComponentPaginatedResponse
): {
  components: Component[]
  pagination: {
    totalCount: number
    page: number
    size: number
    hasMore: boolean
  }
} {
  const components = apiResponse.items.map(apiComponentToDomain)

  return {
    components,
    pagination: {
      totalCount: apiResponse.pagination.total || 0,
      page: apiResponse.pagination.page,
      size: apiResponse.pagination.size,
      hasMore: (apiResponse.pagination.page * apiResponse.pagination.size) < (apiResponse.pagination.total || 0),
    },
  }
}

/**
 * Converts API advanced search response to domain ComponentCatalog
 */
export function apiAdvancedSearchToDomain(
  apiResponse: ComponentAdvancedSearchResponse
): {
  catalog: ComponentCatalog
  metadata: {
    totalCount: number
    searchScore: number
    suggestions: string[]
  }
} {
  const components = apiResponse.results.map(apiComponentToDomain)
  const catalog = ComponentCatalog.fromComponents(components)

  return {
    catalog,
    metadata: {
      totalCount: apiResponse.total_count,
      searchScore: apiResponse.search_metadata?.relevance_score || 0,
      suggestions: apiResponse.search_metadata?.suggestions || [],
    },
  }
}

/**
 * Domain-aware validation using ComponentValidationService
 */
function validateComponentData(
  componentData: ComponentCreateData,
  validationService?: ComponentValidationService
): ValidationResult {
  const service = validationService || new ComponentValidationService()
  return service.validateComponentCreation(componentData)
}

// Export with alias to avoid naming conflicts
export { validateComponentData as validateComponentDataAdapter }

/**
 * Converts domain validation result to API-compatible format
 */
export function domainValidationToApi(validationResult: ValidationResult): {
  is_valid: boolean
  errors: string[]
  warnings: string[]
  score: number
} {
  return {
    is_valid: validationResult.isValid,
    errors: validationResult.errors.map(error => typeof error === 'string' ? error : String(error)),
    warnings: validationResult.warnings.map(warning => typeof warning === 'string' ? warning : String(warning)),
    score: validationResult.score,
  }
}

/**
 * Search criteria adapter - converts domain search criteria to API parameters
 */
export function domainSearchToApiParams(criteria: {
  namePattern?: string
  manufacturer?: string
  componentTypeId?: number
  categoryId?: number
  isPreferred?: boolean
  stockStatus?: string
  isActive?: boolean
  priceRange?: {
    min?: number
    max?: number
    currency?: string
  }
}): {
  search_term?: string
  manufacturer?: string
  component_type?: string
  category?: string
  is_preferred?: boolean
  is_active?: boolean
  min_price?: number
  max_price?: number
  currency?: string
} {
  const apiParams: any = {}

  if (criteria.namePattern) apiParams.search_term = criteria.namePattern
  if (criteria.manufacturer) apiParams.manufacturer = criteria.manufacturer
  if (criteria.componentTypeId)
    apiParams.component_type = criteria.componentTypeId.toString()
  if (criteria.categoryId) apiParams.category = criteria.categoryId.toString()
  if (criteria.isPreferred !== undefined)
    apiParams.is_preferred = criteria.isPreferred
  if (criteria.isActive !== undefined) apiParams.is_active = criteria.isActive
  if (criteria.stockStatus) apiParams.stock_status = criteria.stockStatus

  if (criteria.priceRange) {
    if (criteria.priceRange.min !== undefined)
      apiParams.min_price = criteria.priceRange.min
    if (criteria.priceRange.max !== undefined)
      apiParams.max_price = criteria.priceRange.max
    if (criteria.priceRange.currency)
      apiParams.currency = criteria.priceRange.currency
  }

  return apiParams
}

/**
 * Bulk operation adapter - converts domain components to API format
 */
export function domainComponentsToApiBulk(
  components: Component[]
): ComponentCreate[] {
  return components.map(domainComponentToApiCreate)
}

/**
 * Error adapter - converts API errors to domain-friendly format
 */
export class DomainApiError extends Error {
  constructor(
    message: string,
    public originalError: any,
    public context: "create" | "update" | "delete" | "search" | "validation"
  ) {
    super(message)
    this.name = "DomainApiError"
  }

  static fromApiError(
    apiError: any,
    context: DomainApiError["context"]
  ): DomainApiError {
    return new DomainApiError(
      apiError.message || "Unknown API error",
      apiError,
      context
    )
  }
}

/**
 * Utility function to handle API responses with domain conversion
 */
export async function handleApiResponseWithDomain<T, D>(
  apiPromise: Promise<{ data?: T; error?: any; status: number }>,
  converter: (data: T) => D,
  context: DomainApiError["context"]
): Promise<{ data?: D; error?: DomainApiError }> {
  try {
    const response = await apiPromise

    if (response.error) {
      return {
        error: DomainApiError.fromApiError(response.error, context),
      }
    }

    if (response.data) {
      return {
        data: converter(response.data),
      }
    }

    return {
      error: new DomainApiError("No data received from API", null, context),
    }
  } catch (error) {
    return {
      error: DomainApiError.fromApiError(error, context),
    }
  }
}
