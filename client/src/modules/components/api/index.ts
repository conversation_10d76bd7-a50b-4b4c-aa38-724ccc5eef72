/**
 * Re-exports API wrappers and types specific to the 'components' domain.
 */

// Original API layer
export * from "./componentApi" // Component API client
export * from "./componentMutations" // React Query mutations
export * from "./componentQueries" // React Query hooks

// Domain-aware API layer
export * from "./domainAdapter" // Domain/API conversion utilities
export * from "./domainComponentApi" // Domain-aware component API service
export { domainComponentApi } from "./domainComponentApi" // Default instance

// Schema types
export * from "@/types/componentCategory"
export * from "@/types/component"

// Re-export domain types for convenience
export type {
  Component,
  ComponentCatalog,
  ComponentCreateData,
  ComponentUpdateData,
  ComponentValidationService,
  ValidationResult,
} from "../domain"
