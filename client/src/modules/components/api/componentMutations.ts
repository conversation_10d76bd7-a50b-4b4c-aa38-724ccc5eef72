/**
 * React Query mutations for component management
 */

import type {
  ComponentBulkCreate,
  ComponentBulk<PERSON>pdate,
  ComponentCreate,
  ComponentRead,
  ComponentUpdate,
  ComponentValidationResult,
} from "@/modules/components/api"

import { MutationKeys, QueryKeys } from "@/types/api"
import {
  useMutation,
  UseMutationOptions,
  useQueryClient,
} from "@tanstack/react-query"

import { componentApi } from "@/modules/components/api"

// Create component mutation
export function useCreateComponent(
  options?: UseMutationOptions<ComponentRead, Error, ComponentCreate>
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.components.createComponent,
    mutationFn: async (component: ComponentCreate) => {
      const response = await componentApi.create(component)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    ...options,
    onSuccess: (data, variables, context) => {
      // Invalidate and refetch component lists
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })

      // Add the new component to the cache
      queryClient.setQueryData(QueryKeys.components.detail(data.id), data)
      options?.onSuccess?.(data, variables, context)
    },
  })
}

// Update component mutation
export function useUpdateComponent(
  options?: UseMutationOptions<
    ComponentRead,
    Error,
    { id: number; component: ComponentUpdate }
  >
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.components.updateComponent,
    mutationFn: async ({
      id,
      component,
    }: {
      id: number
      component: ComponentUpdate
    }) => {
      const response = await componentApi.update(id, component)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    ...options,
    onSuccess: (data, variables, context) => {
      // Update the component in the cache
      queryClient.setQueryData(QueryKeys.components.detail(variables.id), data)

      // Invalidate component lists to reflect changes
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })
      options?.onSuccess?.(data, variables, context)
    },
  })
}

// Delete component mutation
export function useDeleteComponent(
  options?: UseMutationOptions<void, Error, number>
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.components.deleteComponent,
    mutationFn: async (id: number) => {
      const response = await componentApi.delete(id)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    ...options,
    onSuccess: (data, variables, context) => {
      // Remove the component from the cache
      queryClient.removeQueries({ queryKey: QueryKeys.components.detail(variables) })

      // Invalidate component lists
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })
      options?.onSuccess?.(data, variables, context)
    },
  })
}

// Bulk create components mutation
export function useBulkCreateComponents(
  options?: UseMutationOptions<
    ComponentValidationResult[],
    Error,
    ComponentBulkCreate
  >
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.components.bulkCreateComponents,
    mutationFn: async (data: ComponentBulkCreate) => {
      const response = await componentApi.bulkCreate(data)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    ...options,
    onSuccess: (data, variables, context) => {
      // Invalidate all component-related queries
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })
      options?.onSuccess?.(data, variables, context)
    },
  })
}

// Bulk update components mutation
export function useBulkUpdateComponents(
  options?: UseMutationOptions<
    ComponentValidationResult[],
    Error,
    ComponentBulkUpdate
  >
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.components.bulkUpdateComponents,
    mutationFn: async (data: ComponentBulkUpdate) => {
      const response = await componentApi.bulkUpdate(data)
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    ...options,
    onSuccess: (data, variables, context) => {
      // Invalidate specific components and lists
      variables.components.forEach((comp: any) => {
        queryClient.invalidateQueries({ queryKey: QueryKeys.components.detail(comp.id) })
      })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })
      options?.onSuccess?.(data, variables, context)
    },
  })
}

// Bulk delete components mutation
export function useBulkDeleteComponents(
  options?: UseMutationOptions<
    { deleted_count: number; errors: any[] },
    Error,
    { componentIds: number[]; soft_delete?: boolean }
  >
) {
  const queryClient = useQueryClient()

  return useMutation({
    mutationKey: MutationKeys.components.bulkDeleteComponents,
    mutationFn: async ({
      componentIds,
      soft_delete,
    }: {
      componentIds: number[]
      soft_delete?: boolean
    }) => {
      const response = await componentApi.bulkDelete(componentIds, {
        soft_delete,
      })
      if (response.error) {
        throw new Error(response.error.detail)
      }
      return response.data!
    },
    ...options,
    onSuccess: (data, variables, context) => {
      // Remove components from cache
      variables.componentIds.forEach((id) => {
        queryClient.removeQueries({ queryKey: QueryKeys.components.detail(id) })
      })

      // Invalidate component lists
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.all })
      queryClient.invalidateQueries({ queryKey: QueryKeys.components.stats })
      options?.onSuccess?.(data, variables, context)
    },
  })
}

// Optimistic update helper for component updates
export function useOptimisticComponentUpdate() {
  const queryClient = useQueryClient()

  return {
    updateComponent: (id: number, updates: Partial<ComponentRead>) => {
      queryClient.setQueryData(
        QueryKeys.components.detail(id),
        (old: ComponentRead | undefined) => {
          if (!old) return old
          return { ...old, ...updates }
        }
      )
    },

    revertComponent: (id: number, previousData: ComponentRead) => {
      queryClient.setQueryData(QueryKeys.components.detail(id), previousData)
    },
  }
}

// Prefetch helper for component data
export function usePrefetchComponent() {
  const queryClient = useQueryClient()

  return {
    prefetchComponent: (id: number) => {
      queryClient.prefetchQuery({
        queryKey: QueryKeys.components.detail(id),
        queryFn: async () => {
          const response = await componentApi.getById(id)
          if (response.error) {
            throw new Error(response.error.detail)
          }
          return response.data!
        },
        staleTime: 10 * 60 * 1000, // 10 minutes
      })
    },

    prefetchComponents: (params: any = {}) => {
      queryClient.prefetchQuery({
        queryKey: QueryKeys.components.list(params),
        queryFn: async () => {
          const response = await componentApi.list(params)
          if (response.error) {
            throw new Error(response.error.detail)
          }
          return response.data!
        },
        staleTime: 5 * 60 * 1000, // 5 minutes
      })
    },
  }
}
