/**
 * Component Management API Client
 * Enhanced API client with comprehensive error handling, validation, and type safety
 */

import type {
  ApiResponse,
  ComponentAdvancedSearch,
  ComponentAdvancedSearchResponse,
  ComponentBulkCreate,
  ComponentBulkUpdate,
  ComponentCreate,
  ComponentPaginatedResponse,
  ComponentRead,
  ComponentSearch,
  ComponentStats,
  ComponentSummary,
  ComponentUpdate,
  ComponentValidationResult,
  RequestOptions,
} from "@/types/api"

// Enhanced error handling
export class ComponentApiError extends Error {
  constructor(
    message: string,
    public status: number,
    public code?: string,
    public details?: any
  ) {
    super(message)
    this.name = "ComponentApiError"
  }
}


// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000"
const API_VERSION = "/api/v1"

// Helper function to build API URLs
const buildUrl = (endpoint: string): string =>
  `${API_BASE_URL}${API_VERSION}${endpoint}`

// Helper function to get auth headers
const getAuthHeaders = (): Record<string, string> => {
  const token = localStorage.getItem("access_token")
  return token ? { Authorization: `Bearer ${token}` } : {}
}

// Generic API request function
async function apiRequest<T>(
  url: string,
  options: RequestInit & RequestOptions = {}
): Promise<ApiResponse<T>> {
  const { headers = {}, timeout = 30000, signal, ...restOptions } = options

  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), timeout)

  try {
    const response = await fetch(url, {
      ...restOptions,
      headers: {
        "Content-Type": "application/json",
        ...getAuthHeaders(),
        ...headers,
      },
      signal: signal || controller.signal,
    })

    clearTimeout(timeoutId)

    let data = undefined
    let error = undefined

    if (response.ok) {
      try {
        data = await response.json()
      } catch (e) {
        error = { detail: "Failed to parse JSON response" }
      }
    } else {
      try {
        error = await response.json()
      } catch (e) {
        error = { detail: "Unknown error" }
      }
    }

    return {
      data: data,
      error: error,
      status: response.status,
    }
  } catch (err) {
    clearTimeout(timeoutId)
    return {
      data: undefined,
      error: { 
        code: "NETWORK_ERROR",
        detail: err instanceof Error ? err.message : "Network error",
        category: "ClientError",
        status_code: 0
      },
      status: 0,
    }
  }
}

// Component API functions
export const componentApi = {
  // List components with pagination and filtering
  async list(
    params: {
      page?: number
      size?: number
      search_term?: string
      category?: string
      component_type?: string
      manufacturer?: string
      is_preferred?: boolean
      is_active?: boolean
    } = {},
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentPaginatedResponse>> {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value))
      }
    })

    const url = buildUrl(`/components/?${searchParams.toString()}`)
    return apiRequest<ComponentPaginatedResponse>(url, options)
  },

  // Get component by ID
  async getById(
    id: number,
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentRead>> {
    const url = buildUrl(`/components/${id}`)
    return apiRequest<ComponentRead>(url, options)
  },

  // Create new component
  async create(
    component: ComponentCreate,
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentRead>> {
    const url = buildUrl("/components/")
    return apiRequest<ComponentRead>(url, {
      method: "POST",
      body: JSON.stringify(component),
      ...options,
    })
  },

  // Update component
  async update(
    id: number,
    component: ComponentUpdate,
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentRead>> {
    const url = buildUrl(`/components/${id}`)
    return apiRequest<ComponentRead>(url, {
      method: "PUT",
      body: JSON.stringify(component),
      ...options,
    })
  },

  // Delete component
  async delete(
    id: number,
    options?: RequestOptions
  ): Promise<ApiResponse<void>> {
    const url = buildUrl(`/components/${id}`)
    return apiRequest<void>(url, {
      method: "DELETE",
      ...options,
    })
  },

  // Search components
  async search(
    searchParams: ComponentSearch,
    pagination: { page?: number; size?: number } = {},
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentPaginatedResponse>> {
    const queryParams = new URLSearchParams()
    if (pagination.page) queryParams.append("page", String(pagination.page))
    if (pagination.size) queryParams.append("size", String(pagination.size))

    const url = buildUrl(`/components/search?${queryParams.toString()}`)
    return apiRequest<ComponentPaginatedResponse>(url, {
      method: "POST",
      body: JSON.stringify(searchParams),
      ...options,
    })
  },

  // Advanced search
  async advancedSearch(
    searchParams: ComponentAdvancedSearch,
    pagination: { page?: number; size?: number } = {},
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentAdvancedSearchResponse>> {
    const queryParams = new URLSearchParams()
    if (pagination.page) queryParams.append("page", String(pagination.page))
    if (pagination.size) queryParams.append("size", String(pagination.size))

    const url = buildUrl(
      `/components/search/advanced?${queryParams.toString()}`
    )
    return apiRequest<ComponentAdvancedSearchResponse>(url, {
      method: "POST",
      body: JSON.stringify(searchParams),
      ...options,
    })
  },

  // Search by specifications
  async searchBySpecifications(
    specifications: Record<string, any>,
    pagination: { page?: number; size?: number } = {},
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentPaginatedResponse>> {
    const queryParams = new URLSearchParams()
    if (pagination.page) queryParams.append("page", String(pagination.page))
    if (pagination.size) queryParams.append("size", String(pagination.size))

    const url = buildUrl(
      `/components/search/specifications?${queryParams.toString()}`
    )
    return apiRequest<ComponentPaginatedResponse>(url, {
      method: "POST",
      body: JSON.stringify(specifications),
      ...options,
    })
  },

  // Get search suggestions
  async getSuggestions(
    query: string,
    field: string = "name",
    limit: number = 10,
    options?: RequestOptions
  ): Promise<ApiResponse<string[]>> {
    const searchParams = new URLSearchParams({
      query,
      field,
      limit: String(limit),
    })

    const url = buildUrl(
      `/components/search/suggestions?${searchParams.toString()}`
    )
    return apiRequest<string[]>(url, options)
  },

  // Get preferred components
  async getPreferred(
    params: { skip?: number; limit?: number } = {},
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentSummary[]>> {
    const searchParams = new URLSearchParams()
    if (params.skip !== undefined)
      searchParams.append("skip", String(params.skip))
    if (params.limit !== undefined)
      searchParams.append("limit", String(params.limit))

    const url = buildUrl(`/components/preferred?${searchParams.toString()}`)
    return apiRequest<ComponentSummary[]>(url, options)
  },

  // Get component statistics
  async getStats(
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentStats>> {
    const url = buildUrl("/components/stats")
    return apiRequest<ComponentStats>(url, options)
  },

  // Get component categories
  async getCategories(
    options?: RequestOptions
  ): Promise<ApiResponse<Array<{ name: string; value: string }>>> {
    const url = buildUrl("/components/categories")
    return apiRequest<Array<{ name: string; value: string }>>(url, options)
  },

  // Get component types
  async getTypes(
    categoryId?: number,
    options?: RequestOptions
  ): Promise<ApiResponse<Array<{ name: string; value: string }>>> {
    const searchParams = new URLSearchParams()
    if (categoryId) searchParams.append("category_id", categoryId.toString())

    const url = buildUrl(`/components/types?${searchParams.toString()}`)
    return apiRequest<Array<{ name: string; value: string }>>(url, options)
  },

  // Bulk operations
  async bulkCreate(
    data: ComponentBulkCreate,
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentValidationResult[]>> {
    const url = buildUrl("/components/bulk/create")
    return apiRequest<ComponentValidationResult[]>(url, {
      method: "POST",
      body: JSON.stringify(data),
      ...options,
    })
  },

  async bulkUpdate(
    data: ComponentBulkUpdate,
    options?: RequestOptions
  ): Promise<ApiResponse<ComponentValidationResult[]>> {
    const url = buildUrl("/components/bulk/update")
    return apiRequest<ComponentValidationResult[]>(url, {
      method: "POST",
      body: JSON.stringify(data),
      ...options,
    })
  },

  async bulkDelete(
    componentIds: number[],
    options: { soft_delete?: boolean } & RequestOptions = {}
  ): Promise<ApiResponse<{ deleted_count: number; errors: any[] }>> {
    const { soft_delete, ...requestOptions } = options // Destructure soft_delete from options
    const searchParams = new URLSearchParams()
    if (soft_delete !== undefined) {
      searchParams.append("soft_delete", String(soft_delete))
    }

    const url = buildUrl(`/components/bulk/delete?${searchParams.toString()}`)
    return apiRequest<{ deleted_count: number; errors: any[] }>(url, {
      method: "DELETE",
      body: JSON.stringify({ component_ids: componentIds }),
      ...requestOptions, // Pass remaining requestOptions
    })
  },
}
