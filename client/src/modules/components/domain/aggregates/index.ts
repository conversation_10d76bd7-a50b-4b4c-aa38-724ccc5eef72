/**
 * Domain Aggregates - Component Catalog Context
 *
 * Aggregates define consistency boundaries and encapsulate
 * complex business operations across multiple entities.
 */

// Aggregate exports
export { ComponentCatalog } from "./ComponentCatalog"
export type {
  CatalogSearchCriteria,
  CatalogSortOptions,
  CatalogStatistics,
  BulkOperationResult,
  CatalogValidationResult,
} from "./ComponentCatalog"
