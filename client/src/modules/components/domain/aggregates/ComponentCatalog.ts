import type {
  ComponentCreateData,
  ComponentUpdateData,
} from "../entities/Component"

import { Component } from "../entities/Component"
import { ComponentDomainError } from "../errors/ComponentDomainError"
import { ComponentStatus } from "../value-objects/ComponentStatus"
import { PriceValue } from "../value-objects/PriceValue"
import { Specifications } from "../value-objects/Specifications"

/**
 * ComponentCatalog Aggregate
 *
 * Manages a collection of electrical components with business rules
 * for catalog operations, validation, and consistency enforcement.
 *
 * This aggregate ensures that:
 * - Components maintain catalog integrity
 * - Business rules are enforced across the collection
 * - Component relationships are consistent
 * - Catalog operations are atomic
 */

/**
 * Search criteria for catalog operations
 */
export interface CatalogSearchCriteria {
  namePattern?: string
  manufacturer?: string
  categoryIds?: number[]
  typeIds?: number[]
  statuses?: ComponentStatus[]
  priceRange?: {
    min: PriceValue
    max: PriceValue
  }
  hasSpecifications?: boolean
  isPreferred?: boolean
  isActive?: boolean
}

/**
 * Sorting options for catalog operations
 */
export interface CatalogSortOptions {
  field: "name" | "manufacturer" | "price" | "created_at" | "updated_at"
  direction: "asc" | "desc"
}

/**
 * Catalog statistics
 */
export interface CatalogStatistics {
  totalComponents: number
  activeComponents: number
  inactiveComponents: number
  discontinuedComponents: number
  preferredComponents: number
  componentsWithSpecifications: number
  componentsWithPricing: number
  averagePrice?: PriceValue
  priceRange?: {
    min: PriceValue
    max: PriceValue
  }
  manufacturerCount: number
  categoryDistribution: Map<number, number>
  typeDistribution: Map<number, number>
}

/**
 * Bulk operation result
 */
export interface BulkOperationResult {
  successful: Component[]
  failed: Array<{
    data: ComponentCreateData | ComponentUpdateData
    error: ComponentDomainError
  }>
  statistics: {
    attempted: number
    successful: number
    failed: number
  }
}

/**
 * Validation result for catalog operations
 */
export interface CatalogValidationResult {
  isValid: boolean
  errors: ComponentDomainError[]
  warnings: string[]
  duplicates: Component[]
  conflicts: Array<{
    existing: Component
    conflictType: "name" | "model_number" | "part_number"
    conflictValue: string
  }>
}

/**
 * ComponentCatalog Aggregate Root
 *
 * Manages the entire component catalog with business rules,
 * validation, and consistency enforcement.
 */
export class ComponentCatalog {
  private readonly _components: Map<number, Component>
  private readonly _nameIndex: Map<string, number>
  private readonly _manufacturerIndex: Map<string, Set<number>>
  private readonly _categoryIndex: Map<number, Set<number>>
  private readonly _typeIndex: Map<number, Set<number>>
  private readonly _partNumberIndex: Map<string, number>
  private _version: number

  constructor(components: Component[] = []) {
    this._components = new Map()
    this._nameIndex = new Map()
    this._manufacturerIndex = new Map()
    this._categoryIndex = new Map()
    this._typeIndex = new Map()
    this._partNumberIndex = new Map()
    this._version = 1

    // Initialize with provided components
    components.forEach((component) => {
      this.addComponentInternal(component, false)
    })
  }

  /**
   * Create empty catalog
   */
  static empty(): ComponentCatalog {
    return new ComponentCatalog()
  }

  /**
   * Create catalog from component array
   */
  static fromComponents(components: Component[]): ComponentCatalog {
    return new ComponentCatalog(components)
  }

  // Core Catalog Operations

  /**
   * Add component to catalog
   */
  addComponent(componentData: ComponentCreateData): Component {
    // Validate before adding
    this.validateComponentForAddition(componentData)

    // Create component entity
    const component = Component.create(componentData)

    // Add to catalog
    this.addComponentInternal(component, true)

    return component
  }

  /**
   * Add existing component to catalog
   */
  addExistingComponent(component: Component): void {
    // Validate before adding
    this.validateExistingComponentForAddition(component)

    // Add to catalog
    this.addComponentInternal(component, true)
  }

  /**
   * Update component in catalog
   */
  updateComponent(
    componentId: number,
    updateData: ComponentUpdateData
  ): Component {
    const existingComponent = this.getComponent(componentId)
    if (!existingComponent) {
      throw ComponentDomainError.businessRuleViolation(
        `Component with ID ${componentId} not found in catalog`
      )
    }

    // Validate update
    this.validateComponentForUpdate(existingComponent, updateData)

    // Update component
    existingComponent.update(updateData)

    // Update indexes if necessary
    this.updateIndexesForComponent(existingComponent)
    this.incrementVersion()

    return existingComponent
  }

  /**
   * Remove component from catalog
   */
  removeComponent(componentId: number): boolean {
    const component = this._components.get(componentId)
    if (!component) {
      return false
    }

    // Remove from indexes
    this.removeFromIndexes(component)

    // Remove from main collection
    this._components.delete(componentId)
    this.incrementVersion()

    return true
  }

  /**
   * Get component by ID
   */
  getComponent(componentId: number): Component | null {
    return this._components.get(componentId) || null
  }

  /**
   * Check if component exists
   */
  hasComponent(componentId: number): boolean {
    return this._components.has(componentId)
  }

  /**
   * Get all components
   */
  getAllComponents(): Component[] {
    return Array.from(this._components.values())
  }

  /**
   * Get component count
   */
  getComponentCount(): number {
    return this._components.size
  }

  // Search and Query Operations

  /**
   * Search components by criteria
   */
  searchComponents(
    criteria: CatalogSearchCriteria,
    sort?: CatalogSortOptions
  ): Component[] {
    let results = Array.from(this._components.values())

    // Apply filters
    if (criteria.namePattern) {
      const pattern = criteria.namePattern.toLowerCase()
      results = results.filter(
        (component) =>
          component.name.toLowerCase().includes(pattern) ||
          component.getDisplayName().toLowerCase().includes(pattern)
      )
    }

    if (criteria.manufacturer) {
      const manufacturer = criteria.manufacturer.toLowerCase()
      results = results.filter((component) =>
        component.manufacturer.toLowerCase().includes(manufacturer)
      )
    }

    if (criteria.categoryIds && criteria.categoryIds.length > 0) {
      results = results.filter((component) =>
        criteria.categoryIds!.includes(component.category_id)
      )
    }

    if (criteria.typeIds && criteria.typeIds.length > 0) {
      results = results.filter((component) =>
        criteria.typeIds!.includes(component.component_type_id)
      )
    }

    if (criteria.statuses && criteria.statuses.length > 0) {
      const statusValues = criteria.statuses.map((s) => s.toString())
      results = results.filter((component) =>
        statusValues.includes(component.stock_status)
      )
    }

    if (criteria.priceRange) {
      results = results.filter((component) => {
        if (!component.unit_price) return false
        const price = PriceValue.from(component.unit_price, component.currency)
        return price.isInRange(
          criteria.priceRange!.min,
          criteria.priceRange!.max
        )
      })
    }

    if (criteria.hasSpecifications !== undefined) {
      results = results.filter(
        (component) =>
          component.hasSpecifications() === criteria.hasSpecifications
      )
    }

    if (criteria.isPreferred !== undefined) {
      results = results.filter(
        (component) => component.isPreferred() === criteria.isPreferred
      )
    }

    if (criteria.isActive !== undefined) {
      results = results.filter(
        (component) => component.isActive() === criteria.isActive
      )
    }

    // Apply sorting
    if (sort) {
      results.sort((a, b) => {
        let comparison = 0

        switch (sort.field) {
          case "name":
            comparison = a.name.localeCompare(b.name)
            break
          case "manufacturer":
            comparison = a.manufacturer.localeCompare(b.manufacturer)
            break
          case "price": {
            const priceA = a.unit_price || 0
            const priceB = b.unit_price || 0
            comparison = priceA - priceB
            break
          }
          case "created_at":
            comparison = a.created_at.getTime() - b.created_at.getTime()
            break
          case "updated_at":
            comparison = a.updated_at.getTime() - b.updated_at.getTime()
            break
        }

        return sort.direction === "desc" ? -comparison : comparison
      })
    }

    return results
  }

  /**
   * Find components by manufacturer
   */
  findByManufacturer(manufacturer: string): Component[] {
    const componentIds = this._manufacturerIndex.get(manufacturer.toLowerCase())
    if (!componentIds) return []

    return Array.from(componentIds)
      .map((id) => this._components.get(id))
      .filter((component): component is Component => component !== undefined)
  }

  /**
   * Find components by category
   */
  findByCategory(categoryId: number): Component[] {
    const componentIds = this._categoryIndex.get(categoryId)
    if (!componentIds) return []

    return Array.from(componentIds)
      .map((id) => this._components.get(id))
      .filter((component): component is Component => component !== undefined)
  }

  /**
   * Find components by type
   */
  findByType(typeId: number): Component[] {
    const componentIds = this._typeIndex.get(typeId)
    if (!componentIds) return []

    return Array.from(componentIds)
      .map((id) => this._components.get(id))
      .filter((component): component is Component => component !== undefined)
  }

  /**
   * Find component by part number
   */
  findByPartNumber(partNumber: string): Component | null {
    const componentId = this._partNumberIndex.get(partNumber.toLowerCase())
    return componentId ? this._components.get(componentId) || null : null
  }

  // Bulk Operations

  /**
   * Add multiple components
   */
  addMultipleComponents(
    componentsData: ComponentCreateData[]
  ): BulkOperationResult {
    const result: BulkOperationResult = {
      successful: [],
      failed: [],
      statistics: {
        attempted: componentsData.length,
        successful: 0,
        failed: 0,
      },
    }

    for (const componentData of componentsData) {
      try {
        const component = this.addComponent(componentData)
        result.successful.push(component)
        result.statistics.successful++
      } catch (error) {
        result.failed.push({
          data: componentData,
          error:
            error instanceof ComponentDomainError
              ? error
              : ComponentDomainError.businessRuleViolation(
                  "Unknown error during bulk creation"
                ),
        })
        result.statistics.failed++
      }
    }

    return result
  }

  /**
   * Update multiple components
   */
  updateMultipleComponents(
    updates: Array<{ id: number; data: ComponentUpdateData }>
  ): BulkOperationResult {
    const result: BulkOperationResult = {
      successful: [],
      failed: [],
      statistics: {
        attempted: updates.length,
        successful: 0,
        failed: 0,
      },
    }

    for (const update of updates) {
      try {
        const component = this.updateComponent(update.id, update.data)
        result.successful.push(component)
        result.statistics.successful++
      } catch (error) {
        result.failed.push({
          data: update.data,
          error:
            error instanceof ComponentDomainError
              ? error
              : ComponentDomainError.businessRuleViolation(
                  "Unknown error during bulk update"
                ),
        })
        result.statistics.failed++
      }
    }

    return result
  }

  // Catalog Analysis and Statistics

  /**
   * Get catalog statistics
   */
  getStatistics(): CatalogStatistics {
    const components = Array.from(this._components.values())
    const activeComponents = components.filter((c) => c.isActive())
    const inactiveComponents = components.filter((c) => !c.isActive())
    const discontinuedComponents = components.filter(
      (c) => c.stock_status === "discontinued" || c.stock_status === "obsolete"
    )
    const preferredComponents = components.filter((c) => c.isPreferred())
    const componentsWithSpecs = components.filter((c) => c.hasSpecifications())
    const componentsWithPricing = components.filter((c) => c.hasPricing())

    // Calculate price statistics
    const pricesWithCurrency = components
      .filter((c) => c.unit_price && c.unit_price > 0)
      .map((c) => ({ price: c.unit_price!, currency: c.currency }))

    let averagePrice: PriceValue | undefined
    let priceRange: { min: PriceValue; max: PriceValue } | undefined

    if (pricesWithCurrency.length > 0) {
      // Group by currency for statistics
      const eurPrices = pricesWithCurrency
        .filter((p) => p.currency === "EUR")
        .map((p) => p.price)

      if (eurPrices.length > 0) {
        const sum = eurPrices.reduce((acc, price) => acc + price, 0)
        averagePrice = PriceValue.from(sum / eurPrices.length, "EUR")

        const min = Math.min(...eurPrices)
        const max = Math.max(...eurPrices)
        priceRange = {
          min: PriceValue.from(min, "EUR"),
          max: PriceValue.from(max, "EUR"),
        }
      }
    }

    // Calculate distributions
    const categoryDistribution = new Map<number, number>()
    const typeDistribution = new Map<number, number>()
    const manufacturerSet = new Set<string>()

    components.forEach((component) => {
      // Category distribution
      const categoryCount = categoryDistribution.get(component.category_id) || 0
      categoryDistribution.set(component.category_id, categoryCount + 1)

      // Type distribution
      const typeCount = typeDistribution.get(component.component_type_id) || 0
      typeDistribution.set(component.component_type_id, typeCount + 1)

      // Manufacturer count
      manufacturerSet.add(component.manufacturer.toLowerCase())
    })

    return {
      totalComponents: components.length,
      activeComponents: activeComponents.length,
      inactiveComponents: inactiveComponents.length,
      discontinuedComponents: discontinuedComponents.length,
      preferredComponents: preferredComponents.length,
      componentsWithSpecifications: componentsWithSpecs.length,
      componentsWithPricing: componentsWithPricing.length,
      averagePrice,
      priceRange,
      manufacturerCount: manufacturerSet.size,
      categoryDistribution,
      typeDistribution,
    }
  }

  /**
   * Validate catalog integrity
   */
  validateCatalog(): CatalogValidationResult {
    const errors: ComponentDomainError[] = []
    const warnings: string[] = []
    const duplicates: Component[] = []
    const conflicts: Array<{
      existing: Component
      conflictType: "name" | "model_number" | "part_number"
      conflictValue: string
    }> = []

    const components = Array.from(this._components.values())

    // Check for duplicates and conflicts
    const nameMap = new Map<string, Component[]>()
    const modelMap = new Map<string, Component[]>()
    const partNumberMap = new Map<string, Component[]>()

    components.forEach((component) => {
      // Group by name
      const nameKey =
        `${component.manufacturer}-${component.name}`.toLowerCase()
      if (!nameMap.has(nameKey)) nameMap.set(nameKey, [])
      nameMap.get(nameKey)!.push(component)

      // Group by model number
      const modelKey =
        `${component.manufacturer}-${component.model_number}`.toLowerCase()
      if (!modelMap.has(modelKey)) modelMap.set(modelKey, [])
      modelMap.get(modelKey)!.push(component)

      // Group by part number
      if (component.part_number) {
        const partKey = component.part_number.toLowerCase()
        if (!partNumberMap.has(partKey)) partNumberMap.set(partKey, [])
        partNumberMap.get(partKey)!.push(component)
      }
    })

    // Check for actual duplicates
    nameMap.forEach((comps, key) => {
      if (comps.length > 1) {
        comps.forEach((comp) => duplicates.push(comp))
        conflicts.push({
          existing: comps[0],
          conflictType: "name",
          conflictValue: key,
        })
      }
    })

    modelMap.forEach((comps, key) => {
      if (comps.length > 1) {
        conflicts.push({
          existing: comps[0],
          conflictType: "model_number",
          conflictValue: key,
        })
      }
    })

    partNumberMap.forEach((comps, key) => {
      if (comps.length > 1) {
        conflicts.push({
          existing: comps[0],
          conflictType: "part_number",
          conflictValue: key,
        })
      }
    })

    // Check for missing specifications on active components
    const activeWithoutSpecs = components.filter(
      (c) => c.isActive() && !c.hasSpecifications()
    ).length

    if (activeWithoutSpecs > 0) {
      warnings.push(
        `${activeWithoutSpecs} active components missing specifications`
      )
    }

    // Check for missing pricing on active components
    const activeWithoutPricing = components.filter(
      (c) => c.isActive() && !c.hasPricing()
    ).length

    if (activeWithoutPricing > 0) {
      warnings.push(`${activeWithoutPricing} active components missing pricing`)
    }

    return {
      isValid: errors.length === 0 && conflicts.length === 0,
      errors,
      warnings,
      duplicates,
      conflicts,
    }
  }

  // Aggregate State

  /**
   * Get catalog version
   */
  getVersion(): number {
    return this._version
  }

  /**
   * Check if catalog is empty
   */
  isEmpty(): boolean {
    return this._components.size === 0
  }

  /**
   * Clear all components
   */
  clear(): void {
    this._components.clear()
    this._nameIndex.clear()
    this._manufacturerIndex.clear()
    this._categoryIndex.clear()
    this._typeIndex.clear()
    this._partNumberIndex.clear()
    this.incrementVersion()
  }

  // Private Helper Methods

  private addComponentInternal(
    component: Component,
    incrementVersion: boolean
  ): void {
    this._components.set(component.id, component)
    this.addToIndexes(component)

    if (incrementVersion) {
      this.incrementVersion()
    }
  }

  private addToIndexes(component: Component): void {
    // Name index
    const nameKey = `${component.manufacturer}-${component.name}`.toLowerCase()
    this._nameIndex.set(nameKey, component.id)

    // Manufacturer index
    const manufacturerKey = component.manufacturer.toLowerCase()
    if (!this._manufacturerIndex.has(manufacturerKey)) {
      this._manufacturerIndex.set(manufacturerKey, new Set())
    }
    this._manufacturerIndex.get(manufacturerKey)!.add(component.id)

    // Category index
    if (!this._categoryIndex.has(component.category_id)) {
      this._categoryIndex.set(component.category_id, new Set())
    }
    this._categoryIndex.get(component.category_id)!.add(component.id)

    // Type index
    if (!this._typeIndex.has(component.component_type_id)) {
      this._typeIndex.set(component.component_type_id, new Set())
    }
    this._typeIndex.get(component.component_type_id)!.add(component.id)

    // Part number index
    if (component.part_number) {
      const partNumberKey = component.part_number.toLowerCase()
      this._partNumberIndex.set(partNumberKey, component.id)
    }
  }

  private removeFromIndexes(component: Component): void {
    // Name index
    const nameKey = `${component.manufacturer}-${component.name}`.toLowerCase()
    this._nameIndex.delete(nameKey)

    // Manufacturer index
    const manufacturerKey = component.manufacturer.toLowerCase()
    const manufacturerSet = this._manufacturerIndex.get(manufacturerKey)
    if (manufacturerSet) {
      manufacturerSet.delete(component.id)
      if (manufacturerSet.size === 0) {
        this._manufacturerIndex.delete(manufacturerKey)
      }
    }

    // Category index
    const categorySet = this._categoryIndex.get(component.category_id)
    if (categorySet) {
      categorySet.delete(component.id)
      if (categorySet.size === 0) {
        this._categoryIndex.delete(component.category_id)
      }
    }

    // Type index
    const typeSet = this._typeIndex.get(component.component_type_id)
    if (typeSet) {
      typeSet.delete(component.id)
      if (typeSet.size === 0) {
        this._typeIndex.delete(component.component_type_id)
      }
    }

    // Part number index
    if (component.part_number) {
      const partNumberKey = component.part_number.toLowerCase()
      this._partNumberIndex.delete(partNumberKey)
    }
  }

  private updateIndexesForComponent(component: Component): void {
    // Remove old indexes
    this.removeFromIndexes(component)

    // Add new indexes
    this.addToIndexes(component)
  }

  private validateComponentForAddition(
    componentData: ComponentCreateData
  ): void {
    // Check for duplicate names within same manufacturer
    const nameKey =
      `${componentData.manufacturer}-${componentData.name}`.toLowerCase()
    if (this._nameIndex.has(nameKey)) {
      throw ComponentDomainError.businessRuleViolation(
        `Component with name "${componentData.name}" already exists for manufacturer "${componentData.manufacturer}"`
      )
    }

    // Check for duplicate part numbers
    if (componentData.part_number) {
      const partNumberKey = componentData.part_number.toLowerCase()
      if (this._partNumberIndex.has(partNumberKey)) {
        throw ComponentDomainError.businessRuleViolation(
          `Component with part number "${componentData.part_number}" already exists`
        )
      }
    }
  }

  private validateExistingComponentForAddition(component: Component): void {
    // Check if already exists
    if (this._components.has(component.id)) {
      throw ComponentDomainError.businessRuleViolation(
        `Component with ID ${component.id} already exists in catalog`
      )
    }

    // Check for conflicts
    const nameKey = `${component.manufacturer}-${component.name}`.toLowerCase()
    if (this._nameIndex.has(nameKey)) {
      throw ComponentDomainError.businessRuleViolation(
        `Component with name "${component.name}" already exists for manufacturer "${component.manufacturer}"`
      )
    }

    if (component.part_number) {
      const partNumberKey = component.part_number.toLowerCase()
      if (this._partNumberIndex.has(partNumberKey)) {
        throw ComponentDomainError.businessRuleViolation(
          `Component with part number "${component.part_number}" already exists`
        )
      }
    }
  }

  private validateComponentForUpdate(
    existingComponent: Component,
    updateData: ComponentUpdateData
  ): void {
    // If name or manufacturer is being updated, check for conflicts
    if (updateData.name || updateData.manufacturer) {
      const newName = updateData.name || existingComponent.name
      const newManufacturer =
        updateData.manufacturer || existingComponent.manufacturer
      const nameKey = `${newManufacturer}-${newName}`.toLowerCase()

      const existingId = this._nameIndex.get(nameKey)
      if (existingId && existingId !== existingComponent.id) {
        throw ComponentDomainError.businessRuleViolation(
          `Component with name "${newName}" already exists for manufacturer "${newManufacturer}"`
        )
      }
    }

    // If part number is being updated, check for conflicts
    if (updateData.part_number) {
      const partNumberKey = updateData.part_number.toLowerCase()
      const existingId = this._partNumberIndex.get(partNumberKey)
      if (existingId && existingId !== existingComponent.id) {
        throw ComponentDomainError.businessRuleViolation(
          `Component with part number "${updateData.part_number}" already exists`
        )
      }
    }
  }

  private incrementVersion(): void {
    this._version++
  }
}
