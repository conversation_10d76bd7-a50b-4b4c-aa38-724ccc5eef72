/**
 * Domain Integration Tests
 *
 * Tests to verify that all domain objects can be imported and work together
 */

import { describe, expect, it } from "vitest"
import {
  // Entities
  Component,
  // Aggregates
  ComponentCatalog,
  // Errors
  ComponentDomainError,
  // Value Objects
  ComponentStatus,
  // Domain Services
  ComponentValidationService,
  PriceValue,
  Specifications,
  type ComponentCreateData,
} from "../index"

describe("Domain Integration", () => {
  describe("Entity and Value Object Integration", () => {
    it("should create component with value objects", () => {
      const componentData: ComponentCreateData = {
        name: "Test Resistor",
        manufacturer: "TestCorp",
        model_number: "R100",
        description: "A test resistor",
        component_type_id: 1,
        category_id: 1,
        specifications: {
          electrical: {
            resistance: "100Ω",
            tolerance: "5%",
          },
        },
        unit_price: 1.5,
        currency: "EUR",
        supplier: "Test Supplier",
        part_number: "TC-R100",
        is_preferred: false,
        stock_status: "available",
        version: "1.0.0",
      }

      const component = Component.create(componentData)

      expect(component.name).toBe("Test Resistor")
      expect(component.manufacturer).toBe("TestCorp")
      expect(component.hasSpecifications()).toBe(true)
      expect(component.hasPricing()).toBe(true)
    })

    it("should work with component status transitions", () => {
      const componentData: ComponentCreateData = {
        name: "Status Test Component",
        manufacturer: "StatusCorp",
        model_number: "ST001",
        component_type_id: 1,
        category_id: 1,
        stock_status: "limited",
        is_preferred: false,
        version: "1.0.0",
      }

      const component = Component.create(componentData)
      expect(component.isAvailable()).toBe(true)

      // Update to out of stock
      component.updateStockStatus("out_of_stock")
      expect(component.isAvailable()).toBe(false)
    })
  })

  describe("Aggregate Integration", () => {
    it("should create catalog and manage components", () => {
      const catalog = ComponentCatalog.empty()
      expect(catalog.isEmpty()).toBe(true)

      const componentData: ComponentCreateData = {
        name: "Catalog Test Component",
        manufacturer: "CatalogCorp",
        model_number: "CT001",
        component_type_id: 1,
        category_id: 1,
        stock_status: "available",
        is_preferred: false,
        version: "1.0.0",
      }

      const component = catalog.addComponent(componentData)
      expect(catalog.getComponentCount()).toBe(1)
      expect(catalog.hasComponent(component.id)).toBe(true)
    })

    it("should search components in catalog", () => {
      // Note: This test shows the limitation of using domain objects with ID 0
      // In practice, IDs would be assigned by the persistence layer
      const catalog = ComponentCatalog.empty()

      // Add first component
      const component1 = catalog.addComponent({
        name: "Resistor 100R",
        manufacturer: "ResistorCorp",
        model_number: "R100",
        component_type_id: 1,
        category_id: 1,
        stock_status: "available",
        is_preferred: false,
        version: "1.0.0",
      })

      // Verify component was added
      expect(catalog.getComponentCount()).toBe(1)

      // Search by name pattern (case-insensitive)
      const resistors = catalog.searchComponents({
        namePattern: "Resistor",
      })
      expect(resistors).toHaveLength(1)
      expect(resistors[0].name).toBe("Resistor 100R")

      // Search by manufacturer
      const resistorCorpComponents = catalog.searchComponents({
        manufacturer: "ResistorCorp",
      })
      expect(resistorCorpComponents).toHaveLength(1)
      expect(resistorCorpComponents[0].name).toBe("Resistor 100R")
    })
  })

  describe("Domain Service Integration", () => {
    it("should validate component creation", () => {
      const validationService = new ComponentValidationService()

      const validComponentData: ComponentCreateData = {
        name: "Valid Component",
        manufacturer: "ValidCorp",
        model_number: "VC001",
        component_type_id: 1,
        category_id: 1,
        specifications: {
          electrical: {
            voltage: "5V",
            current: "1A",
          },
        },
        unit_price: 10.0,
        currency: "EUR",
        stock_status: "available",
        is_preferred: false,
        version: "1.0.0",
      }

      const result =
        validationService.validateComponentCreation(validComponentData)
      expect(result.isValid).toBe(true)
      expect(result.score).toBeGreaterThan(70)
    })

    it("should identify invalid component data", () => {
      const validationService = new ComponentValidationService()

      const invalidComponentData: ComponentCreateData = {
        name: "X", // Too short
        manufacturer: "V", // Too short
        model_number: "", // Empty
        component_type_id: 1,
        category_id: 1,
        stock_status: "available",
        is_preferred: false,
        version: "1.0.0",
      }

      const result =
        validationService.validateComponentCreation(invalidComponentData)
      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe("Value Object Integration", () => {
    it("should work with price comparisons", () => {
      const price1 = PriceValue.from(10.0, "EUR")
      const price2 = PriceValue.from(15.0, "EUR")

      expect(price1.isLessThan(price2)).toBe(true)
      expect(price2.isGreaterThan(price1)).toBe(true)
      expect(price1.equals(price2)).toBe(false)
    })

    it("should work with specifications validation", () => {
      const specsData = {
        electrical: {
          voltage: "5V",
          current: "1A",
          power: "5W",
        },
        mechanical: {
          dimensions: "10x10x5mm",
          weight: "2g",
        },
      }

      const specs = new Specifications(specsData)
      expect(specs.hasElectrical()).toBe(true)
      expect(specs.hasMechanical()).toBe(true)
      expect(specs.getCompletenessScore()).toBeGreaterThanOrEqual(0.5)
    })
  })

  describe("Error Handling Integration", () => {
    it("should handle domain errors properly", () => {
      expect(() => {
        ComponentStatus.fromString("invalid_status")
      }).toThrow(ComponentDomainError)
    })

    it("should handle business rule violations in catalog", () => {
      const catalog = ComponentCatalog.empty()

      const componentData: ComponentCreateData = {
        name: "Duplicate Test",
        manufacturer: "DuplicateCorp",
        model_number: "DT001",
        component_type_id: 1,
        category_id: 1,
        stock_status: "available",
        is_preferred: false,
        version: "1.0.0",
      }

      // Add first component
      catalog.addComponent(componentData)

      // Try to add duplicate
      expect(() => {
        catalog.addComponent(componentData)
      }).toThrow(ComponentDomainError)
    })
  })
})
