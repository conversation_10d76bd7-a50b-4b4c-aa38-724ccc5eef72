/**
 * Domain Services - Component Catalog Context
 *
 * Domain Services contain business logic that doesn't naturally
 * belong to any single Entity or Value Object.
 */

// Domain Service exports
export { ComponentValidationService } from "./ComponentValidationService"
export type {
  ValidationRules,
  ValidationResult,
  ComponentComparisonResult,
} from "./ComponentValidationService"

// Future domain services
// export { ComponentSearchService } from './ComponentSearchService'
// export { BulkOperationService } from './BulkOperationService'
