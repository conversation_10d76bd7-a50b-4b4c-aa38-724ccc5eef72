import type {
  ComponentCreateData,
  ComponentUpdateData,
} from "../entities/Component"

import { Component } from "../entities/Component"
import { ComponentDomainError } from "../errors/ComponentDomainError"
import { PriceValue } from "../value-objects/PriceValue"
import { Specifications } from "../value-objects/Specifications"

/**
 * ComponentValidationService
 *
 * Domain service providing validation logic that doesn't naturally
 * belong to any single entity or value object. This service
 * orchestrates complex validation rules across multiple domain objects.
 */

/**
 * Validation rule configuration
 */
export interface ValidationRules {
  requireSpecifications: boolean
  requirePricing: boolean
  requireStandardsCompliance: boolean
  maxPriceDeviation: number // Percentage
  allowedManufacturers?: string[]
  prohibitedKeywords?: string[]
  requiredSpecificationCategories?: string[]
}

/**
 * Validation result with detailed feedback
 */
export interface ValidationResult {
  isValid: boolean
  errors: ComponentDomainError[]
  warnings: string[]
  suggestions: string[]
  score: number // 0-100 quality score
}

/**
 * Component comparison result
 */
export interface ComponentComparisonResult {
  isDuplicate: boolean
  similarityScore: number // 0-100
  conflictingFields: string[]
  recommendations: string[]
}

/**
 * ComponentValidationService
 *
 * Provides comprehensive validation services for components,
 * including business rule validation, quality scoring, and
 * duplicate detection.
 */
export class ComponentValidationService {
  private readonly defaultRules: ValidationRules = {
    requireSpecifications: true,
    requirePricing: false,
    requireStandardsCompliance: false,
    maxPriceDeviation: 50, // 50% deviation allowed
    prohibitedKeywords: ["test", "sample", "dummy", "fake"],
    requiredSpecificationCategories: ["electrical"],
  }

  constructor(private readonly customRules: Partial<ValidationRules> = {}) {}

  /**
   * Get effective validation rules
   */
  private getRules(): ValidationRules {
    return { ...this.defaultRules, ...this.customRules }
  }

  /**
   * Validate component creation data
   */
  validateComponentCreation(data: ComponentCreateData): ValidationResult {
    const errors: ComponentDomainError[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100

    const rules = this.getRules()

    // Basic data validation
    this.validateBasicData(data, errors, warnings, suggestions)

    // Specifications validation
    if (rules.requireSpecifications) {
      const specResult = this.validateSpecifications(data.specifications, rules)
      errors.push(...specResult.errors)
      warnings.push(...specResult.warnings)
      suggestions.push(...specResult.suggestions)
      score -= (100 - specResult.score) * 0.3 // 30% weight
    }

    // Pricing validation
    if (rules.requirePricing) {
      const priceResult = this.validatePricing(
        data.unit_price,
        data.currency,
        rules
      )
      errors.push(...priceResult.errors)
      warnings.push(...priceResult.warnings)
      score -= (100 - priceResult.score) * 0.2 // 20% weight
    }

    // Content validation
    const contentResult = this.validateContent(data, rules)
    errors.push(...contentResult.errors)
    warnings.push(...contentResult.warnings)
    suggestions.push(...contentResult.suggestions)
    score -= (100 - contentResult.score) * 0.2 // 20% weight

    // Business rules validation
    const businessResult = this.validateBusinessRules(data, rules)
    errors.push(...businessResult.errors)
    warnings.push(...businessResult.warnings)
    suggestions.push(...businessResult.suggestions)
    score -= (100 - businessResult.score) * 0.3 // 30% weight

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      score: Math.max(0, Math.round(score)),
    }
  }

  /**
   * Validate component update data
   */
  validateComponentUpdate(
    existingComponent: Component,
    updateData: ComponentUpdateData
  ): ValidationResult {
    const errors: ComponentDomainError[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100

    const rules = this.getRules()

    // Create merged data for validation
    const mergedData: ComponentCreateData = {
      name: updateData.name ?? existingComponent.name,
      manufacturer: updateData.manufacturer ?? existingComponent.manufacturer,
      model_number: updateData.model_number ?? existingComponent.model_number,
      description:
        updateData.description ?? existingComponent.description ?? undefined,
      component_type_id:
        updateData.component_type_id ?? existingComponent.component_type_id,
      category_id: updateData.category_id ?? existingComponent.category_id,
      specifications:
        updateData.specifications ?? existingComponent.specifications,
      unit_price:
        updateData.unit_price ?? existingComponent.unit_price ?? undefined,
      currency: updateData.currency ?? existingComponent.currency,
      supplier: updateData.supplier ?? existingComponent.supplier ?? undefined,
      part_number:
        updateData.part_number ?? existingComponent.part_number ?? undefined,
      weight_kg:
        updateData.weight_kg ?? existingComponent.weight_kg ?? undefined,
      dimensions:
        updateData.dimensions ?? existingComponent.dimensions ?? undefined,
      is_preferred: updateData.is_preferred ?? existingComponent.is_preferred,
      stock_status: updateData.stock_status ?? existingComponent.stock_status,
      version: updateData.version ?? existingComponent.version,
      metadata: updateData.metadata ?? existingComponent.metadata ?? undefined,
    }

    // Validate like a creation but with additional update-specific checks
    const creationResult = this.validateComponentCreation(mergedData)
    errors.push(...creationResult.errors)
    warnings.push(...creationResult.warnings)
    suggestions.push(...creationResult.suggestions)
    score = creationResult.score

    // Update-specific validations
    if (updateData.unit_price !== undefined && existingComponent.unit_price) {
      const existingPrice = PriceValue.from(
        existingComponent.unit_price,
        existingComponent.currency
      )
      const newPrice = PriceValue.from(
        updateData.unit_price,
        updateData.currency ?? existingComponent.currency
      )

      if (existingPrice.hasSameCurrency(newPrice)) {
        const priceChange =
          (Math.abs(newPrice.getAmount() - existingPrice.getAmount()) /
            existingPrice.getAmount()) *
          100

        if (priceChange > rules.maxPriceDeviation) {
          warnings.push(
            `Price change of ${priceChange.toFixed(1)}% exceeds recommended maximum of ${rules.maxPriceDeviation}%`
          )
        }
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      score,
    }
  }

  /**
   * Compare two components for similarity and conflicts
   */
  compareComponents(
    component1: Component,
    component2: Component
  ): ComponentComparisonResult {
    let similarityScore = 0
    const conflictingFields: string[] = []
    const recommendations: string[] = []

    // Name similarity
    if (component1.name.toLowerCase() === component2.name.toLowerCase()) {
      similarityScore += 30
      conflictingFields.push("name")
    } else if (
      this.calculateStringSimilarity(component1.name, component2.name) > 0.8
    ) {
      similarityScore += 20
      recommendations.push(
        "Names are very similar - verify these are different components"
      )
    }

    // Manufacturer comparison
    if (
      component1.manufacturer.toLowerCase() ===
      component2.manufacturer.toLowerCase()
    ) {
      similarityScore += 20
      if (
        component1.model_number.toLowerCase() ===
        component2.model_number.toLowerCase()
      ) {
        similarityScore += 30
        conflictingFields.push("model_number")
      }
    }

    // Part number comparison
    if (component1.part_number && component2.part_number) {
      if (
        component1.part_number.toLowerCase() ===
        component2.part_number.toLowerCase()
      ) {
        similarityScore += 40
        conflictingFields.push("part_number")
      }
    }

    // Specifications comparison
    if (component1.hasSpecifications() && component2.hasSpecifications()) {
      const specs1 = new Specifications(component1.specifications!)
      const specs2 = new Specifications(component2.specifications!)

      if (specs1.equals(specs2)) {
        similarityScore += 15
      }
    }

    // Category and type comparison
    if (component1.category_id === component2.category_id) {
      similarityScore += 5
      if (component1.component_type_id === component2.component_type_id) {
        similarityScore += 10
      }
    }

    const isDuplicate = similarityScore >= 70 || conflictingFields.length > 0

    if (isDuplicate) {
      recommendations.push(
        "These components appear to be duplicates or have conflicting information"
      )
    }

    return {
      isDuplicate,
      similarityScore: Math.min(100, similarityScore),
      conflictingFields,
      recommendations,
    }
  }

  /**
   * Validate specifications completeness and quality
   */
  validateSpecifications(
    specifications: any,
    rules: ValidationRules
  ): ValidationResult {
    const errors: ComponentDomainError[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100

    if (!specifications) {
      if (rules.requireSpecifications) {
        errors.push(
          ComponentDomainError.invalidSpecification(
            "Specifications are required",
            {}
          )
        )
        score = 0
      }
      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        suggestions,
        score,
      }
    }

    try {
      const specs = new Specifications(specifications)

      // Check completeness
      const completenessScore = specs.getCompletenessScore()
      score = completenessScore * 100

      if (completenessScore < 0.5) {
        warnings.push(
          "Specifications are incomplete - consider adding more details"
        )
      }

      // Check required categories
      if (rules.requiredSpecificationCategories) {
        for (const category of rules.requiredSpecificationCategories) {
          if (
            !specs.getCategory(category) ||
            Object.keys(specs.getCategory(category)).length === 0
          ) {
            warnings.push(
              `Missing required specification category: ${category}`
            )
            score -= 20
          }
        }
      }

      // Standards compliance check
      if (rules.requireStandardsCompliance && !specs.hasStandardsCompliance()) {
        warnings.push("Standards compliance information is missing")
        score -= 15
      }

      // Electrical validation
      if (specs.hasElectrical()) {
        const electrical = specs.getElectrical()
        if (!electrical.voltage && !electrical.current && !electrical.power) {
          warnings.push(
            "Electrical specifications should include at least voltage, current, or power"
          )
          score -= 10
        }
      }

      suggestions.push(
        "Consider adding environmental specifications for better component selection"
      )
    } catch (error) {
      if (error instanceof ComponentDomainError) {
        errors.push(error)
        score = 0
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      score: Math.max(0, Math.round(score)),
    }
  }

  // Private validation methods

  private validateBasicData(
    data: ComponentCreateData,
    errors: ComponentDomainError[],
    warnings: string[],
    suggestions: string[]
  ): void {
    // Name validation
    if (data.name.length < 3) {
      errors.push(
        ComponentDomainError.invalidComponent(
          "name",
          data.name,
          "Component name must be at least 3 characters long"
        )
      )
    }

    // Manufacturer validation
    if (data.manufacturer.length < 2) {
      errors.push(
        ComponentDomainError.invalidComponent(
          "manufacturer",
          data.manufacturer,
          "Manufacturer name must be at least 2 characters long"
        )
      )
    }

    // Model number validation
    if (data.model_number.length < 1) {
      errors.push(
        ComponentDomainError.invalidComponent(
          "model_number",
          data.model_number,
          "Model number cannot be empty"
        )
      )
    }

    // Suggest description if missing
    if (!data.description || data.description.trim().length === 0) {
      suggestions.push(
        "Consider adding a description to help users understand the component's purpose"
      )
    }
  }

  private validatePricing(
    unitPrice: number | undefined,
    currency: string | undefined,
    rules: ValidationRules
  ): ValidationResult {
    const errors: ComponentDomainError[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100

    if (unitPrice === undefined || unitPrice === null) {
      if (rules.requirePricing) {
        errors.push(
          ComponentDomainError.invalidComponent(
            "unit_price",
            unitPrice,
            "Pricing information is required"
          )
        )
        score = 0
      } else {
        warnings.push("No pricing information provided")
        score = 50
      }
      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        suggestions,
        score,
      }
    }

    if (unitPrice < 0) {
      errors.push(
        ComponentDomainError.invalidComponent(
          "unit_price",
          unitPrice,
          "Price cannot be negative"
        )
      )
      score = 0
    }

    if (unitPrice === 0) {
      warnings.push("Component price is zero - verify this is correct")
      score = 80
    }

    if (!currency) {
      warnings.push("Currency not specified - defaulting to EUR")
      score -= 10
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      score,
    }
  }

  private validateContent(
    data: ComponentCreateData,
    rules: ValidationRules
  ): ValidationResult {
    const errors: ComponentDomainError[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100

    // Check for prohibited keywords
    if (rules.prohibitedKeywords) {
      const allText = [
        data.name,
        data.manufacturer,
        data.model_number,
        data.description || "",
      ]
        .join(" ")
        .toLowerCase()

      for (const keyword of rules.prohibitedKeywords) {
        if (allText.includes(keyword.toLowerCase())) {
          warnings.push(
            `Prohibited keyword "${keyword}" found in component data`
          )
          score -= 15
        }
      }
    }

    // Check for allowed manufacturers
    if (rules.allowedManufacturers && rules.allowedManufacturers.length > 0) {
      const manufacturerAllowed = rules.allowedManufacturers.some(
        (allowed) => allowed.toLowerCase() === data.manufacturer.toLowerCase()
      )

      if (!manufacturerAllowed) {
        warnings.push(
          `Manufacturer "${data.manufacturer}" is not in the approved list`
        )
        score -= 20
      }
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      score,
    }
  }

  private validateBusinessRules(
    data: ComponentCreateData,
    rules: ValidationRules
  ): ValidationResult {
    const errors: ComponentDomainError[] = []
    const warnings: string[] = []
    const suggestions: string[] = []
    let score = 100

    // Validate component type and category consistency
    // This would normally check against actual type/category data
    // For now, we'll do basic validation

    if (data.component_type_id === data.category_id) {
      warnings.push(
        "Component type ID and category ID are the same - verify this is correct"
      )
      score -= 10
    }

    // Weight validation
    if (data.weight_kg !== undefined && data.weight_kg > 100) {
      warnings.push("Component weight exceeds 100kg - verify this is correct")
      score -= 5
    }

    // Version validation
    if (data.version && !/^\d+\.\d+(\.\d+)?$/.test(data.version)) {
      warnings.push(
        "Version should follow semantic versioning format (e.g., 1.0.0)"
      )
      score -= 5
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      suggestions,
      score,
    }
  }

  private calculateStringSimilarity(str1: string, str2: string): number {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1

    if (longer.length === 0) return 1.0

    const distance = this.calculateLevenshteinDistance(longer, shorter)
    return (longer.length - distance) / longer.length
  }

  private calculateLevenshteinDistance(str1: string, str2: string): number {
    const matrix = Array(str2.length + 1)
      .fill(null)
      .map(() => Array(str1.length + 1).fill(null))

    for (let i = 0; i <= str1.length; i++) matrix[0][i] = i
    for (let j = 0; j <= str2.length; j++) matrix[j][0] = j

    for (let j = 1; j <= str2.length; j++) {
      for (let i = 1; i <= str1.length; i++) {
        const indicator = str1[i - 1] === str2[j - 1] ? 0 : 1
        matrix[j][i] = Math.min(
          matrix[j][i - 1] + 1,
          matrix[j - 1][i] + 1,
          matrix[j - 1][i - 1] + indicator
        )
      }
    }

    return matrix[str2.length][str1.length]
  }
}
