/**
 * Component Domain Error
 *
 * Represents errors that occur within the component domain,
 * typically due to domain invariant violations or business rule violations.
 */

export class ComponentDomainError extends Error {
  constructor(
    message: string,
    public readonly code?: string,
    public readonly details?: Record<string, any>
  ) {
    super(message)
    this.name = "ComponentDomainError"

    // Ensure proper prototype chain for instanceof checks
    Object.setPrototypeOf(this, ComponentDomainError.prototype)
  }

  /**
   * Creates a domain error for invalid component data
   */
  static invalidComponent(
    field: string,
    value: any,
    constraint: string
  ): ComponentDomainError {
    return new ComponentDomainError(
      `Invalid component ${field}: ${constraint}`,
      "INVALID_COMPONENT",
      { field, value, constraint }
    )
  }

  /**
   * Creates a domain error for specification validation failures
   */
  static invalidSpecification(
    message: string,
    specs: Record<string, any>
  ): ComponentDomainError {
    return new ComponentDomainError(
      `Invalid component specification: ${message}`,
      "INVALID_SPECIFICATION",
      { specifications: specs }
    )
  }

  /**
   * Creates a domain error for business rule violations
   */
  static businessRuleViolation(
    rule: string,
    context?: Record<string, any>
  ): ComponentDomainError {
    return new ComponentDomainError(
      `Business rule violation: ${rule}`,
      "BUSINESS_RULE_VIOLATION",
      context
    )
  }
}
