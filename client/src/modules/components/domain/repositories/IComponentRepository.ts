/**
 * Component Repository Interface
 *
 * Defines the contract for component data access operations.
 * This interface belongs to the domain layer and is implemented
 * in the infrastructure layer.
 */

import {
  Component,
  ComponentCreateData,
  ComponentUpdateData,
} from "../entities/Component"

/**
 * Component search criteria for repository queries
 */
export interface ComponentSearchCriteria {
  namePattern?: string
  manufacturer?: string
  componentTypeId?: number
  categoryId?: number
  isPreferred?: boolean
  stockStatus?: string
  isActive?: boolean
  hasSpecifications?: boolean
  priceRange?: {
    min?: number
    max?: number
    currency?: string
  }
  limit?: number
  offset?: number
}

/**
 * Component sorting options
 */
export interface ComponentSortOptions {
  field:
    | "name"
    | "manufacturer"
    | "model_number"
    | "unit_price"
    | "created_at"
    | "updated_at"
  direction: "asc" | "desc"
}

/**
 * Paginated result for component queries
 */
export interface ComponentSearchResult {
  components: Component[]
  totalCount: number
  hasMore: boolean
  nextOffset?: number
}

/**
 * Bulk operation result
 */
export interface BulkOperationResult<T> {
  successful: T[]
  failed: Array<{
    data: any
    error: Error
  }>
  statistics: {
    total: number
    successful: number
    failed: number
  }
}

/**
 * Component Repository Interface
 *
 * Defines all data access operations for components.
 * Implementation details are left to the infrastructure layer.
 */
export interface IComponentRepository {
  // Basic CRUD Operations

  /**
   * Find component by ID
   */
  findById(id: number): Promise<Component | null>

  /**
   * Find multiple components by IDs
   */
  findByIds(ids: number[]): Promise<Component[]>

  /**
   * Find all components with pagination
   */
  findAll(offset?: number, limit?: number): Promise<ComponentSearchResult>

  /**
   * Save new component
   */
  save(component: Component): Promise<Component>

  /**
   * Create new component from data
   */
  create(data: ComponentCreateData): Promise<Component>

  /**
   * Update existing component
   */
  update(id: number, data: ComponentUpdateData): Promise<Component>

  /**
   * Delete component by ID
   */
  delete(id: number): Promise<void>

  /**
   * Check if component exists
   */
  exists(id: number): Promise<boolean>

  // Advanced Search Operations

  /**
   * Search components with criteria and sorting
   */
  search(
    criteria: ComponentSearchCriteria,
    sort?: ComponentSortOptions
  ): Promise<ComponentSearchResult>

  /**
   * Find components by manufacturer
   */
  findByManufacturer(manufacturer: string): Promise<Component[]>

  /**
   * Find components by type
   */
  findByType(componentTypeId: number): Promise<Component[]>

  /**
   * Find components by category
   */
  findByCategory(categoryId: number): Promise<Component[]>

  /**
   * Find preferred components
   */
  findPreferred(): Promise<Component[]>

  /**
   * Find components by stock status
   */
  findByStockStatus(status: string): Promise<Component[]>

  /**
   * Find similar components (by specifications)
   */
  findSimilar(component: Component, limit?: number): Promise<Component[]>

  // Bulk Operations

  /**
   * Create multiple components
   */
  createBulk(
    data: ComponentCreateData[]
  ): Promise<BulkOperationResult<Component>>

  /**
   * Update multiple components
   */
  updateBulk(
    updates: Array<{ id: number; data: ComponentUpdateData }>
  ): Promise<BulkOperationResult<Component>>

  /**
   * Delete multiple components
   */
  deleteBulk(ids: number[]): Promise<BulkOperationResult<{ id: number }>>

  // Statistics and Analytics

  /**
   * Get total component count
   */
  count(): Promise<number>

  /**
   * Get component count by criteria
   */
  countByCriteria(criteria: ComponentSearchCriteria): Promise<number>

  /**
   * Get component statistics
   */
  getStatistics(): Promise<{
    total: number
    active: number
    inactive: number
    preferred: number
    byManufacturer: Record<string, number>
    byType: Record<number, number>
    byCategory: Record<number, number>
    byStockStatus: Record<string, number>
  }>

  // Validation Operations

  /**
   * Check for duplicate components
   */
  checkDuplicates(data: ComponentCreateData): Promise<Component[]>

  /**
   * Validate component data before creation
   */
  validateForCreation(data: ComponentCreateData): Promise<{
    isValid: boolean
    errors: string[]
  }>

  /**
   * Validate component data before update
   */
  validateForUpdate(
    id: number,
    data: ComponentUpdateData
  ): Promise<{
    isValid: boolean
    errors: string[]
  }>
}
