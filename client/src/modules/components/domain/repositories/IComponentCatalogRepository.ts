/**
 * Component Catalog Repository Interface
 *
 * Defines the contract for component catalog data access operations.
 * This handles operations at the aggregate level, managing collections
 * of components and their relationships.
 */

import { ComponentCatalog } from "../aggregates/ComponentCatalog"
import { Component, ComponentCreateData } from "../entities/Component"

/**
 * Catalog search and filter criteria
 */
export interface CatalogSearchCriteria {
  namePattern?: string
  manufacturer?: string
  componentTypeId?: number
  categoryId?: number
  isPreferred?: boolean
  stockStatus?: string
  isActive?: boolean
  priceRange?: {
    min?: number
    max?: number
    currency?: string
  }
  hasSpecifications?: boolean
  tags?: string[]
}

/**
 * Catalog sorting options
 */
export interface CatalogSortOptions {
  field:
    | "name"
    | "manufacturer"
    | "model_number"
    | "unit_price"
    | "created_at"
    | "updated_at"
  direction: "asc" | "desc"
}

/**
 * Catalog snapshot for persistence
 */
export interface CatalogSnapshot {
  id: string
  name: string
  description?: string
  components: Component[]
  metadata: {
    version: number
    createdAt: Date
    updatedAt: Date
    totalComponents: number
    statistics: {
      activeComponents: number
      preferredComponents: number
      manufacturerCount: number
      categoryCount: number
    }
  }
}

/**
 * Component Catalog Repository Interface
 *
 * Manages persistence and retrieval of component catalogs.
 * Handles catalog-level operations and aggregate consistency.
 */
export interface IComponentCatalogRepository {
  // Catalog Lifecycle Operations

  /**
   * Create and save new catalog
   */
  create(name: string, description?: string): Promise<ComponentCatalog>

  /**
   * Find catalog by ID
   */
  findById(id: string): Promise<ComponentCatalog | null>

  /**
   * Find catalog by name
   */
  findByName(name: string): Promise<ComponentCatalog | null>

  /**
   * Get all available catalogs
   */
  findAll(): Promise<ComponentCatalog[]>

  /**
   * Save catalog changes
   */
  save(catalog: ComponentCatalog): Promise<ComponentCatalog>

  /**
   * Delete catalog
   */
  delete(id: string): Promise<void>

  /**
   * Check if catalog exists
   */
  exists(id: string): Promise<boolean>

  // Catalog Content Operations

  /**
   * Load catalog with components
   */
  loadWithComponents(id: string): Promise<ComponentCatalog | null>

  /**
   * Add component to catalog
   */
  addComponent(
    catalogId: string,
    componentData: ComponentCreateData
  ): Promise<Component>

  /**
   * Remove component from catalog
   */
  removeComponent(catalogId: string, componentId: number): Promise<void>

  /**
   * Update component in catalog
   */
  updateComponent(
    catalogId: string,
    componentId: number,
    data: Partial<ComponentCreateData>
  ): Promise<Component>

  /**
   * Move component between catalogs
   */
  moveComponent(
    fromCatalogId: string,
    toCatalogId: string,
    componentId: number
  ): Promise<void>

  /**
   * Copy component between catalogs
   */
  copyComponent(
    fromCatalogId: string,
    toCatalogId: string,
    componentId: number
  ): Promise<Component>

  // Search and Filter Operations

  /**
   * Search components within catalog
   */
  searchComponents(
    catalogId: string,
    criteria: CatalogSearchCriteria,
    sort?: CatalogSortOptions
  ): Promise<Component[]>

  /**
   * Get components by manufacturer within catalog
   */
  getComponentsByManufacturer(
    catalogId: string,
    manufacturer: string
  ): Promise<Component[]>

  /**
   * Get preferred components from catalog
   */
  getPreferredComponents(catalogId: string): Promise<Component[]>

  /**
   * Get components by category within catalog
   */
  getComponentsByCategory(
    catalogId: string,
    categoryId: number
  ): Promise<Component[]>

  // Bulk Operations

  /**
   * Import components into catalog
   */
  importComponents(
    catalogId: string,
    components: ComponentCreateData[]
  ): Promise<{
    successful: Component[]
    failed: Array<{
      data: ComponentCreateData
      error: string
    }>
    statistics: {
      total: number
      successful: number
      failed: number
    }
  }>

  /**
   * Export catalog components
   */
  exportComponents(
    catalogId: string,
    format: "json" | "csv" | "xlsx"
  ): Promise<{
    data: any
    filename: string
    mimeType: string
  }>

  /**
   * Merge catalogs
   */
  mergeCatalogs(
    sourceCatalogId: string,
    targetCatalogId: string
  ): Promise<ComponentCatalog>

  /**
   * Clone catalog
   */
  cloneCatalog(
    sourceCatalogId: string,
    newName: string
  ): Promise<ComponentCatalog>

  // Statistics and Analytics

  /**
   * Get catalog statistics
   */
  getStatistics(catalogId: string): Promise<{
    totalComponents: number
    activeComponents: number
    preferredComponents: number
    manufacturerCount: number
    categoryCount: number
    lastUpdated: Date
    componentsByManufacturer: Record<string, number>
    componentsByCategory: Record<number, number>
    componentsByStockStatus: Record<string, number>
    priceDistribution: {
      min: number
      max: number
      average: number
      median: number
    }
  }>

  /**
   * Get catalog health report
   */
  getHealthReport(catalogId: string): Promise<{
    score: number
    issues: Array<{
      type: "warning" | "error"
      category: "data_quality" | "completeness" | "consistency"
      message: string
      component?: Component
    }>
    recommendations: string[]
  }>

  // Version Control Operations

  /**
   * Create catalog snapshot
   */
  createSnapshot(
    catalogId: string,
    description?: string
  ): Promise<CatalogSnapshot>

  /**
   * Restore catalog from snapshot
   */
  restoreFromSnapshot(
    catalogId: string,
    snapshotId: string
  ): Promise<ComponentCatalog>

  /**
   * Get catalog snapshots
   */
  getSnapshots(catalogId: string): Promise<CatalogSnapshot[]>

  /**
   * Compare catalog with snapshot
   */
  compareWithSnapshot(
    catalogId: string,
    snapshotId: string
  ): Promise<{
    added: Component[]
    modified: Component[]
    removed: Component[]
    statistics: {
      totalChanges: number
      additions: number
      modifications: number
      deletions: number
    }
  }>

  // Synchronization Operations

  /**
   * Sync catalog with external source
   */
  syncWithExternalSource(
    catalogId: string,
    sourceConfig: any
  ): Promise<{
    synchronized: Component[]
    conflicts: Array<{
      component: Component
      conflictType: "name" | "manufacturer" | "specifications" | "pricing"
      resolution: "keep_local" | "take_remote" | "manual"
    }>
    statistics: {
      total: number
      synchronized: number
      conflicts: number
    }
  }>

  /**
   * Get sync status
   */
  getSyncStatus(catalogId: string): Promise<{
    lastSync: Date | null
    status: "synced" | "outdated" | "conflicts" | "error"
    pendingChanges: number
    conflictCount: number
  }>
}
