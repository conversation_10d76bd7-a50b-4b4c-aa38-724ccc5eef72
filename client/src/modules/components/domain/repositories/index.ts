/**
 * Repository Interfaces - Component Catalog Context
 *
 * Repository interfaces define contracts for data access without
 * specifying implementation details. They belong to the domain layer
 * and are implemented in the infrastructure layer.
 */

// Repository interface exports
export type {
  IComponentRepository,
  ComponentSearchCriteria,
  ComponentSortOptions,
  ComponentSearchResult,
  BulkOperationResult,
} from "./IComponentRepository"

export type {
  IComponentCatalogRepository,
  CatalogSearchCriteria,
  CatalogSortOptions,
  CatalogSnapshot,
} from "./IComponentCatalogRepository"

// Future repository interfaces
// export type { IComponentCategoryRepository } from './IComponentCategoryRepository'
// export type { IComponentTypeRepository } from './IComponentTypeRepository'
