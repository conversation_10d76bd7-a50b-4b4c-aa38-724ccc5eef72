/**
 * ComponentStatus Value Object
 *
 * Represents the status of a component with business rules and validation.
 * This value object encapsulates the logic for component status transitions
 * and ensures only valid status values are used.
 */

import { ComponentDomainError } from "../errors/ComponentDomainError"
import { ComponentStatusEnum, componentStatusSchema } from "@/types/api"

/**
 * ComponentStatus Value Object
 *
 * Immutable value object representing a component's lifecycle status.
 * Provides validation and business logic for status transitions.
 */
export class ComponentStatus {
  private constructor(private readonly _value: ComponentStatusEnum) {
    this.validate()
  }

  /**
   * Create active status
   */
  static active(): ComponentStatus {
    return new ComponentStatus(componentStatusSchema.enum.active)
  }

  /**
   * Create inactive status
   */
  static inactive(): ComponentStatus {
    return new ComponentStatus(componentStatusSchema.enum.inactive)
  }

  /**
   * Create discontinued status
   */
  static discontinued(): ComponentStatus {
    return new ComponentStatus(componentStatusSchema.enum.discontinued)
  }

  /**
   * Create pending status
   */
  static pending(): ComponentStatus {
    return new ComponentStatus(componentStatusSchema.enum.pending)
  }

  /**
   * Create obsolete status
   */
  static obsolete(): ComponentStatus {
    return new ComponentStatus(componentStatusSchema.enum.obsolete)
  }

  /**
   * Create ComponentStatus from string value
   */
  static fromString(value: string): ComponentStatus {
    const trimmedValue = value.trim().toLowerCase()
    
    switch (trimmedValue) {
      case 'active':
        return ComponentStatus.active()
      case 'inactive':
        return ComponentStatus.inactive()
      case 'discontinued':
        return ComponentStatus.discontinued()
      case 'pending':
        return ComponentStatus.pending()
      case 'obsolete':
        return ComponentStatus.obsolete()
      default:
        throw ComponentDomainError.invalidComponent(
          "status",
          value,
          `Invalid component status: ${value}`
        )
    }
  }

  // Business Logic Methods

  /**
   * Check if component is available for use
   */
  isAvailable(): boolean {
    return this._value === componentStatusSchema.enum.active
  }

  /**
   * Check if component is active
   */
  isActive(): boolean {
    return this._value === componentStatusSchema.enum.active
  }

  /**
   * Check if component is inactive
   */
  isInactive(): boolean {
    return this._value === componentStatusSchema.enum.inactive
  }

  /**
   * Check if component is discontinued
   */
  isDiscontinued(): boolean {
    return (
      this._value === componentStatusSchema.enum.discontinued ||
      this._value === componentStatusSchema.enum.obsolete
    )
  }

  /**
   * Check if component is pending approval
   */
  isPending(): boolean {
    return this._value === componentStatusSchema.enum.pending
  }

  /**
   * Check if status can transition to another status
   */
  canTransitionTo(newStatus: ComponentStatus): boolean {
    const currentStatus = this._value
    const targetStatus = newStatus._value

    // Define valid status transitions
    const allowedTransitions: Record<
      ComponentStatusEnum,
      ComponentStatusEnum[]
    > = {
      [componentStatusSchema.enum.pending]: [
        componentStatusSchema.enum.active,
        componentStatusSchema.enum.inactive,
        componentStatusSchema.enum.discontinued,
      ],
      [componentStatusSchema.enum.active]: [
        componentStatusSchema.enum.inactive,
        componentStatusSchema.enum.discontinued,
        componentStatusSchema.enum.obsolete,
      ],
      [componentStatusSchema.enum.inactive]: [
        componentStatusSchema.enum.active,
        componentStatusSchema.enum.discontinued,
        componentStatusSchema.enum.obsolete,
      ],
      [componentStatusSchema.enum.discontinued]: [componentStatusSchema.enum.obsolete],
      [componentStatusSchema.enum.obsolete]: [], // Terminal state
    }

    return allowedTransitions[currentStatus]?.includes(targetStatus) || false
  }

  /**
   * Get display name for status
   */
  getDisplayName(): string {
    const displayNames: Record<ComponentStatusEnum, string> = {
      [componentStatusSchema.enum.active]: "Active",
      [componentStatusSchema.enum.inactive]: "Inactive",
      [componentStatusSchema.enum.discontinued]: "Discontinued",
      [componentStatusSchema.enum.pending]: "Pending Approval",
      [componentStatusSchema.enum.obsolete]: "Obsolete",
    }

    return displayNames[this._value]
  }

  /**
   * Get CSS class for status display
   */
  getCssClass(): string {
    const cssClasses: Record<ComponentStatusEnum, string> = {
      [componentStatusSchema.enum.active]: "text-green-600 bg-green-100",
      [componentStatusSchema.enum.inactive]: "text-gray-600 bg-gray-100",
      [componentStatusSchema.enum.discontinued]: "text-red-600 bg-red-100",
      [componentStatusSchema.enum.pending]: "text-yellow-600 bg-yellow-100",
      [componentStatusSchema.enum.obsolete]: "text-red-800 bg-red-200",
    }

    return cssClasses[this._value]
  }

  /**
   * Get priority order for sorting
   */
  getPriority(): number {
    const priorities: Record<ComponentStatusEnum, number> = {
      [componentStatusSchema.enum.active]: 1,
      [componentStatusSchema.enum.pending]: 2,
      [componentStatusSchema.enum.inactive]: 3,
      [componentStatusSchema.enum.discontinued]: 4,
      [componentStatusSchema.enum.obsolete]: 5,
    }

    return priorities[this._value]
  }

  // Value Object Implementation

  /**
   * Get the raw status value
   */
  getValue(): ComponentStatusEnum {
    return this._value
  }

  /**
   * Convert to string representation
   */
  toString(): string {
    return this._value
  }

  /**
   * Convert to JSON representation
   */
  toJSON(): string {
    return this._value
  }

  /**
   * Check equality with another ComponentStatus
   */
  equals(other: ComponentStatus): boolean {
    return this._value === other._value
  }

  /**
   * Get hash code for this status
   */
  hashCode(): string {
    return `status-${this._value}`
  }

  /**
   * Compare with another ComponentStatus for sorting
   */
  compareTo(other: ComponentStatus): number {
    return this.getPriority() - other.getPriority()
  }

  // Validation

  private validate(): void {
    if (!Object.values(componentStatusSchema.enum).includes(this._value)) {
      throw ComponentDomainError.invalidComponent(
        "status",
        this._value,
        "Invalid component status"
      )
    }
  }
}
