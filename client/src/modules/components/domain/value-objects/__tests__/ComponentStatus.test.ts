/**
 * ComponentStatus Value Object Tests
 */

import { describe, expect, it } from "vitest"
import { ComponentDomainError } from "../../errors/ComponentDomainError"
import { ComponentStatus } from "../ComponentStatus"
import { componentStatusSchema } from "@/types/api"

describe("ComponentStatus Value Object", () => {
  describe("Creation", () => {
    it("should create status from valid string", () => {
      const status = ComponentStatus.fromString("active")
      expect(status.getValue()).toBe(componentStatusSchema.enum.active)
    })

    it("should create status from string with different cases", () => {
      const status = ComponentStatus.fromString("ACTIVE")
      expect(status.getValue()).toBe(componentStatusSchema.enum.active)
    })

    it("should create status from string with whitespace", () => {
      const status = ComponentStatus.fromString("  active  ")
      expect(status.getValue()).toBe(componentStatusSchema.enum.active)
    })

    it("should throw error for invalid status string", () => {
      expect(() => ComponentStatus.fromString("invalid")).toThrow(
        ComponentDomainError
      )
    })

    it("should create active status", () => {
      const status = ComponentStatus.active()
      expect(status.getValue()).toBe(componentStatusSchema.enum.active)
    })

    it("should create inactive status", () => {
      const status = ComponentStatus.inactive()
      expect(status.getValue()).toBe(componentStatusSchema.enum.inactive)
    })

    it("should create discontinued status", () => {
      const status = ComponentStatus.discontinued()
      expect(status.getValue()).toBe(componentStatusSchema.enum.discontinued)
    })
  })

  describe("Business Logic", () => {
    it("should check if active status is available", () => {
      const status = ComponentStatus.active()
      expect(status.isAvailable()).toBe(true)
    })

    it("should check if inactive status is not available", () => {
      const status = ComponentStatus.inactive()
      expect(status.isAvailable()).toBe(false)
    })

    it("should check if discontinued status is discontinued", () => {
      const status = ComponentStatus.discontinued()
      expect(status.isDiscontinued()).toBe(true)
    })

    it("should check if obsolete status is discontinued", () => {
      const status = ComponentStatus.obsolete()
      expect(status.isDiscontinued()).toBe(true)
    })

    it("should check pending status", () => {
      const status = ComponentStatus.pending()
      expect(status.isPending()).toBe(true)
    })
  })

  describe("Status Transitions", () => {
    it("should allow valid transitions from pending", () => {
      const pending = ComponentStatus.pending()
      const active = ComponentStatus.active()

      expect(pending.canTransitionTo(active)).toBe(true)
    })

    it("should allow transition from active to inactive", () => {
      const active = ComponentStatus.active()
      const inactive = ComponentStatus.inactive()

      expect(active.canTransitionTo(inactive)).toBe(true)
    })

    it("should not allow transition from obsolete", () => {
      const obsolete = ComponentStatus.obsolete()
      const active = ComponentStatus.active()

      expect(obsolete.canTransitionTo(active)).toBe(false)
    })

    it("should not allow invalid transitions", () => {
      const discontinued = ComponentStatus.discontinued()
      const active = ComponentStatus.active()

      expect(discontinued.canTransitionTo(active)).toBe(false)
    })
  })

  describe("Display Methods", () => {
    it("should get correct display name", () => {
      const status = ComponentStatus.active()
      expect(status.getDisplayName()).toBe("Active")
    })

    it("should get correct CSS class", () => {
      const status = ComponentStatus.active()
      expect(status.getCssClass()).toBe("text-green-600 bg-green-100")
    })

    it("should get correct priority", () => {
      const active = ComponentStatus.active()
      const inactive = ComponentStatus.inactive()

      expect(active.getPriority()).toBeLessThan(inactive.getPriority())
    })
  })

  describe("Value Object Behavior", () => {
    it("should check equality correctly", () => {
      const status1 = ComponentStatus.active()
      const status2 = ComponentStatus.active()
      const status3 = ComponentStatus.inactive()

      expect(status1.equals(status2)).toBe(true)
      expect(status1.equals(status3)).toBe(false)
    })

    it("should generate consistent hash code", () => {
      const status = ComponentStatus.active()
      expect(status.hashCode()).toBe("status-active")
    })

    it("should convert to string", () => {
      const status = ComponentStatus.active()
      expect(status.toString()).toBe("active")
    })

    it("should compare for sorting", () => {
      const active = ComponentStatus.active()
      const inactive = ComponentStatus.inactive()

      expect(active.compareTo(inactive)).toBeLessThan(0)
      expect(inactive.compareTo(active)).toBeGreaterThan(0)
      expect(active.compareTo(ComponentStatus.active())).toBe(0)
    })
  })
})
