/**
 * Specifications Value Object
 *
 * Represents electrical component specifications with validation
 * and engineering standards compliance.
 */

import type { ComponentSpecifications } from "@/modules/components/api"

import { ComponentDomainError } from "../errors/ComponentDomainError"

/**
 * Electrical specification categories
 */
export interface ElectricalSpecs {
  voltage?: string | number
  current?: string | number
  power?: string | number
  frequency?: string | number
  resistance?: string | number
  capacitance?: string | number
  inductance?: string | number
  [key: string]: any
}

/**
 * Mechanical specification categories
 */
export interface MechanicalSpecs {
  mounting?: string
  enclosure?: string
  material?: string
  finish?: string
  connector_type?: string
  [key: string]: any
}

/**
 * Environmental specification categories
 */
export interface EnvironmentalSpecs {
  ip_rating?: string
  operating_temp_min?: number
  operating_temp_max?: number
  humidity_max?: number
  altitude_max?: number
  vibration_resistance?: string
  [key: string]: any
}

/**
 * Standards compliance specification
 */
export interface StandardsCompliance {
  iec?: string[]
  ieee?: string[]
  en?: string[]
  ul?: string[]
  ce?: boolean
  rohs?: boolean
  [key: string]: any
}

/**
 * Complete specification structure
 */
export interface SpecificationData {
  electrical?: ElectricalSpecs
  mechanical?: MechanicalSpecs
  environmental?: EnvironmentalSpecs
  standards_compliance?: StandardsCompliance
  thermal?: Record<string, any>
  [category: string]: any
}

/**
 * Specifications Value Object
 *
 * Immutable value object that encapsulates component specifications
 * with validation and engineering standards compliance.
 */
export class Specifications {
  private readonly _data: SpecificationData

  constructor(data: ComponentSpecifications | SpecificationData) {
    this._data = this.normalizeData(data)
    this.validate()
  }

  /**
   * Create empty specifications
   */
  static empty(): Specifications {
    return new Specifications({})
  }

  /**
   * Create specifications from electrical parameters
   */
  static fromElectrical(electrical: ElectricalSpecs): Specifications {
    return new Specifications({ electrical })
  }

  /**
   * Create specifications with all categories
   */
  static create(data: SpecificationData): Specifications {
    return new Specifications(data)
  }

  // Specification Access Methods

  /**
   * Get electrical specifications
   */
  getElectrical(): ElectricalSpecs {
    return this._data.electrical || {}
  }

  /**
   * Get mechanical specifications
   */
  getMechanical(): MechanicalSpecs {
    return this._data.mechanical || {}
  }

  /**
   * Get environmental specifications
   */
  getEnvironmental(): EnvironmentalSpecs {
    return this._data.environmental || {}
  }

  /**
   * Get standards compliance information
   */
  getStandardsCompliance(): StandardsCompliance {
    return this._data.standards_compliance || {}
  }

  /**
   * Get thermal specifications
   */
  getThermal(): Record<string, any> {
    return this._data.thermal || {}
  }

  /**
   * Get specification by category
   */
  getCategory(category: string): Record<string, any> {
    return this._data[category] || {}
  }

  /**
   * Get all specification data
   */
  getAllData(): SpecificationData {
    return { ...this._data }
  }

  // Business Logic Methods

  /**
   * Check if specifications are empty
   */
  isEmpty(): boolean {
    return (
      Object.keys(this._data).length === 0 ||
      Object.values(this._data).every(
        (category) => !category || Object.keys(category).length === 0
      )
    )
  }

  /**
   * Check if has electrical specifications
   */
  hasElectrical(): boolean {
    return (
      this._data.electrical !== undefined &&
      Object.keys(this._data.electrical).length > 0
    )
  }

  /**
   * Check if has mechanical specifications
   */
  hasMechanical(): boolean {
    return (
      this._data.mechanical !== undefined &&
      Object.keys(this._data.mechanical).length > 0
    )
  }

  /**
   * Check if has environmental specifications
   */
  hasEnvironmental(): boolean {
    return (
      this._data.environmental !== undefined &&
      Object.keys(this._data.environmental).length > 0
    )
  }

  /**
   * Check if has standards compliance information
   */
  hasStandardsCompliance(): boolean {
    return (
      this._data.standards_compliance !== undefined &&
      Object.keys(this._data.standards_compliance).length > 0
    )
  }

  /**
   * Get voltage rating if available
   */
  getVoltageRating(): string | number | null {
    return this._data.electrical?.voltage || null
  }

  /**
   * Get current rating if available
   */
  getCurrentRating(): string | number | null {
    return this._data.electrical?.current || null
  }

  /**
   * Get power rating if available
   */
  getPowerRating(): string | number | null {
    return this._data.electrical?.power || null
  }

  /**
   * Get IP rating if available
   */
  getIpRating(): string | null {
    return this._data.environmental?.ip_rating || null
  }

  /**
   * Get operating temperature range
   */
  getOperatingTemperatureRange(): { min?: number; max?: number } | null {
    const env = this._data.environmental
    if (!env) return null

    const min = env.operating_temp_min
    const max = env.operating_temp_max

    if (min !== undefined || max !== undefined) {
      return { min, max }
    }

    return null
  }

  /**
   * Check if component is CE marked
   */
  isCeMarked(): boolean {
    return this._data.standards_compliance?.ce === true
  }

  /**
   * Check if component is RoHS compliant
   */
  isRohsCompliant(): boolean {
    return this._data.standards_compliance?.rohs === true
  }

  /**
   * Get applicable IEC standards
   */
  getIecStandards(): string[] {
    return this._data.standards_compliance?.iec || []
  }

  /**
   * Get applicable IEEE standards
   */
  getIeeeStandards(): string[] {
    return this._data.standards_compliance?.ieee || []
  }

  /**
   * Check if specifications are valid for electrical use
   */
  isValidForElectricalUse(): boolean {
    const electrical = this._data.electrical
    if (!electrical) return false

    // Basic electrical parameters should be present
    return !!(electrical.voltage || electrical.current || electrical.power)
  }

  /**
   * Get specification completeness score (0-1)
   */
  getCompletenessScore(): number {
    let score = 0
    let maxScore = 0

    // Check electrical specifications (weight: 0.4)
    maxScore += 0.4
    if (this.hasElectrical()) {
      const electrical = this._data.electrical!
      const electricalParams = ["voltage", "current", "power", "frequency"]
      const presentParams = electricalParams.filter(
        (param) => electrical[param] !== undefined
      )
      score += (presentParams.length / electricalParams.length) * 0.4
    }

    // Check mechanical specifications (weight: 0.2)
    maxScore += 0.2
    if (this.hasMechanical()) {
      score += 0.2
    }

    // Check environmental specifications (weight: 0.2)
    maxScore += 0.2
    if (this.hasEnvironmental()) {
      score += 0.2
    }

    // Check standards compliance (weight: 0.2)
    maxScore += 0.2
    if (this.hasStandardsCompliance()) {
      score += 0.2
    }

    return maxScore > 0 ? score / maxScore : 0
  }

  // Transformation Methods

  /**
   * Add or update electrical specifications
   */
  withElectrical(electrical: ElectricalSpecs): Specifications {
    return new Specifications({
      ...this._data,
      electrical: { ...this._data.electrical, ...electrical },
    })
  }

  /**
   * Add or update mechanical specifications
   */
  withMechanical(mechanical: MechanicalSpecs): Specifications {
    return new Specifications({
      ...this._data,
      mechanical: { ...this._data.mechanical, ...mechanical },
    })
  }

  /**
   * Add or update environmental specifications
   */
  withEnvironmental(environmental: EnvironmentalSpecs): Specifications {
    return new Specifications({
      ...this._data,
      environmental: { ...this._data.environmental, ...environmental },
    })
  }

  /**
   * Add or update standards compliance
   */
  withStandardsCompliance(standards: StandardsCompliance): Specifications {
    return new Specifications({
      ...this._data,
      standards_compliance: {
        ...this._data.standards_compliance,
        ...standards,
      },
    })
  }

  /**
   * Add or update category
   */
  withCategory(category: string, data: Record<string, any>): Specifications {
    return new Specifications({
      ...this._data,
      [category]: { ...this._data[category], ...data },
    })
  }

  /**
   * Remove category
   */
  withoutCategory(category: string): Specifications {
    const newData = { ...this._data }
    delete newData[category]
    return new Specifications(newData)
  }

  // Value Object Implementation

  /**
   * Check equality with another Specifications
   */
  equals(other: Specifications): boolean {
    return JSON.stringify(this._data) === JSON.stringify(other._data)
  }

  /**
   * Get hash code
   */
  hashCode(): string {
    return `specs-${JSON.stringify(this._data)}`
  }

  /**
   * Convert to string representation
   */
  toString(): string {
    const categories = Object.keys(this._data)
    if (categories.length === 0) return "Empty specifications"

    return `Specifications: ${categories.join(", ")}`
  }

  /**
   * Convert to JSON representation
   */
  toJSON(): SpecificationData {
    return { ...this._data }
  }

  /**
   * Convert to API format
   */
  toApiFormat(): ComponentSpecifications {
    return this._data as ComponentSpecifications
  }

  // Validation and Normalization

  private normalizeData(
    data: ComponentSpecifications | SpecificationData
  ): SpecificationData {
    if (!data || typeof data !== "object") {
      return {}
    }

    const normalized: SpecificationData = {}

    // Copy all data, ensuring proper structure
    for (const [key, value] of Object.entries(data)) {
      if (value && typeof value === "object" && !Array.isArray(value)) {
        normalized[key] = { ...value }
      } else if (value !== undefined && value !== null) {
        normalized[key] = value
      }
    }

    return normalized
  }

  private validate(): void {
    // Validate electrical specifications
    if (this._data.electrical) {
      this.validateElectricalSpecs(this._data.electrical)
    }

    // Validate environmental specifications
    if (this._data.environmental) {
      this.validateEnvironmentalSpecs(this._data.environmental)
    }

    // Validate standards compliance
    if (this._data.standards_compliance) {
      this.validateStandardsCompliance(this._data.standards_compliance)
    }
  }

  private validateElectricalSpecs(electrical: ElectricalSpecs): void {
    // Validate voltage
    if (electrical.voltage !== undefined) {
      const voltage = electrical.voltage
      if (typeof voltage === "number" && voltage < 0) {
        throw ComponentDomainError.invalidSpecification(
          "Voltage cannot be negative",
          electrical
        )
      }
    }

    // Validate current
    if (electrical.current !== undefined) {
      const current = electrical.current
      if (typeof current === "number" && current < 0) {
        throw ComponentDomainError.invalidSpecification(
          "Current cannot be negative",
          electrical
        )
      }
    }

    // Validate power
    if (electrical.power !== undefined) {
      const power = electrical.power
      if (typeof power === "number" && power < 0) {
        throw ComponentDomainError.invalidSpecification(
          "Power cannot be negative",
          electrical
        )
      }
    }
  }

  private validateEnvironmentalSpecs(environmental: EnvironmentalSpecs): void {
    // Validate temperature range
    if (
      environmental.operating_temp_min !== undefined &&
      environmental.operating_temp_max !== undefined
    ) {
      if (environmental.operating_temp_min > environmental.operating_temp_max) {
        throw ComponentDomainError.invalidSpecification(
          "Minimum operating temperature cannot be higher than maximum",
          environmental
        )
      }
    }

    // Validate IP rating format
    if (environmental.ip_rating) {
      const ipRating = environmental.ip_rating.toString().toUpperCase()
      if (!/^IP\d{2}$/.test(ipRating)) {
        throw ComponentDomainError.invalidSpecification(
          "IP rating must be in format IPXX (e.g., IP65)",
          environmental
        )
      }
    }

    // Validate humidity
    if (environmental.humidity_max !== undefined) {
      if (environmental.humidity_max < 0 || environmental.humidity_max > 100) {
        throw ComponentDomainError.invalidSpecification(
          "Humidity must be between 0 and 100 percent",
          environmental
        )
      }
    }
  }

  private validateStandardsCompliance(standards: StandardsCompliance): void {
    // Validate IEC standards format
    if (standards.iec) {
      for (const standard of standards.iec) {
        if (!/^IEC\s?\d+(-\d+)*/.test(standard)) {
          throw ComponentDomainError.invalidSpecification(
            `Invalid IEC standard format: ${standard}`,
            standards
          )
        }
      }
    }

    // Validate IEEE standards format
    if (standards.ieee) {
      for (const standard of standards.ieee) {
        if (!/^IEEE\s?\d+/.test(standard)) {
          throw ComponentDomainError.invalidSpecification(
            `Invalid IEEE standard format: ${standard}`,
            standards
          )
        }
      }
    }

    // Validate EN standards format
    if (standards.en) {
      for (const standard of standards.en) {
        if (!/^EN\s?\d+/.test(standard)) {
          throw ComponentDomainError.invalidSpecification(
            `Invalid EN standard format: ${standard}`,
            standards
          )
        }
      }
    }
  }
}
