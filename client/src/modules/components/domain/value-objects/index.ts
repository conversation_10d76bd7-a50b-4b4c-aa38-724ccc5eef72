/**
 * Domain Value Objects - Component Catalog Context
 *
 * Value Objects represent concepts that are defined by their attributes
 * rather than their identity. They are immutable and equality is based on
 * their values.
 */

// Core Value Objects
export { ComponentStatus, ComponentStatusType } from "./ComponentStatus"
export { Specifications } from "./Specifications"
export type {
  SpecificationData,
  ElectricalSpecs,
  MechanicalSpecs,
  EnvironmentalSpecs,
  StandardsCompliance,
} from "./Specifications"

export { PriceValue, PriceComparison, SUPPORTED_CURRENCIES } from "./PriceValue"
export type { CurrencyInfo } from "./PriceValue"

// Future value objects
// export { Dimensions } from './Dimensions'
