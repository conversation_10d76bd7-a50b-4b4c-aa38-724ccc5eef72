/**
 * PriceValue Value Object
 *
 * Represents monetary price with currency and validation.
 * Encapsulates price-related business logic and formatting.
 */

import { PriceComparisonEnum, priceComparisonSchema } from "@/types/api"
import { ComponentDomainError } from "../errors/ComponentDomainError"

/**
 * Supported currencies with their properties
 */
export interface CurrencyInfo {
  code: string
  symbol: string
  decimals: number
  name: string
}

/**
 * Currency definitions
 */
export const SUPPORTED_CURRENCIES: Record<string, CurrencyInfo> = {
  USD: { code: "USD", symbol: "$", decimals: 2, name: "US Dollar" },
  EUR: { code: "EUR", symbol: "€", decimals: 2, name: "Euro" },
  GBP: { code: "GBP", symbol: "£", decimals: 2, name: "British Pound" },
  JPY: { code: "JPY", symbol: "¥", decimals: 0, name: "Japanese Yen" },
  CAD: { code: "CAD", symbol: "C$", decimals: 2, name: "Canadian Dollar" },
  AUD: { code: "AUD", symbol: "A$", decimals: 2, name: "Australian Dollar" },
  CHF: { code: "CHF", symbol: "CHF", decimals: 2, name: "Swiss Franc" },
  CNY: { code: "CNY", symbol: "¥", decimals: 2, name: "Chinese Yuan" },
  SEK: { code: "SEK", symbol: "kr", decimals: 2, name: "Swedish Krona" },
  NOK: { code: "NOK", symbol: "kr", decimals: 2, name: "Norwegian Krone" },
  DKK: { code: "DKK", symbol: "kr", decimals: 2, name: "Danish Krone" },
  PLN: { code: "PLN", symbol: "zł", decimals: 2, name: "Polish Zloty" },
}

/**
 * PriceValue Value Object
 *
 * Immutable value object representing a monetary price with currency.
 * Provides validation, formatting, and comparison capabilities.
 */
export class PriceValue {
  private readonly _amount: number
  private readonly _currency: string
  private readonly _currencyInfo: CurrencyInfo

  private constructor(amount: number, currency: string) {
    this._amount = amount
    this._currency = currency.toUpperCase()
    this._currencyInfo = SUPPORTED_CURRENCIES[this._currency]

    this.validate()
  }

  /**
   * Create PriceValue from amount and currency
   */
  static from(amount: number, currency: string): PriceValue {
    return new PriceValue(amount, currency)
  }

  /**
   * Create PriceValue from string representation
   */
  static fromString(priceString: string): PriceValue {
    // Parse formats like "25.99 USD", "$25.99", "€15.50"
    const cleanString = priceString.trim()

    // Try to match currency symbol patterns
    const symbolMatches = cleanString.match(
      /^([€$£¥]|CHF|kr|zł)\s*([0-9,]+\.?\d*)$/i
    )
    if (symbolMatches) {
      const symbol = symbolMatches[1]
      const amount = parseFloat(symbolMatches[2].replace(/,/g, ""))

      // Find currency by symbol
      const currency = Object.entries(SUPPORTED_CURRENCIES).find(
        ([_, info]) => info.symbol.toLowerCase() === symbol.toLowerCase()
      )

      if (currency && !isNaN(amount)) {
        return new PriceValue(amount, currency[0])
      }
    }

    // Try to match amount + currency code patterns
    const codeMatches = cleanString.match(/^([0-9,]+\.?\d*)\s*([A-Z]{3})$/i)
    if (codeMatches) {
      const amount = parseFloat(codeMatches[1].replace(/,/g, ""))
      const currency = codeMatches[2].toUpperCase()

      if (!isNaN(amount) && SUPPORTED_CURRENCIES[currency]) {
        return new PriceValue(amount, currency)
      }
    }

    throw ComponentDomainError.invalidComponent(
      "price",
      priceString,
      'Invalid price format. Use formats like "25.99 USD", "$25.99", or "€15.50"'
    )
  }

  /**
   * Create zero price
   */
  static zero(currency: string = "EUR"): PriceValue {
    return new PriceValue(0, currency)
  }

  /**
   * Create free price (explicitly zero)
   */
  static free(currency: string = "EUR"): PriceValue {
    return new PriceValue(0, currency)
  }

  // Getters

  /**
   * Get the numeric amount
   */
  getAmount(): number {
    return this._amount
  }

  /**
   * Get the currency code
   */
  getCurrency(): string {
    return this._currency
  }

  /**
   * Get currency information
   */
  getCurrencyInfo(): CurrencyInfo {
    return { ...this._currencyInfo }
  }

  /**
   * Get currency symbol
   */
  getCurrencySymbol(): string {
    return this._currencyInfo.symbol
  }

  /**
   * Get currency name
   */
  getCurrencyName(): string {
    return this._currencyInfo.name
  }

  // Business Logic Methods

  /**
   * Check if price is zero
   */
  isZero(): boolean {
    return this._amount === 0
  }

  /**
   * Check if price is free (zero with explicit free designation)
   */
  isFree(): boolean {
    return this._amount === 0
  }

  /**
   * Check if price is positive
   */
  isPositive(): boolean {
    return this._amount > 0
  }

  /**
   * Check if price is negative
   */
  isNegative(): boolean {
    return this._amount < 0
  }

  /**
   * Check if this price is in the same currency as another
   */
  hasSameCurrency(other: PriceValue): boolean {
    return this._currency === other._currency
  }

  /**
   * Compare with another price (must be same currency)
   */
  compareTo(other: PriceValue): PriceComparisonEnum {
    if (!this.hasSameCurrency(other)) {
      throw ComponentDomainError.businessRuleViolation(
        "Cannot compare prices in different currencies",
        {
          thisCurrency: this._currency,
          otherCurrency: other._currency,
        }
      )
    }

    if (this._amount < other._amount) return priceComparisonSchema.enum.lower
    if (this._amount > other._amount) return priceComparisonSchema.enum.higher
    return priceComparisonSchema.enum.equal
  }

  /**
   * Check if this price is less than another
   */
  isLessThan(other: PriceValue): boolean {
    return this.compareTo(other) === priceComparisonSchema.enum.lower
  }

  /**
   * Check if this price is greater than another
   */
  isGreaterThan(other: PriceValue): boolean {
    return this.compareTo(other) === priceComparisonSchema.enum.higher
  }

  /**
   * Check if this price equals another
   */
  isEqualTo(other: PriceValue): boolean {
    return this.compareTo(other) === priceComparisonSchema.enum.equal
  }

  /**
   * Check if price is within range (inclusive)
   */
  isInRange(min: PriceValue, max: PriceValue): boolean {
    return !this.isLessThan(min) && !this.isGreaterThan(max)
  }

  // Transformation Methods

  /**
   * Add to this price (must be same currency)
   */
  add(other: PriceValue): PriceValue {
    if (!this.hasSameCurrency(other)) {
      throw ComponentDomainError.businessRuleViolation(
        "Cannot add prices in different currencies",
        {
          thisCurrency: this._currency,
          otherCurrency: other._currency,
        }
      )
    }

    return new PriceValue(this._amount + other._amount, this._currency)
  }

  /**
   * Subtract from this price (must be same currency)
   */
  subtract(other: PriceValue): PriceValue {
    if (!this.hasSameCurrency(other)) {
      throw ComponentDomainError.businessRuleViolation(
        "Cannot subtract prices in different currencies",
        {
          thisCurrency: this._currency,
          otherCurrency: other._currency,
        }
      )
    }

    return new PriceValue(this._amount - other._amount, this._currency)
  }

  /**
   * Multiply by a factor
   */
  multiply(factor: number): PriceValue {
    if (factor < 0) {
      throw ComponentDomainError.invalidComponent(
        "factor",
        factor,
        "Multiplication factor cannot be negative"
      )
    }

    return new PriceValue(this._amount * factor, this._currency)
  }

  /**
   * Apply discount percentage (0-100)
   */
  applyDiscount(discountPercent: number): PriceValue {
    if (discountPercent < 0 || discountPercent > 100) {
      throw ComponentDomainError.invalidComponent(
        "discount",
        discountPercent,
        "Discount percentage must be between 0 and 100"
      )
    }

    const discountFactor = (100 - discountPercent) / 100
    return this.multiply(discountFactor)
  }

  /**
   * Apply markup percentage
   */
  applyMarkup(markupPercent: number): PriceValue {
    if (markupPercent < 0) {
      throw ComponentDomainError.invalidComponent(
        "markup",
        markupPercent,
        "Markup percentage cannot be negative"
      )
    }

    const markupFactor = 1 + markupPercent / 100
    return this.multiply(markupFactor)
  }

  /**
   * Convert to different currency (requires exchange rate)
   */
  convertTo(targetCurrency: string, exchangeRate: number): PriceValue {
    if (exchangeRate <= 0) {
      throw ComponentDomainError.invalidComponent(
        "exchangeRate",
        exchangeRate,
        "Exchange rate must be positive"
      )
    }

    const convertedAmount = this._amount * exchangeRate
    return new PriceValue(convertedAmount, targetCurrency)
  }

  /**
   * Round to currency's decimal places
   */
  round(): PriceValue {
    const decimals = this._currencyInfo.decimals
    const factor = Math.pow(10, decimals)
    const roundedAmount = Math.round(this._amount * factor) / factor
    return new PriceValue(roundedAmount, this._currency)
  }

  // Formatting Methods

  /**
   * Format as string with currency symbol
   */
  format(options?: {
    showSymbol?: boolean
    showCode?: boolean
    locale?: string
  }): string {
    const opts = {
      showSymbol: true,
      showCode: false,
      locale: "en-US",
      ...options,
    }

    const decimals = this._currencyInfo.decimals
    const formattedAmount = this._amount.toLocaleString(opts.locale, {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    })

    if (opts.showSymbol && opts.showCode) {
      return `${this._currencyInfo.symbol}${formattedAmount} ${this._currency}`
    } else if (opts.showSymbol) {
      return `${this._currencyInfo.symbol}${formattedAmount}`
    } else if (opts.showCode) {
      return `${formattedAmount} ${this._currency}`
    } else {
      return formattedAmount
    }
  }

  /**
   * Format for display in lists
   */
  formatCompact(): string {
    return this.format({ showSymbol: true, showCode: false })
  }

  /**
   * Format for detailed display
   */
  formatDetailed(): string {
    return this.format({ showSymbol: true, showCode: true })
  }

  /**
   * Format for API/database storage
   */
  formatForStorage(): { amount: number; currency: string } {
    return {
      amount: this._amount,
      currency: this._currency,
    }
  }

  // Value Object Implementation

  /**
   * Check equality with another PriceValue
   */
  equals(other: PriceValue): boolean {
    return this._amount === other._amount && this._currency === other._currency
  }

  /**
   * Get hash code
   */
  hashCode(): string {
    return `price-${this._amount}-${this._currency}`
  }

  /**
   * Convert to string representation
   */
  toString(): string {
    return this.formatCompact()
  }

  /**
   * Convert to JSON representation
   */
  toJSON(): { amount: number; currency: string } {
    return this.formatForStorage()
  }

  // Validation

  private validate(): void {
    // Validate amount
    if (!Number.isFinite(this._amount)) {
      throw ComponentDomainError.invalidComponent(
        "amount",
        this._amount,
        "Price amount must be a finite number"
      )
    }

    if (this._amount < 0) {
      throw ComponentDomainError.invalidComponent(
        "amount",
        this._amount,
        "Price amount cannot be negative"
      )
    }

    // Check decimal precision
    const decimals = this._currencyInfo.decimals
    const factor = Math.pow(10, decimals)
    if (Math.round(this._amount * factor) !== this._amount * factor) {
      throw ComponentDomainError.invalidComponent(
        "amount",
        this._amount,
        `Price amount cannot have more than ${decimals} decimal places for ${this._currency}`
      )
    }

    // Validate currency
    if (!this._currencyInfo) {
      throw ComponentDomainError.invalidComponent(
        "currency",
        this._currency,
        `Unsupported currency: ${this._currency}. Supported currencies: ${Object.keys(SUPPORTED_CURRENCIES).join(", ")}`
      )
    }
  }
}
