/**
 * Domain Layer - Component Catalog Context
 *
 * This is the heart of the Component Catalog bounded context,
 * containing all domain logic, entities, value objects, and business rules.
 *
 * The domain layer is independent of any external frameworks or
 * infrastructure concerns.
 */

// Domain layer exports
export * from "./entities"
export * from "./value-objects"
export * from "./aggregates"
export * from "./domain-services"
export * from "./events"
export * from "./repositories"
export * from "./specifications"

// Domain-specific errors
export { ComponentDomainError } from "./errors/ComponentDomainError"
