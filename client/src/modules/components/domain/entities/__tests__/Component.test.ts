/**
 * Component Entity Tests
 *
 * Comprehensive test suite for the Component domain entity,
 * covering domain invariants, business logic, and edge cases.
 */

import { beforeEach, describe, expect, it, vi } from "vitest"
import type {
  ComponentDimensions,
  ComponentSpecifications,
} from "@/types/componentCategory"
import { ComponentDomainError } from "../../errors/ComponentDomainError"
import {
  Component,
  type ComponentCreateData,
  type ComponentEntityProps,
} from "../Component"

describe("Component Entity", () => {
  let validCreateData: ComponentCreateData
  let validApiData: ComponentEntityProps

  beforeEach(() => {
    validCreateData = {
      name: "Test Component",
      manufacturer: "Test Manufacturer",
      model_number: "TC-001",
      description: "A test component for unit testing",
      component_type_id: 1,
      category_id: 1,
      specifications: {
        electrical: {
          voltage: "12V",
          current: "2A",
          power: "24W",
        },
        mechanical: {
          mounting: "DIN rail",
        },
        environmental: {
          ip_rating: "IP65",
        },
      },
      unit_price: 25.99,
      currency: "EUR",
      supplier: "Test Supplier",
      part_number: "TPN-001",
      weight_kg: 0.5,
      dimensions: {
        length: 100,
        width: 50,
        height: 25,
        unit: "mm",
      },
      is_preferred: false,
      stock_status: "available",
      version: "1.0",
      metadata: {
        test: "metadata",
      },
    }

    validApiData = {
      id: 1,
      name: "Test Component",
      manufacturer: "Test Manufacturer",
      model_number: "TC-001",
      description: "A test component for unit testing",
      component_type_id: 1,
      category_id: 1,
      specifications: {
        electrical: {
          voltage: "12V",
          current: "2A",
          power: "24W",
        },
      },
      unit_price: 25.99,
      currency: "EUR",
      supplier: "Test Supplier",
      part_number: "TPN-001",
      weight_kg: 0.5,
      dimensions: {
        length: 100,
        width: 50,
        height: 25,
        unit: "mm",
      },
      is_active: true,
      is_preferred: false,
      stock_status: "available",
      version: "1.0",
      metadata: {
        test: "metadata",
      },
      created_at: "2024-01-01T10:00:00Z",
      updated_at: "2024-01-01T10:00:00Z",
    }
  })

  describe("Component Creation", () => {
    it("should create a component with valid data", () => {
      const component = Component.create(validCreateData)

      expect(component.name).toBe("Test Component")
      expect(component.manufacturer).toBe("Test Manufacturer")
      expect(component.model_number).toBe("TC-001")
      expect(component.component_type_id).toBe(1)
      expect(component.category_id).toBe(1)
      expect(component.unit_price).toBe(25.99)
      expect(component.currency).toBe("EUR")
      expect(component.is_active).toBe(true)
      expect(component.is_preferred).toBe(false)
      expect(component.stock_status).toBe("available")
    })

    it("should create a component with minimal required data", () => {
      const minimalData: ComponentCreateData = {
        name: "Minimal Component",
        manufacturer: "Min Manufacturer",
        model_number: "MIN-001",
        component_type_id: 1,
        category_id: 1,
      }

      const component = Component.create(minimalData)

      expect(component.name).toBe("Minimal Component")
      expect(component.manufacturer).toBe("Min Manufacturer")
      expect(component.model_number).toBe("MIN-001")
      expect(component.currency).toBe("EUR") // Default value
      expect(component.version).toBe("1.0") // Default value
      expect(component.stock_status).toBe("available") // Default value
      expect(component.is_active).toBe(true) // Default value
      expect(component.is_preferred).toBe(false) // Default value
    })

    it("should create component from API response", () => {
      const component = Component.fromApiResponse(validApiData)

      expect(component.id).toBe(1)
      expect(component.name).toBe("Test Component")
      expect(component.manufacturer).toBe("Test Manufacturer")
      expect(component.created_at).toEqual(new Date("2024-01-01T10:00:00Z"))
      expect(component.updated_at).toEqual(new Date("2024-01-01T10:00:00Z"))
    })
  })

  describe("Domain Invariants", () => {
    it("should enforce minimum name length", () => {
      const invalidData = { ...validCreateData, name: "" }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Name cannot be empty"
      )
    })

    it("should enforce maximum name length", () => {
      const invalidData = { ...validCreateData, name: "a".repeat(201) }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Name cannot exceed 200 characters"
      )
    })

    it("should enforce minimum manufacturer length", () => {
      const invalidData = { ...validCreateData, manufacturer: "" }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Manufacturer cannot be empty"
      )
    })

    it("should enforce maximum manufacturer length", () => {
      const invalidData = { ...validCreateData, manufacturer: "a".repeat(101) }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Manufacturer cannot exceed 100 characters"
      )
    })

    it("should enforce minimum model number length", () => {
      const invalidData = { ...validCreateData, model_number: "" }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Model number cannot be empty"
      )
    })

    it("should enforce positive component type ID", () => {
      const invalidData = { ...validCreateData, component_type_id: 0 }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Component type ID must be a positive integer"
      )
    })

    it("should enforce positive category ID", () => {
      const invalidData = { ...validCreateData, category_id: -1 }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Category ID must be a positive integer"
      )
    })

    it("should enforce non-negative price", () => {
      const invalidData = { ...validCreateData, unit_price: -10 }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Price must be non-negative"
      )
    })

    it("should enforce price decimal precision", () => {
      const invalidData = { ...validCreateData, unit_price: 10.123 }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Price must have at most 2 decimal places"
      )
    })

    it("should enforce 3-letter currency code", () => {
      const invalidData = { ...validCreateData, currency: "EURO" }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Currency must be a 3-letter ISO code"
      )
    })

    it("should enforce non-negative weight", () => {
      const invalidData = { ...validCreateData, weight_kg: -1 }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Weight must be non-negative"
      )
    })

    it("should enforce valid stock status", () => {
      const invalidData = { ...validCreateData, stock_status: "invalid_status" }

      expect(() => Component.create(invalidData)).toThrow(ComponentDomainError)
      expect(() => Component.create(invalidData)).toThrow(
        "Stock status must be one of"
      )
    })
  })

  describe("Domain Operations", () => {
    let component: Component

    beforeEach(() => {
      component = Component.create(validCreateData)
    })

    it("should update specifications", () => {
      const newSpecs: ComponentSpecifications = {
        electrical: {
          voltage: "24V",
          current: "1A",
        },
      }

      component.updateSpecifications(newSpecs)

      expect(component.specifications).toEqual(newSpecs)
    })

    it("should update pricing", () => {
      component.updatePricing(15.5, "USD")

      expect(component.unit_price).toBe(15.5)
      expect(component.currency).toBe("USD")
    })

    it("should mark as preferred", () => {
      expect(component.is_preferred).toBe(false)

      component.markAsPreferred()

      expect(component.is_preferred).toBe(true)
    })

    it("should remove preferred status", () => {
      component.markAsPreferred()
      expect(component.is_preferred).toBe(true)

      component.removePreferredStatus()

      expect(component.is_preferred).toBe(false)
    })

    it("should activate component", () => {
      component.deactivate()
      expect(component.is_active).toBe(false)

      component.activate()

      expect(component.is_active).toBe(true)
    })

    it("should deactivate component", () => {
      expect(component.is_active).toBe(true)

      component.deactivate()

      expect(component.is_active).toBe(false)
    })

    it("should update stock status", () => {
      component.updateStockStatus("limited")

      expect(component.stock_status).toBe("limited")
    })

    it("should update component with partial data", () => {
      const updateData = {
        name: "Updated Component",
        unit_price: 30.0,
        is_preferred: true,
      }

      component.update(updateData)

      expect(component.name).toBe("Updated Component")
      expect(component.unit_price).toBe(30.0)
      expect(component.is_preferred).toBe(true)
      // Other fields should remain unchanged
      expect(component.manufacturer).toBe("Test Manufacturer")
      expect(component.model_number).toBe("TC-001")
    })
  })

  describe("Business Logic", () => {
    let component: Component

    beforeEach(() => {
      component = Component.create(validCreateData)
    })

    it("should check if component is available", () => {
      expect(component.isAvailable()).toBe(true)

      component.deactivate()
      expect(component.isAvailable()).toBe(false)

      component.activate()
      component.updateStockStatus("out_of_stock")
      expect(component.isAvailable()).toBe(false)

      component.updateStockStatus("discontinued")
      expect(component.isAvailable()).toBe(false)
    })

    it("should check if component is preferred", () => {
      expect(component.isPreferred()).toBe(false)

      component.markAsPreferred()
      expect(component.isPreferred()).toBe(true)
    })

    it("should check if component is active", () => {
      expect(component.isActive()).toBe(true)

      component.deactivate()
      expect(component.isActive()).toBe(false)
    })

    it("should get display name", () => {
      expect(component.getDisplayName()).toBe("Test Manufacturer TC-001")
    })

    it("should get full name", () => {
      expect(component.getFullName()).toBe(
        "Test Component (Test Manufacturer TC-001)"
      )
    })

    it("should get full name when name equals model number", () => {
      const data = { ...validCreateData, name: "TC-001" }
      const comp = Component.create(data)

      expect(comp.getFullName()).toBe("Test Manufacturer TC-001")
    })

    it("should check if component has specifications", () => {
      expect(component.hasSpecifications()).toBe(true)

      const dataWithoutSpecs = { ...validCreateData }
      delete dataWithoutSpecs.specifications
      const compWithoutSpecs = Component.create(dataWithoutSpecs)

      expect(compWithoutSpecs.hasSpecifications()).toBe(false)
    })

    it("should check if component has pricing", () => {
      expect(component.hasPricing()).toBe(true)

      const dataWithoutPrice = { ...validCreateData }
      delete dataWithoutPrice.unit_price
      const compWithoutPrice = Component.create(dataWithoutPrice)

      expect(compWithoutPrice.hasPricing()).toBe(false)
    })
  })

  describe("Serialization", () => {
    let component: Component

    beforeEach(() => {
      component = Component.create(validCreateData)
    })

    it("should convert to create payload", () => {
      const payload = component.toCreatePayload()

      expect(payload.name).toBe("Test Component")
      expect(payload.manufacturer).toBe("Test Manufacturer")
      expect(payload.model_number).toBe("TC-001")
      expect(payload.unit_price).toBe(25.99)
      expect(payload).not.toHaveProperty("id")
      expect(payload).not.toHaveProperty("created_at")
      expect(payload).not.toHaveProperty("updated_at")
    })

    it("should convert to update payload", () => {
      const component = Component.fromApiResponse(validApiData)
      const payload = component.toUpdatePayload()

      expect(payload.id).toBe(1)
      expect(payload.name).toBe("Test Component")
      expect(payload.manufacturer).toBe("Test Manufacturer")
      expect(payload.unit_price).toBe(25.99)
    })

    it("should convert to JSON", () => {
      const component = Component.fromApiResponse(validApiData)
      const json = component.toJSON()

      expect(json.id).toBe(1)
      expect(json.name).toBe("Test Component")
      expect(json.created_at).toBe("2024-01-01T10:00:00.000Z")
      expect(json.updated_at).toBe("2024-01-01T10:00:00.000Z")
    })
  })

  describe("Equality and Hashing", () => {
    it("should check equality based on ID", () => {
      const comp1 = Component.fromApiResponse(validApiData)
      const comp2 = Component.fromApiResponse({
        ...validApiData,
        name: "Different Name",
      })
      const comp3 = Component.fromApiResponse({ ...validApiData, id: 2 })

      expect(comp1.equals(comp2)).toBe(true) // Same ID
      expect(comp1.equals(comp3)).toBe(false) // Different ID
    })

    it("should generate consistent hash code", () => {
      const component = Component.fromApiResponse(validApiData)

      expect(component.hashCode()).toBe("component-1")
    })
  })

  describe("Edge Cases", () => {
    it("should handle null/undefined optional fields", () => {
      const dataWithNulls: ComponentCreateData = {
        name: "Test Component",
        manufacturer: "Test Manufacturer",
        model_number: "TC-001",
        component_type_id: 1,
        category_id: 1,
        description: undefined,
        unit_price: undefined,
        weight_kg: undefined,
      }

      const component = Component.create(dataWithNulls)

      expect(component.description).toBe(null)
      expect(component.unit_price).toBe(null)
      expect(component.weight_kg).toBe(null)
    })

    it("should handle empty specifications object", () => {
      const dataWithEmptySpecs = {
        ...validCreateData,
        specifications: {},
      }

      const component = Component.create(dataWithEmptySpecs)

      expect(component.hasSpecifications()).toBe(false)
    })

    it("should validate specifications object type", () => {
      const dataWithInvalidSpecs = {
        ...validCreateData,
        specifications: "invalid" as any,
      }

      expect(() => Component.create(dataWithInvalidSpecs)).toThrow(
        ComponentDomainError
      )
      expect(() => Component.create(dataWithInvalidSpecs)).toThrow(
        "Specifications must be an object"
      )
    })
  })

  describe("Timestamp Management", () => {
    it("should update timestamp on domain operations", () => {
      vi.useFakeTimers()
      const component = Component.create(validCreateData)
      const originalUpdatedAt = component.updated_at

      // Advance time
      vi.advanceTimersByTime(1000)

      component.markAsPreferred()

      expect(component.updated_at).not.toEqual(originalUpdatedAt)
      expect(component.updated_at.getTime()).toBeGreaterThan(
        originalUpdatedAt.getTime()
      )

      vi.useRealTimers()
    })

    it("should not change created_at on updates", () => {
      const component = Component.fromApiResponse(validApiData)
      const originalCreatedAt = component.created_at

      component.markAsPreferred()

      expect(component.created_at).toEqual(originalCreatedAt)
    })
  })
})
