/**
 * Domain Entities - Component Catalog Context
 *
 * Entities represent objects with distinct identity and lifecycle.
 * They encapsulate business logic and domain invariants.
 */

// Entity exports
export { Component } from "./Component"
export type {
  ComponentEntityProps,
  ComponentCreateData,
  ComponentUpdateData,
} from "./Component"

// Placeholder for future entity exports
// export { ComponentCategory } from './ComponentCategory'
// export { ComponentType } from './ComponentType'
