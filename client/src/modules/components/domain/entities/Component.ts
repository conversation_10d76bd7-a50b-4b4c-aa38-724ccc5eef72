/**
 * Component Entity - Component Catalog Domain
 *
 * The Component entity represents an electrical component in the catalog.
 * It encapsulates business logic, domain invariants, and component lifecycle.
 *
 * Key Domain Rules:
 * - Components must have valid name, manufacturer, and model number
 * - Component specifications must be valid according to electrical standards
 * - Price and weight must be non-negative
 * - Component status affects availability and operations
 */

import type { ComponentDimensions, ComponentSpecifications } from "@/modules/components/api"

import { ComponentDomainError } from "../errors/ComponentDomainError"

// Domain Value Objects (to be implemented)

/**
 * Props for creating a Component entity
 */
export interface ComponentEntityProps {
  id: number
  name: string
  manufacturer: string
  model_number: string
  description?: string | null
  component_type_id: number
  category_id: number
  specifications?: ComponentSpecifications
  unit_price?: number | null
  currency: string
  supplier?: string | null
  part_number?: string | null
  weight_kg?: number | null
  dimensions?: ComponentDimensions | null
  is_active: boolean
  is_preferred: boolean
  stock_status: string
  version: string
  metadata?: Record<string, any> | null
  created_at: string
  updated_at: string
}

/**
 * Data needed to create a new Component
 */
export interface ComponentCreateData {
  name: string
  manufacturer: string
  model_number: string
  description?: string
  component_type_id: number
  category_id: number
  specifications?: ComponentSpecifications
  unit_price?: number
  currency?: string
  supplier?: string
  part_number?: string
  weight_kg?: number
  dimensions?: ComponentDimensions
  is_preferred?: boolean
  stock_status?: string
  version?: string
  metadata?: Record<string, any>
}

/**
 * Data for updating a Component
 */
export interface ComponentUpdateData {
  name?: string
  manufacturer?: string
  model_number?: string
  description?: string
  component_type_id?: number
  category_id?: number
  specifications?: ComponentSpecifications
  unit_price?: number
  currency?: string
  supplier?: string
  part_number?: string
  weight_kg?: number
  dimensions?: ComponentDimensions
  is_preferred?: boolean
  stock_status?: string
  version?: string
  metadata?: Record<string, any>
}

/**
 * Component Entity
 *
 * Represents an electrical component with its properties, specifications,
 * and business rules. This entity ensures data integrity and encapsulates
 * component-specific business logic.
 */
export class Component {
  private constructor(
    private readonly _id: number,
    private _name: string,
    private _manufacturer: string,
    private _model_number: string,
    private _description: string | null,
    private _component_type_id: number,
    private _category_id: number,
    private _specifications: ComponentSpecifications | undefined,
    private _unit_price: number | null,
    private _currency: string,
    private _supplier: string | null,
    private _part_number: string | null,
    private _weight_kg: number | null,
    private _dimensions: ComponentDimensions | null,
    private _is_active: boolean,
    private _is_preferred: boolean,
    private _stock_status: string,
    private _version: string,
    private _metadata: Record<string, any> | null,
    private readonly _created_at: Date,
    private _updated_at: Date
  ) {
    this.validate()
  }

  /**
   * Create a new Component entity
   */
  static create(data: ComponentCreateData): Component {
    const now = new Date()

    return new Component(
      0, // Will be set by persistence layer
      data.name,
      data.manufacturer,
      data.model_number,
      data.description || null,
      data.component_type_id,
      data.category_id,
      data.specifications,
      data.unit_price || null,
      data.currency || "EUR",
      data.supplier || null,
      data.part_number || null,
      data.weight_kg || null,
      data.dimensions || null,
      true, // is_active defaults to true
      data.is_preferred || false,
      data.stock_status || "available",
      data.version || "1.0",
      data.metadata || null,
      now,
      now
    )
  }

  /**
   * Reconstitute Component entity from API response
   */
  static fromApiResponse(data: ComponentEntityProps): Component {
    return new Component(
      data.id,
      data.name,
      data.manufacturer,
      data.model_number,
      data.description,
      data.component_type_id,
      data.category_id,
      data.specifications,
      data.unit_price,
      data.currency,
      data.supplier,
      data.part_number,
      data.weight_kg,
      data.dimensions,
      data.is_active,
      data.is_preferred,
      data.stock_status,
      data.version,
      data.metadata,
      new Date(data.created_at),
      new Date(data.updated_at)
    )
  }

  // Domain Operations

  /**
   * Update component specifications
   */
  updateSpecifications(specifications: ComponentSpecifications): void {
    this.validateSpecifications(specifications)
    this._specifications = specifications
    this._updated_at = new Date()
  }

  /**
   * Update component pricing
   */
  updatePricing(unit_price: number, currency: string): void {
    this.validatePrice(unit_price)
    this.validateCurrency(currency)

    this._unit_price = unit_price
    this._currency = currency
    this._updated_at = new Date()
  }

  /**
   * Mark component as preferred
   */
  markAsPreferred(): void {
    this._is_preferred = true
    this._updated_at = new Date()
  }

  /**
   * Remove preferred status
   */
  removePreferredStatus(): void {
    this._is_preferred = false
    this._updated_at = new Date()
  }

  /**
   * Activate component
   */
  activate(): void {
    this._is_active = true
    this._updated_at = new Date()
  }

  /**
   * Deactivate component
   */
  deactivate(): void {
    this._is_active = false
    this._updated_at = new Date()
  }

  /**
   * Update stock status
   */
  updateStockStatus(status: string): void {
    this.validateStockStatus(status)
    this._stock_status = status
    this._updated_at = new Date()
  }

  /**
   * Update component with partial data
   */
  update(data: ComponentUpdateData): void {
    if (data.name !== undefined) {
      this.validateName(data.name)
      this._name = data.name
    }

    if (data.manufacturer !== undefined) {
      this.validateManufacturer(data.manufacturer)
      this._manufacturer = data.manufacturer
    }

    if (data.model_number !== undefined) {
      this.validateModelNumber(data.model_number)
      this._model_number = data.model_number
    }

    if (data.description !== undefined) {
      this._description = data.description || null
    }

    if (data.component_type_id !== undefined) {
      this.validateComponentTypeId(data.component_type_id)
      this._component_type_id = data.component_type_id
    }

    if (data.category_id !== undefined) {
      this.validateCategoryId(data.category_id)
      this._category_id = data.category_id
    }

    if (data.specifications !== undefined) {
      this.validateSpecifications(data.specifications)
      this._specifications = data.specifications
    }

    if (data.unit_price !== undefined) {
      if (data.unit_price !== null) {
        this.validatePrice(data.unit_price)
      }
      this._unit_price = data.unit_price
    }

    if (data.currency !== undefined) {
      this.validateCurrency(data.currency)
      this._currency = data.currency
    }

    if (data.weight_kg !== undefined) {
      if (data.weight_kg !== null) {
        this.validateWeight(data.weight_kg)
      }
      this._weight_kg = data.weight_kg
    }

    if (data.is_preferred !== undefined) {
      this._is_preferred = data.is_preferred
    }

    if (data.stock_status !== undefined) {
      this.validateStockStatus(data.stock_status)
      this._stock_status = data.stock_status
    }

    this._updated_at = new Date()
    this.validate()
  }

  // Business Logic Methods

  /**
   * Check if component is available for use
   */
  isAvailable(): boolean {
    return (
      this._is_active &&
      this._stock_status !== "discontinued" &&
      this._stock_status !== "out_of_stock"
    )
  }

  /**
   * Check if component is preferred
   */
  isPreferred(): boolean {
    return this._is_preferred
  }

  /**
   * Check if component is active
   */
  isActive(): boolean {
    return this._is_active
  }

  /**
   * Get component's full display name
   */
  getDisplayName(): string {
    return `${this._manufacturer} ${this._model_number}`
  }

  /**
   * Get component's full name including description
   */
  getFullName(): string {
    const baseName = this.getDisplayName()
    return this._name && this._name !== this._model_number
      ? `${this._name} (${baseName})`
      : baseName
  }

  /**
   * Check if component has specifications
   */
  hasSpecifications(): boolean {
    return (
      this._specifications !== undefined &&
      Object.keys(this._specifications).length > 0
    )
  }

  /**
   * Check if component has pricing information
   */
  hasPricing(): boolean {
    return this._unit_price !== null && this._unit_price > 0
  }

  // Domain Invariants Validation

  private validate(): void {
    this.validateName(this._name)
    this.validateManufacturer(this._manufacturer)
    this.validateModelNumber(this._model_number)
    this.validateComponentTypeId(this._component_type_id)
    this.validateCategoryId(this._category_id)
    this.validateCurrency(this._currency)

    if (this._unit_price !== null) {
      this.validatePrice(this._unit_price)
    }

    if (this._weight_kg !== null) {
      this.validateWeight(this._weight_kg)
    }

    if (this._specifications !== undefined) {
      this.validateSpecifications(this._specifications)
    }

    this.validateStockStatus(this._stock_status)
  }

  private validateName(name: string): void {
    if (!name || name.trim().length < 1) {
      throw ComponentDomainError.invalidComponent(
        "name",
        name,
        "Name cannot be empty"
      )
    }
    if (name.length > 200) {
      throw ComponentDomainError.invalidComponent(
        "name",
        name,
        "Name cannot exceed 200 characters"
      )
    }
  }

  private validateManufacturer(manufacturer: string): void {
    if (!manufacturer || manufacturer.trim().length < 1) {
      throw ComponentDomainError.invalidComponent(
        "manufacturer",
        manufacturer,
        "Manufacturer cannot be empty"
      )
    }
    if (manufacturer.length > 100) {
      throw ComponentDomainError.invalidComponent(
        "manufacturer",
        manufacturer,
        "Manufacturer cannot exceed 100 characters"
      )
    }
  }

  private validateModelNumber(model_number: string): void {
    if (!model_number || model_number.trim().length < 1) {
      throw ComponentDomainError.invalidComponent(
        "model_number",
        model_number,
        "Model number cannot be empty"
      )
    }
    if (model_number.length > 100) {
      throw ComponentDomainError.invalidComponent(
        "model_number",
        model_number,
        "Model number cannot exceed 100 characters"
      )
    }
  }

  private validateComponentTypeId(component_type_id: number): void {
    if (!Number.isInteger(component_type_id) || component_type_id <= 0) {
      throw ComponentDomainError.invalidComponent(
        "component_type_id",
        component_type_id,
        "Component type ID must be a positive integer"
      )
    }
  }

  private validateCategoryId(category_id: number): void {
    if (!Number.isInteger(category_id) || category_id <= 0) {
      throw ComponentDomainError.invalidComponent(
        "category_id",
        category_id,
        "Category ID must be a positive integer"
      )
    }
  }

  private validatePrice(price: number): void {
    if (price < 0) {
      throw ComponentDomainError.invalidComponent(
        "unit_price",
        price,
        "Price must be non-negative"
      )
    }
    // Check for reasonable decimal precision (2 decimal places max)
    if (Math.round(price * 100) !== price * 100) {
      throw ComponentDomainError.invalidComponent(
        "unit_price",
        price,
        "Price must have at most 2 decimal places"
      )
    }
  }

  private validateCurrency(currency: string): void {
    if (!currency || currency.trim().length !== 3) {
      throw ComponentDomainError.invalidComponent(
        "currency",
        currency,
        "Currency must be a 3-letter ISO code"
      )
    }
  }

  private validateWeight(weight: number): void {
    if (weight < 0) {
      throw ComponentDomainError.invalidComponent(
        "weight_kg",
        weight,
        "Weight must be non-negative"
      )
    }
  }

  private validateSpecifications(
    specifications: ComponentSpecifications
  ): void {
    if (specifications && typeof specifications !== "object") {
      throw ComponentDomainError.invalidSpecification(
        "Specifications must be an object",
        specifications
      )
    }

    // Additional specification validation can be added here
    // For example, validating electrical parameters, standards compliance, etc.
  }

  private validateStockStatus(status: string): void {
    const validStatuses = [
      "available",
      "limited",
      "out_of_stock",
      "discontinued",
      "obsolete",
      "special_order",
      "pre_order",
    ]
    if (!validStatuses.includes(status)) {
      throw ComponentDomainError.invalidComponent(
        "stock_status",
        status,
        `Stock status must be one of: ${validStatuses.join(", ")}`
      )
    }
  }

  // Getters for accessing private properties

  get id(): number {
    return this._id
  }
  get name(): string {
    return this._name
  }
  get manufacturer(): string {
    return this._manufacturer
  }
  get model_number(): string {
    return this._model_number
  }
  get description(): string | null {
    return this._description
  }
  get component_type_id(): number {
    return this._component_type_id
  }
  get category_id(): number {
    return this._category_id
  }
  get specifications(): ComponentSpecifications | undefined {
    return this._specifications
  }
  get unit_price(): number | null {
    return this._unit_price
  }
  get currency(): string {
    return this._currency
  }
  get supplier(): string | null {
    return this._supplier
  }
  get part_number(): string | null {
    return this._part_number
  }
  get weight_kg(): number | null {
    return this._weight_kg
  }
  get dimensions(): ComponentDimensions | null {
    return this._dimensions
  }
  get is_active(): boolean {
    return this._is_active
  }
  get is_preferred(): boolean {
    return this._is_preferred
  }
  get stock_status(): string {
    return this._stock_status
  }
  get version(): string {
    return this._version
  }
  get metadata(): Record<string, any> | null {
    return this._metadata
  }
  get created_at(): Date {
    return this._created_at
  }
  get updated_at(): Date {
    return this._updated_at
  }

  // Serialization Methods

  /**
   * Convert to API payload for creation
   */
  toCreatePayload(): ComponentCreateData {
    return {
      name: this._name,
      manufacturer: this._manufacturer,
      model_number: this._model_number,
      description: this._description || undefined,
      component_type_id: this._component_type_id,
      category_id: this._category_id,
      specifications: this._specifications,
      unit_price: this._unit_price || undefined,
      currency: this._currency,
      supplier: this._supplier || undefined,
      part_number: this._part_number || undefined,
      weight_kg: this._weight_kg || undefined,
      dimensions: this._dimensions || undefined,
      is_preferred: this._is_preferred,
      stock_status: this._stock_status,
      version: this._version,
      metadata: this._metadata || undefined,
    }
  }

  /**
   * Convert to API payload for updates
   */
  toUpdatePayload(): ComponentUpdateData & { id: number } {
    return {
      id: this._id,
      name: this._name,
      manufacturer: this._manufacturer,
      model_number: this._model_number,
      description: this._description || undefined,
      component_type_id: this._component_type_id,
      category_id: this._category_id,
      specifications: this._specifications,
      unit_price: this._unit_price || undefined,
      currency: this._currency,
      supplier: this._supplier || undefined,
      part_number: this._part_number || undefined,
      weight_kg: this._weight_kg || undefined,
      dimensions: this._dimensions || undefined,
      is_preferred: this._is_preferred,
      stock_status: this._stock_status,
      version: this._version,
      metadata: this._metadata || undefined,
    }
  }

  /**
   * Convert to plain object for serialization
   */
  toJSON(): ComponentEntityProps {
    return {
      id: this._id,
      name: this._name,
      manufacturer: this._manufacturer,
      model_number: this._model_number,
      description: this._description,
      component_type_id: this._component_type_id,
      category_id: this._category_id,
      specifications: this._specifications,
      unit_price: this._unit_price,
      currency: this._currency,
      supplier: this._supplier,
      part_number: this._part_number,
      weight_kg: this._weight_kg,
      dimensions: this._dimensions,
      is_active: this._is_active,
      is_preferred: this._is_preferred,
      stock_status: this._stock_status,
      version: this._version,
      metadata: this._metadata,
      created_at: this._created_at.toISOString(),
      updated_at: this._updated_at.toISOString(),
    }
  }

  /**
   * Check if two components are equal
   */
  equals(other: Component): boolean {
    return this._id === other._id
  }

  /**
   * Get component hash code (based on ID)
   */
  hashCode(): string {
    return `component-${this._id}`
  }
}
