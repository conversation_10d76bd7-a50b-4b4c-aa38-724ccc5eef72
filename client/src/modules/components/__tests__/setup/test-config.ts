/**
 * Test Configuration
 * Comprehensive test setup and configuration for the components module
 */

import { cleanup } from "@testing-library/react"
import { http, HttpResponse } from "msw"
import { setupServer } from "msw/node"
import { afterAll, afterEach, beforeAll, beforeEach } from "vitest"

import "@testing-library/jest-dom"

// Mock data for testing
import type { ComponentRead } from "@/types/api"

// Local type alias for mock data
type Component = ComponentRead

export const mockComponents: Component[] = [
  {
    id: 1,
    name: "Test Resistor",
    manufacturer: "Test Corp",
    model_number: "TR-001",
    component_type_id: 1,
    category_id: 1,
    unit_price: 0.25,
    currency: "EUR",
    is_active: true,
    is_preferred: false,
    stock_status: "available",
    weight_kg: 0.001,
    dimensions: {
      length: 10,
      width: 5,
      height: 2,
      unit: "mm",
    },
    specifications: {
      resistance: "1kΩ",
      tolerance: "5%",
      power: "0.25W",
      temperature_coefficient: "100ppm/°C",
    },
    created_at: "2024-01-01T00:00:00Z",
    updated_at: "2024-01-01T00:00:00Z",
  },
  {
    id: 2,
    name: "Test Capacitor",
    manufacturer: "Test Corp",
    model_number: "TC-002",
    component_type_id: 2,
    category_id: 2,
    unit_price: 1.5,
    currency: "EUR",
    is_active: true,
    is_preferred: true,
    stock_status: "limited",
    weight_kg: 0.005,
    dimensions: {
      diameter: 8,
      height: 12,
      unit: "mm",
    },
    specifications: {
      capacitance: "100µF",
      voltage: "25V",
      tolerance: "20%",
      type: "Electrolytic",
    },
    created_at: "2024-01-02T00:00:00Z",
    updated_at: "2024-01-02T00:00:00Z",
  },
  {
    id: 3,
    name: "Test Switch",
    manufacturer: "Switch Corp",
    model_number: "TS-003",
    component_type_id: 3,
    category_id: 3,
    unit_price: 5.99,
    currency: "EUR",
    is_active: false,
    is_preferred: false,
    stock_status: "discontinued",
    weight_kg: 0.025,
    dimensions: {
      length: 20,
      width: 15,
      height: 10,
      unit: "mm",
    },
    specifications: {
      type: "Momentary",
      contacts: "SPST",
      current_rating: "1A",
      voltage_rating: "250V",
    },
    created_at: "2024-01-03T00:00:00Z",
    updated_at: "2024-01-03T00:00:00Z",
  },
]

// MSW server for API mocking
export const server = setupServer(
  // Get components
  http.get("/api/v1/components", ({ request }) => {
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get("page") || "1")
    const size = parseInt(url.searchParams.get("size") || "20")
    const search = url.searchParams.get("search")
    const manufacturer = url.searchParams.get("manufacturer")
    const category = url.searchParams.get("category")
    const isActive = url.searchParams.get("is_active")
    const isPreferred = url.searchParams.get("is_preferred")
    const stockStatus = url.searchParams.get("stock_status")

    let filteredComponents = [...mockComponents]

    // Apply filters
    if (search) {
      filteredComponents = filteredComponents.filter(
        (c) =>
          c.name.toLowerCase().includes(search.toLowerCase()) ||
          c.manufacturer.toLowerCase().includes(search.toLowerCase()) ||
          c.model_number.toLowerCase().includes(search.toLowerCase())
      )
    }

    if (manufacturer) {
      filteredComponents = filteredComponents.filter(
        (c) => c.manufacturer.toLowerCase() === manufacturer.toLowerCase()
      )
    }

    if (category) {
      filteredComponents = filteredComponents.filter(
        (c) => c.category_id === parseInt(category)
      )
    }

    if (isActive !== null) {
      filteredComponents = filteredComponents.filter(
        (c) => c.is_active === (isActive === "true")
      )
    }

    if (isPreferred !== null) {
      filteredComponents = filteredComponents.filter(
        (c) => c.is_preferred === (isPreferred === "true")
      )
    }

    if (stockStatus) {
      filteredComponents = filteredComponents.filter(
        (c) => c.stock_status === stockStatus
      )
    }

    // Apply pagination
    const startIndex = (page - 1) * size
    const endIndex = startIndex + size
    const paginatedComponents = filteredComponents.slice(startIndex, endIndex)

    return HttpResponse.json({
      items: paginatedComponents,
      total: filteredComponents.length,
      page,
      size,
      pages: Math.ceil(filteredComponents.length / size),
      has_next: endIndex < filteredComponents.length,
      has_prev: page > 1,
    })
  }),

  // Get component by ID
  http.get("/api/v1/components/:id", ({ params }) => {
    const id = parseInt(params.id as string)
    const component = mockComponents.find((c) => c.id === id)

    if (!component) {
      return new HttpResponse(JSON.stringify({ detail: "Component not found" }), { status: 404 })
    }

    return HttpResponse.json(component)
  }),

  // Create component
  http.post("/api/v1/components", async ({ request }) => {
    const body = await request.json() as any
    const newComponent = {
      id: mockComponents.length + 1,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      ...body,
    } as Component

    mockComponents.push(newComponent)

    return new HttpResponse(JSON.stringify(newComponent), { status: 201 })
  }),

  // Update component
  http.put("/api/v1/components/:id", async ({ params, request }) => {
    const id = parseInt(params.id as string)
    const componentIndex = mockComponents.findIndex((c) => c.id === id)

    if (componentIndex === -1) {
      return new HttpResponse(JSON.stringify({ detail: "Component not found" }), { status: 404 })
    }

    const body = await request.json() as any
    const updatedComponent = {
      ...mockComponents[componentIndex],
      ...body,
      updated_at: new Date().toISOString(),
    } as Component

    mockComponents[componentIndex] = updatedComponent

    return HttpResponse.json(updatedComponent)
  }),

  // Delete component
  http.delete("/api/v1/components/:id", ({ params }) => {
    const id = parseInt(params.id as string)
    const componentIndex = mockComponents.findIndex((c) => c.id === id)

    if (componentIndex === -1) {
      return new HttpResponse(JSON.stringify({ detail: "Component not found" }), { status: 404 })
    }

    mockComponents.splice(componentIndex, 1)

    return new HttpResponse(null, { status: 204 })
  }),

  // Bulk operations
  http.put("/api/v1/components/bulk", async ({ request }) => {
    const { component_ids, updates } = await request.json() as any

    const results = component_ids.map((id: number) => {
      const componentIndex = mockComponents.findIndex((c) => c.id === id)
      if (componentIndex !== -1) {
        mockComponents[componentIndex] = {
          ...mockComponents[componentIndex],
          ...updates,
          updated_at: new Date().toISOString(),
        } as any
        return { id, success: true }
      }
      return { id, success: false, message: "Component not found" }
    })

    return HttpResponse.json({
      success: true,
      updated_count: results.filter((r: any) => r.success).length,
      results,
    })
  }),

  http.delete("/api/v1/components/bulk", async ({ request }) => {
    const { component_ids } = await request.json() as any

    const results = component_ids.map((id: number) => {
      const componentIndex = mockComponents.findIndex((c) => c.id === id)
      if (componentIndex !== -1) {
        mockComponents.splice(componentIndex, 1)
        return { id, success: true }
      }
      return { id, success: false, message: "Component not found" }
    })

    return HttpResponse.json({
      success: true,
      deleted_count: results.filter((r: any) => r.success).length,
      results,
    })
  }),

  // Component statistics
  http.get("/api/v1/components/stats", () => {
    const activeComponents = mockComponents.filter((c) => c.is_active)
    const preferredComponents = mockComponents.filter((c) => c.is_preferred)

    const byCategory = mockComponents.reduce(
      (acc, component) => {
        const categoryKey = `category_${component.category_id}`
        acc[categoryKey] = (acc[categoryKey] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    const byManufacturer = mockComponents.reduce(
      (acc, component) => {
        acc[component.manufacturer] = (acc[component.manufacturer] || 0) + 1
        return acc
      },
      {} as Record<string, number>
    )

    const prices = mockComponents
      .filter((c) => c.unit_price)
      .map((c) => c.unit_price as number)

    return HttpResponse.json({
      total_components: mockComponents.length,
      active_components: activeComponents.length,
      inactive_components: mockComponents.length - activeComponents.length,
      preferred_components: preferredComponents.length,
      by_category: byCategory,
      by_manufacturer: byManufacturer,
      by_type: {},
      price_range: {
        min: Math.min(...prices),
        max: Math.max(...prices),
        average:
          prices.reduce((sum, price) => sum + price, 0) / prices.length,
      },
      last_updated: new Date().toISOString(),
    })
  }),

  // Search suggestions
  http.get("/api/v1/components/suggestions", ({ request }) => {
    const url = new URL(request.url)
    const query = url.searchParams.get("q") || ""

    const suggestions = [
      "Siemens contactors",
      "ABB circuit breakers",
      "Schneider switches",
      "Phoenix connectors",
      "Weidmuller terminals",
    ].filter((suggestion) =>
      suggestion.toLowerCase().includes(query.toLowerCase())
    )

    return HttpResponse.json({
      query,
      suggestions: suggestions.map((text) => ({
        text,
        type: "general",
        count: Math.floor(Math.random() * 100),
      })),
      max_suggestions: 10,
    })
  })
)

// Test setup and teardown
beforeAll(() => {
  server.listen({ onUnhandledRequest: "error" })
})

afterAll(() => {
  server.close()
})

beforeEach(() => {
  // Reset mock data before each test
  mockComponents.length = 0
  mockComponents.push(
    {
      id: 1,
      name: "Test Resistor",
      manufacturer: "Test Corp",
      model_number: "TR-001",
      component_type_id: 1,
      category_id: 1,
      unit_price: 0.25,
      currency: "EUR",
      is_active: true,
      is_preferred: false,
      stock_status: "available",
      weight_kg: 0.001,
      dimensions: {
        length: 10,
        width: 5,
        height: 2,
        unit: "mm",
      },
      specifications: {
        resistance: "1kΩ",
        tolerance: "5%",
        power: "0.25W",
        temperature_coefficient: "100ppm/°C",
      },
      created_at: "2024-01-01T00:00:00Z",
      updated_at: "2024-01-01T00:00:00Z",
    },
    {
      id: 2,
      name: "Test Capacitor",
      manufacturer: "Test Corp",
      model_number: "TC-002",
      component_type_id: 2,
      category_id: 2,
      unit_price: 1.5,
      currency: "EUR",
      is_active: true,
      is_preferred: true,
      stock_status: "limited",
      weight_kg: 0.005,
      dimensions: {
        diameter: 8,
        height: 12,
        unit: "mm",
      },
      specifications: {
        capacitance: "100µF",
        voltage: "25V",
        tolerance: "20%",
        type: "Electrolytic",
      },
      created_at: "2024-01-02T00:00:00Z",
      updated_at: "2024-01-02T00:00:00Z",
    },
    {
      id: 3,
      name: "Test Switch",
      manufacturer: "Switch Corp",
      model_number: "TS-003",
      component_type_id: 3,
      category_id: 3,
      unit_price: 5.99,
      currency: "EUR",
      is_active: false,
      is_preferred: false,
      stock_status: "discontinued",
      weight_kg: 0.025,
      dimensions: {
        length: 20,
        width: 15,
        height: 10,
        unit: "mm",
      },
      specifications: {
        type: "Momentary",
        contacts: "SPST",
        current_rating: "1A",
        voltage_rating: "250V",
      },
      created_at: "2024-01-03T00:00:00Z",
      updated_at: "2024-01-03T00:00:00Z",
    }
  )
})

afterEach(() => {
  cleanup()
  server.resetHandlers()
})

// Test utilities
export const createMockComponent = (overrides = {}) => ({
  id: Math.floor(Math.random() * 1000),
  name: "Mock Component",
  manufacturer: "Mock Corp",
  model_number: "MC-001",
  component_type_id: 1,
  category_id: 1,
  unit_price: 1.0,
  currency: "EUR",
  is_active: true,
  is_preferred: false,
  stock_status: "available",
  created_at: new Date().toISOString(),
  updated_at: new Date().toISOString(),
  ...overrides,
})

export const createMockComponentList = (count: number) =>
  Array.from({ length: count }, (_, i) =>
    createMockComponent({ id: i + 1, name: `Mock Component ${i + 1}` })
  )

// Custom render function with providers
export { render, screen, waitFor, within } from "@testing-library/react"
export { default as userEvent } from "@testing-library/user-event"
