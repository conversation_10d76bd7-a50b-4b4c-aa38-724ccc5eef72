/**
 * Infrastructure Adapters Integration Tests
 *
 * Comprehensive test coverage for all adapter functions including:
 * - Project entity adapters
 * - ProjectMember entity adapters
 * - Value object adapters
 * - Error handling and edge cases
 */

import { describe, expect, test } from "vitest"
import { CreateProjectData, Project } from "../../domain/entities/Project"
import {
  CreateProjectMemberData,
  ProjectMember,
} from "../../domain/entities/ProjectMember"
import { ProjectBudget } from "../../domain/value-objects/ProjectBudget"
import { ProjectStatus } from "../../domain/value-objects/ProjectStatus"
import { TeamRole } from "../../domain/value-objects/TeamRole"
import {
  apiAssignmentContextToDomain,
  apiBudgetFieldsToDomain,
  apiPaginatedResponseToDomain,
  apiProjectBudgetToDomain,
  apiProjectMembersToDomain,
  // ProjectMember Adapters
  apiProjectMemberToDomain,
  // Value Object Adapters
  apiProjectStatusToDomain,
  // Project Adapters
  apiProjectToDomain,
  apiRoleStringToDomain,
  apiStatusStringToDomain,
  apiTeamRoleToDomain,
  batchConvertApiToDomain,
  domainAssignmentContextToApi,
  domainBulkMemberOpsToApi,
  DomainProjectApiError,
  domainProjectBudgetToApi,
  domainProjectMemberToApiCreate,
  domainProjectMemberToApiUpdate,
  domainProjectStatusToApi,
  domainProjectToApiCreate,
  domainProjectToApiUpdate,
  domainSearchCriteriaToApiParams,
  domainTeamRoleToApi,
  ProjectAdapterError,
  ProjectMemberAdapterError,
  validateApiDataForDomain,
  ValueObjectAdapterError,
  ValueObjectAdapterFactories,
  type ApiPaginatedResponse,
  type ApiProject,
  type ApiProjectCreate,
  type ApiProjectMember,
} from "../adapters"

describe("Infrastructure Adapters", () => {
  // Test data fixtures
  const sampleApiProject: ApiProject = {
    id: "project_001",
    name: "Test Project",
    description: "A test project for adapter testing",
    owner_id: 2001,
    client_id: 1001,
    status: "Planning",
    priority: "High",
    budget_total: 100000,
    budget_spent: 25000,
    budget_currency: "EUR",
    start_date: "2024-02-01T00:00:00Z",
    end_date: "2024-12-31T23:59:59Z",
    tags: ["electrical", "high-voltage"],
    location: "Amsterdam",
    external_project_id: "EXT_001",
    is_offline: false,
    requires_approval: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-15T12:00:00Z",
    members: [],
  }

  const sampleApiProjectMember: ApiProjectMember = {
    id: "member_001",
    user_id: 3001,
    project_id: "project_001",
    role: "Electrical Engineer",
    assigned_at: "2024-01-15T10:00:00Z",
    assigned_by: 2001,
    expires_at: "2025-12-31T23:59:59Z",
    is_active: true,
  }

  describe("Project Entity Adapters", () => {
    describe("apiProjectToDomain", () => {
      test("should convert complete API project to domain entity", () => {
        const domainProject = apiProjectToDomain(sampleApiProject)

        expect(domainProject).toBeInstanceOf(Project)
        expect(domainProject.id).toBe("project_001")
        expect(domainProject.name).toBe("Test Project")
        expect(domainProject.description).toBe(
          "A test project for adapter testing"
        )
        expect(domainProject.ownerId).toBe(2001)
        expect(domainProject.clientId).toBe(1001)
        expect(domainProject.priority).toBe("High")
        expect(domainProject.tags).toEqual(["electrical", "high-voltage"])
        expect(domainProject.location).toBe("Amsterdam")
        expect(domainProject.externalProjectId).toBe("EXT_001")
        expect(domainProject.requiresApproval()).toBe(true)
      })

      test("should handle API project with minimal data", () => {
        const minimalApiProject: ApiProject = {
          id: "minimal_001",
          name: "Minimal Project",
          owner_id: 2001,
          status: "Draft",
          priority: "Low",
          created_at: "2024-01-15T10:00:00Z",
          updated_at: "2024-01-15T10:00:00Z",
        }

        const domainProject = apiProjectToDomain(minimalApiProject)

        expect(domainProject.id).toBe("minimal_001")
        expect(domainProject.name).toBe("Minimal Project")
        expect(domainProject.ownerId).toBe(2001)
        expect(domainProject.priority).toBe("Low")
        expect(domainProject.clientId).toBeUndefined()
        expect(domainProject.description).toBeUndefined()
      })

      test("should convert budget data correctly", () => {
        const domainProject = apiProjectToDomain(sampleApiProject)

        expect(domainProject.budget.totalAmount).toBe(100000)
        expect(domainProject.budget.spentAmount).toBe(25000)
        expect(domainProject.budget.currency).toBe("EUR")
        expect(domainProject.budget.getRemainingAmount()).toBe(75000)
      })

      test("should handle empty budget gracefully", () => {
        const projectWithoutBudget: ApiProject = {
          ...sampleApiProject,
          budget_total: undefined,
          budget_spent: undefined,
          budget_currency: undefined,
        }

        const domainProject = apiProjectToDomain(projectWithoutBudget)

        expect(domainProject.budget.totalAmount).toBe(0)
        expect(domainProject.budget.spentAmount).toBe(0)
      })

      test("should throw ProjectAdapterError on invalid data", () => {
        const invalidApiProject = {
          ...sampleApiProject,
          owner_id: null, // Invalid owner ID
        }

        expect(() => apiProjectToDomain(invalidApiProject as any)).toThrow(
          ProjectAdapterError
        )
      })
    })

    describe("domainProjectToApiCreate", () => {
      test("should convert domain project to API create format", () => {
        const createData: CreateProjectData = {
          id: "new_project_001",
          name: "New Project",
          description: "A new project",
          ownerId: 2001,
          clientId: 1001,
          priority: "Medium",
          budgetData: {
            totalAmount: 75000,
            currency: "USD",
          },
          startDate: "2024-03-01T00:00:00Z",
          endDate: "2024-11-30T23:59:59Z",
          tags: ["new", "electrical"],
          location: "Berlin",
        }

        const domainProject = Project.create(createData)
        const apiCreate = domainProjectToApiCreate(domainProject)

        expect(apiCreate).toEqual({
          name: "New Project",
          description: "A new project",
          owner_id: 2001,
          client_id: 1001,
          priority: "Medium",
          budget_total: 75000,
          budget_currency: "USD",
          start_date: "2024-03-01T00:00:00Z",
          end_date: "2024-11-30T23:59:59Z",
          tags: ["new", "electrical"],
          location: "Berlin",
        })
      })

      test("should exclude undefined optional fields", () => {
        const minimalCreateData: CreateProjectData = {
          id: "minimal_project",
          name: "Minimal Project",
          ownerId: 2001,
          priority: "Low",
        }

        const domainProject = Project.create(minimalCreateData)
        const apiCreate = domainProjectToApiCreate(domainProject)

        expect(apiCreate).toEqual({
          name: "Minimal Project",
          owner_id: 2001,
          client_id: undefined,
          priority: "Low",
          budget_total: undefined,
          budget_currency: undefined,
          start_date: undefined,
          end_date: undefined,
          tags: undefined,
          location: undefined,
          external_project_id: undefined,
        })
      })
    })

    describe("domainProjectToApiUpdate", () => {
      test("should convert domain project to API update format", () => {
        const domainProject = apiProjectToDomain(sampleApiProject)
        const apiUpdate = domainProjectToApiUpdate(domainProject)

        expect(apiUpdate).toEqual({
          name: "Test Project",
          description: "A test project for adapter testing",
          status: "Planning",
          priority: "High",
          budget_total: 100000,
          budget_spent: 25000,
          budget_currency: "EUR",
          start_date: "2024-02-01T00:00:00Z",
          end_date: "2024-12-31T23:59:59Z",
          tags: ["electrical", "high-voltage"],
          location: "Amsterdam",
          requires_approval: true,
        })
      })

      test("should include only changed fields when specified", () => {
        const domainProject = apiProjectToDomain(sampleApiProject)
        const changedFields = new Set(["name", "description", "priority"])
        const apiUpdate = domainProjectToApiUpdate(domainProject, changedFields)

        expect(apiUpdate).toEqual({
          name: "Test Project",
          description: "A test project for adapter testing",
          priority: "High",
        })
      })
    })

    describe("apiPaginatedResponseToDomain", () => {
      test("should convert API paginated response to domain format", () => {
        const apiResponse: ApiPaginatedResponse<ApiProject> = {
          items: [sampleApiProject],
          total: 25,
          page: 2,
          size: 10,
          has_more: true,
        }

        const domainResult = apiPaginatedResponseToDomain(
          apiResponse,
          apiProjectToDomain
        )

        expect(domainResult).toEqual({
          items: expect.arrayContaining([expect.any(Project)]),
          totalCount: 25,
          offset: 10, // (page - 1) * size
          limit: 10,
          hasMore: true,
        })

        expect(domainResult.items[0].id).toBe("project_001")
      })

      test("should handle empty paginated response", () => {
        const apiResponse: ApiPaginatedResponse<ApiProject> = {
          items: [],
          total: 0,
          page: 1,
          size: 10,
          has_more: false,
        }

        const domainResult = apiPaginatedResponseToDomain(
          apiResponse,
          apiProjectToDomain
        )

        expect(domainResult.items).toHaveLength(0)
        expect(domainResult.totalCount).toBe(0)
        expect(domainResult.hasMore).toBe(false)
      })
    })

    describe("domainSearchCriteriaToApiParams", () => {
      test("should convert domain search criteria to API parameters", () => {
        const criteria = {
          clientId: 1001,
          ownerId: 2001,
          status: "Active",
          priority: "High",
          tags: ["electrical", "urgent"],
          createdAfter: "2024-01-01T00:00:00Z",
          budgetMin: 10000,
          budgetMax: 100000,
          currency: "EUR",
          memberUserId: 3001,
          location: "Amsterdam",
          hasExpiredMembers: true,
          requiresApproval: false,
        }

        const apiParams = domainSearchCriteriaToApiParams(criteria)

        expect(apiParams).toEqual({
          client_id: 1001,
          owner_id: 2001,
          status: "Active",
          priority: "High",
          tags: "electrical,urgent",
          created_after: "2024-01-01T00:00:00Z",
          budget_min: 10000,
          budget_max: 100000,
          currency: "EUR",
          member_user_id: 3001,
          location: "Amsterdam",
          has_expired_members: true,
          requires_approval: false,
        })
      })

      test("should handle empty criteria", () => {
        const apiParams = domainSearchCriteriaToApiParams({})

        expect(apiParams).toEqual({})
      })
    })
  })

  describe("ProjectMember Entity Adapters", () => {
    describe("apiProjectMemberToDomain", () => {
      test("should convert API project member to domain entity", () => {
        const domainMember = apiProjectMemberToDomain(sampleApiProjectMember)

        expect(domainMember).toBeInstanceOf(ProjectMember)
        expect(domainMember.id).toBe("member_001")
        expect(domainMember.userId).toBe(3001)
        expect(domainMember.projectId).toBe("project_001")
        expect(domainMember.role.role).toBe("Electrical Engineer")
        expect(domainMember.assignedAt).toBe("2024-01-15T10:00:00Z")
        expect(domainMember.assignedBy).toBe(2001)
        expect(domainMember.expiresAt).toBe("2025-12-31T23:59:59Z")
        expect(domainMember.isActive).toBe(true)
      })

      test("should handle member without expiration", () => {
        const permanentMember: ApiProjectMember = {
          ...sampleApiProjectMember,
          expires_at: undefined,
        }

        const domainMember = apiProjectMemberToDomain(permanentMember)

        expect(domainMember.expiresAt).toBeUndefined()
        expect(domainMember.hasExpired()).toBe(false)
      })
    })

    describe("apiProjectMembersToDomain", () => {
      test("should convert array of API members to domain entities", () => {
        const apiMembers = [
          sampleApiProjectMember,
          {
            ...sampleApiProjectMember,
            id: "member_002",
            user_id: 3002,
            role: "Electrical Engineer",
          },
        ]

        const domainMembers = apiProjectMembersToDomain(apiMembers)

        expect(domainMembers).toHaveLength(2)
        expect(domainMembers[0].id).toBe("member_001")
        expect(domainMembers[1].id).toBe("member_002")
        expect(domainMembers[1].role.role).toBe("Electrical Engineer")
      })
    })

    describe("domainProjectMemberToApiCreate", () => {
      test("should convert domain member to API create format", () => {
        const createData: CreateProjectMemberData = {
          id: "new_member",
          userId: 3003,
          projectId: "project_001",
          role: TeamRole.create({ role: "Project Manager" }),
          assignedAt: "2024-01-20T14:00:00Z",
          assignedBy: 2001,
          expiresAt: "2024-06-30T23:59:59Z",
        }

        const domainMember = ProjectMember.create(createData)
        const apiCreate = domainProjectMemberToApiCreate(domainMember, 2001)

        expect(apiCreate).toEqual({
          user_id: 3003,
          role: "Project Manager",
          assigned_by: 2001,
          expires_at: "2024-06-30T23:59:59Z",
        })
      })
    })

    describe("domainAssignmentContextToApi and apiAssignmentContextToDomain", () => {
      test("should convert assignment context bidirectionally", () => {
        const domainContext = {
          assignedBy: 2001,
          reason: "Project needs QA expertise",
          priority: "High" as const,
          skillsRequired: ["quality-assurance", "testing"],
          estimatedWorkload: 80,
          isTemporaryAssignment: false,
          requiresApproval: true,
        }

        const apiContext = domainAssignmentContextToApi(domainContext)
        const reconvertedContext = apiAssignmentContextToDomain(apiContext)

        expect(reconvertedContext).toEqual(domainContext)
      })
    })

    describe("domainBulkMemberOpsToApi", () => {
      test("should convert bulk add operation to API format", () => {
        const domainMember = apiProjectMemberToDomain(sampleApiProjectMember)
        const bulkOp = {
          operation: "add" as const,
          members: [domainMember],
          assignedBy: 2001,
        }

        const apiBulkOp = domainBulkMemberOpsToApi(bulkOp)

        expect(apiBulkOp.operation).toBe("add")
        expect(apiBulkOp.members).toHaveLength(1)
        expect((apiBulkOp.members[0] as any).user_id).toBe(3001)
        expect((apiBulkOp.members[0] as any).role).toBe("Electrical Engineer")
      })

      test("should convert bulk remove operation to API format", () => {
        const bulkOp = {
          operation: "remove" as const,
          members: ["member_001", "member_002"],
        }

        const apiBulkOp = domainBulkMemberOpsToApi(bulkOp)

        expect(apiBulkOp.operation).toBe("remove")
        expect(apiBulkOp.members).toEqual(["member_001", "member_002"])
      })
    })
  })

  describe("Value Object Adapters", () => {
    describe("ProjectStatus Adapters", () => {
      test("should convert API status to domain and back", () => {
        const apiStatus = {
          status: "Active",
          last_transition_at: "2024-01-15T12:00:00Z",
          reason: "Project approved and started",
        }

        const domainStatus = apiProjectStatusToDomain(apiStatus)
        const reconvertedApi = domainProjectStatusToApi(domainStatus)

        expect(domainStatus.status).toBe("Active")
        expect(domainStatus.lastTransitionAt).toBe("2024-01-15T12:00:00Z")
        expect(domainStatus.reason).toBe("Project approved and started")

        expect(reconvertedApi.status).toBe("Active")
        expect(reconvertedApi.last_transition_at).toBe("2024-01-15T12:00:00Z")
        expect(reconvertedApi.reason).toBe("Project approved and started")
      })

      test("should convert simple status string to domain", () => {
        const domainStatus = apiStatusStringToDomain(
          "Draft",
          "2024-01-15T10:00:00Z"
        )

        expect(domainStatus.status).toBe("Draft")
        expect(domainStatus.lastTransitionAt).toBe("2024-01-15T10:00:00Z")
      })
    })

    describe("ProjectBudget Adapters", () => {
      test("should convert API budget to domain and back", () => {
        const apiBudget = {
          total_amount: 150000,
          spent_amount: 45000,
          currency: "USD",
          allocated_amount: 120000,
        }

        const domainBudget = apiProjectBudgetToDomain(apiBudget)
        const reconvertedApi = domainProjectBudgetToApi(domainBudget)

        expect(domainBudget.totalAmount).toBe(150000)
        expect(domainBudget.spentAmount).toBe(45000)
        expect(domainBudget.currency).toBe("USD")
        expect(domainBudget.allocatedAmount).toBe(120000)

        expect(reconvertedApi.total_amount).toBe(150000)
        expect(reconvertedApi.spent_amount).toBe(45000)
        expect(reconvertedApi.currency).toBe("USD")
        expect(reconvertedApi.allocated_amount).toBe(120000)
        expect(reconvertedApi.remaining_amount).toBe(105000)
        expect(reconvertedApi.is_over_budget).toBe(false)
      })

      test("should convert budget fields to domain", () => {
        const domainBudget = apiBudgetFieldsToDomain(100000, 30000, "EUR")

        expect(domainBudget.totalAmount).toBe(100000)
        expect(domainBudget.spentAmount).toBe(30000)
        expect(domainBudget.currency).toBe("EUR")
      })
    })

    describe("TeamRole Adapters", () => {
      test("should convert API role to domain and back", () => {
        const apiRole = {
          role: "Project Manager",
          permissions: ["manage_project", "assign_members"],
          hierarchy_level: 8,
        }

        const domainRole = apiTeamRoleToDomain(apiRole)
        const reconvertedApi = domainTeamRoleToApi(domainRole)

        expect(domainRole.role).toBe("Project Manager")
        expect(domainRole.canManageProject()).toBe(true)

        expect(reconvertedApi.role).toBe("Project Manager")
        expect(reconvertedApi.can_manage_project).toBe(true)
        expect(typeof reconvertedApi.hierarchy_level).toBe("number")
      })

      test("should convert role string to domain", () => {
        const domainRole = apiRoleStringToDomain("Electrical Engineer")

        expect(domainRole.role).toBe("Electrical Engineer")
        expect(domainRole.canViewBudget()).toBe(false) // Electrical Engineers cannot view budget
      })
    })

    describe("Value Object Adapter Factories", () => {
      test("should create status from API data", () => {
        const status = ValueObjectAdapterFactories.createStatusFromApi("Active")

        expect(status.status).toBe("Active")
        expect(status.canTransitionTo("Completed")).toBe(true)
      })

      test("should create budget from API data", () => {
        const budget = ValueObjectAdapterFactories.createBudgetFromApi(
          75000,
          15000,
          "GBP"
        )

        expect(budget.totalAmount).toBe(75000)
        expect(budget.spentAmount).toBe(15000)
        expect(budget.currency).toBe("GBP")
      })

      test("should create role from API data", () => {
        const role =
          ValueObjectAdapterFactories.createRoleFromApi("Project Manager")

        expect(role.role).toBe("Project Manager")
        expect(role.canManageProject()).toBe(true)
      })

      test("should create empty/default value objects", () => {
        const emptyBudget = ValueObjectAdapterFactories.createEmptyBudget()
        const defaultStatus = ValueObjectAdapterFactories.createDefaultStatus()
        const viewerRole = ValueObjectAdapterFactories.createViewerRole()

        expect(emptyBudget.totalAmount).toBe(0)
        expect(defaultStatus.status).toBe("Draft")
        expect(viewerRole.role).toBe("Viewer")
      })
    })
  })

  describe("Validation and Error Handling", () => {
    describe("validateApiDataForDomain", () => {
      test("should validate ProjectStatus data", () => {
        const validStatus = {
          status: "Active",
          last_transition_at: "2024-01-15T10:00:00Z",
        }
        const invalidStatus = {
          status: "",
          last_transition_at: "2024-01-15T10:00:00Z",
        }

        const validResult = validateApiDataForDomain(
          validStatus,
          "ProjectStatus"
        )
        const invalidResult = validateApiDataForDomain(
          invalidStatus,
          "ProjectStatus"
        )

        expect(validResult.isValid).toBe(true)
        expect(validResult.errors).toHaveLength(0)

        expect(invalidResult.isValid).toBe(false)
        expect(invalidResult.errors).toContain("Status is required")
      })

      test("should validate ProjectBudget data", () => {
        const validBudget = { total_amount: 50000, currency: "EUR" }
        const invalidBudget = { total_amount: -1000, currency: "" }

        const validResult = validateApiDataForDomain(
          validBudget,
          "ProjectBudget"
        )
        const invalidResult = validateApiDataForDomain(
          invalidBudget,
          "ProjectBudget"
        )

        expect(validResult.isValid).toBe(true)
        expect(invalidResult.isValid).toBe(false)
        expect(invalidResult.errors).toContain(
          "Total amount cannot be negative"
        )
        expect(invalidResult.errors).toContain("Currency is required")
      })

      test("should validate TeamRole data", () => {
        const validRole = { role: "Electrical Engineer" }
        const invalidRole = { role: "" }

        const validResult = validateApiDataForDomain(validRole, "TeamRole")
        const invalidResult = validateApiDataForDomain(invalidRole, "TeamRole")

        expect(validResult.isValid).toBe(true)
        expect(invalidResult.isValid).toBe(false)
        expect(invalidResult.errors).toContain("Role is required")
      })
    })

    describe("batchConvertApiToDomain", () => {
      test("should handle successful batch conversion", () => {
        const apiProjects = [
          sampleApiProject,
          { ...sampleApiProject, id: "project_002" },
        ]

        const result = batchConvertApiToDomain(apiProjects, apiProjectToDomain)

        expect(result.successful).toHaveLength(2)
        expect(result.failed).toHaveLength(0)
        expect(result.statistics.total).toBe(2)
        expect(result.statistics.successful).toBe(2)
        expect(result.statistics.failed).toBe(0)
      })

      test("should handle mixed success/failure batch conversion", () => {
        const apiProjects = [
          sampleApiProject,
          { ...sampleApiProject, id: "invalid", owner_id: null }, // Invalid data
        ]

        const result = batchConvertApiToDomain(apiProjects, apiProjectToDomain)

        expect(result.successful).toHaveLength(1)
        expect(result.failed).toHaveLength(1)
        expect(result.failed[0].originalData.id).toBe("invalid")
        expect(result.statistics.total).toBe(2)
        expect(result.statistics.successful).toBe(1)
        expect(result.statistics.failed).toBe(1)
      })
    })

    describe("Error Classes", () => {
      test("should create ProjectAdapterError with correct properties", () => {
        const error = new ProjectAdapterError(
          "Conversion failed",
          "TO_DOMAIN",
          sampleApiProject
        )

        expect(error.name).toBe("ProjectAdapterError")
        expect(error.message).toBe("Conversion failed")
        expect(error.operation).toBe("TO_DOMAIN")
        expect(error.originalData).toBe(sampleApiProject)
      })

      test("should create ProjectMemberAdapterError with correct properties", () => {
        const error = new ProjectMemberAdapterError(
          "Member conversion failed",
          "TO_API"
        )

        expect(error.name).toBe("ProjectMemberAdapterError")
        expect(error.operation).toBe("TO_API")
      })

      test("should create ValueObjectAdapterError with correct properties", () => {
        const error = new ValueObjectAdapterError(
          "Value object conversion failed",
          "TO_DOMAIN",
          "ProjectStatus",
          { status: "invalid" }
        )

        expect(error.name).toBe("ValueObjectAdapterError")
        expect(error.operation).toBe("TO_DOMAIN")
        expect(error.valueObjectType).toBe("ProjectStatus")
      })

      test("should create DomainProjectApiError from API error", () => {
        const apiError = {
          detail: "Validation failed",
          code: "VALIDATION_ERROR",
        }
        const domainError = DomainProjectApiError.fromApiError(
          apiError,
          "validation"
        )

        expect(domainError.message).toBe("Validation failed")
        expect(domainError.context).toBe("validation")
        expect(domainError.originalError).toBe(apiError)
      })
    })
  })

  describe("Edge Cases and Resilience", () => {
    test("should handle null/undefined values gracefully", () => {
      const apiProjectWithNulls: ApiProject = {
        id: "null_test",
        name: "Test Project",
        owner_id: 2001,
        status: "Draft",
        priority: "Low",
        description: undefined,
        client_id: undefined,
        budget_total: undefined,
        tags: undefined,
        location: undefined,
        created_at: "2024-01-15T10:00:00Z",
        updated_at: "2024-01-15T10:00:00Z",
      }

      const domainProject = apiProjectToDomain(apiProjectWithNulls)

      expect(domainProject.description).toBeUndefined()
      expect(domainProject.clientId).toBeUndefined()
      expect(domainProject.tags).toEqual([])
      expect(domainProject.location).toBeUndefined()
      expect(domainProject.budget.totalAmount).toBe(0) // Empty budget created
    })

    test("should handle empty arrays gracefully", () => {
      const emptyPaginatedResponse: ApiPaginatedResponse<ApiProject> = {
        items: [],
        total: 0,
        page: 1,
        size: 10,
        has_more: false,
      }

      const result = apiPaginatedResponseToDomain(
        emptyPaginatedResponse,
        apiProjectToDomain
      )

      expect(result.items).toHaveLength(0)
      expect(result.totalCount).toBe(0)
    })

    test("should preserve data integrity through round-trip conversions", () => {
      // API → Domain → API → Domain round trip
      const domainProject1 = apiProjectToDomain(sampleApiProject)
      const apiCreate = domainProjectToApiCreate(domainProject1)

      // Simulate API response after creation
      const apiResponse: ApiProject = {
        ...sampleApiProject,
        ...apiCreate,
        id: "round_trip_test",
        created_at: "2024-01-15T10:00:00Z",
        updated_at: "2024-01-15T10:00:00Z",
      }

      const domainProject2 = apiProjectToDomain(apiResponse)

      // Core data should be preserved
      expect(domainProject2.name).toBe(domainProject1.name)
      expect(domainProject2.ownerId).toBe(domainProject1.ownerId)
      expect(domainProject2.priority).toBe(domainProject1.priority)
      expect(domainProject2.budget.totalAmount).toBe(
        domainProject1.budget.totalAmount
      )
    })
  })
})
