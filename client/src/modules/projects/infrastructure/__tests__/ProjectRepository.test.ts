/**
 * ProjectRepository Integration Tests
 *
 * Comprehensive test coverage for the ProjectRepository infrastructure implementation.
 * Tests cover API integration, data transformation, caching behavior, and error handling.
 */

import { afterEach, beforeEach, describe, expect, test, vi } from "vitest"
import { QueryClient } from "@tanstack/react-query"
import { CreateProjectData, Project } from "../../domain/entities/Project"
import { ProjectMember } from "../../domain/entities/ProjectMember"
import {
  ProjectNotFoundError,
  ProjectPersistenceError,
  ProjectRepositoryError,
} from "../../domain/repositories/IProjectRepository"
import { ProjectBudget } from "../../domain/value-objects/ProjectBudget"
import { ProjectStatus } from "../../domain/value-objects/ProjectStatus"
import { TeamRole } from "../../domain/value-objects/TeamRole"
import {
  type ApiPaginatedResponse,
  type ApiProject,
  type ApiProjectCreate,
  type ApiProjectUpdate,
} from "../adapters"
import {
  projectQueryKeys,
  ProjectRepository,
  type ProjectApiClient,
} from "../ProjectRepository"

describe("ProjectRepository", () => {
  let repository: ProjectRepository
  let mockApiClient: jest.Mocked<ProjectApiClient>
  let queryClient: QueryClient
  let sampleProject: Project
  let sampleApiProject: ApiProject

  beforeEach(() => {
    // Create mock API client
    mockApiClient = {
      get: vi.fn(),
      post: vi.fn(),
      put: vi.fn(),
      delete: vi.fn(),
    } as jest.Mocked<ProjectApiClient>

    // Create fresh query client for each test
    queryClient = new QueryClient({
      defaultOptions: {
        queries: { retry: false },
        mutations: { retry: false },
      },
    })

    repository = new ProjectRepository(mockApiClient, queryClient)

    // Sample test data
    const projectCreateData: CreateProjectData = {
      id: "project_001",
      name: "Test Project",
      description: "A test project for repository testing",
      ownerId: 2001,
      clientId: 1001,
      priority: "Medium",
      budgetData: {
        totalAmount: 50000,
        spentAmount: 0,
        currency: "EUR",
      },
      startDate: "2024-02-01T00:00:00Z",
      endDate: "2024-12-31T23:59:59Z",
      tags: ["electrical", "test"],
      location: "Amsterdam",
    }

    sampleProject = Project.create(projectCreateData)

    sampleApiProject = {
      id: "project_001",
      name: "Test Project",
      description: "A test project for repository testing",
      owner_id: 2001,
      client_id: 1001,
      status: "Draft",
      priority: "Medium",
      budget_total: 50000,
      budget_spent: 0,
      budget_currency: "EUR",
      start_date: "2024-02-01T00:00:00Z",
      end_date: "2024-12-31T23:59:59Z",
      tags: ["electrical", "test"],
      location: "Amsterdam",
      is_offline: false,
      requires_approval: false,
      created_at: "2024-01-15T10:00:00Z",
      updated_at: "2024-01-15T10:00:00Z",
      members: [],
    }
  })

  afterEach(() => {
    queryClient.clear()
  })

  describe("Core CRUD Operations", () => {
    describe("save", () => {
      test("should create new project when project does not exist", async () => {
        // Mock existence check
        mockApiClient.get.mockRejectedValueOnce({ status: 404 })

        // Mock successful creation
        mockApiClient.post.mockResolvedValueOnce({
          data: sampleApiProject,
          status: 201,
        })

        await repository.save(sampleProject)

        expect(mockApiClient.post).toHaveBeenCalledWith(
          "/projects",
          expect.objectContaining({
            name: "Test Project",
            owner_id: 2001,
            client_id: 1001,
          })
        )

        // Verify cache was updated
        const cachedProject = queryClient.getQueryData(
          projectQueryKeys.detail("project_001")
        )
        expect(cachedProject).toBeDefined()
      })

      test("should update existing project when project exists", async () => {
        // Mock existence check
        mockApiClient.get.mockResolvedValueOnce({ data: {}, status: 200 })

        // Mock successful update
        mockApiClient.put.mockResolvedValueOnce({
          data: { ...sampleApiProject, name: "Updated Project" },
          status: 200,
        })

        const updatedProject = sampleProject.updateBasicInfo("Updated Project")
        await repository.save(updatedProject)

        expect(mockApiClient.put).toHaveBeenCalledWith(
          "/projects/project_001",
          expect.objectContaining({
            name: "Updated Project",
          })
        )
      })

      test("should throw ProjectPersistenceError on API failure", async () => {
        mockApiClient.get.mockRejectedValueOnce({ status: 404 })
        mockApiClient.post.mockRejectedValueOnce(new Error("API Error"))

        await expect(repository.save(sampleProject)).rejects.toThrow(
          ProjectPersistenceError
        )
      })
    })

    describe("findById", () => {
      test("should return project when found", async () => {
        mockApiClient.get.mockResolvedValueOnce({
          data: sampleApiProject,
          status: 200,
        })

        const result = await repository.findById("project_001")

        expect(result).toBeDefined()
        expect(result!.id).toBe("project_001")
        expect(result!.name).toBe("Test Project")
        expect(mockApiClient.get).toHaveBeenCalledWith("/projects/project_001")
      })

      test("should return null when project not found", async () => {
        mockApiClient.get.mockRejectedValueOnce({ status: 404 })

        const result = await repository.findById("nonexistent")

        expect(result).toBeNull()
      })

      test("should throw ProjectRepositoryError on API failure", async () => {
        mockApiClient.get.mockRejectedValueOnce({
          status: 500,
          message: "Server Error",
        })

        await expect(repository.findById("project_001")).rejects.toThrow(
          ProjectRepositoryError
        )
      })
    })

    describe("findByIds", () => {
      test("should return array of found projects", async () => {
        const apiProjects = [
          sampleApiProject,
          { ...sampleApiProject, id: "project_002", name: "Project 2" },
        ]

        // Mock individual findById calls
        mockApiClient.get
          .mockResolvedValueOnce({ data: apiProjects[0], status: 200 })
          .mockResolvedValueOnce({ data: apiProjects[1], status: 200 })

        const result = await repository.findByIds([
          "project_001",
          "project_002",
        ])

        expect(result).toHaveLength(2)
        expect(result[0].id).toBe("project_001")
        expect(result[1].id).toBe("project_002")
      })

      test("should handle mixed found/not found results", async () => {
        mockApiClient.get
          .mockResolvedValueOnce({ data: sampleApiProject, status: 200 })
          .mockRejectedValueOnce({ status: 404 })

        const result = await repository.findByIds([
          "project_001",
          "nonexistent",
        ])

        expect(result).toHaveLength(1)
        expect(result[0].id).toBe("project_001")
      })
    })

    describe("exists", () => {
      test("should return true when project exists", async () => {
        mockApiClient.get.mockResolvedValueOnce({ status: 200 })

        const result = await repository.exists("project_001")

        expect(result).toBe(true)
        expect(mockApiClient.get).toHaveBeenCalledWith(
          "/projects/project_001/exists"
        )
      })

      test("should return false when project does not exist", async () => {
        mockApiClient.get.mockRejectedValueOnce({ status: 404 })

        const result = await repository.exists("nonexistent")

        expect(result).toBe(false)
      })
    })

    describe("remove", () => {
      test("should delete project successfully", async () => {
        mockApiClient.delete.mockResolvedValueOnce({ status: 204 })

        await repository.remove("project_001")

        expect(mockApiClient.delete).toHaveBeenCalledWith(
          "/projects/project_001"
        )
      })

      test("should throw ProjectNotFoundError when project not found", async () => {
        mockApiClient.delete.mockRejectedValueOnce({ status: 404 })

        await expect(repository.remove("nonexistent")).rejects.toThrow(
          ProjectNotFoundError
        )
      })
    })
  })

  describe("Query Operations", () => {
    describe("findByOwnerId", () => {
      test("should return paginated projects for owner", async () => {
        const paginatedResponse: ApiPaginatedResponse<ApiProject> = {
          items: [sampleApiProject],
          total: 1,
          page: 1,
          size: 10,
          has_more: false,
        }

        mockApiClient.get.mockResolvedValueOnce({
          data: paginatedResponse,
          status: 200,
        })

        const result = await repository.findByOwnerId(2001, {
          limit: 10,
          offset: 0,
        })

        expect(result.projects).toHaveLength(1)
        expect(result.totalCount).toBe(1)
        expect(result.hasMore).toBe(false)
        expect(mockApiClient.get).toHaveBeenCalledWith(
          "/projects/by-owner",
          expect.objectContaining({
            owner_id: 2001,
            page: 1,
            size: 10,
          })
        )
      })
    })

    describe("findByClientId", () => {
      test("should return paginated projects for client", async () => {
        const paginatedResponse: ApiPaginatedResponse<ApiProject> = {
          items: [sampleApiProject],
          total: 1,
          page: 1,
          size: 10,
          has_more: false,
        }

        mockApiClient.get.mockResolvedValueOnce({
          data: paginatedResponse,
          status: 200,
        })

        const result = await repository.findByClientId(1001)

        expect(result.projects).toHaveLength(1)
        expect(mockApiClient.get).toHaveBeenCalledWith(
          "/projects/by-client",
          expect.objectContaining({
            client_id: 1001,
          })
        )
      })
    })

    describe("findByMemberUserId", () => {
      test("should return projects where user is a member", async () => {
        const paginatedResponse: ApiPaginatedResponse<ApiProject> = {
          items: [sampleApiProject],
          total: 1,
          page: 1,
          size: 10,
          has_more: false,
        }

        mockApiClient.get.mockResolvedValueOnce({
          data: paginatedResponse,
          status: 200,
        })

        const result = await repository.findByMemberUserId(3001)

        expect(result.projects).toHaveLength(1)
        expect(mockApiClient.get).toHaveBeenCalledWith(
          "/projects/by-member",
          expect.objectContaining({
            member_user_id: 3001,
          })
        )
      })
    })

    describe("findByCriteria", () => {
      test("should return projects matching search criteria", async () => {
        const paginatedResponse: ApiPaginatedResponse<ApiProject> = {
          items: [sampleApiProject],
          total: 1,
          page: 1,
          size: 10,
          has_more: false,
        }

        mockApiClient.get.mockResolvedValueOnce({
          data: paginatedResponse,
          status: 200,
        })

        const criteria = {
          status: "Active",
          priority: "High" as const,
          tags: ["electrical"],
        }

        const result = await repository.findByCriteria(criteria)

        expect(result.projects).toHaveLength(1)
        expect(mockApiClient.get).toHaveBeenCalledWith(
          "/projects/search",
          expect.objectContaining({
            status: "Active",
            priority: "High",
            tags: "electrical",
          })
        )
      })
    })

    describe("count", () => {
      test("should return project count", async () => {
        mockApiClient.get.mockResolvedValueOnce({
          data: { count: 5 },
          status: 200,
        })

        const result = await repository.count()

        expect(result).toBe(5)
        expect(mockApiClient.get).toHaveBeenCalledWith("/projects/count", {})
      })

      test("should return project count with criteria", async () => {
        mockApiClient.get.mockResolvedValueOnce({
          data: { count: 3 },
          status: 200,
        })

        const criteria = { status: "Active" }
        const result = await repository.count(criteria)

        expect(result).toBe(3)
        expect(mockApiClient.get).toHaveBeenCalledWith("/projects/count", {
          status: "Active",
        })
      })
    })
  })

  describe("Business-Specific Operations", () => {
    describe("findProjectsRequiringAttention", () => {
      test("should return projects requiring attention", async () => {
        mockApiClient.get.mockResolvedValueOnce({
          data: [sampleApiProject],
          status: 200,
        })

        const result = await repository.findProjectsRequiringAttention(2001)

        expect(result).toHaveLength(1)
        expect(mockApiClient.get).toHaveBeenCalledWith(
          "/projects/requiring-attention",
          {
            user_id: 2001,
          }
        )
      })

      test("should return all projects requiring attention when no userId provided", async () => {
        mockApiClient.get.mockResolvedValueOnce({
          data: [sampleApiProject],
          status: 200,
        })

        const result = await repository.findProjectsRequiringAttention()

        expect(result).toHaveLength(1)
        expect(mockApiClient.get).toHaveBeenCalledWith(
          "/projects/requiring-attention",
          {}
        )
      })
    })

    describe("findProjectsWithExpiredMembers", () => {
      test("should return projects with expired members", async () => {
        const paginatedResponse: ApiPaginatedResponse<ApiProject> = {
          items: [sampleApiProject],
          total: 1,
          page: 1,
          size: 10,
          has_more: false,
        }

        mockApiClient.get.mockResolvedValueOnce({
          data: paginatedResponse,
          status: 200,
        })

        const result = await repository.findProjectsWithExpiredMembers()

        expect(result.projects).toHaveLength(1)
        expect(mockApiClient.get).toHaveBeenCalledWith(
          "/projects/with-expired-members",
          expect.objectContaining({
            page: 1,
            size: 10,
          })
        )
      })
    })

    describe("findOverBudgetProjects", () => {
      test("should return over-budget projects", async () => {
        const paginatedResponse: ApiPaginatedResponse<ApiProject> = {
          items: [{ ...sampleApiProject, budget_spent: 60000 }],
          total: 1,
          page: 1,
          size: 10,
          has_more: false,
        }

        mockApiClient.get.mockResolvedValueOnce({
          data: paginatedResponse,
          status: 200,
        })

        const result = await repository.findOverBudgetProjects()

        expect(result.projects).toHaveLength(1)
        expect(mockApiClient.get).toHaveBeenCalledWith(
          "/projects/over-budget",
          expect.objectContaining({
            page: 1,
            size: 10,
          })
        )
      })
    })

    describe("archiveCompletedProjects", () => {
      test("should archive completed projects successfully", async () => {
        // Mock findByIds to return completed projects
        const completedProject = Project.create({
          id: "completed_001",
          name: "Completed Project",
          ownerId: 2001,
        })
          .activateProject(2001)
          .completeProject(2001)

        vi.spyOn(repository, "findByIds").mockResolvedValueOnce([
          completedProject,
        ])

        mockApiClient.post.mockResolvedValueOnce({ status: 200 })

        await repository.archiveCompletedProjects(["completed_001"])

        expect(mockApiClient.post).toHaveBeenCalledWith("/projects/archive", {
          project_ids: ["completed_001"],
        })
      })

      test("should throw error when trying to archive non-completed projects", async () => {
        // Mock findByIds to return non-completed project
        vi.spyOn(repository, "findByIds").mockResolvedValueOnce([sampleProject])

        await expect(
          repository.archiveCompletedProjects(["project_001"])
        ).rejects.toThrow(ProjectRepositoryError)
      })
    })

    describe("validateAggregateConsistency", () => {
      test("should return true for consistent project", async () => {
        vi.spyOn(repository, "findById").mockResolvedValueOnce(sampleProject)

        mockApiClient.get.mockResolvedValueOnce({
          data: { is_consistent: true },
          status: 200,
        })

        const result =
          await repository.validateAggregateConsistency("project_001")

        expect(result).toBe(true)
        expect(mockApiClient.get).toHaveBeenCalledWith(
          "/projects/project_001/validate-consistency"
        )
      })

      test("should return false and log issues for inconsistent project", async () => {
        const consoleSpy = vi
          .spyOn(console, "warn")
          .mockImplementation(() => {})

        vi.spyOn(repository, "findById").mockResolvedValueOnce(sampleProject)

        mockApiClient.get.mockResolvedValueOnce({
          data: {
            is_consistent: false,
            issues: ["Budget mismatch", "Member role inconsistency"],
          },
          status: 200,
        })

        const result =
          await repository.validateAggregateConsistency("project_001")

        expect(result).toBe(false)
        expect(consoleSpy).toHaveBeenCalledWith(
          "Project project_001 consistency issues:",
          ["Budget mismatch", "Member role inconsistency"]
        )

        consoleSpy.mockRestore()
      })

      test("should throw ProjectNotFoundError when project does not exist", async () => {
        vi.spyOn(repository, "findById").mockResolvedValueOnce(null)

        await expect(
          repository.validateAggregateConsistency("nonexistent")
        ).rejects.toThrow(ProjectNotFoundError)
      })
    })
  })

  describe("Error Handling", () => {
    test("should handle network errors gracefully", async () => {
      mockApiClient.get.mockRejectedValueOnce(new Error("Network Error"))

      await expect(repository.findById("project_001")).rejects.toThrow(
        ProjectRepositoryError
      )
    })

    test("should handle malformed API responses", async () => {
      mockApiClient.get.mockResolvedValueOnce({
        data: null, // Malformed response
        status: 200,
      })

      const result = await repository.findById("project_001")

      expect(result).toBeNull()
    })

    test("should preserve error context for debugging", async () => {
      const originalError = new Error("Detailed API Error")
      mockApiClient.post.mockRejectedValueOnce(originalError)
      mockApiClient.get.mockRejectedValueOnce({ status: 404 }) // For exists check

      try {
        await repository.save(sampleProject)
      } catch (error) {
        expect(error).toBeInstanceOf(ProjectPersistenceError)
        expect((error as ProjectPersistenceError).message).toContain(
          "Failed to save project"
        )
      }
    })
  })

  describe("Caching Behavior", () => {
    test("should update cache on successful save", async () => {
      mockApiClient.get.mockRejectedValueOnce({ status: 404 }) // exists check
      mockApiClient.post.mockResolvedValueOnce({
        data: sampleApiProject,
        status: 201,
      })

      await repository.save(sampleProject)

      const cachedProject = queryClient.getQueryData(
        projectQueryKeys.detail("project_001")
      )
      expect(cachedProject).toBeDefined()
    })

    test("should invalidate appropriate caches on operations", async () => {
      const invalidateSpy = vi.spyOn(queryClient, "invalidateQueries")

      mockApiClient.delete.mockResolvedValueOnce({ status: 204 })

      await repository.remove("project_001")

      expect(invalidateSpy).toHaveBeenCalledWith({
        queryKey: projectQueryKeys.lists(),
      })
    })
  })
})
