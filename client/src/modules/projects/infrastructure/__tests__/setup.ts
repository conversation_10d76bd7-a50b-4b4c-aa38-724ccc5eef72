/**
 * Infrastructure Layer Test Setup
 *
 * Provides shared test utilities, mocks, and configuration for infrastructure tests.
 * Ensures consistent testing environment across all infrastructure components.
 */

import type {
  ApiPaginatedResponse,
  ApiProject,
  ApiProjectCreate,
  ApiProjectMember,
  ApiProjectUpdate,
} from "../adapters"
import type { ProjectApiClient } from "../ProjectRepository"

import { QueryClient } from "@tanstack/react-query"
import { vi } from "vitest"

/**
 * Creates a fresh QueryClient instance for testing
 * Configured with no retries and immediate cache expiry
 */
export function createTestQueryClient(): QueryClient {
  return new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
        gcTime: 0, // Immediate garbage collection
        staleTime: 0, // Always consider stale
      },
      mutations: {
        retry: false,
      },
    },
  })
}

/**
 * Creates a mock API client with all methods stubbed
 * Returns properly typed mock that can be configured per test
 */
export function createMockApiClient(): jest.Mocked<ProjectApiClient> {
  return {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
  }
}

/**
 * Test Data Factory - Project Fixtures
 */
export const ProjectTestFixtures = {
  /**
   * Complete API project with all fields populated
   */
  completeApiProject: (): ApiProject => ({
    id: "project_001",
    name: "Complete Test Project",
    description: "A comprehensive project for testing",
    owner_id: 2001,
    client_id: 1001,
    status: "Active",
    priority: "High",
    budget_total: 150000,
    budget_spent: 45000,
    budget_currency: "EUR",
    start_date: "2024-02-01T00:00:00Z",
    end_date: "2024-12-31T23:59:59Z",
    tags: ["electrical", "high-voltage", "industrial"],
    location: "Amsterdam, Netherlands",
    external_project_id: "EXT_PROJ_001",
    is_offline: false,
    requires_approval: true,
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-15T12:30:00Z",
    members: [],
  }),

  /**
   * Minimal API project with only required fields
   */
  minimalApiProject: (): ApiProject => ({
    id: "project_minimal",
    name: "Minimal Project",
    owner_id: 2001,
    status: "Draft",
    priority: "Low",
    created_at: "2024-01-15T10:00:00Z",
    updated_at: "2024-01-15T10:00:00Z",
  }),

  /**
   * API project with budget information
   */
  budgetedApiProject: (
    totalAmount: number = 100000,
    spentAmount: number = 25000
  ): ApiProject => ({
    ...ProjectTestFixtures.minimalApiProject(),
    id: "project_budget",
    name: "Budgeted Project",
    budget_total: totalAmount,
    budget_spent: spentAmount,
    budget_currency: "EUR",
  }),

  /**
   * Over-budget API project for testing
   */
  overBudgetApiProject: (): ApiProject => ({
    ...ProjectTestFixtures.budgetedApiProject(50000, 65000),
    id: "project_over_budget",
    name: "Over Budget Project",
    status: "Active",
  }),

  /**
   * Completed API project
   */
  completedApiProject: (): ApiProject => ({
    ...ProjectTestFixtures.completeApiProject(),
    id: "project_completed",
    name: "Completed Project",
    status: "Completed",
    budget_spent: 145000, // Near budget limit
  }),

  /**
   * Array of API projects for pagination testing
   */
  projectArray: (count: number = 3): ApiProject[] => {
    return Array.from({ length: count }, (_, index) => ({
      ...ProjectTestFixtures.completeApiProject(),
      id: `project_${String(index + 1).padStart(3, "0")}`,
      name: `Test Project ${index + 1}`,
      owner_id: 2001 + (index % 3), // Distribute across owners
      priority: (["Low", "Medium", "High", "Critical"] as const)[index % 4],
    }))
  },
}

/**
 * Test Data Factory - ProjectMember Fixtures
 */
export const ProjectMemberTestFixtures = {
  /**
   * Complete API project member
   */
  completeApiMember: (): ApiProjectMember => ({
    id: "member_001",
    user_id: 3001,
    project_id: "project_001",
    role: "Electrical Engineer",
    assigned_at: "2024-01-15T10:00:00Z",
    assigned_by: 2001,
    expires_at: "2024-12-31T23:59:59Z",
    is_active: true,
  }),

  /**
   * Permanent member (no expiration)
   */
  permanentApiMember: (): ApiProjectMember => ({
    ...ProjectMemberTestFixtures.completeApiMember(),
    id: "member_permanent",
    user_id: 3002,
    role: "Project Manager",
    expires_at: undefined,
  }),

  /**
   * Expired member for testing
   */
  expiredApiMember: (): ApiProjectMember => ({
    ...ProjectMemberTestFixtures.completeApiMember(),
    id: "member_expired",
    user_id: 3003,
    role: "Quality Assurance",
    expires_at: "2023-12-31T23:59:59Z", // Past date
    is_active: false,
  }),

  /**
   * Array of API members with different roles
   */
  memberArray: (count: number = 3): ApiProjectMember[] => {
    const roles = [
      "Project Manager",
      "Electrical Engineer",
      "Senior Engineer",
      "Quality Assurance",
      "Consultant",
    ]

    return Array.from({ length: count }, (_, index) => ({
      ...ProjectMemberTestFixtures.completeApiMember(),
      id: `member_${String(index + 1).padStart(3, "0")}`,
      user_id: 3001 + index,
      role: roles[index % roles.length],
      assigned_by: 2001,
      is_active: index % 4 !== 3, // Make every 4th member inactive
    }))
  },
}

/**
 * Mock Response Builders
 */
export const MockResponseBuilders = {
  /**
   * Creates a successful API response
   */
  success: <T>(data: T, status: number = 200) => ({
    data,
    status,
  }),

  /**
   * Creates a paginated API response
   */
  paginated: <T>(
    items: T[],
    page: number = 1,
    size: number = 10,
    total?: number
  ): { data: ApiPaginatedResponse<T>; status: number } => ({
    data: {
      items,
      total: total ?? items.length,
      page,
      size,
      has_more: page * size < (total ?? items.length),
    },
    status: 200,
  }),

  /**
   * Creates an error response
   */
  error: (message: string, status: number = 400) => ({
    status,
    message,
    response: { data: { detail: message } },
  }),

  /**
   * Creates a not found error
   */
  notFound: (resource: string = "Resource") =>
    MockResponseBuilders.error(`${resource} not found`, 404),

  /**
   * Creates a validation error
   */
  validationError: (field: string, issue: string) =>
    MockResponseBuilders.error(`Validation failed for ${field}: ${issue}`, 422),

  /**
   * Creates a server error
   */
  serverError: (message: string = "Internal server error") =>
    MockResponseBuilders.error(message, 500),
}

/**
 * Test Scenario Builders
 */
export const TestScenarios = {
  /**
   * Sets up a mock API client for successful CRUD operations
   */
  setupSuccessfulCrud: (mockApiClient: jest.Mocked<ProjectApiClient>) => {
    const testProject = ProjectTestFixtures.completeApiProject()

    // Setup standard responses
    mockApiClient.get.mockImplementation((url) => {
      if (url.includes("/exists")) {
        return Promise.resolve({ status: 200 })
      }
      if (url.includes("/projects/")) {
        return Promise.resolve(MockResponseBuilders.success(testProject))
      }
      return Promise.resolve(MockResponseBuilders.success([testProject]))
    })

    mockApiClient.post.mockResolvedValue(
      MockResponseBuilders.success(testProject, 201)
    )

    mockApiClient.put.mockResolvedValue(
      MockResponseBuilders.success({ ...testProject, name: "Updated Project" })
    )

    mockApiClient.delete.mockResolvedValue({ status: 204 })

    return testProject
  },

  /**
   * Sets up a mock API client that simulates various error conditions
   */
  setupErrorConditions: (mockApiClient: jest.Mocked<ProjectApiClient>) => {
    mockApiClient.get.mockImplementation((url) => {
      if (url.includes("nonexistent")) {
        return Promise.reject(MockResponseBuilders.notFound("Project"))
      }
      if (url.includes("server-error")) {
        return Promise.reject(MockResponseBuilders.serverError())
      }
      return Promise.resolve(
        MockResponseBuilders.success(ProjectTestFixtures.completeApiProject())
      )
    })

    mockApiClient.post.mockImplementation((url, data) => {
      if ((data as any)?.name === "invalid") {
        return Promise.reject(
          MockResponseBuilders.validationError("name", "Invalid project name")
        )
      }
      return Promise.resolve(
        MockResponseBuilders.success(
          ProjectTestFixtures.completeApiProject(),
          201
        )
      )
    })

    mockApiClient.put.mockImplementation((url) => {
      if (url.includes("readonly")) {
        return Promise.reject(
          MockResponseBuilders.error("Project is read-only", 403)
        )
      }
      return Promise.resolve(
        MockResponseBuilders.success(ProjectTestFixtures.completeApiProject())
      )
    })

    mockApiClient.delete.mockImplementation((url) => {
      if (url.includes("protected")) {
        return Promise.reject(
          MockResponseBuilders.error("Project cannot be deleted", 403)
        )
      }
      return Promise.resolve({ status: 204 })
    })
  },

  /**
   * Sets up pagination scenarios for testing
   */
  setupPaginationScenarios: (mockApiClient: jest.Mocked<ProjectApiClient>) => {
    const allProjects = ProjectTestFixtures.projectArray(25) // 25 projects total

    mockApiClient.get.mockImplementation((url, params: any) => {
      const page = params?.page || 1
      const size = params?.size || 10
      const startIndex = (page - 1) * size
      const endIndex = startIndex + size
      const pageItems = allProjects.slice(startIndex, endIndex)

      return Promise.resolve(
        MockResponseBuilders.paginated(
          pageItems,
          page,
          size,
          allProjects.length
        )
      )
    })

    return allProjects
  },
}

/**
 * Assertion Helpers
 */
export const AssertionHelpers = {
  /**
   * Asserts that an API call was made with expected parameters
   */
  expectApiCallWith: (
    mockFunction: jest.Mock,
    expectedUrl: string,
    expectedParams?: any
  ) => {
    expect(mockFunction).toHaveBeenCalledWith(expectedUrl, expectedParams)
  },

  /**
   * Asserts that a domain object has expected properties
   */
  expectDomainObjectProperties: (
    domainObject: any,
    expectedProperties: Record<string, any>
  ) => {
    Object.entries(expectedProperties).forEach(([key, value]) => {
      expect(domainObject[key]).toEqual(value)
    })
  },

  /**
   * Asserts that a paginated result has expected structure
   */
  expectPaginatedResult: (
    result: any,
    expectedItemCount: number,
    expectedTotal: number,
    expectedHasMore: boolean
  ) => {
    expect(result.items).toHaveLength(expectedItemCount)
    expect(result.totalCount).toBe(expectedTotal)
    expect(result.hasMore).toBe(expectedHasMore)
  },
}

/**
 * Cleanup utilities
 */
export const TestCleanup = {
  /**
   * Clears all mocks and resets call history
   */
  clearAllMocks: (mockApiClient: jest.Mocked<ProjectApiClient>) => {
    Object.values(mockApiClient).forEach((mockFn) => {
      if (typeof mockFn.mockClear === "function") {
        mockFn.mockClear()
      }
    })
  },

  /**
   * Resets query client state
   */
  resetQueryClient: (queryClient: QueryClient) => {
    queryClient.clear()
    queryClient.getQueryCache().clear()
    queryClient.getMutationCache().clear()
  },
}
