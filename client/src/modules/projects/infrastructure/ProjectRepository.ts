/**
 * ProjectRepository Implementation
 *
 * Concrete implementation of IProjectRepository using React Query for API integration.
 * Provides comprehensive CRUD operations and query capabilities for Project aggregates.
 *
 * This implementation bridges the domain layer with the REST API, handling:
 * - Domain-to-API data transformation
 * - Caching and optimistic updates via React Query
 * - Error handling and retry logic
 * - Aggregate consistency maintenance
 */
import type {
  ApiPaginatedResponse,
  ApiProject,
} from "./adapters"

import {
  QueryClient,
  useMutation,
  useQuery,
  useQueryClient,
} from "@tanstack/react-query"

import { Project } from "../domain/entities/Project"
import {
  IProjectRepository,
  ProjectNotFoundError,
  ProjectPaginationOptions,
  ProjectPersistenceError,
  ProjectQueryResult,
  ProjectRepositoryError,
  ProjectSearchCriteria,
} from "../domain/repositories/IProjectRepository"
import {
  apiPaginatedResponseToDomain,
  apiProjectToDomain,
  DomainProjectApiError,
  domainProjectToApiCreate,
  domainProjectToApiUpdate,
  domainSearchCriteriaToApiParams,
} from "./adapters"

/**
 * API client interface for Project operations
 * This would typically be injected or imported from a shared API client
 */
interface ProjectApiClient {
  get<T>(
    url: string,
    params?: Record<string, any>
  ): Promise<{ data: T; status: number }>
  post<T>(url: string, data: any): Promise<{ data: T; status: number }>
  put<T>(url: string, data: any): Promise<{ data: T; status: number }>
  delete(url: string): Promise<{ status: number }>
}

/**
 * React Query key factory for consistent cache key generation
 */
export const projectQueryKeys = {
  all: ["projects"] as const,
  lists: () => [...projectQueryKeys.all, "list"] as const,
  list: (criteria?: ProjectSearchCriteria) =>
    [...projectQueryKeys.lists(), criteria] as const,
  details: () => [...projectQueryKeys.all, "detail"] as const,
  detail: (id: string) => [...projectQueryKeys.details(), id] as const,
  search: (criteria: ProjectSearchCriteria) =>
    [...projectQueryKeys.all, "search", criteria] as const,
  stats: () => [...projectQueryKeys.all, "stats"] as const,
  byOwner: (ownerId: number) =>
    [...projectQueryKeys.all, "owner", ownerId] as const,
  byClient: (clientId: number) =>
    [...projectQueryKeys.all, "client", clientId] as const,
  byMember: (userId: number) =>
    [...projectQueryKeys.all, "member", userId] as const,
  requiresAttention: (userId?: number) =>
    [...projectQueryKeys.all, "attention", userId] as const,
  expiredMembers: () => [...projectQueryKeys.all, "expired-members"] as const,
  overBudget: () => [...projectQueryKeys.all, "over-budget"] as const,
} as const

/**
 * Concrete implementation of IProjectRepository using React Query
 */
export class ProjectRepository implements IProjectRepository {
  constructor(
    private readonly apiClient: ProjectApiClient,
    private readonly queryClient?: QueryClient
  ) {}

  // ===== Core CRUD Operations =====

  /**
   * Persists a Project aggregate to storage via API
   * Handles both creation and updates with optimistic UI updates
   */
  async save(project: Project): Promise<void> {
    try {
      const isNewProject = !(await this.exists(project.id))

      if (isNewProject) {
        // Create new project
        const apiData = domainProjectToApiCreate(project)
        const response = await this.apiClient.post<ApiProject>(
          "/projects",
          apiData
        )

        if (!response.data) {
          throw new ProjectPersistenceError(
            "No data returned from create operation",
            project.id
          )
        }

        // Update query cache with new project
        const domainProject = apiProjectToDomain(response.data)
        this.queryClient?.setQueryData(
          projectQueryKeys.detail(project.id),
          domainProject
        )

        // Invalidate list queries to include new project
        this.queryClient?.invalidateQueries({
          queryKey: projectQueryKeys.lists(),
        })
      } else {
        // Update existing project
        const apiData = domainProjectToApiUpdate(project)
        const response = await this.apiClient.put<ApiProject>(
          `/projects/${project.id}`,
          apiData
        )

        if (!response.data) {
          throw new ProjectPersistenceError(
            "No data returned from update operation",
            project.id
          )
        }

        // Update query cache with updated project
        const domainProject = apiProjectToDomain(response.data)
        this.queryClient?.setQueryData(
          projectQueryKeys.detail(project.id),
          domainProject
        )

        // Invalidate related queries
        this.queryClient?.invalidateQueries({
          queryKey: projectQueryKeys.byOwner(project.ownerId),
        })
        if (project.clientId) {
          this.queryClient?.invalidateQueries({
            queryKey: projectQueryKeys.byClient(project.clientId),
          })
        }
      }
    } catch (error) {
      if (error instanceof DomainProjectApiError) {
        throw new ProjectPersistenceError(error.message, project.id)
      }
      throw new ProjectPersistenceError(
        `Failed to save project: ${error instanceof Error ? error.message : "Unknown error"}`,
        project.id
      )
    }
  }

  /**
   * Retrieves a Project aggregate by its unique identifier
   */
  async findById(id: string): Promise<Project | null> {
    try {
      const response = await this.apiClient.get<ApiProject>(`/projects/${id}`)

      if (!response.data) {
        return null
      }

      return apiProjectToDomain(response.data)
    } catch (error: any) {
      if (error.status === 404) {
        return null
      }

      throw new ProjectRepositoryError(
        `Failed to retrieve project ${id}: ${error.message || "Unknown error"}`,
        "FIND_BY_ID_FAILED",
        id
      )
    }
  }

  /**
   * Retrieves multiple Project aggregates by their identifiers
   */
  async findByIds(ids: string[]): Promise<Project[]> {
    try {
      // Use batch API if available, otherwise make individual calls
      const promises = ids.map((id) => this.findById(id))
      const results = await Promise.allSettled(promises)

      return results
        .filter(
          (result): result is PromiseFulfilledResult<Project | null> =>
            result.status === "fulfilled" && result.value !== null
        )
        .map((result) => result.value!)
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to retrieve projects by IDs: ${error instanceof Error ? error.message : "Unknown error"}`,
        "BATCH_RETRIEVAL_FAILED"
      )
    }
  }

  /**
   * Retrieves all Project aggregates owned by a specific user
   */
  async findByOwnerId(
    ownerId: number,
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult> {
    try {
      const params = {
        owner_id: ownerId,
        page: options?.offset
          ? Math.floor(options.offset / (options.limit || 10)) + 1
          : 1,
        size: options?.limit || 10,
        sort_by: options?.sortBy || "createdAt",
        sort_order: options?.sortOrder || "desc",
      }

      const response = await this.apiClient.get<
        ApiPaginatedResponse<ApiProject>
      >("/projects/by-owner", params)

      const adapterResult = apiPaginatedResponseToDomain(response.data, apiProjectToDomain)
      return {
        projects: adapterResult.items,
        totalCount: adapterResult.totalCount,
        offset: adapterResult.offset,
        limit: adapterResult.limit,
        hasMore: adapterResult.hasMore,
      }
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to retrieve projects for owner ${ownerId}: ${error instanceof Error ? error.message : "Unknown error"}`,
        "OWNER_QUERY_FAILED"
      )
    }
  }

  /**
   * Retrieves all Project aggregates for a specific client
   */
  async findByClientId(
    clientId: number,
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult> {
    try {
      const params = {
        client_id: clientId,
        page: options?.offset
          ? Math.floor(options.offset / (options.limit || 10)) + 1
          : 1,
        size: options?.limit || 10,
        sort_by: options?.sortBy || "createdAt",
        sort_order: options?.sortOrder || "desc",
      }

      const response = await this.apiClient.get<
        ApiPaginatedResponse<ApiProject>
      >("/projects/by-client", params)

      const adapterResult = apiPaginatedResponseToDomain(response.data, apiProjectToDomain)
      return {
        projects: adapterResult.items,
        totalCount: adapterResult.totalCount,
        offset: adapterResult.offset,
        limit: adapterResult.limit,
        hasMore: adapterResult.hasMore,
      }
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to retrieve projects for client ${clientId}: ${error instanceof Error ? error.message : "Unknown error"}`,
        "CLIENT_QUERY_FAILED"
      )
    }
  }

  /**
   * Retrieves Project aggregates where a user is a team member
   */
  async findByMemberUserId(
    userId: number,
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult> {
    try {
      const params = {
        member_user_id: userId,
        page: options?.offset
          ? Math.floor(options.offset / (options.limit || 10)) + 1
          : 1,
        size: options?.limit || 10,
        sort_by: options?.sortBy || "createdAt",
        sort_order: options?.sortOrder || "desc",
      }

      const response = await this.apiClient.get<
        ApiPaginatedResponse<ApiProject>
      >("/projects/by-member", params)

      const adapterResult = apiPaginatedResponseToDomain(response.data, apiProjectToDomain)
      return {
        projects: adapterResult.items,
        totalCount: adapterResult.totalCount,
        offset: adapterResult.offset,
        limit: adapterResult.limit,
        hasMore: adapterResult.hasMore,
      }
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to retrieve projects for member ${userId}: ${error instanceof Error ? error.message : "Unknown error"}`,
        "MEMBER_QUERY_FAILED"
      )
    }
  }

  /**
   * Retrieves Project aggregates matching specific search criteria
   */
  async findByCriteria(
    criteria: ProjectSearchCriteria,
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult> {
    try {
      const apiParams = domainSearchCriteriaToApiParams(criteria)
      const params = {
        ...apiParams,
        page: options?.offset
          ? Math.floor(options.offset / (options.limit || 10)) + 1
          : 1,
        size: options?.limit || 10,
        sort_by: options?.sortBy || "createdAt",
        sort_order: options?.sortOrder || "desc",
      }

      const response = await this.apiClient.get<
        ApiPaginatedResponse<ApiProject>
      >("/projects/search", params)

      const adapterResult = apiPaginatedResponseToDomain(response.data, apiProjectToDomain)
      return {
        projects: adapterResult.items,
        totalCount: adapterResult.totalCount,
        offset: adapterResult.offset,
        limit: adapterResult.limit,
        hasMore: adapterResult.hasMore,
      }
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to search projects: ${error instanceof Error ? error.message : "Unknown error"}`,
        "SEARCH_FAILED"
      )
    }
  }

  /**
   * Retrieves all Project aggregates with optional pagination
   */
  async findAll(
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult> {
    try {
      const params = {
        page: options?.offset
          ? Math.floor(options.offset / (options.limit || 10)) + 1
          : 1,
        size: options?.limit || 10,
        sort_by: options?.sortBy || "createdAt",
        sort_order: options?.sortOrder || "desc",
      }

      const response = await this.apiClient.get<
        ApiPaginatedResponse<ApiProject>
      >("/projects", params)

      const adapterResult = apiPaginatedResponseToDomain(response.data, apiProjectToDomain)
      return {
        projects: adapterResult.items,
        totalCount: adapterResult.totalCount,
        offset: adapterResult.offset,
        limit: adapterResult.limit,
        hasMore: adapterResult.hasMore,
      }
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to retrieve all projects: ${error instanceof Error ? error.message : "Unknown error"}`,
        "FIND_ALL_FAILED"
      )
    }
  }

  /**
   * Checks if a project with the given ID exists
   */
  async exists(id: string): Promise<boolean> {
    try {
      const response = await this.apiClient.get(`/projects/${id}/exists`)
      return response.status === 200
    } catch (error: any) {
      if (error.status === 404) {
        return false
      }
      throw new ProjectRepositoryError(
        `Failed to check project existence: ${error.message || "Unknown error"}`,
        "EXISTENCE_CHECK_FAILED",
        id
      )
    }
  }

  /**
   * Counts Project aggregates matching specific criteria
   */
  async count(criteria?: ProjectSearchCriteria): Promise<number> {
    try {
      const apiParams = criteria
        ? domainSearchCriteriaToApiParams(criteria)
        : {}
      const response = await this.apiClient.get<{ count: number }>(
        "/projects/count",
        apiParams
      )

      return response.data.count
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to count projects: ${error instanceof Error ? error.message : "Unknown error"}`,
        "COUNT_FAILED"
      )
    }
  }

  /**
   * Removes a Project aggregate from storage
   */
  async remove(id: string): Promise<void> {
    try {
      await this.apiClient.delete(`/projects/${id}`)

      // Invalidate caches
      this.queryClient?.removeQueries({ queryKey: projectQueryKeys.detail(id) })
      this.queryClient?.invalidateQueries({
        queryKey: projectQueryKeys.lists(),
      })
    } catch (error: any) {
      if (error.status === 404) {
        throw new ProjectNotFoundError(id)
      }

      throw new ProjectPersistenceError(
        `Failed to remove project: ${error.message || "Unknown error"}`,
        id
      )
    }
  }

  /**
   * Removes multiple Project aggregates from storage
   */
  async removeMany(ids: string[]): Promise<void> {
    try {
      // Use batch delete API if available, otherwise individual deletes
      const promises = ids.map((id) => this.remove(id))
      await Promise.all(promises)
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to remove multiple projects: ${error instanceof Error ? error.message : "Unknown error"}`,
        "BATCH_DELETE_FAILED"
      )
    }
  }

  // ===== Business-Specific Query Methods =====

  /**
   * Retrieves Project aggregates requiring immediate attention
   * Business query for dashboard alerts and notifications
   */
  async findProjectsRequiringAttention(userId?: number): Promise<Project[]> {
    try {
      const params = userId ? { user_id: userId } : {}
      const response = await this.apiClient.get<ApiProject[]>(
        "/projects/requiring-attention",
        params
      )

      return response.data.map(apiProjectToDomain)
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to retrieve projects requiring attention: ${error instanceof Error ? error.message : "Unknown error"}`,
        "ATTENTION_QUERY_FAILED"
      )
    }
  }

  /**
   * Retrieves Project aggregates with expired team members
   * Maintenance query for team member lifecycle management
   */
  async findProjectsWithExpiredMembers(
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult> {
    try {
      const params = {
        page: options?.offset
          ? Math.floor(options.offset / (options.limit || 10)) + 1
          : 1,
        size: options?.limit || 10,
        sort_by: options?.sortBy || "updatedAt",
        sort_order: options?.sortOrder || "desc",
      }

      const response = await this.apiClient.get<
        ApiPaginatedResponse<ApiProject>
      >("/projects/with-expired-members", params)

      const adapterResult = apiPaginatedResponseToDomain(response.data, apiProjectToDomain)
      return {
        projects: adapterResult.items,
        totalCount: adapterResult.totalCount,
        offset: adapterResult.offset,
        limit: adapterResult.limit,
        hasMore: adapterResult.hasMore,
      }
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to retrieve projects with expired members: ${error instanceof Error ? error.message : "Unknown error"}`,
        "EXPIRED_MEMBERS_QUERY_FAILED"
      )
    }
  }

  /**
   * Retrieves Project aggregates that are over budget
   * Financial monitoring query for budget management
   */
  async findOverBudgetProjects(
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult> {
    try {
      const params = {
        page: options?.offset
          ? Math.floor(options.offset / (options.limit || 10)) + 1
          : 1,
        size: options?.limit || 10,
        sort_by: options?.sortBy || "updatedAt",
        sort_order: options?.sortOrder || "desc",
      }

      const response = await this.apiClient.get<
        ApiPaginatedResponse<ApiProject>
      >("/projects/over-budget", params)

      const adapterResult = apiPaginatedResponseToDomain(response.data, apiProjectToDomain)
      return {
        projects: adapterResult.items,
        totalCount: adapterResult.totalCount,
        offset: adapterResult.offset,
        limit: adapterResult.limit,
        hasMore: adapterResult.hasMore,
      }
    } catch (error) {
      throw new ProjectRepositoryError(
        `Failed to retrieve over-budget projects: ${error instanceof Error ? error.message : "Unknown error"}`,
        "OVER_BUDGET_QUERY_FAILED"
      )
    }
  }

  /**
   * Archives completed projects for long-term storage
   * Business operation for project lifecycle management
   */
  async archiveCompletedProjects(projectIds: string[]): Promise<void> {
    try {
      // Validate all projects are in completed state before archiving
      const projects = await this.findByIds(projectIds)
      const nonCompletedProjects = projects.filter(
        (project) => !project.isCompleted()
      )

      if (nonCompletedProjects.length > 0) {
        throw new ProjectRepositoryError(
          `Cannot archive non-completed projects: ${nonCompletedProjects.map((p) => p.id).join(", ")}`,
          "INVALID_ARCHIVE_STATE"
        )
      }

      // Use batch archive API
      await this.apiClient.post("/projects/archive", {
        project_ids: projectIds,
      })

      // Invalidate caches for archived projects
      projectIds.forEach((id) => {
        this.queryClient?.removeQueries({
          queryKey: projectQueryKeys.detail(id),
        })
      })
      this.queryClient?.invalidateQueries({
        queryKey: projectQueryKeys.lists(),
      })
    } catch (error) {
      if (error instanceof ProjectRepositoryError) {
        throw error
      }

      throw new ProjectRepositoryError(
        `Failed to archive projects: ${error instanceof Error ? error.message : "Unknown error"}`,
        "ARCHIVE_FAILED"
      )
    }
  }

  /**
   * Validates aggregate consistency and business rules
   * Infrastructure-level validation supporting domain integrity
   */
  async validateAggregateConsistency(id: string): Promise<boolean> {
    try {
      // First check if project exists
      const project = await this.findById(id)
      if (!project) {
        throw new ProjectNotFoundError(id)
      }

      // Validate project aggregate consistency via API
      const response = await this.apiClient.get<{
        is_consistent: boolean
        issues?: string[]
      }>(`/projects/${id}/validate-consistency`)

      // Log any consistency issues found
      if (response.data.issues?.length) {
        console.warn(`Project ${id} consistency issues:`, response.data.issues)
      }

      return response.data.is_consistent
    } catch (error) {
      if (error instanceof ProjectNotFoundError) {
        throw error
      }

      throw new ProjectRepositoryError(
        `Failed to validate project consistency: ${error instanceof Error ? error.message : "Unknown error"}`,
        "CONSISTENCY_VALIDATION_FAILED",
        id
      )
    }
  }
}

// ===== React Query Hooks for Repository Operations =====

/**
 * Custom hook for project repository operations
 * Provides a clean interface for components to interact with the repository
 */
export function useProjectRepository(apiClient: ProjectApiClient) {
  const queryClient = useQueryClient()
  const repository = new ProjectRepository(apiClient, queryClient)

  return {
    repository,

    // Query hooks
    useProject: (id: string) =>
      useQuery({
        queryKey: projectQueryKeys.detail(id),
        queryFn: () => repository.findById(id),
        enabled: !!id,
      }),

    useProjects: (
      criteria?: ProjectSearchCriteria,
      options?: ProjectPaginationOptions
    ) =>
      useQuery({
        queryKey: projectQueryKeys.list(criteria),
        queryFn: () => repository.findByCriteria(criteria || {}, options),
      }),

    useProjectsByOwner: (ownerId: number, options?: ProjectPaginationOptions) =>
      useQuery({
        queryKey: projectQueryKeys.byOwner(ownerId),
        queryFn: () => repository.findByOwnerId(ownerId, options),
        enabled: ownerId > 0,
      }),

    useProjectsByClient: (
      clientId: number,
      options?: ProjectPaginationOptions
    ) =>
      useQuery({
        queryKey: projectQueryKeys.byClient(clientId),
        queryFn: () => repository.findByClientId(clientId, options),
        enabled: clientId > 0,
      }),

    useProjectsByMember: (userId: number, options?: ProjectPaginationOptions) =>
      useQuery({
        queryKey: projectQueryKeys.byMember(userId),
        queryFn: () => repository.findByMemberUserId(userId, options),
        enabled: userId > 0,
      }),

    // Business-specific query hooks
    useProjectsRequiringAttention: (userId?: number) =>
      useQuery({
        queryKey: projectQueryKeys.requiresAttention(userId),
        queryFn: () => repository.findProjectsRequiringAttention(userId),
        refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes for attention alerts
      }),

    useProjectsWithExpiredMembers: (options?: ProjectPaginationOptions) =>
      useQuery({
        queryKey: projectQueryKeys.expiredMembers(),
        queryFn: () => repository.findProjectsWithExpiredMembers(options),
        refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
      }),

    useOverBudgetProjects: (options?: ProjectPaginationOptions) =>
      useQuery({
        queryKey: projectQueryKeys.overBudget(),
        queryFn: () => repository.findOverBudgetProjects(options),
        refetchInterval: 15 * 60 * 1000, // Refetch every 15 minutes
      }),

    // Mutation hooks
    useSaveProject: () =>
      useMutation({
        mutationFn: (project: Project) => repository.save(project),
        onSuccess: (_, project) => {
          // Invalidate specific queries that might be affected
          queryClient.invalidateQueries({ queryKey: projectQueryKeys.lists() })
          queryClient.invalidateQueries({
            queryKey: projectQueryKeys.byOwner(project.ownerId),
          })
          if (project.clientId) {
            queryClient.invalidateQueries({
              queryKey: projectQueryKeys.byClient(project.clientId),
            })
          }

          // Invalidate attention queries since project status might have changed
          queryClient.invalidateQueries({
            queryKey: projectQueryKeys.requiresAttention(),
          })
        },
      }),

    useDeleteProject: () =>
      useMutation({
        mutationFn: (id: string) => repository.remove(id),
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: projectQueryKeys.lists() })
          queryClient.invalidateQueries({
            queryKey: projectQueryKeys.requiresAttention(),
          })
        },
      }),

    useArchiveProjects: () =>
      useMutation({
        mutationFn: (projectIds: string[]) =>
          repository.archiveCompletedProjects(projectIds),
        onSuccess: () => {
          queryClient.invalidateQueries({ queryKey: projectQueryKeys.lists() })
          queryClient.invalidateQueries({
            queryKey: projectQueryKeys.requiresAttention(),
          })
        },
      }),

    useValidateProjectConsistency: () =>
      useMutation({
        mutationFn: (id: string) => repository.validateAggregateConsistency(id),
        onSuccess: (isConsistent, id) => {
          if (!isConsistent) {
            // Refetch project data if inconsistency detected
            queryClient.invalidateQueries({
              queryKey: projectQueryKeys.detail(id),
            })
          }
        },
      }),
  }
}
