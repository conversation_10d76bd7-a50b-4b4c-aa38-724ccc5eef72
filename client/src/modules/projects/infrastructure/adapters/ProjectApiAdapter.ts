/**
 * Project API Domain Adapter
 *
 * Bridges the gap between API layer (DTOs) and domain layer (rich domain objects).
 * Handles bidirectional transformation between API responses and Project domain entities.
 *
 * Following DDD principles, this adapter ensures that domain objects remain
 * infrastructure-agnostic while enabling seamless API integration.
 */

import { CreateProjectData, Project } from "../../domain/entities/Project"
import { ProjectMember } from "../../domain/entities/ProjectMember"
import { ProjectBudget } from "../../domain/value-objects/ProjectBudget"
import { ProjectStatus } from "../../domain/value-objects/ProjectStatus"
import { TeamRole } from "../../domain/value-objects/TeamRole"

// API DTOs (matching backend API structure)
export interface ApiProject {
  id: string
  name: string
  description?: string
  owner_id: number
  client_id?: number
  status: string
  priority: "Low" | "Medium" | "High" | "Critical"
  budget_total?: number
  budget_spent?: number
  budget_currency?: string
  start_date?: string
  end_date?: string
  tags?: string[]
  location?: string
  external_project_id?: string
  is_offline?: boolean
  requires_approval?: boolean
  created_at: string
  updated_at: string
  members?: ApiProjectMember[]
}

export interface ApiProjectMember {
  id: string
  user_id: number
  project_id: string
  role: string
  assigned_at: string
  assigned_by: number
  expires_at?: string
  is_active: boolean
}

export interface ApiProjectCreate {
  name: string
  description?: string
  owner_id: number
  client_id?: number
  priority?: "Low" | "Medium" | "High" | "Critical"
  budget_total?: number
  budget_currency?: string
  start_date?: string
  end_date?: string
  tags?: string[]
  location?: string
  external_project_id?: string
}

export interface ApiProjectUpdate {
  name?: string
  description?: string
  status?: string
  priority?: "Low" | "Medium" | "High" | "Critical"
  budget_total?: number
  budget_spent?: number
  budget_currency?: string
  start_date?: string
  end_date?: string
  tags?: string[]
  location?: string
  requires_approval?: boolean
}

export interface ApiPaginatedResponse<T> {
  items: T[]
  total: number
  page: number
  size: number
  has_more: boolean
}

/**
 * Domain Adapter Error for infrastructure-related issues
 */
export class ProjectAdapterError extends Error {
  constructor(
    message: string,
    public readonly operation: "TO_DOMAIN" | "TO_API",
    public readonly originalData?: any
  ) {
    super(message)
    this.name = "ProjectAdapterError"
  }
}

/**
 * Converts API Project DTO to domain Project entity
 *
 * @param apiProject - Raw API project data
 * @returns Domain Project entity with rich business behavior
 * @throws ProjectAdapterError if transformation fails
 */
export function apiProjectToDomain(apiProject: ApiProject): Project {
  try {
    // Transform API data to domain creation format
    const createData: CreateProjectData = {
      id: apiProject.id,
      name: apiProject.name,
      description: apiProject.description,
      ownerId: apiProject.owner_id,
      clientId: apiProject.client_id,
      priority: apiProject.priority || "Medium",
      startDate: apiProject.start_date,
      endDate: apiProject.end_date,
      tags: apiProject.tags,
      location: apiProject.location,
      externalProjectId: apiProject.external_project_id,
      budgetData: apiProject.budget_total
        ? {
            totalAmount: apiProject.budget_total,
            spentAmount: apiProject.budget_spent || 0,
            currency: apiProject.budget_currency || "EUR",
          }
        : undefined,
    }

    // Convert API members to domain members
    const domainMembers = (apiProject.members || []).map(apiMemberToDomain)
    
    // Ensure owner is always included as administrator member
    const ownerExists = domainMembers.some(member => member.userId === apiProject.owner_id)
    if (!ownerExists) {
      const ownerMember = ProjectMember.create({
        id: `${apiProject.id}_owner`,
        userId: apiProject.owner_id,
        projectId: apiProject.id,
        role: TeamRole.createAdministrator(),
        assignedAt: apiProject.created_at,
        assignedBy: apiProject.owner_id,
        isActive: true,
      })
      domainMembers.push(ownerMember)
    }

    // Create project using domain factory
    const project = Project.restore({
      ...createData,
      status: ProjectStatus.create({
        status: apiProject.status as any,
        lastTransitionAt: apiProject.updated_at,
      }),
      budget: createData.budgetData
        ? ProjectBudget.create(createData.budgetData)
        : ProjectBudget.createEmpty(),
      members: domainMembers,
      requiresApproval: apiProject.requires_approval || false,
      createdAt: apiProject.created_at,
      updatedAt: apiProject.updated_at,
    })

    return project
  } catch (error) {
    throw new ProjectAdapterError(
      `Failed to convert API project to domain: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_DOMAIN",
      apiProject
    )
  }
}

/**
 * Converts API ProjectMember DTO to domain ProjectMember entity
 */
function apiMemberToDomain(apiMember: ApiProjectMember): ProjectMember {
  return ProjectMember.create({
    id: apiMember.id,
    userId: apiMember.user_id,
    projectId: apiMember.project_id,
    role: TeamRole.create({ role: apiMember.role }),
    assignedAt: apiMember.assigned_at,
    assignedBy: apiMember.assigned_by,
    expiresAt: apiMember.expires_at,
    isActive: apiMember.is_active,
  })
}

/**
 * Converts domain Project entity to API creation format
 *
 * @param project - Domain Project entity
 * @returns API-compatible project creation data
 * @throws ProjectAdapterError if transformation fails
 */
export function domainProjectToApiCreate(project: Project): ApiProjectCreate {
  try {
    return {
      name: project.name,
      description: project.description,
      owner_id: project.ownerId,
      client_id: project.clientId,
      priority: project.priority,
      budget_total:
        project.budget.totalAmount > 0 ? project.budget.totalAmount : undefined,
      budget_currency:
        project.budget.totalAmount > 0 ? project.budget.currency : undefined,
      start_date: project.startDate,
      end_date: project.endDate,
      tags: project.tags?.length ? project.tags : undefined,
      location: project.location,
      external_project_id: project.externalProjectId,
    }
  } catch (error) {
    throw new ProjectAdapterError(
      `Failed to convert domain project to API create: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_API",
      project
    )
  }
}

/**
 * Converts domain Project entity to API update format
 * Only includes changed fields to optimize network usage
 */
export function domainProjectToApiUpdate(
  project: Project,
  changedFields?: Set<string>
): ApiProjectUpdate {
  try {
    const update: ApiProjectUpdate = {}

    // Include only changed fields if specified, otherwise include all updatable fields
    const shouldInclude = (field: string) =>
      !changedFields || changedFields.has(field)

    if (shouldInclude("name")) update.name = project.name
    if (shouldInclude("description")) update.description = project.description
    if (shouldInclude("status")) update.status = project.status.status
    if (shouldInclude("priority")) update.priority = project.priority
    if (shouldInclude("budget") && project.budget.totalAmount > 0) {
      update.budget_total = project.budget.totalAmount
      update.budget_spent = project.budget.spentAmount
      update.budget_currency = project.budget.currency
    }
    if (shouldInclude("startDate")) update.start_date = project.startDate
    if (shouldInclude("endDate")) update.end_date = project.endDate
    if (shouldInclude("tags")) update.tags = project.tags
    if (shouldInclude("location")) update.location = project.location
    if (shouldInclude("requiresApproval"))
      update.requires_approval = project.requiresApproval()

    return update
  } catch (error) {
    throw new ProjectAdapterError(
      `Failed to convert domain project to API update: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_API",
      project
    )
  }
}

/**
 * Converts API paginated response to domain-compatible format
 *
 * @param apiResponse - API paginated response
 * @returns Domain-compatible pagination result
 */
export function apiPaginatedResponseToDomain<T, D>(
  apiResponse: ApiPaginatedResponse<T>,
  itemConverter: (item: T) => D
): {
  items: D[]
  totalCount: number
  offset: number
  limit: number
  hasMore: boolean
} {
  try {
    return {
      items: apiResponse.items.map(itemConverter),
      totalCount: apiResponse.total,
      offset: (apiResponse.page - 1) * apiResponse.size,
      limit: apiResponse.size,
      hasMore: apiResponse.has_more,
    }
  } catch (error) {
    throw new ProjectAdapterError(
      `Failed to convert API paginated response: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_DOMAIN",
      apiResponse
    )
  }
}

/**
 * Converts domain search criteria to API query parameters
 *
 * @param criteria - Domain search criteria
 * @returns API-compatible query parameters
 */
export function domainSearchCriteriaToApiParams(criteria: {
  clientId?: number
  ownerId?: number
  status?: string
  priority?: string
  tags?: string[]
  createdAfter?: string
  createdBefore?: string
  budgetMin?: number
  budgetMax?: number
  currency?: string
  memberUserId?: number
  location?: string
  hasExpiredMembers?: boolean
  requiresApproval?: boolean
}): Record<string, any> {
  const params: Record<string, any> = {}

  if (criteria.clientId !== undefined) params.client_id = criteria.clientId
  if (criteria.ownerId !== undefined) params.owner_id = criteria.ownerId
  if (criteria.status) params.status = criteria.status
  if (criteria.priority) params.priority = criteria.priority
  if (criteria.tags?.length) params.tags = criteria.tags.join(",")
  if (criteria.createdAfter) params.created_after = criteria.createdAfter
  if (criteria.createdBefore) params.created_before = criteria.createdBefore
  if (criteria.budgetMin !== undefined) params.budget_min = criteria.budgetMin
  if (criteria.budgetMax !== undefined) params.budget_max = criteria.budgetMax
  if (criteria.currency) params.currency = criteria.currency
  if (criteria.memberUserId !== undefined)
    params.member_user_id = criteria.memberUserId
  if (criteria.location) params.location = criteria.location
  if (criteria.hasExpiredMembers !== undefined)
    params.has_expired_members = criteria.hasExpiredMembers
  if (criteria.requiresApproval !== undefined)
    params.requires_approval = criteria.requiresApproval

  return params
}

/**
 * Handles API errors and converts them to domain-friendly format
 */
export class DomainProjectApiError extends Error {
  constructor(
    message: string,
    public readonly originalError: any,
    public readonly context:
      | "create"
      | "update"
      | "delete"
      | "search"
      | "validation"
      | "member_operation"
  ) {
    super(message)
    this.name = "DomainProjectApiError"
  }

  static fromApiError(
    apiError: any,
    context: DomainProjectApiError["context"]
  ): DomainProjectApiError {
    let message = "Unknown API error"

    if (apiError?.detail) {
      message = apiError.detail
    } else if (apiError?.message) {
      message = apiError.message
    } else if (typeof apiError === "string") {
      message = apiError
    }

    return new DomainProjectApiError(message, apiError, context)
  }
}

/**
 * Utility function to handle API responses with domain conversion
 * Provides consistent error handling and data transformation
 */
export async function handleApiResponseWithDomain<T, D>(
  apiPromise: Promise<{ data?: T; error?: any; status: number }>,
  converter: (data: T) => D,
  context: DomainProjectApiError["context"]
): Promise<{ data?: D; error?: DomainProjectApiError }> {
  try {
    const response = await apiPromise

    if (response.error) {
      return {
        error: DomainProjectApiError.fromApiError(response.error, context),
      }
    }

    if (response.data) {
      try {
        return {
          data: converter(response.data),
        }
      } catch (conversionError) {
        return {
          error: new DomainProjectApiError(
            `Data conversion failed: ${conversionError instanceof Error ? conversionError.message : "Unknown error"}`,
            conversionError,
            context
          ),
        }
      }
    }

    return {
      error: new DomainProjectApiError(
        "No data received from API",
        null,
        context
      ),
    }
  } catch (error) {
    return {
      error: DomainProjectApiError.fromApiError(error, context),
    }
  }
}
