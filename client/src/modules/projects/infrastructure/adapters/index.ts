/**
 * Infrastructure Adapters Index
 *
 * Centralized exports for all domain-to-API transformation utilities.
 * Provides a clean interface for the infrastructure layer components.
 */

// Project Entity Adapters
export {
  type ApiProject,
  type ApiProjectCreate,
  type ApiProjectUpdate,
  type ApiPaginatedResponse,
  ProjectAdapterError,
  apiProjectToDomain,
  domainProjectToApiCreate,
  domainProjectToApiUpdate,
  apiPaginatedResponseToDomain,
  domainSearchCriteriaToApiParams,
  DomainProjectApiError,
  handleApiResponseWithDomain,
} from "./ProjectApiAdapter"

// ProjectMember Entity Adapters
export {
  type ApiProjectMember,
  type ApiProjectMemberCreate,
  type ApiProjectMemberUpdate,
  type ApiTeamAssignmentContext,
  type ApiBulkMemberOperation,
  type ApiBulkMemberResult,
  ProjectMemberAdapterError,
  apiProjectMemberToDomain,
  apiProjectMembersToDomain,
  domainProjectMemberToApiCreate,
  domainProjectMemberToApiUpdate,
  domainAssignmentContextToApi,
  apiAssignmentContextToDomain,
  domainMemberSearchToApiParams,
  domainBulkMemberOpsToApi,
  apiBulkMemberResultToDomain,
} from "./ProjectMemberAdapter"

// Value Object Adapters
export {
  type ApiProjectStatus,
  type ApiProjectBudget,
  type ApiTeamRole,
  ValueObjectAdapterError,
  // ProjectStatus adapters
  apiProjectStatusToDomain,
  domainProjectStatusToApi,
  apiStatusStringToDomain,
  // ProjectBudget adapters
  apiProjectBudgetToDomain,
  domainProjectBudgetToApi,
  apiBudgetFieldsToDomain,
  createEmptyDomainBudget,
  // TeamRole adapters
  apiTeamRoleToDomain,
  domainTeamRoleToApi,
  apiRoleStringToDomain,
  apiRoleStringsToDomain,
  // Utility functions
  validateApiDataForDomain,
  batchConvertApiToDomain,
  ValueObjectAdapterFactories,
} from "./ValueObjectAdapters"

// Common adapter types and utilities
export interface AdapterResult<T> {
  data?: T
  error?: Error
  warnings?: string[]
}

export interface BatchAdapterResult<T> {
  successful: T[]
  failed: Array<{
    originalData: any
    error: string
  }>
  statistics: {
    total: number
    successful: number
    failed: number
  }
}

/**
 * Common adapter error types for consistent error handling
 */
export const AdapterErrorTypes = {
  CONVERSION_FAILED: "CONVERSION_FAILED",
  VALIDATION_FAILED: "VALIDATION_FAILED",
  MISSING_REQUIRED_FIELD: "MISSING_REQUIRED_FIELD",
  INVALID_DATA_TYPE: "INVALID_DATA_TYPE",
  BUSINESS_RULE_VIOLATION: "BUSINESS_RULE_VIOLATION",
} as const

export type AdapterErrorType =
  (typeof AdapterErrorTypes)[keyof typeof AdapterErrorTypes]

/**
 * Base adapter error class with consistent structure
 */
export class BaseAdapterError extends Error {
  constructor(
    message: string,
    public readonly errorType: AdapterErrorType,
    public readonly operation: "TO_DOMAIN" | "TO_API",
    public readonly originalData?: any
  ) {
    super(message)
    this.name = "BaseAdapterError"
  }
}
