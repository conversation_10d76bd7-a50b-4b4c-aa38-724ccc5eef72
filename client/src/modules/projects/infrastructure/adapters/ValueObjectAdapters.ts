/**
 * Value Object API Domain Adapters
 *
 * Handles bidirectional transformation between API DTOs and Value Objects.
 * Ensures that value object invariants are maintained during API operations.
 */

import { ProjectStatusEnum } from "@/types/api"
import { ProjectBudget } from "../../domain/value-objects/ProjectBudget"
import {
  ProjectStatus,
} from "../../domain/value-objects/ProjectStatus"
import { TeamRole } from "../../domain/value-objects/TeamRole"

/**
 * API DTOs for Value Objects
 */
export interface ApiProjectStatus {
  status: string
  last_transition_at?: string
  reason?: string
  can_transition_to?: string[]
}

export interface ApiProjectBudget {
  total_amount: number
  spent_amount?: number
  currency: string
  allocated_amount?: number
  remaining_amount?: number
  is_over_budget?: boolean
  budget_utilization_percent?: number
}

export interface ApiTeamRole {
  role: string
  permissions?: string[]
  hierarchy_level?: number
  can_assign_roles?: string[]
  can_manage_project?: boolean
  can_view_budget?: boolean
}

/**
 * Value Object Adapter Error
 */
export class ValueObjectAdapterError extends Error {
  constructor(
    message: string,
    public readonly operation: "TO_DOMAIN" | "TO_API",
    public readonly valueObjectType:
      | "ProjectStatus"
      | "ProjectBudget"
      | "TeamRole",
    public readonly originalData?: any
  ) {
    super(message)
    this.name = "ValueObjectAdapterError"
  }
}

// ===== ProjectStatus Adapters =====

/**
 * Converts API ProjectStatus DTO to domain ProjectStatus value object
 *
 * @param apiStatus - Raw API project status data
 * @returns Domain ProjectStatus value object
 * @throws ValueObjectAdapterError if transformation fails
 */
export function apiProjectStatusToDomain(
  apiStatus: ApiProjectStatus
): ProjectStatus {
  try {
    return ProjectStatus.create({
      status: apiStatus.status as ProjectStatusEnum,
      changedAt: apiStatus.last_transition_at,
      reason: apiStatus.reason,
    })
  } catch (error) {
    throw new ValueObjectAdapterError(
      `Failed to convert API project status to domain: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_DOMAIN",
      "ProjectStatus",
      apiStatus
    )
  }
}

/**
 * Converts domain ProjectStatus value object to API format
 *
 * @param status - Domain ProjectStatus value object
 * @returns API-compatible project status data
 * @throws ValueObjectAdapterError if transformation fails
 */
export function domainProjectStatusToApi(
  status: ProjectStatus
): ApiProjectStatus {
  try {
    return {
      status: status.status,
      last_transition_at: status.changedAt,
      reason: status.reason,
      can_transition_to: status.getValidTransitions(),
    }
  } catch (error) {
    throw new ValueObjectAdapterError(
      `Failed to convert domain project status to API: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_API",
      "ProjectStatus",
      status
    )
  }
}

/**
 * Converts simple API status string to domain ProjectStatus
 *
 * @param statusString - Simple status string from API
 * @param lastTransitionAt - Optional last transition timestamp
 * @returns Domain ProjectStatus value object
 */
export function apiStatusStringToDomain(
  statusString: string,
  lastTransitionAt?: string
): ProjectStatus {
  return ProjectStatus.create({
    status: statusString as ProjectStatusEnum,
    changedAt: lastTransitionAt,
  })
}

// ===== ProjectBudget Adapters =====

/**
 * Converts API ProjectBudget DTO to domain ProjectBudget value object
 *
 * @param apiBudget - Raw API project budget data
 * @returns Domain ProjectBudget value object
 * @throws ValueObjectAdapterError if transformation fails
 */
export function apiProjectBudgetToDomain(
  apiBudget: ApiProjectBudget
): ProjectBudget {
  try {
    return ProjectBudget.create({
      totalAmount: apiBudget.total_amount,
      spentAmount: apiBudget.spent_amount || 0,
      currency: apiBudget.currency,
      allocatedAmount: apiBudget.allocated_amount,
    })
  } catch (error) {
    throw new ValueObjectAdapterError(
      `Failed to convert API project budget to domain: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_DOMAIN",
      "ProjectBudget",
      apiBudget
    )
  }
}

/**
 * Converts domain ProjectBudget value object to API format
 *
 * @param budget - Domain ProjectBudget value object
 * @returns API-compatible project budget data
 * @throws ValueObjectAdapterError if transformation fails
 */
export function domainProjectBudgetToApi(
  budget: ProjectBudget
): ApiProjectBudget {
  try {
    return {
      total_amount: budget.totalAmount,
      spent_amount: budget.spentAmount,
      currency: budget.currency,
      allocated_amount: budget.allocatedAmount,
      remaining_amount: budget.getRemainingAmount(),
      is_over_budget: budget.isOverBudget(),
      budget_utilization_percent: budget.getUtilizationPercentage(),
    }
  } catch (error) {
    throw new ValueObjectAdapterError(
      `Failed to convert domain project budget to API: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_API",
      "ProjectBudget",
      budget
    )
  }
}

/**
 * Converts simple API budget fields to domain ProjectBudget
 *
 * @param totalAmount - Total budget amount
 * @param spentAmount - Spent budget amount
 * @param currency - Currency code
 * @returns Domain ProjectBudget value object
 */
export function apiBudgetFieldsToDomain(
  totalAmount: number,
  spentAmount: number = 0,
  currency: string = "EUR"
): ProjectBudget {
  return ProjectBudget.create({
    totalAmount,
    spentAmount,
    currency,
  })
}

/**
 * Creates empty domain ProjectBudget for projects without budget
 *
 * @returns Empty domain ProjectBudget value object
 */
export function createEmptyDomainBudget(): ProjectBudget {
  return ProjectBudget.createEmpty()
}

// ===== TeamRole Adapters =====

/**
 * Converts API TeamRole DTO to domain TeamRole value object
 *
 * @param apiRole - Raw API team role data
 * @returns Domain TeamRole value object
 * @throws ValueObjectAdapterError if transformation fails
 */
export function apiTeamRoleToDomain(apiRole: ApiTeamRole): TeamRole {
  try {
    // Create base role
    const role = TeamRole.create({ role: apiRole.role })

    // The domain TeamRole already contains all business logic,
    // so we don't need to override permissions from API
    return role
  } catch (error) {
    throw new ValueObjectAdapterError(
      `Failed to convert API team role to domain: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_DOMAIN",
      "TeamRole",
      apiRole
    )
  }
}

/**
 * Converts domain TeamRole value object to API format
 *
 * @param role - Domain TeamRole value object
 * @returns API-compatible team role data
 * @throws ValueObjectAdapterError if transformation fails
 */
export function domainTeamRoleToApi(role: TeamRole): ApiTeamRole {
  try {
    return {
      role: role.role,
      permissions: role.getPermissions(),
      hierarchy_level: role.getHierarchyLevel(),
      can_assign_roles: role.getAssignableRoles(),
      can_manage_project: role.canManageProject(),
      can_view_budget: role.canViewBudget(),
    }
  } catch (error) {
    throw new ValueObjectAdapterError(
      `Failed to convert domain team role to API: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_API",
      "TeamRole",
      role
    )
  }
}

/**
 * Converts simple API role string to domain TeamRole
 *
 * @param roleString - Simple role string from API
 * @returns Domain TeamRole value object
 */
export function apiRoleStringToDomain(roleString: string): TeamRole {
  return TeamRole.create({ role: roleString })
}

/**
 * Converts array of API role strings to domain TeamRole array
 *
 * @param roleStrings - Array of role strings from API
 * @returns Array of domain TeamRole value objects
 */
export function apiRoleStringsToDomain(roleStrings: string[]): TeamRole[] {
  return roleStrings.map(apiRoleStringToDomain)
}

// ===== Utility Functions =====

/**
 * Validates that API data can be safely converted to domain value objects
 *
 * @param apiData - Raw API data
 * @param valueObjectType - Type of value object being validated
 * @returns Validation result with errors if any
 */
export function validateApiDataForDomain(
  apiData: any,
  valueObjectType: "ProjectStatus" | "ProjectBudget" | "TeamRole"
): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  switch (valueObjectType) {
    case "ProjectStatus":
      if (!apiData.status) {
        errors.push("Status is required")
      }
      break

    case "ProjectBudget":
      if (typeof apiData.total_amount !== "number") {
        errors.push("Total amount must be a number")
      }
      if (apiData.total_amount < 0) {
        errors.push("Total amount cannot be negative")
      }
      if (!apiData.currency) {
        errors.push("Currency is required")
      }
      break

    case "TeamRole":
      if (!apiData.role) {
        errors.push("Role is required")
      }
      break
  }

  return {
    isValid: errors.length === 0,
    errors,
  }
}

/**
 * Batch converts multiple API value objects to domain objects
 * Provides error resilience for bulk operations
 *
 * @param apiObjects - Array of API objects
 * @param converter - Conversion function
 * @returns Conversion results with successful and failed items
 */
export function batchConvertApiToDomain<T, D>(
  apiObjects: T[],
  converter: (apiObject: T) => D
): {
  successful: D[]
  failed: Array<{
    originalData: T
    error: string
  }>
  statistics: {
    total: number
    successful: number
    failed: number
  }
} {
  const successful: D[] = []
  const failed: Array<{ originalData: T; error: string }> = []

  apiObjects.forEach((apiObject) => {
    try {
      const domainObject = converter(apiObject)
      successful.push(domainObject)
    } catch (error) {
      failed.push({
        originalData: apiObject,
        error:
          error instanceof Error ? error.message : "Unknown conversion error",
      })
    }
  })

  return {
    successful,
    failed,
    statistics: {
      total: apiObjects.length,
      successful: successful.length,
      failed: failed.length,
    },
  }
}

/**
 * Creates factory functions for common value object conversions
 */
export const ValueObjectAdapterFactories = {
  /**
   * Creates a ProjectStatus from minimal API data
   */
  createStatusFromApi: (status: string, lastTransitionAt?: string) =>
    apiStatusStringToDomain(status, lastTransitionAt),

  /**
   * Creates a ProjectBudget from minimal API data
   */
  createBudgetFromApi: (
    totalAmount: number,
    spentAmount?: number,
    currency?: string
  ) => apiBudgetFieldsToDomain(totalAmount, spentAmount, currency),

  /**
   * Creates a TeamRole from minimal API data
   */
  createRoleFromApi: (role: string) => apiRoleStringToDomain(role),

  /**
   * Creates empty/default value objects
   */
  createEmptyBudget: () => createEmptyDomainBudget(),
  createDefaultStatus: () => ProjectStatus.createDraft(),
  createViewerRole: () => TeamRole.createViewer(),
} as const
