/**
 * ProjectMember API Domain Adapter
 *
 * Handles bidirectional transformation between API DTOs and ProjectMember domain entities.
 * Ensures clean separation between infrastructure and domain concerns.
 */

import {
  CreateProjectMemberData,
  ProjectMember,
} from "../../domain/entities/ProjectMember"
import { TeamRole } from "../../domain/value-objects/TeamRole"

/**
 * API DTOs for ProjectMember operations
 */
export interface ApiProjectMember {
  id: string
  user_id: number
  project_id: string
  role: string
  assigned_at: string
  assigned_by: number
  expires_at?: string
  is_active: boolean
  user?: {
    id: number
    email: string
    first_name: string
    last_name: string
    is_active: boolean
  }
}

export interface ApiProjectMemberCreate {
  user_id: number
  role: string
  assigned_by: number
  expires_at?: string
}

export interface ApiProjectMemberUpdate {
  role?: string
  expires_at?: string
  is_active?: boolean
}

export interface ApiTeamAssignmentContext {
  assigned_by: number
  reason?: string
  priority?: "Low" | "Medium" | "High" | "Critical"
  skills_required?: string[]
  estimated_workload?: number
  is_temporary_assignment?: boolean
  requires_approval?: boolean
}

/**
 * ProjectMember Adapter Error
 */
export class ProjectMemberAdapterError extends Error {
  constructor(
    message: string,
    public readonly operation: "TO_DOMAIN" | "TO_API",
    public readonly originalData?: any
  ) {
    super(message)
    this.name = "ProjectMemberAdapterError"
  }
}

/**
 * Converts API ProjectMember DTO to domain ProjectMember entity
 *
 * @param apiMember - Raw API project member data
 * @returns Domain ProjectMember entity
 * @throws ProjectMemberAdapterError if transformation fails
 */
export function apiProjectMemberToDomain(
  apiMember: ApiProjectMember
): ProjectMember {
  try {
    const createData: CreateProjectMemberData = {
      id: apiMember.id,
      userId: apiMember.user_id,
      projectId: apiMember.project_id,
      role: TeamRole.create({ role: apiMember.role }),
      assignedAt: apiMember.assigned_at,
      assignedBy: apiMember.assigned_by,
      expiresAt: apiMember.expires_at,
      isActive: apiMember.is_active,
    }

    return ProjectMember.create(createData)
  } catch (error) {
    throw new ProjectMemberAdapterError(
      `Failed to convert API project member to domain: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_DOMAIN",
      apiMember
    )
  }
}

/**
 * Converts multiple API ProjectMembers to domain entities
 *
 * @param apiMembers - Array of API project member data
 * @returns Array of domain ProjectMember entities
 */
export function apiProjectMembersToDomain(
  apiMembers: ApiProjectMember[]
): ProjectMember[] {
  return apiMembers.map(apiProjectMemberToDomain)
}

/**
 * Converts domain ProjectMember entity to API creation format
 *
 * @param member - Domain ProjectMember entity
 * @param assignedBy - User ID of the assigner
 * @returns API-compatible project member creation data
 * @throws ProjectMemberAdapterError if transformation fails
 */
export function domainProjectMemberToApiCreate(
  member: ProjectMember,
  assignedBy: number
): ApiProjectMemberCreate {
  try {
    return {
      user_id: member.userId,
      role: member.role.role,
      assigned_by: assignedBy,
      expires_at: member.expiresAt,
    }
  } catch (error) {
    throw new ProjectMemberAdapterError(
      `Failed to convert domain project member to API create: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_API",
      member
    )
  }
}

/**
 * Converts domain ProjectMember entity to API update format
 * Only includes changed fields to optimize network usage
 *
 * @param member - Domain ProjectMember entity
 * @param changedFields - Set of fields that have changed
 * @returns API-compatible project member update data
 */
export function domainProjectMemberToApiUpdate(
  member: ProjectMember,
  changedFields?: Set<string>
): ApiProjectMemberUpdate {
  try {
    const update: ApiProjectMemberUpdate = {}

    const shouldInclude = (field: string) =>
      !changedFields || changedFields.has(field)

    if (shouldInclude("role")) update.role = member.role.role
    if (shouldInclude("expiresAt")) update.expires_at = member.expiresAt
    if (shouldInclude("isActive")) update.is_active = member.isActive

    return update
  } catch (error) {
    throw new ProjectMemberAdapterError(
      `Failed to convert domain project member to API update: ${error instanceof Error ? error.message : "Unknown error"}`,
      "TO_API",
      member
    )
  }
}

/**
 * Converts domain team assignment context to API format
 *
 * @param context - Domain team assignment context
 * @returns API-compatible assignment context
 */
export function domainAssignmentContextToApi(context: {
  assignedBy: number
  reason?: string
  priority?: "Low" | "Medium" | "High" | "Critical"
  skillsRequired?: string[]
  estimatedWorkload?: number
  isTemporaryAssignment?: boolean
  requiresApproval?: boolean
}): ApiTeamAssignmentContext {
  return {
    assigned_by: context.assignedBy,
    reason: context.reason,
    priority: context.priority,
    skills_required: context.skillsRequired,
    estimated_workload: context.estimatedWorkload,
    is_temporary_assignment: context.isTemporaryAssignment,
    requires_approval: context.requiresApproval,
  }
}

/**
 * Converts API team assignment context to domain format
 *
 * @param apiContext - API assignment context
 * @returns Domain-compatible assignment context
 */
export function apiAssignmentContextToDomain(
  apiContext: ApiTeamAssignmentContext
): {
  assignedBy: number
  reason?: string
  priority?: "Low" | "Medium" | "High" | "Critical"
  skillsRequired?: string[]
  estimatedWorkload?: number
  isTemporaryAssignment?: boolean
  requiresApproval?: boolean
} {
  return {
    assignedBy: apiContext.assigned_by,
    reason: apiContext.reason,
    priority: apiContext.priority,
    skillsRequired: apiContext.skills_required,
    estimatedWorkload: apiContext.estimated_workload,
    isTemporaryAssignment: apiContext.is_temporary_assignment,
    requiresApproval: apiContext.requires_approval,
  }
}

/**
 * Converts domain member search criteria to API query parameters
 *
 * @param criteria - Domain member search criteria
 * @returns API-compatible query parameters
 */
export function domainMemberSearchToApiParams(criteria: {
  projectId?: string
  userId?: number
  role?: string
  isActive?: boolean
  isExpired?: boolean
  assignedAfter?: string
  assignedBefore?: string
  expiresAfter?: string
  expiresBefore?: string
}): Record<string, any> {
  const params: Record<string, any> = {}

  if (criteria.projectId) params.project_id = criteria.projectId
  if (criteria.userId !== undefined) params.user_id = criteria.userId
  if (criteria.role) params.role = criteria.role
  if (criteria.isActive !== undefined) params.is_active = criteria.isActive
  if (criteria.isExpired !== undefined) params.is_expired = criteria.isExpired
  if (criteria.assignedAfter) params.assigned_after = criteria.assignedAfter
  if (criteria.assignedBefore) params.assigned_before = criteria.assignedBefore
  if (criteria.expiresAfter) params.expires_after = criteria.expiresAfter
  if (criteria.expiresBefore) params.expires_before = criteria.expiresBefore

  return params
}

/**
 * Batch operations support for project members
 */
export interface ApiBulkMemberOperation {
  operation: "add" | "remove" | "update" | "extend_expiration"
  members: ApiProjectMemberCreate[] | string[] | ApiProjectMemberUpdate[]
  context?: ApiTeamAssignmentContext
}

export interface ApiBulkMemberResult {
  successful: ApiProjectMember[]
  failed: Array<{
    member_data: any
    error: string
    error_code?: string
  }>
  statistics: {
    total: number
    successful: number
    failed: number
  }
}

/**
 * Converts domain bulk member operations to API format
 *
 * @param operations - Domain bulk operations
 * @returns API-compatible bulk operations
 */
export function domainBulkMemberOpsToApi(operations: {
  operation: "add" | "remove" | "update" | "extend_expiration"
  members: ProjectMember[] | string[]
  assignedBy?: number
  context?: any
}): ApiBulkMemberOperation {
  if (operations.operation === "add" && Array.isArray(operations.members)) {
    const members = operations.members as ProjectMember[]
    return {
      operation: operations.operation,
      members: members.map((member) =>
        domainProjectMemberToApiCreate(member, operations.assignedBy!)
      ),
      context: operations.context
        ? domainAssignmentContextToApi(operations.context)
        : undefined,
    }
  }

  if (operations.operation === "remove" && Array.isArray(operations.members)) {
    return {
      operation: operations.operation,
      members: operations.members as string[], // Member IDs for removal
    }
  }

  if (operations.operation === "update" && Array.isArray(operations.members)) {
    const members = operations.members as ProjectMember[]
    return {
      operation: operations.operation,
      members: members.map((member) => domainProjectMemberToApiUpdate(member)),
    }
  }

  throw new ProjectMemberAdapterError(
    `Unsupported bulk operation: ${operations.operation}`,
    "TO_API",
    operations
  )
}

/**
 * Converts API bulk member results to domain format
 *
 * @param apiResult - API bulk operation result
 * @returns Domain-compatible bulk operation result
 */
export function apiBulkMemberResultToDomain(apiResult: ApiBulkMemberResult): {
  successful: ProjectMember[]
  failed: Array<{
    memberData: any
    error: string
    errorCode?: string
  }>
  statistics: {
    total: number
    successful: number
    failed: number
  }
} {
  return {
    successful: apiResult.successful.map(apiProjectMemberToDomain),
    failed: apiResult.failed.map((failure) => ({
      memberData: failure.member_data,
      error: failure.error,
      errorCode: failure.error_code,
    })),
    statistics: apiResult.statistics,
  }
}
