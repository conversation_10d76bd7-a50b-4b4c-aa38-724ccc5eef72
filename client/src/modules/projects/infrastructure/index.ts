/**
 * Projects Infrastructure Layer Index
 *
 * Centralized exports for the Projects module infrastructure layer.
 * Provides clean access to repository implementations and adapters.
 */

// Repository Implementation
export {
  ProjectRepository,
  projectQueryKeys,
  useProjectRepository,
} from "./ProjectRepository"

// Adapters
export * from "./adapters"

// Infrastructure types and utilities
export interface InfrastructureConfig {
  apiBaseUrl: string
  cacheConfig: {
    staleTime: number
    gcTime: number
    refetchOnWindowFocus: boolean
  }
  retryConfig: {
    retries: number
    retryDelay: (attemptIndex: number) => number
  }
}

export const defaultInfrastructureConfig: InfrastructureConfig = {
  apiBaseUrl: "/api/v1",
  cacheConfig: {
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    refetchOnWindowFocus: false,
  },
  retryConfig: {
    retries: 3,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
  },
}

/**
 * Infrastructure layer health check
 * Validates that all critical components are properly configured
 */
export function validateInfrastructureHealth(): {
  isHealthy: boolean
  issues: string[]
  components: {
    adapters: boolean
    repository: boolean
    queryClient: boolean
  }
} {
  const issues: string[] = []
  const components = {
    adapters: true,
    repository: true,
    queryClient: true,
  }

  // Validate adapters are available
  try {
    const { apiProjectToDomain } = require("./adapters")
    if (typeof apiProjectToDomain !== "function") {
      issues.push("Project adapters not properly loaded")
      components.adapters = false
    }
  } catch (error) {
    issues.push("Failed to load adapter functions")
    components.adapters = false
  }

  // Validate repository class is available
  try {
    if (typeof ProjectRepository !== "function") {
      issues.push("ProjectRepository class not available")
      components.repository = false
    }
  } catch (error) {
    issues.push("Failed to load ProjectRepository")
    components.repository = false
  }

  return {
    isHealthy: issues.length === 0,
    issues,
    components,
  }
}
