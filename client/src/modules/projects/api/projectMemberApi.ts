import {
  ProjectMember,
  ProjectMemberCreate,
  ProjectMemberUpdate,
} from "@/types/api"

import { apiClient } from "@/lib/api/client"

export const addProjectMember = async (
  projectId: number,
  memberData: ProjectMemberCreate
): Promise<ProjectMember> => {
  const response = await apiClient.post(
    `/projects/${projectId}/members`,
    memberData
  )
  return response.data as ProjectMember
}

export const removeProjectMember = async (
  projectId: number,
  userId: number
): Promise<void> => {
  await apiClient.delete(`/projects/${projectId}/members/${userId}`)
}

export const updateProjectMember = async (
  memberId: number,
  memberData: ProjectMemberUpdate
): Promise<ProjectMember> => {
  const response = await apiClient.put(
    `/projects/members/${memberId}`,
    memberData
  )
  return response.data as ProjectMember
}

export const getProjectMembers = async (
  projectId: number
): Promise<ProjectMember[]> => {
  const response = await apiClient.get(`/projects/${projectId}/members`)
  return response.data as ProjectMember[]
}
