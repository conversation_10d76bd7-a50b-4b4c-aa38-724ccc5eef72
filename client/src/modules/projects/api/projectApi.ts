import {
  Project,
  ProjectCreate,
  ProjectUpdate,
} from "@/types/api"

import { apiClient } from "@/lib/api/client"

export const createProject = async (
  projectData: ProjectCreate
): Promise<Project> => {
  const response = await apiClient.post("/projects", projectData)
  return response.data as Project
}

export const getProject = async (projectId: number): Promise<Project> => {
  const response = await apiClient.get(`/projects/${projectId}`)
  return response.data as Project
}

export const updateProject = async (
  projectId: number,
  projectData: ProjectUpdate
): Promise<Project> => {
  const response = await apiClient.put(`/projects/${projectId}`, projectData)
  return response.data as Project
}

export const deleteProject = async (projectId: number): Promise<void> => {
  await apiClient.delete(`/projects/${projectId}`)
}

export const getProjects = async (): Promise<Project[]> => {
  const response = await apiClient.get("/projects")
  return response.data as Project[]
}
