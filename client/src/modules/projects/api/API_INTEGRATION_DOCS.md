# Project API Integration Documentation

This document details how the `is_offline` field is handled in API service calls and React Query hooks for the project management system.

## API Service Integration

### Project API Service

**File**: `client/src/modules/projects/api/projectApi.ts`

#### Field Handling

The `is_offline` boolean field is automatically included in all project CRUD operations:

##### Create Project

```typescript
// API Call Structure
POST /api/projects
Content-Type: application/json

{
  "name": "Project Name",
  "description": "Project Description",
  "owner_id": 123,
  "is_offline": false  // ✨ Optional field, defaults to false
}
```

##### Update Project

```typescript
// API Call Structure
PUT /api/projects/{project_id}
Content-Type: application/json

{
  "name": "Updated Project Name",
  "description": "Updated Description",
  "is_offline": true  // ✨ Admin-only modification
}
```

##### Get Project / List Projects

```typescript
// Response Structure
{
  "id": 123,
  "name": "Project Name",
  "description": "Project Description",
  "owner_id": 456,
  "members": [...],
  "created_at": "2025-07-21T12:00:00Z",
  "updated_at": "2025-07-21T12:30:00Z",
  "is_offline": true  // ✨ Always included in responses
}
```

---

## React Query Integration

### Project Hooks

**File**: `client/src/modules/projects/hooks/useProjects.ts`

#### Query Keys

```typescript
// Updated query keys to include offline status
const projectQueryKeys = {
  all: ['projects'] as const,
  lists: () => [...projectQueryKeys.all, 'list'] as const,
  list: (filters: string) => [...projectQueryKeys.lists(), { filters }] as const,
  details: () => [...projectQueryKeys.all, 'detail'] as const,
  detail: (id: number) => [...projectQueryKeys.details(), id] as const,
}
```

#### Mutation Handling

##### Create Project Mutation

```typescript
const useCreateProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async (projectData: ProjectCreate) => {
      // is_offline field automatically included
      return projectApi.createProject(projectData)
    },
    onSuccess: () => {
      // Invalidate project list to show updated offline status
      queryClient.invalidateQueries({ queryKey: projectQueryKeys.lists() })
    },
  })
}
```

##### Update Project Mutation

```typescript
const useUpdateProject = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({
      projectId,
      projectData,
    }: {
      projectId: number
      projectData: ProjectUpdate
    }) => {
      // is_offline field handled in projectData
      return projectApi.updateProject(projectId, projectData)
    },
    onSuccess: (data, { projectId }) => {
      // Update project detail cache with new offline status
      queryClient.setQueryData(projectQueryKeys.detail(projectId), data)
      // Invalidate list to update badges
      queryClient.invalidateQueries({ queryKey: projectQueryKeys.lists() })
    },
  })
}
```

#### Data Fetching

##### Project List Query

```typescript
const useProjects = () => {
  return useQuery({
    queryKey: projectQueryKeys.lists(),
    queryFn: projectApi.getProjects,
    select: (data) => {
      // Data includes is_offline field for each project
      return data.map((project) => ({
        ...project,
        // is_offline is already included from API
      }))
    },
  })
}
```

##### Single Project Query

```typescript
const useProject = (projectId: number) => {
  return useQuery({
    queryKey: projectQueryKeys.detail(projectId),
    queryFn: () => projectApi.getProject(projectId),
    enabled: !!projectId,
    // is_offline field automatically available in project data
  })
}
```

---

## Cache Management

### Optimistic Updates

#### Offline Status Toggle

```typescript
const useOptimisticOfflineToggle = () => {
  const queryClient = useQueryClient()

  return useMutation({
    mutationFn: async ({ projectId, is_offline }: { projectId: number; is_offline: boolean }) => {
      // Optimistically update cache before API call
      queryClient.setQueryData(projectQueryKeys.detail(projectId), (oldData: Project) => ({
        ...oldData,
        is_offline,
        updated_at: new Date().toISOString(),
      }))

      return projectApi.updateProject(projectId, { is_offline })
    },
    onError: (error, { projectId }, context) => {
      // Rollback optimistic update on error
      queryClient.invalidateQueries({
        queryKey: projectQueryKeys.detail(projectId),
      })
    },
  })
}
```

### Cache Invalidation Strategy

#### When to Invalidate

1. **Project Update**: Always invalidate both detail and list queries
2. **Offline Status Change**: Invalidate project lists to update badges
3. **Role Change**: Invalidate queries when user role changes (affects visibility)

#### Implementation

```typescript
// After offline status update
queryClient.invalidateQueries({
  queryKey: projectQueryKeys.all,
  exact: false,
})

// Specific invalidation for better performance
queryClient.invalidateQueries({
  queryKey: projectQueryKeys.detail(projectId),
})
queryClient.invalidateQueries({
  queryKey: projectQueryKeys.lists(),
})
```

---

## Error Handling

### API Error Scenarios

#### Permission Denied

```typescript
// 403 Forbidden - Non-admin trying to modify is_offline
{
  "error": "Permission denied",
  "message": "Only administrators can modify project offline status",
  "code": "INSUFFICIENT_PERMISSIONS"
}
```

#### Validation Error

```typescript
// 400 Bad Request - Invalid is_offline value
{
  "error": "Validation failed",
  "message": "is_offline must be a boolean value",
  "field": "is_offline",
  "code": "VALIDATION_ERROR"
}
```

### Frontend Error Handling

#### Mutation Error Handling

```typescript
const { mutate: updateProject, error } = useMutation({
  mutationFn: projectApi.updateProject,
  onError: (error: ApiError) => {
    if (error.code === 'INSUFFICIENT_PERMISSIONS') {
      toast.error('Only administrators can change offline status')
    } else if (error.code === 'VALIDATION_ERROR') {
      toast.error(`Invalid input: ${error.message}`)
    } else {
      toast.error('Failed to update project. Please try again.')
    }
  },
})
```

#### Query Error Handling

```typescript
const { data: projects, error, isError } = useProjects()

if (isError) {
  return (
    <div className="error-state">
      <h3>Failed to load projects</h3>
      <p>{error.message}</p>
      <button onClick={refetch}>Try Again</button>
    </div>
  )
}
```

---

## Performance Optimization

### Query Optimization

#### Selective Fetching

```typescript
// Only fetch offline status when needed
const useProjectOfflineStatus = (projectId: number) => {
  return useQuery({
    queryKey: [...projectQueryKeys.detail(projectId), 'offline-status'],
    queryFn: async () => {
      const project = await projectApi.getProject(projectId)
      return { id: project.id, is_offline: project.is_offline }
    },
    select: (data) => data.is_offline,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })
}
```

#### Background Updates

```typescript
// Keep offline status fresh in background
const useProjectsWithBackground = () => {
  return useQuery({
    queryKey: projectQueryKeys.lists(),
    queryFn: projectApi.getProjects,
    staleTime: 2 * 60 * 1000, // 2 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
    refetchOnWindowFocus: true,
  })
}
```

### Data Normalization

#### Normalized Cache Structure

```typescript
// Normalized approach for large project lists
const useNormalizedProjects = () => {
  return useQuery({
    queryKey: projectQueryKeys.lists(),
    queryFn: projectApi.getProjects,
    select: (data) => {
      const byId = data.reduce(
        (acc, project) => {
          acc[project.id] = project
          return acc
        },
        {} as Record<number, Project>
      )

      const ids = data.map((project) => project.id)

      return { byId, ids }
    },
  })
}
```

---

## Real-time Updates

### WebSocket Integration

#### Offline Status Changes

```typescript
// Listen for real-time offline status updates
const useRealtimeOfflineStatus = () => {
  const queryClient = useQueryClient()

  useEffect(() => {
    const ws = new WebSocket('/ws/project-updates')

    ws.onmessage = (event) => {
      const update = JSON.parse(event.data)

      if (update.type === 'PROJECT_OFFLINE_STATUS_CHANGED') {
        // Update cache with real-time data
        queryClient.setQueryData(
          projectQueryKeys.detail(update.project_id),
          (oldData: Project) => ({
            ...oldData,
            is_offline: update.is_offline,
            updated_at: update.timestamp,
          })
        )

        // Update project list cache
        queryClient.invalidateQueries({
          queryKey: projectQueryKeys.lists(),
        })
      }
    }

    return () => ws.close()
  }, [queryClient])
}
```

---

## Type Safety

### API Response Types

```typescript
// Ensure API responses match expected types
interface ApiProject {
  id: number
  name: string
  description?: string
  owner_id: number
  members: ApiProjectMember[]
  created_at: string
  updated_at: string
  is_offline: boolean // ✨ Always present in API responses
}

// Transform API types to frontend types
const transformApiProject = (apiProject: ApiProject): Project => {
  return {
    ...apiProject,
    // Ensure is_offline is properly typed as boolean
    is_offline: Boolean(apiProject.is_offline),
  }
}
```

### Request Validation

```typescript
// Validate requests before sending
const validateProjectUpdate = (data: ProjectUpdate): void => {
  if (data.is_offline !== undefined && typeof data.is_offline !== 'boolean') {
    throw new Error('is_offline must be a boolean value')
  }
}
```

---

## Testing Integration

### API Testing

#### Mock Service Worker (MSW)

```typescript
// Mock API responses for testing
import { rest } from 'msw'

export const projectHandlers = [
  rest.put('/api/projects/:id', (req, res, ctx) => {
    const { is_offline } = req.body

    // Simulate admin permission check
    const isAdmin = req.headers.get('x-user-role') === 'ADMIN'
    if (is_offline !== undefined && !isAdmin) {
      return res(
        ctx.status(403),
        ctx.json({
          error: 'Permission denied',
          code: 'INSUFFICIENT_PERMISSIONS',
        })
      )
    }

    return res(
      ctx.json({
        id: Number(req.params.id),
        is_offline: Boolean(is_offline),
        // ... other fields
      })
    )
  }),
]
```

#### React Query Testing

```typescript
// Test React Query hooks with offline status
describe('useProjects', () => {
  it('handles offline status in project list', async () => {
    const mockProjects = [
      { id: 1, name: 'Online Project', is_offline: false },
      { id: 2, name: 'Offline Project', is_offline: true },
    ]

    server.use(
      rest.get('/api/projects', (req, res, ctx) => {
        return res(ctx.json(mockProjects))
      })
    )

    const { result } = renderHook(() => useProjects(), {
      wrapper: queryClientWrapper,
    })

    await waitFor(() => {
      expect(result.current.data).toEqual(mockProjects)
    })
  })
})
```

---

## Migration Notes

### Backward Compatibility

- `is_offline` field defaults to `false` for existing projects
- API handles missing field gracefully
- Frontend components handle undefined values safely

### Database Migration

- Backend handles database schema migration
- Frontend automatically works with updated API responses
- No frontend migration required

### Rollback Strategy

- Feature can be disabled by hiding UI components
- API field remains for future re-enablement
- No data loss if feature is rolled back

---

## Monitoring and Analytics

### Usage Tracking

```typescript
// Track offline mode usage
const trackOfflineToggle = (projectId: number, newStatus: boolean) => {
  analytics.track('Project Offline Status Changed', {
    project_id: projectId,
    is_offline: newStatus,
    user_role: 'admin',
    timestamp: new Date().toISOString(),
  })
}
```

### Performance Monitoring

```typescript
// Monitor API performance for offline status updates
const measureApiPerformance = async (apiCall: Promise<any>) => {
  const startTime = performance.now()
  try {
    const result = await apiCall
    const endTime = performance.now()

    analytics.track('API Performance', {
      operation: 'update_project_offline_status',
      duration: endTime - startTime,
      success: true,
    })

    return result
  } catch (error) {
    analytics.track('API Error', {
      operation: 'update_project_offline_status',
      error: error.message,
    })
    throw error
  }
}
```
