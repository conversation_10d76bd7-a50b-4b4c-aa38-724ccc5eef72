/**
 * Projects Module Zustand Store
 *
 * Domain-aware UI state management for the Projects module.
 * Handles selection, filtering, notifications, and cross-component coordination.
 */

import { create } from "zustand"
import { devtools, persist } from "zustand/middleware"
import { immer } from "zustand/middleware/immer"

import { Project } from "../domain/entities/Project"
import { ProjectSelectionFilters } from "../hooks/useProjectSelection"

/**
 * Notification types for user feedback
 */
export interface Notification {
  id: string
  type: "success" | "error" | "warning" | "info"
  message: string
  details?: string | Record<string, any>
  timestamp: number
  duration?: number
  action?: {
    label: string
    handler: () => void
  }
}

/**
 * UI layout state for project views
 */
export interface ProjectViewState {
  layout: "grid" | "list" | "table"
  density: "compact" | "comfortable" | "spacious"
  sidebarOpen: boolean
  filtersPanelOpen: boolean
  detailsPanelOpen: boolean
  groupBy?: "status" | "priority" | "client" | "owner" | "none"
  showArchived: boolean
}

/**
 * Form state for project operations
 */
export interface ProjectFormState {
  isFormOpen: boolean
  formMode: "create" | "edit" | "view"
  editingProjectId?: string
  isDirty: boolean
  hasUnsavedChanges: boolean
}

/**
 * Dialog and modal state
 */
export interface DialogState {
  confirmDialog?: {
    isOpen: boolean
    title: string
    message: string
    confirmText?: string
    cancelText?: string
    onConfirm: () => void
    onCancel?: () => void
    variant?: "default" | "destructive"
  }
  archiveDialog?: {
    isOpen: boolean
    projectIds: string[]
    onConfirm: (projectIds: string[]) => void
  }
  memberManagementDialog?: {
    isOpen: boolean
    projectId: string
    mode: "add" | "edit" | "remove"
    memberId?: string
  }
}

/**
 * Recent activity tracking
 */
export interface RecentActivity {
  id: string
  type:
    | "created"
    | "updated"
    | "archived"
    | "status_changed"
    | "member_added"
    | "member_removed"
  projectId: string
  projectName: string
  timestamp: number
  userId: number
  details?: Record<string, any>
}

/**
 * Project store state
 */
interface ProjectStoreState {
  // Selection state
  selectedProjectIds: string[]
  selectedProject: Project | null

  // Filtering state
  filters: ProjectSelectionFilters
  quickFilter: string
  searchQuery: string

  // UI state
  viewState: ProjectViewState
  formState: ProjectFormState
  dialogState: DialogState

  // Notifications
  notifications: Notification[]

  // Recent activity
  recentActivity: RecentActivity[]
  maxRecentItems: number

  // Performance optimization
  isLoading: boolean
  lastRefresh: number

  // Actions
  // Selection actions
  selectProject: (project: Project | null) => void
  selectProjects: (projectIds: string[]) => void
  toggleProjectSelection: (projectId: string) => void
  clearSelection: () => void

  // Filter actions
  updateFilters: (filters: Partial<ProjectSelectionFilters>) => void
  clearFilters: () => void
  setQuickFilter: (filter: string) => void
  setSearchQuery: (query: string) => void

  // View actions
  updateViewState: (state: Partial<ProjectViewState>) => void
  toggleSidebar: () => void
  toggleFiltersPanel: () => void
  toggleDetailsPanel: () => void
  setLayout: (layout: ProjectViewState["layout"]) => void
  setDensity: (density: ProjectViewState["density"]) => void
  setGroupBy: (groupBy: ProjectViewState["groupBy"]) => void

  // Form actions
  openForm: (mode: ProjectFormState["formMode"], projectId?: string) => void
  closeForm: () => void
  setFormDirty: (isDirty: boolean) => void
  setHasUnsavedChanges: (hasChanges: boolean) => void

  // Dialog actions
  openConfirmDialog: (config: NonNullable<DialogState["confirmDialog"]>) => void
  closeConfirmDialog: () => void
  openArchiveDialog: (
    projectIds: string[],
    onConfirm: (ids: string[]) => void
  ) => void
  closeArchiveDialog: () => void
  openMemberDialog: (
    config: NonNullable<DialogState["memberManagementDialog"]>
  ) => void
  closeMemberDialog: () => void

  // Notification actions
  addNotification: (
    notification: Omit<Notification, "id" | "timestamp">
  ) => void
  removeNotification: (id: string) => void
  clearNotifications: () => void

  // Activity actions
  addActivity: (activity: Omit<RecentActivity, "id" | "timestamp">) => void
  clearActivity: () => void

  // Utility actions
  setLoading: (isLoading: boolean) => void
  refreshTimestamp: () => void
  resetStore: () => void
}

/**
 * Initial state
 */
const initialState = {
  // Selection
  selectedProjectIds: [],
  selectedProject: null,

  // Filters
  filters: {},
  quickFilter: "",
  searchQuery: "",

  // UI state
  viewState: {
    layout: "grid" as const,
    density: "comfortable" as const,
    sidebarOpen: true,
    filtersPanelOpen: false,
    detailsPanelOpen: false,
    groupBy: "none" as const,
    showArchived: false,
  },

  formState: {
    isFormOpen: false,
    formMode: "create" as const,
    editingProjectId: undefined,
    isDirty: false,
    hasUnsavedChanges: false,
  },

  dialogState: {},

  // Data
  notifications: [],
  recentActivity: [],
  maxRecentItems: 50,

  // Performance
  isLoading: false,
  lastRefresh: 0,
}

/**
 * Utility function to generate unique IDs
 */
const generateId = () =>
  `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`

/**
 * Create project store with middleware
 */
export const useProjectStore = create<ProjectStoreState>()(
  devtools(
    persist(
      immer((set, get) => ({
        ...initialState,

        // Selection actions
        selectProject: (project) =>
          set((state) => {
            state.selectedProject = project
            state.selectedProjectIds = project ? [project.id] : []
          }),

        selectProjects: (projectIds) =>
          set((state) => {
            state.selectedProjectIds = projectIds
            state.selectedProject = null // Clear single selection when multiple are selected
          }),

        toggleProjectSelection: (projectId) =>
          set((state) => {
            const index = state.selectedProjectIds.indexOf(projectId)
            if (index >= 0) {
              state.selectedProjectIds.splice(index, 1)
            } else {
              state.selectedProjectIds.push(projectId)
            }

            // Clear single project selection when toggling multiple
            if (state.selectedProjectIds.length !== 1) {
              state.selectedProject = null
            }
          }),

        clearSelection: () =>
          set((state) => {
            state.selectedProjectIds = []
            state.selectedProject = null
          }),

        // Filter actions
        updateFilters: (filters) =>
          set((state) => {
            state.filters = { ...state.filters, ...filters }
          }),

        clearFilters: () =>
          set((state) => {
            state.filters = {}
            state.quickFilter = ""
            state.searchQuery = ""
          }),

        setQuickFilter: (filter) =>
          set((state) => {
            state.quickFilter = filter
          }),

        setSearchQuery: (query) =>
          set((state) => {
            state.searchQuery = query
          }),

        // View actions
        updateViewState: (viewState) =>
          set((state) => {
            state.viewState = { ...state.viewState, ...viewState }
          }),

        toggleSidebar: () =>
          set((state) => {
            state.viewState.sidebarOpen = !state.viewState.sidebarOpen
          }),

        toggleFiltersPanel: () =>
          set((state) => {
            state.viewState.filtersPanelOpen = !state.viewState.filtersPanelOpen
          }),

        toggleDetailsPanel: () =>
          set((state) => {
            state.viewState.detailsPanelOpen = !state.viewState.detailsPanelOpen
          }),

        setLayout: (layout) =>
          set((state) => {
            state.viewState.layout = layout
          }),

        setDensity: (density) =>
          set((state) => {
            state.viewState.density = density
          }),

        setGroupBy: (groupBy) =>
          set((state) => {
            state.viewState.groupBy = groupBy
          }),

        // Form actions
        openForm: (mode, projectId) =>
          set((state) => {
            state.formState.isFormOpen = true
            state.formState.formMode = mode
            state.formState.editingProjectId = projectId
            state.formState.isDirty = false
            state.formState.hasUnsavedChanges = false
          }),

        closeForm: () =>
          set((state) => {
            state.formState.isFormOpen = false
            state.formState.editingProjectId = undefined
            state.formState.isDirty = false
            state.formState.hasUnsavedChanges = false
          }),

        setFormDirty: (isDirty) =>
          set((state) => {
            state.formState.isDirty = isDirty
          }),

        setHasUnsavedChanges: (hasChanges) =>
          set((state) => {
            state.formState.hasUnsavedChanges = hasChanges
          }),

        // Dialog actions
        openConfirmDialog: (config) =>
          set((state) => {
            state.dialogState.confirmDialog = { ...config, isOpen: true }
          }),

        closeConfirmDialog: () =>
          set((state) => {
            state.dialogState.confirmDialog = undefined
          }),

        openArchiveDialog: (projectIds, onConfirm) =>
          set((state) => {
            state.dialogState.archiveDialog = {
              isOpen: true,
              projectIds,
              onConfirm,
            }
          }),

        closeArchiveDialog: () =>
          set((state) => {
            state.dialogState.archiveDialog = undefined
          }),

        openMemberDialog: (config) =>
          set((state) => {
            state.dialogState.memberManagementDialog = {
              ...config,
              isOpen: true,
            }
          }),

        closeMemberDialog: () =>
          set((state) => {
            state.dialogState.memberManagementDialog = undefined
          }),

        // Notification actions
        addNotification: (notification) =>
          set((state) => {
            const newNotification: Notification = {
              id: generateId(),
              timestamp: Date.now(),
              duration: notification.duration || 5000, // 5 seconds default
              ...notification,
            }

            state.notifications.push(newNotification)

            // Auto-remove after duration (this would typically be handled by a useEffect)
            if (newNotification.duration > 0) {
              setTimeout(() => {
                const currentState = get()
                currentState.removeNotification(newNotification.id)
              }, newNotification.duration)
            }
          }),

        removeNotification: (id) =>
          set((state) => {
            const index = state.notifications.findIndex((n) => n.id === id)
            if (index >= 0) {
              state.notifications.splice(index, 1)
            }
          }),

        clearNotifications: () =>
          set((state) => {
            state.notifications = []
          }),

        // Activity actions
        addActivity: (activity) =>
          set((state) => {
            const newActivity: RecentActivity = {
              id: generateId(),
              timestamp: Date.now(),
              ...activity,
            }

            // Add to beginning of array
            state.recentActivity.unshift(newActivity)

            // Limit to max items
            if (state.recentActivity.length > state.maxRecentItems) {
              state.recentActivity = state.recentActivity.slice(
                0,
                state.maxRecentItems
              )
            }
          }),

        clearActivity: () =>
          set((state) => {
            state.recentActivity = []
          }),

        // Utility actions
        setLoading: (isLoading) =>
          set((state) => {
            state.isLoading = isLoading
          }),

        refreshTimestamp: () =>
          set((state) => {
            state.lastRefresh = Date.now()
          }),

        resetStore: () =>
          set(() => ({
            ...initialState,
            lastRefresh: Date.now(),
          })),
      })),
      {
        name: "project-store",
        // Only persist certain parts of the state
        partialize: (state) => ({
          viewState: state.viewState,
          filters: state.filters,
          quickFilter: state.quickFilter,
          recentActivity: state.recentActivity.slice(0, 10), // Only persist recent 10 items
        }),
      }
    ),
    {
      name: "project-store",
    }
  )
)

/**
 * Selector hooks for better performance
 */
export const useProjectSelection = () =>
  useProjectStore((state) => ({
    selectedProjectIds: state.selectedProjectIds,
    selectedProject: state.selectedProject,
    selectProject: state.selectProject,
    selectProjects: state.selectProjects,
    toggleProjectSelection: state.toggleProjectSelection,
    clearSelection: state.clearSelection,
  }))

export const useProjectFilters = () =>
  useProjectStore((state) => ({
    filters: state.filters,
    quickFilter: state.quickFilter,
    searchQuery: state.searchQuery,
    updateFilters: state.updateFilters,
    clearFilters: state.clearFilters,
    setQuickFilter: state.setQuickFilter,
    setSearchQuery: state.setSearchQuery,
  }))

export const useProjectViewState = () =>
  useProjectStore((state) => ({
    viewState: state.viewState,
    updateViewState: state.updateViewState,
    toggleSidebar: state.toggleSidebar,
    toggleFiltersPanel: state.toggleFiltersPanel,
    toggleDetailsPanel: state.toggleDetailsPanel,
    setLayout: state.setLayout,
    setDensity: state.setDensity,
    setGroupBy: state.setGroupBy,
  }))

export const useProjectNotifications = () =>
  useProjectStore((state) => ({
    notifications: state.notifications,
    addNotification: state.addNotification,
    removeNotification: state.removeNotification,
    clearNotifications: state.clearNotifications,
  }))

export const useProjectDialogs = () =>
  useProjectStore((state) => ({
    dialogState: state.dialogState,
    openConfirmDialog: state.openConfirmDialog,
    closeConfirmDialog: state.closeConfirmDialog,
    openArchiveDialog: state.openArchiveDialog,
    closeArchiveDialog: state.closeArchiveDialog,
    openMemberDialog: state.openMemberDialog,
    closeMemberDialog: state.closeMemberDialog,
  }))

export const useProjectForm = () =>
  useProjectStore((state) => ({
    formState: state.formState,
    openForm: state.openForm,
    closeForm: state.closeForm,
    setFormDirty: state.setFormDirty,
    setHasUnsavedChanges: state.setHasUnsavedChanges,
  }))

export const useProjectActivity = () =>
  useProjectStore((state) => ({
    recentActivity: state.recentActivity,
    addActivity: state.addActivity,
    clearActivity: state.clearActivity,
  }))
