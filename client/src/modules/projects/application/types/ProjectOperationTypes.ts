/**
 * Application Layer Operation Types
 *
 * Defines request/response types for application services.
 * These types represent the data contracts between presentation and application layers.
 */

import { Project } from "../../domain/entities/Project"
import { ProjectMember } from "../../domain/entities/ProjectMember"
import { ProjectStatus } from "../../domain/value-objects/ProjectStatus"
// import { TeamRole } from "../../domain/value-objects/TeamRole" // Unused
import { UseCaseResult } from "./ApplicationErrors"

// ===== Base Use Case Interface =====

/**
 * Base interface for all use cases
 */
export interface UseCase<TRequest, TResponse> {
  execute(request: TRequest): Promise<UseCaseResult<TResponse>>
}

// ===== Create Project Use Case Types =====

export interface CreateProjectRequest {
  name: string
  description?: string
  ownerId: number
  clientId?: number
  priority?: "Low" | "Medium" | "High" | "Critical"
  budgetData?: {
    totalAmount: number
    currency: string
  }
  startDate?: string
  endDate?: string
  tags?: string[]
  location?: string
  externalProjectId?: string
  requiresApproval?: boolean
}

export interface CreateProjectResponse {
  project: Project
  validationWarnings?: string[]
}

// ===== Update Project Use Case Types =====

export interface UpdateProjectRequest {
  projectId: string
  updates: {
    name?: string
    description?: string
    priority?: "Low" | "Medium" | "High" | "Critical"
    budgetData?: {
      totalAmount?: number
      spentAmount?: number
      currency?: string
      allocatedAmount?: number
    }
    startDate?: string
    endDate?: string
    tags?: string[]
    location?: string
    requiresApproval?: boolean
  }
  userId: number // For permission checking
}

export interface UpdateProjectResponse {
  project: Project
  updatedFields: string[]
  validationWarnings?: string[]
}

// ===== Assign Member Use Case Types =====

export interface AssignMemberRequest {
  projectId: string
  userId: number
  role: string
  assignedBy: number
  expiresAt?: string
  assignmentContext?: {
    reason?: string
    priority?: "Low" | "Medium" | "High"
    skillsRequired?: string[]
    estimatedWorkload?: number
    requiresApproval?: boolean
  }
}

export interface AssignMemberResponse {
  project: Project
  member: ProjectMember
  assignmentId: string
}

// ===== Remove Member Use Case Types =====

export interface RemoveMemberRequest {
  projectId: string
  memberId: string
  removedBy: number
  reason?: string
}

export interface RemoveMemberResponse {
  project: Project
  removedMemberId: string
}

// ===== Change Project Status Use Case Types =====

export interface ChangeProjectStatusRequest {
  projectId: string
  newStatus: "Draft" | "Active" | "Paused" | "Completed" | "Cancelled"
  changedBy: number
  reason?: string
  force?: boolean // Override business rule checks
}

export interface ChangeProjectStatusResponse {
  project: Project
  previousStatus: ProjectStatus
  newStatus: ProjectStatus
  transitionValidated: boolean
}

// ===== Archive Project Use Case Types =====

export interface ArchiveProjectRequest {
  projectIds: string[]
  archivedBy: number
  reason?: string
  validateCompletion?: boolean
}

export interface ArchiveProjectResponse {
  archivedProjectIds: string[]
  skippedProjectIds: string[]
  validationIssues?: Array<{
    projectId: string
    issues: string[]
  }>
}

// ===== Validate Project Use Case Types =====

export interface ValidateProjectRequest {
  projectId: string
  validationType:
    | "business-rules"
    | "data-consistency"
    | "permissions"
    | "complete"
  userId?: number
}

export interface ValidateProjectResponse {
  isValid: boolean
  validationResults: {
    businessRules: {
      passed: string[]
      failed: Array<{
        rule: string
        message: string
        severity: "error" | "warning"
      }>
    }
    dataConsistency: {
      consistent: boolean
      issues?: string[]
    }
    permissions: {
      hasPermission: boolean
      missingPermissions?: string[]
    }
  }
  recommendations?: string[]
}

// ===== Bulk Operations Types =====

export interface BulkProjectOperationRequest {
  projectIds: string[]
  operation: "archive" | "delete" | "status-change" | "export"
  operationData?: Record<string, any>
  userId: number
}

export interface BulkProjectOperationResponse {
  successful: string[]
  failed: Array<{
    projectId: string
    error: string
  }>
  summary: {
    total: number
    successful: number
    failed: number
  }
}

// ===== Search and Query Types =====

export interface ProjectSearchRequest {
  criteria: {
    clientId?: number
    ownerId?: number
    status?: string
    priority?: string
    tags?: string[]
    createdAfter?: string
    createdBefore?: string
    budgetMin?: number
    budgetMax?: number
    currency?: string
    memberUserId?: number
    location?: string
    hasExpiredMembers?: boolean
    requiresApproval?: boolean
    isOverBudget?: boolean
  }
  pagination?: {
    offset: number
    limit: number
    sortBy?: string
    sortOrder?: "asc" | "desc"
  }
}

export interface ProjectSearchResponse {
  projects: Project[]
  totalCount: number
  hasMore: boolean
  searchMetadata: {
    query: string
    appliedFilters: Record<string, any>
    executionTime: number
  }
}

// ===== Project Statistics Types =====

export interface ProjectStatisticsRequest {
  userId?: number
  clientId?: number
  dateRange?: {
    start: string
    end: string
  }
}

export interface ProjectStatisticsResponse {
  summary: {
    totalProjects: number
    activeProjects: number
    completedProjects: number
    overBudgetProjects: number
    projectsRequiringAttention: number
  }
  budgetAnalysis: {
    totalBudget: number
    totalSpent: number
    averageBudgetUtilization: number
    currency: string
  }
  timelineAnalysis: {
    averageProjectDuration: number
    onTimeCompletionRate: number
    overdueProjects: number
  }
  teamAnalysis: {
    totalMembers: number
    averageTeamSize: number
    memberUtilizationRate: number
  }
}

// ===== Permission Checking Types =====

export interface ProjectPermissionRequest {
  projectId: string
  userId: number
  operation:
    | "view"
    | "edit"
    | "delete"
    | "manage-members"
    | "change-status"
    | "manage-budget"
}

export interface ProjectPermissionResponse {
  hasPermission: boolean
  reason?: string
  requiredRole?: string
  additionalContext?: Record<string, any>
}

// ===== Export/Import Types =====

export interface ExportProjectRequest {
  projectIds: string[]
  format: "json" | "csv" | "pdf"
  includeMembers?: boolean
  includeBudgetDetails?: boolean
  includeTimeline?: boolean
}

export interface ExportProjectResponse {
  exportId: string
  downloadUrl: string
  format: string
  size: number
  expiresAt: string
}

// ===== Common Response Metadata =====

export interface ResponseMetadata {
  timestamp: string
  requestId: string
  executionTime: number
  version: string
}

// ===== Type Utilities =====

/**
 * Helper type to extract the request type from a use case
 */
export type UseCaseRequest<T> = T extends UseCase<infer R, any> ? R : never

/**
 * Helper type to extract the response type from a use case
 */
export type UseCaseResponse<T> = T extends UseCase<any, infer R> ? R : never

/**
 * Helper type for paginated responses
 */
export interface PaginatedResponse<T> {
  items: T[]
  totalCount: number
  offset: number
  limit: number
  hasMore: boolean
}

/**
 * Helper type for operation results with metadata
 */
export interface OperationResult<T> {
  data: T
  metadata: ResponseMetadata
  warnings?: string[]
}
