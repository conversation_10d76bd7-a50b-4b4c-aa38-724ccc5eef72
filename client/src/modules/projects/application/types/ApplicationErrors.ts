/**
 * Application Layer Error Types
 *
 * Defines application-specific errors that represent business operation failures
 * at the use case level. These errors bridge domain errors and presentation concerns.
 */

/**
 * Base application error for all Projects module use case failures
 */
export abstract class ProjectApplicationError extends Error {
  abstract readonly code: string
  abstract readonly category:
    | "validation"
    | "business"
    | "resource"
    | "permission"

  constructor(
    message: string,
    public readonly details?: Record<string, any>
  ) {
    super(message)
    this.name = this.constructor.name
  }

  /**
   * Converts application error to a format suitable for presentation layer
   */
  toPresentation() {
    return {
      code: this.code,
      message: this.message,
      category: this.category,
      details: this.details,
    }
  }
}

/**
 * Validation errors for use case input validation
 */
export class ProjectValidationError extends ProjectApplicationError {
  readonly code = "PROJECT_VALIDATION_ERROR"
  readonly category = "validation" as const

  constructor(field: string, reason: string, details?: Record<string, any>) {
    super(`Validation failed for ${field}: ${reason}`, {
      field,
      reason,
      ...details,
    })
  }
}

/**
 * Business rule violations at the use case level
 */
export class ProjectBusinessRuleError extends ProjectApplicationError {
  readonly code = "PROJECT_BUSINESS_RULE_ERROR"
  readonly category = "business" as const

  constructor(rule: string, context?: Record<string, any>) {
    super(`Business rule violation: ${rule}`, context)
  }
}

/**
 * Resource not found errors (projects, members, etc.)
 */
export class ProjectResourceNotFoundError extends ProjectApplicationError {
  readonly code = "PROJECT_RESOURCE_NOT_FOUND"
  readonly category = "resource" as const

  constructor(
    resourceType: string,
    resourceId: string,
    details?: Record<string, any>
  ) {
    super(`${resourceType} with ID ${resourceId} not found`, {
      resourceType,
      resourceId,
      ...details,
    })
  }
}

/**
 * Permission/authorization errors for project operations
 */
export class ProjectPermissionError extends ProjectApplicationError {
  readonly code = "PROJECT_PERMISSION_ERROR"
  readonly category = "permission" as const

  constructor(operation: string, userId: number, projectId?: string) {
    super(`Permission denied for ${operation}`, {
      operation,
      userId,
      projectId,
    })
  }
}

/**
 * Conflict errors when operations cannot proceed due to current state
 */
export class ProjectOperationConflictError extends ProjectApplicationError {
  readonly code = "PROJECT_OPERATION_CONFLICT"
  readonly category = "business" as const

  constructor(operation: string, currentState: string, projectId?: string) {
    super(`Cannot ${operation} - project is in ${currentState} state`, {
      operation,
      currentState,
      projectId,
    })
  }
}

/**
 * Use case execution errors for operation failures
 */
export class ProjectUseCaseExecutionError extends ProjectApplicationError {
  readonly code = "PROJECT_USE_CASE_EXECUTION_ERROR"
  readonly category = "business" as const

  constructor(useCase: string, cause: Error, context?: Record<string, any>) {
    super(`Use case ${useCase} execution failed: ${cause.message}`, {
      useCase,
      originalError: cause.name,
      ...context,
    })
  }
}

/**
 * Utility function to transform domain/infrastructure errors to application errors
 */
export function transformToApplicationError(
  error: unknown,
  useCase: string,
  context?: Record<string, any>
): ProjectApplicationError {
  if (error instanceof ProjectApplicationError) {
    return error
  }

  if (error instanceof Error) {
    // Transform common domain/infrastructure errors
    if (error.name === "ProjectNotFoundError") {
      return new ProjectResourceNotFoundError(
        "Project",
        context?.projectId || "unknown"
      )
    }

    if (error.name === "ProjectValidationError") {
      return new ProjectValidationError("project", error.message, context)
    }

    if (error.name === "ProjectBusinessRuleViolationError") {
      return new ProjectBusinessRuleError(error.message, context)
    }

    if (error.name === "ProjectPersistenceError") {
      return new ProjectUseCaseExecutionError(useCase, error, context)
    }

    // Generic error transformation
    return new ProjectUseCaseExecutionError(useCase, error, context)
  }

  // Unknown error type
  return new ProjectUseCaseExecutionError(
    useCase,
    new Error(String(error)),
    context
  )
}

/**
 * Result wrapper for use case operations
 */
export type UseCaseResult<T> =
  | {
      success: true
      data: T
    }
  | {
      success: false
      error: ProjectApplicationError
    }

/**
 * Utility function to create successful use case results
 */
export function createSuccessResult<T>(data: T): UseCaseResult<T> {
  return { success: true, data }
}

/**
 * Utility function to create failed use case results
 */
export function createErrorResult<T>(
  error: ProjectApplicationError
): UseCaseResult<T> {
  return { success: false, error }
}

/**
 * Type guard to check if result is successful
 */
export function isSuccessResult<T>(
  result: UseCaseResult<T>
): result is { success: true; data: T } {
  return result.success
}

/**
 * Type guard to check if result is an error
 */
export function isErrorResult<T>(
  result: UseCaseResult<T>
): result is { success: false; error: ProjectApplicationError } {
  return !result.success
}
