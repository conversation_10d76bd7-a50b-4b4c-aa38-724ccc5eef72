// Import for internal use
import { ArchiveProjectUseCase } from "./services/ArchiveProjectUseCase"
import { AssignMemberToProjectUseCase } from "./services/AssignMemberToProjectUseCase"
import { ChangeProjectStatusUseCase } from "./services/ChangeProjectStatusUseCase"
import { CreateProjectUseCase } from "./services/CreateProjectUseCase"
import { UpdateProjectUseCase } from "./services/UpdateProjectUseCase"
import { ValidateProjectUseCase } from "./services/ValidateProjectUseCase"

/**
 * Projects Application Layer Index
 *
 * Centralized exports for the Projects module application layer.
 * Provides clean access to use cases, types, and application services.
 */

// ===== Use Cases (Application Services) =====

export { ArchiveProjectUseCase } from "./services/ArchiveProjectUseCase"
export { AssignMemberToProjectUseCase } from "./services/AssignMemberToProjectUseCase"
export { ChangeProjectStatusUseCase } from "./services/ChangeProjectStatusUseCase"
export { CreateProjectUseCase } from "./services/CreateProjectUseCase"
export { UpdateProjectUseCase } from "./services/UpdateProjectUseCase"
export { ValidateProjectUseCase } from "./services/ValidateProjectUseCase"

// ===== Application Types =====

export * from "./types/ApplicationErrors"
export * from "./types/ProjectOperationTypes"

// ===== Application Layer Utilities =====

/**
 * Application service factory for dependency injection
 */
export interface ApplicationServiceFactory {
  createProjectUseCase: () => CreateProjectUseCase
  updateProjectUseCase: () => UpdateProjectUseCase
  assignMemberToProjectUseCase: () => AssignMemberToProjectUseCase
  changeProjectStatusUseCase: () => ChangeProjectStatusUseCase
  archiveProjectUseCase: () => ArchiveProjectUseCase
  validateProjectUseCase: () => ValidateProjectUseCase
}

/**
 * Application layer configuration
 */
export interface ApplicationConfig {
  validation: {
    strictMode: boolean
    enableBusinessRuleValidation: boolean
    enablePermissionChecks: boolean
  }
  operations: {
    maxBatchSize: number
    enableOptimisticUpdates: boolean
    retryFailedOperations: boolean
  }
  logging: {
    logLevel: "debug" | "info" | "warn" | "error"
    logOperations: boolean
    logValidationFailures: boolean
  }
}

export const defaultApplicationConfig: ApplicationConfig = {
  validation: {
    strictMode: true,
    enableBusinessRuleValidation: true,
    enablePermissionChecks: true,
  },
  operations: {
    maxBatchSize: 50,
    enableOptimisticUpdates: true,
    retryFailedOperations: true,
  },
  logging: {
    logLevel: "info",
    logOperations: true,
    logValidationFailures: true,
  },
}

/**
 * Application layer health check
 * Validates that all use cases and dependencies are properly configured
 */
export function validateApplicationHealth(): {
  isHealthy: boolean
  issues: string[]
  components: {
    useCases: boolean
    types: boolean
    config: boolean
  }
} {
  const issues: string[] = []
  const components = {
    useCases: true,
    types: true,
    config: true,
  }

  // Validate use cases are available
  try {
    const useCases = [
      CreateProjectUseCase,
      UpdateProjectUseCase,
      AssignMemberToProjectUseCase,
      ChangeProjectStatusUseCase,
      ArchiveProjectUseCase,
      ValidateProjectUseCase,
    ]

    useCases.forEach((UseCase, index) => {
      if (typeof UseCase !== "function") {
        issues.push(`Use case ${index} is not a valid constructor function`)
        components.useCases = false
      }
    })
  } catch (error) {
    issues.push("Failed to load application use cases")
    components.useCases = false
  }

  // Validate types are available
  try {
    const {
      transformToApplicationError,
      createSuccessResult,
    } = require("./types/ApplicationErrors")
    if (
      typeof transformToApplicationError !== "function" ||
      typeof createSuccessResult !== "function"
    ) {
      issues.push("Application error utilities not properly loaded")
      components.types = false
    }
  } catch (error) {
    issues.push("Failed to load application types")
    components.types = false
  }

  return {
    isHealthy: issues.length === 0,
    issues,
    components,
  }
}

/**
 * Application service registry for centralized use case management
 */
export class ApplicationServiceRegistry {
  private services: Map<string, any> = new Map()

  register<T>(name: string, serviceFactory: () => T): void {
    this.services.set(name, serviceFactory)
  }

  get<T>(name: string): T {
    const factory = this.services.get(name)
    if (!factory) {
      throw new Error(`Application service '${name}' not registered`)
    }
    return factory()
  }

  isRegistered(name: string): boolean {
    return this.services.has(name)
  }

  getRegisteredServices(): string[] {
    return Array.from(this.services.keys())
  }
}

/**
 * Default application service registry with standard use cases
 */
export function createDefaultApplicationRegistry(dependencies: {
  projectRepository: any
  validationService: any
  teamManagementService: any
}): ApplicationServiceRegistry {
  const registry = new ApplicationServiceRegistry()

  registry.register(
    "createProject",
    () =>
      new CreateProjectUseCase(
        dependencies.projectRepository,
        dependencies.validationService
      )
  )

  registry.register(
    "updateProject",
    () =>
      new UpdateProjectUseCase(
        dependencies.projectRepository,
        dependencies.validationService
      )
  )

  registry.register(
    "assignMember",
    () =>
      new AssignMemberToProjectUseCase(
        dependencies.projectRepository,
        dependencies.teamManagementService
      )
  )

  registry.register(
    "changeStatus",
    () =>
      new ChangeProjectStatusUseCase(
        dependencies.projectRepository,
        dependencies.validationService
      )
  )

  registry.register(
    "archiveProject",
    () =>
      new ArchiveProjectUseCase(
        dependencies.projectRepository,
        dependencies.validationService
      )
  )

  registry.register(
    "validateProject",
    () =>
      new ValidateProjectUseCase(
        dependencies.projectRepository,
        dependencies.validationService
      )
  )

  return registry
}
