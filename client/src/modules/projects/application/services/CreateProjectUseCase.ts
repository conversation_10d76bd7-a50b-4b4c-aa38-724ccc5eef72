/**
 * Create Project Use Case
 *
 * Orchestrates the creation of new Project aggregates, handling validation,
 * business rule enforcement, and persistence coordination.
 */
import type { CreateProjectData } from "../../domain/entities/Project"

import { ProjectValidationService } from "../../domain/domain-services/ProjectValidationService"
import { Project } from "../../domain/entities/Project"
import { IProjectRepository } from "../../domain/repositories/IProjectRepository"
import { generateId } from "../../domain/shared/EntityId"
import {
  createErrorResult,
  CreateProjectRequest,
  CreateProjectResponse,
  createSuccessResult,
  ProjectBusinessRuleError,
  ProjectValidationError,
  transformToApplicationError,
  UseCase,
  UseCaseResult,
} from "../types"

/**
 * Use case for creating new projects with comprehensive validation
 * and business rule enforcement
 */
export class CreateProjectUseCase
  implements UseCase<CreateProjectRequest, CreateProjectResponse>
{
  constructor(
    private readonly projectRepository: IProjectRepository,
    private readonly validationService: ProjectValidationService
  ) {}

  /**
   * Executes project creation with validation and persistence
   */
  async execute(
    request: CreateProjectRequest
  ): Promise<UseCaseResult<CreateProjectResponse>> {
    try {
      // Input validation
      const validationResult = this.validateRequest(request)
      if (!validationResult.isValid) {
        return createErrorResult(
          new ProjectValidationError("request", validationResult.error!)
        )
      }

      // Generate unique project ID
      const projectId = generateId("project")

      // Build project create data from request
      const createData: CreateProjectData = {
        id: projectId,
        name: request.name.trim(),
        description: request.description?.trim(),
        ownerId: request.ownerId,
        clientId: request.clientId,
        priority: request.priority || "Medium",
        budgetData: request.budgetData,
        startDate: request.startDate,
        endDate: request.endDate,
        tags: request.tags?.filter(tag => tag && typeof tag === 'string').map(tag => tag.trim()).filter(Boolean),
        location: request.location?.trim(),
        externalProjectId: request.externalProjectId?.trim(),
      }

      // Create domain entity
      const project = Project.create(createData)

      // Business rule validation
      try {
        await this.validationService.validateProjectCreation(createData)
      } catch (validationError) {
        if (validationError instanceof Error) {
          return createErrorResult(
            new ProjectBusinessRuleError(validationError.message, {
              projectId: projectId,
              validationType: "business-rules",
            })
          )
        }
        throw validationError
      }

      // Check for duplicate projects (business rule)
      const duplicateCheck = await this.checkForDuplicates(project)
      if (duplicateCheck.hasDuplicate) {
        return createErrorResult(
          new ProjectBusinessRuleError(
            `Project with similar characteristics already exists: ${duplicateCheck.reason}`,
            {
              projectId: project.id,
              duplicateType: duplicateCheck.duplicateType,
              existingProjectId: duplicateCheck.existingProjectId,
            }
          )
        )
      }

      // Persist the project
      await this.projectRepository.save(project)

      // Return successful result
      return createSuccessResult({
        project,
        validationWarnings: [],
      })
    } catch (error) {
      return createErrorResult(
        transformToApplicationError(error, "CreateProjectUseCase", {
          requestData: this.sanitizeRequestForLogging(request),
        })
      )
    }
  }

  /**
   * Validates the incoming request data
   */
  private validateRequest(request: CreateProjectRequest): {
    isValid: boolean
    error?: string
  } {
    // Required field validation
    if (!request.name || request.name.trim().length === 0) {
      return { isValid: false, error: "Project name is required" }
    }

    if (request.name.trim().length > 200) {
      return {
        isValid: false,
        error: "Project name cannot exceed 200 characters",
      }
    }

    if (!request.ownerId || request.ownerId <= 0) {
      return { isValid: false, error: "Valid owner ID is required" }
    }

    // Optional field validation
    if (request.description && request.description.length > 2000) {
      return {
        isValid: false,
        error: "Project description cannot exceed 2000 characters",
      }
    }

    if (request.clientId && request.clientId <= 0) {
      return {
        isValid: false,
        error: "Client ID must be positive when provided",
      }
    }

    if (
      request.priority &&
      !["Low", "Medium", "High", "Critical"].includes(request.priority)
    ) {
      return { isValid: false, error: "Invalid priority level" }
    }

    // Budget validation
    if (request.budgetData) {
      if (request.budgetData.totalAmount <= 0) {
        return { isValid: false, error: "Budget total amount must be positive" }
      }

      if (
        !request.budgetData.currency ||
        request.budgetData.currency.length !== 3
      ) {
        return {
          isValid: false,
          error: "Valid 3-character currency code is required",
        }
      }
    }

    // Date validation
    if (request.startDate && request.endDate) {
      const startDate = new Date(request.startDate)
      const endDate = new Date(request.endDate)

      if (startDate >= endDate) {
        return { isValid: false, error: "End date must be after start date" }
      }
    }

    // Tags validation
    if (request.tags) {
      if (request.tags.length > 10) {
        return { isValid: false, error: "Maximum 10 tags allowed" }
      }

      // Only validate tags that will pass the filter (non-empty after trimming)
      const validTags = request.tags.filter(tag => tag && typeof tag === 'string' && tag.trim().length > 0)
      const invalidTags = validTags.filter(tag => tag.length > 50)
      
      if (invalidTags.length > 0) {
        return {
          isValid: false,
          error: "Tags cannot exceed 50 characters",
        }
      }
    }

    return { isValid: true }
  }

  /**
   * Checks for potential duplicate projects based on business rules
   */
  private async checkForDuplicates(project: Project): Promise<{
    hasDuplicate: boolean
    duplicateType?: string
    existingProjectId?: string
    reason?: string
  }> {
    try {
      // Check for same external project ID
      if (project.externalProjectId) {
        const existingByExternal = await this.projectRepository.findByCriteria({
          externalProjectId: project.externalProjectId,
          isActive: true,
        })

        if (existingByExternal.projects.length > 0) {
          return {
            hasDuplicate: true,
            duplicateType: "external-id",
            existingProjectId: existingByExternal.projects[0].id,
            reason: `External project ID ${project.externalProjectId} already exists`,
          }
        }
      }

      // Check for same name + client combination (business rule)
      if (project.clientId) {
        const existingByNameClient =
          await this.projectRepository.findByCriteria({
            clientId: project.clientId,
            isActive: true,
          })

        // Filter by name since repository doesn't support name criteria yet
        const duplicateProject = existingByNameClient.projects.find(
          p => p.name.toLowerCase() === project.name.toLowerCase()
        )

        if (duplicateProject) {
          return {
            hasDuplicate: true,
            duplicateType: "name-client",
            existingProjectId: duplicateProject.id,
            reason: `Project with name "${project.name}" already exists for this client`,
          }
        }
      }

      return { hasDuplicate: false }
    } catch (error) {
      // If duplicate check fails, log but don't block creation
      console.warn("Duplicate check failed during project creation:", error)
      return { hasDuplicate: false }
    }
  }

  /**
   * Sanitizes request data for logging (removes sensitive information)
   */
  private sanitizeRequestForLogging(
    request: CreateProjectRequest
  ): Record<string, any> {
    return {
      name: request.name,
      ownerId: request.ownerId,
      clientId: request.clientId,
      priority: request.priority,
      hasBudget: !!request.budgetData,
      hasDateRange: !!(request.startDate && request.endDate),
      tagCount: request.tags?.length || 0,
      hasLocation: !!request.location,
      hasExternalId: !!request.externalProjectId,
      requiresApproval: request.requiresApproval,
    }
  }
}
