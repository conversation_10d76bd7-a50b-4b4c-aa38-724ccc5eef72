/**
 * Assign Member to Project Use Case
 *
 * Orchestrates team member assignment to projects, handling role validation,
 * permission checks, and team capacity management.
 */

import { TeamManagementService } from "../../domain/domain-services/TeamManagementService"
import { Project } from "../../domain/entities/Project"
import { IProjectRepository } from "../../domain/repositories/IProjectRepository"
import { TeamRole } from "../../domain/value-objects/TeamRole"
import {
  AssignMemberRequest,
  AssignMemberResponse,
  createErrorResult,
  createSuccessResult,
  ProjectBusinessRuleError,
  ProjectPermissionError,
  ProjectResourceNotFoundError,
  ProjectValidationError,
  transformToApplicationError,
  UseCase,
  UseCaseResult,
} from "../types"

/**
 * Use case for assigning team members to projects with comprehensive validation
 */
export class AssignMemberToProjectUseCase
  implements UseCase<AssignMemberRequest, AssignMemberResponse>
{
  constructor(
    private readonly projectRepository: IProjectRepository,
    private readonly teamManagementService: TeamManagementService
  ) {}

  /**
   * Executes member assignment with validation and persistence
   */
  async execute(
    request: AssignMemberRequest
  ): Promise<UseCaseResult<AssignMemberResponse>> {
    try {
      // Input validation
      const validationResult = this.validateRequest(request)
      if (!validationResult.isValid) {
        return createErrorResult(
          new ProjectValidationError("request", validationResult.error!)
        )
      }

      // Fetch existing project
      const project = await this.projectRepository.findById(request.projectId)
      if (!project) {
        return createErrorResult(
          new ProjectResourceNotFoundError("Project", request.projectId)
        )
      }

      // Permission check
      const hasPermission = await this.checkAssignmentPermission(
        project,
        request.assignedBy
      )
      if (!hasPermission.allowed) {
        return createErrorResult(
          new ProjectPermissionError(
            "assign team members",
            request.assignedBy,
            request.projectId
          )
        )
      }

      // Check if user is already a member
      const existingMember = project.members.find(
        (member) => member.userId === request.userId
      )
      if (existingMember) {
        return createErrorResult(
          new ProjectBusinessRuleError(
            `User ${request.userId} is already a member of project ${request.projectId}`,
            {
              projectId: request.projectId,
              userId: request.userId,
              existingMemberId: existingMember.id,
              existingRole: existingMember.role.role,
            }
          )
        )
      }

      // Create team role
      const teamRole = TeamRole.create({ role: request.role as any })

      // Build assignment context
      const assignmentContext = request.assignmentContext ? {
        assignedBy: request.assignedBy,
        reason: request.assignmentContext.reason,
        expiresAt: request.expiresAt,
        priority: (request.assignmentContext.priority || "Medium") as "Low" | "Medium" | "High" | "Critical",
        skillsRequired: request.assignmentContext.skillsRequired,
        estimatedWorkload: request.assignmentContext.estimatedWorkload,
        isTemporaryAssignment: false,
        requiresApproval: request.assignmentContext.requiresApproval || false,
      } : undefined

      // Validate team capacity and role assignment
      const teamValidation =
        await this.teamManagementService.validateMemberAssignment(
          project,
          request.userId,
          teamRole,
          assignmentContext
        )

      if (!teamValidation.isValid) {
        return createErrorResult(
          new ProjectBusinessRuleError(teamValidation.errors.join(", "), {
            projectId: request.projectId,
            userId: request.userId,
            role: request.role,
          })
        )
      }

      // Add member to project
      const updatedProject = project.addTeamMember(
        request.userId,
        teamRole,
        request.assignedBy,
        request.expiresAt
      )

      // Persist the updated project
      await this.projectRepository.save(updatedProject)

      // Return successful result
      // Get the newly added member
      const addedMember = updatedProject.getMemberByUserId(request.userId)
      
      return createSuccessResult({
        project: updatedProject,
        member: addedMember!,
        assignmentId: addedMember!.id,
      })
    } catch (error) {
      return createErrorResult(
        transformToApplicationError(error, "AssignMemberToProjectUseCase", {
          projectId: request.projectId,
          userId: request.userId,
          assignedBy: request.assignedBy,
          role: request.role,
        })
      )
    }
  }

  /**
   * Validates the incoming assignment request
   */
  private validateRequest(request: AssignMemberRequest): {
    isValid: boolean
    error?: string
  } {
    // Project ID validation
    if (!request.projectId || request.projectId.trim().length === 0) {
      return { isValid: false, error: "Project ID is required" }
    }

    // User ID validation
    if (!request.userId || request.userId <= 0) {
      return { isValid: false, error: "Valid user ID is required" }
    }

    // Assigned by validation
    if (!request.assignedBy || request.assignedBy <= 0) {
      return { isValid: false, error: "Valid assigner user ID is required" }
    }

    // Role validation
    if (!request.role || request.role.trim().length === 0) {
      return { isValid: false, error: "Team role is required" }
    }

    if (request.role.length > 100) {
      return { isValid: false, error: "Team role cannot exceed 100 characters" }
    }

    // Expiration date validation
    if (request.expiresAt) {
      const expirationDate = new Date(request.expiresAt)
      const now = new Date()

      if (expirationDate <= now) {
        return {
          isValid: false,
          error: "Expiration date must be in the future",
        }
      }

      // Check if expiration is too far in the future (business rule)
      const maxExpirationMonths = 24
      const maxExpiration = new Date()
      maxExpiration.setMonth(maxExpiration.getMonth() + maxExpirationMonths)

      if (expirationDate > maxExpiration) {
        return {
          isValid: false,
          error: `Expiration date cannot be more than ${maxExpirationMonths} months in the future`,
        }
      }
    }

    // Assignment context validation
    if (request.assignmentContext) {
      const context = request.assignmentContext

      if (
        context.priority &&
        !["Low", "Medium", "High"].includes(context.priority)
      ) {
        return {
          isValid: false,
          error: "Invalid priority level in assignment context",
        }
      }

      if (
        context.estimatedWorkload !== undefined &&
        (context.estimatedWorkload < 0 || context.estimatedWorkload > 100)
      ) {
        return {
          isValid: false,
          error: "Estimated workload must be between 0 and 100 percent",
        }
      }

      if (context.skillsRequired && context.skillsRequired.length > 20) {
        return { isValid: false, error: "Maximum 20 skills can be specified" }
      }

      if (context.reason && context.reason.length > 500) {
        return {
          isValid: false,
          error: "Assignment reason cannot exceed 500 characters",
        }
      }
    }

    return { isValid: true }
  }

  /**
   * Checks if user has permission to assign members to the project
   */
  private async checkAssignmentPermission(
    project: Project,
    assignerId: number
  ): Promise<{ allowed: boolean; reason?: string }> {
    // Project owner can always assign members
    if (project.ownerId === assignerId) {
      return { allowed: true }
    }

    // Check if assigner is a team member with appropriate role
    const assignerMember = project.members.find(
      (member) => member.userId === assignerId
    )
    if (assignerMember && assignerMember.role.canManageProject()) {
      return { allowed: true }
    }

    // Check if project allows member assignment by team leads (business rule)
    const teamLeads = project.members.filter(
      (member) =>
        member.role.role.toLowerCase().includes("lead") ||
        member.role.role.toLowerCase().includes("manager")
    )

    if (teamLeads.some((lead) => lead.userId === assignerId)) {
      return { allowed: true }
    }

    return {
      allowed: false,
      reason:
        "User must have project management privileges or be a team lead to assign members",
    }
  }
}
