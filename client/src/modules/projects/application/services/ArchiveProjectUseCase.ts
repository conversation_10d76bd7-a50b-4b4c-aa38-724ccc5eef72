/**
 * Archive Project Use Case
 *
 * Orchestrates project archival operations with validation of completion status,
 * business rules, and batch processing capabilities.
 */

import { ProjectValidationService } from "../../domain/domain-services/ProjectValidationService"
import { Project } from "../../domain/entities/Project"
import { IProjectRepository } from "../../domain/repositories/IProjectRepository"
import {
  ArchiveProjectRequest,
  ArchiveProjectResponse,
  createErrorResult,
  createSuccessResult,
  ProjectBusinessRuleError,
  ProjectValidationError,
  transformToApplicationError,
  UseCase,
  UseCaseResult,
} from "../types"

/**
 * Use case for archiving completed projects with comprehensive validation
 */
export class ArchiveProjectUseCase
  implements UseCase<ArchiveProjectRequest, ArchiveProjectResponse>
{
  constructor(
    private readonly projectRepository: IProjectRepository,
    private readonly validationService: ProjectValidationService
  ) {}

  /**
   * Executes project archival with validation and persistence
   */
  async execute(
    request: ArchiveProjectRequest
  ): Promise<UseCaseResult<ArchiveProjectResponse>> {
    try {
      // Input validation
      const validationResult = this.validateRequest(request)
      if (!validationResult.isValid) {
        return createErrorResult(
          new ProjectValidationError("request", validationResult.error!)
        )
      }

      // Fetch projects to be archived
      const projects = await this.projectRepository.findByIds(
        request.projectIds
      )
      const foundProjectIds = new Set(projects.map((p) => p.id))
      const missingProjectIds = request.projectIds.filter(
        (id) => !foundProjectIds.has(id)
      )

      if (missingProjectIds.length > 0) {
        return createErrorResult(
          new ProjectBusinessRuleError(
            `Projects not found: ${missingProjectIds.join(", ")}`,
            { missingProjectIds }
          )
        )
      }

      // Validate archive permissions and business rules
      const validationResults = await this.validateArchivalEligibility(
        projects,
        request.archivedBy,
        request.validateCompletion || true
      )

      // Separate eligible and ineligible projects
      const eligibleProjects = validationResults
        .filter((result) => result.eligible)
        .map((result) => result.projectId)

      const ineligibleProjects = validationResults.filter(
        (result) => !result.eligible
      )

      // If validation is strict and any projects are ineligible, return error
      if (request.validateCompletion && ineligibleProjects.length > 0) {
        const issues = ineligibleProjects.map((result) => ({
          projectId: result.projectId,
          issues: result.reasons,
        }))

        return createErrorResult(
          new ProjectBusinessRuleError(
            `Some projects cannot be archived: ${ineligibleProjects.map((r) => r.projectId).join(", ")}`,
            { validationIssues: issues }
          )
        )
      }

      // Archive eligible projects
      if (eligibleProjects.length > 0) {
        await this.projectRepository.archiveCompletedProjects(eligibleProjects)
      }

      // Return successful result
      return createSuccessResult({
        archivedProjectIds: eligibleProjects,
        skippedProjectIds: ineligibleProjects.map((r) => r.projectId),
        validationIssues:
          ineligibleProjects.length > 0
            ? ineligibleProjects.map((result) => ({
                projectId: result.projectId,
                issues: result.reasons,
              }))
            : undefined,
      })
    } catch (error) {
      return createErrorResult(
        transformToApplicationError(error, "ArchiveProjectUseCase", {
          projectIds: request.projectIds,
          archivedBy: request.archivedBy,
          validateCompletion: request.validateCompletion,
        })
      )
    }
  }

  /**
   * Validates the incoming archive request
   */
  private validateRequest(request: ArchiveProjectRequest): {
    isValid: boolean
    error?: string
  } {
    // Project IDs validation
    if (!request.projectIds || request.projectIds.length === 0) {
      return { isValid: false, error: "At least one project ID is required" }
    }

    if (request.projectIds.length > 50) {
      return {
        isValid: false,
        error: "Cannot archive more than 50 projects at once",
      }
    }

    const invalidIds = request.projectIds.filter(
      (id) => !id || id.trim().length === 0
    )
    if (invalidIds.length > 0) {
      return {
        isValid: false,
        error: "All project IDs must be non-empty strings",
      }
    }

    // User ID validation
    if (!request.archivedBy || request.archivedBy <= 0) {
      return { isValid: false, error: "Valid user ID is required" }
    }

    // Reason validation
    if (request.reason && request.reason.length > 500) {
      return {
        isValid: false,
        error: "Archive reason cannot exceed 500 characters",
      }
    }

    return { isValid: true }
  }

  /**
   * Validates archival eligibility for each project
   */
  private async validateArchivalEligibility(
    projects: Project[],
    archivedBy: number,
    validateCompletion: boolean
  ): Promise<
    Array<{
      projectId: string
      eligible: boolean
      reasons: string[]
    }>
  > {
    const results = await Promise.all(
      projects.map(async (project) => {
        const reasons: string[] = []
        let eligible = true

        // Permission check
        const hasPermission = await this.checkArchivePermission(
          project,
          archivedBy
        )
        if (!hasPermission.allowed) {
          eligible = false
          reasons.push(hasPermission.reason || "Insufficient permissions")
        }

        // Completion status check
        if (validateCompletion && !project.isCompleted()) {
          eligible = false
          reasons.push(
            `Project status is ${project.status.status}, must be Completed`
          )
        }

        // Business rule validation
        const businessValidation =
          await this.validationService.validateProjectArchival(project)
        if (!businessValidation.isValid) {
          eligible = false
          reasons.push(...businessValidation.errors)
        }

        // Additional archival checks
        const additionalChecks =
          await this.performAdditionalArchivalChecks(project)
        if (additionalChecks.reasons.length > 0) {
          if (additionalChecks.blocking) {
            eligible = false
          }
          reasons.push(...additionalChecks.reasons)
        }

        return {
          projectId: project.id,
          eligible,
          reasons,
        }
      })
    )

    return results
  }

  /**
   * Checks if user has permission to archive the project
   */
  private async checkArchivePermission(
    project: Project,
    userId: number
  ): Promise<{ allowed: boolean; reason?: string }> {
    // Project owner can always archive
    if (project.ownerId === userId) {
      return { allowed: true }
    }

    // Check if user is a team member with appropriate role
    const userMember = project.members.find(
      (member) => member.userId === userId
    )
    if (userMember && userMember.role.canManageProject()) {
      return { allowed: true }
    }

    // Check if user has admin role (would need to be checked via external service)
    // For now, we'll assume this check would be done via a separate service

    return {
      allowed: false,
      reason:
        "User must be project owner or have project management privileges",
    }
  }

  /**
   * Performs additional business-specific archival checks
   */
  private async performAdditionalArchivalChecks(project: Project): Promise<{
    blocking: boolean
    reasons: string[]
  }> {
    const reasons: string[] = []
    let blocking = false

    // Check for outstanding budget issues
    if (project.budget.isOverBudget()) {
      reasons.push(
        "Project is over budget - budget reconciliation may be required"
      )
      // This is a warning, not blocking
    }

    // Check for active team members
    const activeMembers = project.members.filter(
      (member) => member.isActive && !member.hasExpired()
    )
    if (activeMembers.length > 0) {
      reasons.push(
        `${activeMembers.length} team members are still active - consider removing them first`
      )
      // This is a warning, not blocking
    }

    // Check for recent activity (within last 30 days)
    const thirtyDaysAgo = new Date()
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

    if (project.updatedAt && new Date(project.updatedAt) > thirtyDaysAgo) {
      reasons.push(
        "Project was recently updated - confirm archival is intended"
      )
      // This is a warning, not blocking
    }

    // Check if project has pending approval requirements
    if (project.requiresApproval()) {
      const approvalStatus = await this.checkApprovalStatus(project)
      if (!approvalStatus.approved) {
        reasons.push("Project archival requires approval")
        blocking = true
      }
    }

    return { blocking, reasons }
  }

  /**
   * Checks approval status for projects requiring approval
   */
  private async checkApprovalStatus(
    project: Project
  ): Promise<{ approved: boolean; reason?: string }> {
    // This would typically check with an external approval service
    // For now, we'll implement a simple check based on project completion

    if (project.isCompleted() && project.status.changedAt) {
      const completionDate = new Date(project.status.changedAt)
      const approvalWindow = new Date()
      approvalWindow.setDate(approvalWindow.getDate() - 7) // 7 days approval window

      if (completionDate < approvalWindow) {
        return { approved: true }
      }
    }

    return {
      approved: false,
      reason: "Project must be completed for at least 7 days before archival",
    }
  }
}
