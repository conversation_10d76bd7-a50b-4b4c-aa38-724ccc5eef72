/**
 * Validate Project Use Case
 *
 * Orchestrates comprehensive project validation including business rules,
 * data consistency, and permission checks.
 */

import { ProjectValidationService } from "../../domain/domain-services/ProjectValidationService"
import { Project } from "../../domain/entities/Project"
import { IProjectRepository } from "../../domain/repositories/IProjectRepository"
import {
  createErrorResult,
  createSuccessResult,
  ProjectResourceNotFoundError,
  ProjectValidationError,
  transformToApplicationError,
  UseCase,
  UseCaseResult,
  ValidateProjectRequest,
  ValidateProjectResponse,
} from "../types"

/**
 * Use case for comprehensive project validation across multiple dimensions
 */
export class ValidateProjectUseCase
  implements UseCase<ValidateProjectRequest, ValidateProjectResponse>
{
  constructor(
    private readonly projectRepository: IProjectRepository,
    private readonly validationService: ProjectValidationService
  ) {}

  /**
   * Executes comprehensive project validation
   */
  async execute(
    request: ValidateProjectRequest
  ): Promise<UseCaseResult<ValidateProjectResponse>> {
    try {
      // Input validation
      const validationResult = this.validateRequest(request)
      if (!validationResult.isValid) {
        return createErrorResult(
          new ProjectValidationError("request", validationResult.error!)
        )
      }

      // Fetch project
      const project = await this.projectRepository.findById(request.projectId)
      if (!project) {
        return createErrorResult(
          new ProjectResourceNotFoundError("Project", request.projectId)
        )
      }

      // Perform validation based on type
      const validationResults = await this.performValidation(project, request)

      // Generate recommendations based on validation results
      const recommendations = this.generateRecommendations(
        validationResults,
        project
      )

      // Determine overall validity
      const isValid = this.determineOverallValidity(
        validationResults,
        request.validationType
      )

      return createSuccessResult({
        isValid,
        validationResults,
        recommendations,
      })
    } catch (error) {
      return createErrorResult(
        transformToApplicationError(error, "ValidateProjectUseCase", {
          projectId: request.projectId,
          validationType: request.validationType,
          userId: request.userId,
        })
      )
    }
  }

  /**
   * Validates the incoming validation request
   */
  private validateRequest(request: ValidateProjectRequest): {
    isValid: boolean
    error?: string
  } {
    // Project ID validation
    if (!request.projectId || request.projectId.trim().length === 0) {
      return { isValid: false, error: "Project ID is required" }
    }

    // Validation type validation
    const validTypes = [
      "business-rules",
      "data-consistency",
      "permissions",
      "complete",
    ]
    if (!validTypes.includes(request.validationType)) {
      return {
        isValid: false,
        error: `Invalid validation type. Must be one of: ${validTypes.join(", ")}`,
      }
    }

    // User ID validation (required for permission checks)
    if (
      request.validationType === "permissions" ||
      request.validationType === "complete"
    ) {
      if (!request.userId || request.userId <= 0) {
        return {
          isValid: false,
          error: "Valid user ID is required for permission validation",
        }
      }
    }

    return { isValid: true }
  }

  /**
   * Performs validation based on the requested type
   */
  private async performValidation(
    project: Project,
    request: ValidateProjectRequest
  ): Promise<ValidateProjectResponse["validationResults"]> {
    const results: ValidateProjectResponse["validationResults"] = {
      businessRules: { passed: [], failed: [] },
      dataConsistency: { consistent: true },
      permissions: { hasPermission: true },
    }

    // Business rules validation
    if (
      request.validationType === "business-rules" ||
      request.validationType === "complete"
    ) {
      results.businessRules = await this.validateBusinessRules(project)
    }

    // Data consistency validation
    if (
      request.validationType === "data-consistency" ||
      request.validationType === "complete"
    ) {
      results.dataConsistency = await this.validateDataConsistency(project)
    }

    // Permission validation
    if (
      request.validationType === "permissions" ||
      request.validationType === "complete"
    ) {
      results.permissions = await this.validatePermissions(
        project,
        request.userId!
      )
    }

    return results
  }

  /**
   * Validates business rules for the project
   */
  private async validateBusinessRules(project: Project): Promise<{
    passed: string[]
    failed: Array<{
      rule: string
      message: string
      severity: "error" | "warning"
    }>
  }> {
    const passed: string[] = []
    const failed: Array<{
      rule: string
      message: string
      severity: "error" | "warning"
    }> = []

    // Use domain validation service
    const businessValidation =
      await this.validationService.validateProjectBusinessRules(project)

    // Process validation results
    businessValidation.passedRules.forEach((rule) => passed.push(rule))
    businessValidation.violations.forEach((violation) => {
      failed.push({
        rule: violation.rule,
        message: violation.message,
        severity: violation.severity,
      })
    })

    // Additional project-specific validations
    await this.validateProjectSpecificRules(project, passed, failed)

    return { passed, failed }
  }

  /**
   * Validates project-specific business rules
   */
  private async validateProjectSpecificRules(
    project: Project,
    passed: string[],
    failed: Array<{
      rule: string
      message: string
      severity: "error" | "warning"
    }>
  ): Promise<void> {
    // Name validation
    if (project.name && project.name.trim().length > 0) {
      passed.push("Project has valid name")
    } else {
      failed.push({
        rule: "project-name-required",
        message: "Project name is required",
        severity: "error",
      })
    }

    // Owner validation
    if (project.ownerId > 0) {
      passed.push("Project has valid owner")
    } else {
      failed.push({
        rule: "project-owner-required",
        message: "Project must have a valid owner",
        severity: "error",
      })
    }

    // Team composition validation
    if (project.members.length > 0) {
      passed.push("Project has team members")

      // Check for active members
      const activeMembers = project.members.filter(
        (m) => m.isActive && !m.hasExpired()
      )
      if (activeMembers.length > 0) {
        passed.push("Project has active team members")
      } else {
        failed.push({
          rule: "active-members-required",
          message: "Project should have at least one active team member",
          severity: "warning",
        })
      }
    } else {
      failed.push({
        rule: "team-members-required",
        message: "Project should have team members assigned",
        severity: "warning",
      })
    }

    // Budget validation
    if (project.clientId && project.budget.totalAmount === 0) {
      failed.push({
        rule: "client-project-budget-required",
        message: "Client projects should have budget information",
        severity: "warning",
      })
    } else if (project.budget.totalAmount > 0) {
      passed.push("Project has budget information")

      if (project.budget.isOverBudget()) {
        failed.push({
          rule: "budget-exceeded",
          message: "Project is over budget",
          severity: "warning",
        })
      } else {
        passed.push("Project is within budget")
      }
    }

    // Timeline validation
    if (project.startDate && project.endDate) {
      passed.push("Project has timeline defined")

      const _start = new Date(project.startDate) // Unused
      const end = new Date(project.endDate)
      const now = new Date()

      if (end < now && !project.isCompleted()) {
        failed.push({
          rule: "project-overdue",
          message: "Project end date has passed but project is not completed",
          severity: "warning",
        })
      }
    } else {
      failed.push({
        rule: "timeline-missing",
        message: "Project should have start and end dates defined",
        severity: "warning",
      })
    }

    // Status validation
    if (project.status.status === "Active" && project.members.length === 0) {
      failed.push({
        rule: "active-project-needs-team",
        message: "Active projects should have team members assigned",
        severity: "error",
      })
    }
  }

  /**
   * Validates data consistency
   */
  private async validateDataConsistency(project: Project): Promise<{
    consistent: boolean
    issues?: string[]
  }> {
    try {
      // Use repository's consistency validation
      const isConsistent =
        await this.projectRepository.validateAggregateConsistency(project.id)

      if (isConsistent) {
        return { consistent: true }
      } else {
        return {
          consistent: false,
          issues: ["Data consistency issues detected - check logs for details"],
        }
      }
    } catch (error) {
      return {
        consistent: false,
        issues: [
          `Consistency check failed: ${error instanceof Error ? error.message : "Unknown error"}`,
        ],
      }
    }
  }

  /**
   * Validates user permissions for the project
   */
  private async validatePermissions(
    project: Project,
    userId: number
  ): Promise<{
    hasPermission: boolean
    missingPermissions?: string[]
  }> {
    const missingPermissions: string[] = []

    // Check basic view permission
    if (project.ownerId !== userId) {
      const userMember = project.members.find(
        (member) => member.userId === userId
      )
      if (!userMember) {
        missingPermissions.push("view-project")
      } else {
        // User is a team member, check specific permissions
        if (!userMember.role.role.includes('Manager') && !userMember.role.role.includes('Admin') && project.clientId) {
          missingPermissions.push("view-budget")
        }

        if (!userMember.role.canManageProject()) {
          missingPermissions.push("manage-project")
        }
      }
    }

    return {
      hasPermission: missingPermissions.length === 0,
      missingPermissions:
        missingPermissions.length > 0 ? missingPermissions : undefined,
    }
  }

  /**
   * Determines overall validity based on validation results
   */
  private determineOverallValidity(
    results: ValidateProjectResponse["validationResults"],
    validationType: string
  ): boolean {
    switch (validationType) {
      case "business-rules":
        return (
          results.businessRules.failed.filter((f) => f.severity === "error")
            .length === 0
        )

      case "data-consistency":
        return results.dataConsistency.consistent

      case "permissions":
        return results.permissions.hasPermission

      case "complete":
        return (
          results.businessRules.failed.filter((f) => f.severity === "error")
            .length === 0 &&
          results.dataConsistency.consistent &&
          results.permissions.hasPermission
        )

      default:
        return false
    }
  }

  /**
   * Generates recommendations based on validation results
   */
  private generateRecommendations(
    results: ValidateProjectResponse["validationResults"],
    project: Project
  ): string[] {
    const recommendations: string[] = []

    // Business rule recommendations
    const errorRules = results.businessRules.failed.filter(
      (f) => f.severity === "error"
    )
    const warningRules = results.businessRules.failed.filter(
      (f) => f.severity === "warning"
    )

    if (errorRules.length > 0) {
      recommendations.push(
        `Address ${errorRules.length} critical business rule violations`
      )
    }

    if (warningRules.length > 0) {
      recommendations.push(
        `Review ${warningRules.length} business rule warnings`
      )
    }

    // Data consistency recommendations
    if (!results.dataConsistency.consistent) {
      recommendations.push("Investigate and resolve data consistency issues")
    }

    // Permission recommendations
    if (!results.permissions.hasPermission) {
      recommendations.push("Ensure proper user permissions are configured")
    }

    // Project-specific recommendations
    if (project.budget.isOverBudget()) {
      recommendations.push("Review and adjust project budget or scope")
    }

    if (project.members.filter((m) => m.hasExpired()).length > 0) {
      recommendations.push("Remove or extend expired team member assignments")
    }

    if (project.status.status === "Active" && !project.startDate) {
      recommendations.push("Set project start date for active projects")
    }

    return recommendations
  }
}
