/**
 * Update Project Use Case
 *
 * Orchestrates project updates by fetching existing aggregates, applying business methods,
 * and coordinating persistence. Handles partial updates and field-level validation.
 */

import { ProjectValidationService } from "../../domain/domain-services/ProjectValidationService"
import { Project } from "../../domain/entities/Project"
import { IProjectRepository } from "../../domain/repositories/IProjectRepository"
import { ProjectBudget } from "../../domain/value-objects/ProjectBudget"
import {
  createErrorResult,
  createSuccessResult,
  ProjectBusinessRuleError,
  ProjectPermissionError,
  ProjectResourceNotFoundError,
  ProjectValidationError,
  transformToApplicationError,
  UpdateProjectRequest,
  UpdateProjectResponse,
  UseCase,
  UseCaseResult,
} from "../types"

/**
 * Use case for updating existing projects with validation and business rule enforcement
 */
export class UpdateProjectUseCase
  implements UseCase<UpdateProjectRequest, UpdateProjectResponse>
{
  constructor(
    private readonly projectRepository: IProjectRepository,
    private readonly validationService: ProjectValidationService
  ) {}

  /**
   * Executes project update with validation and persistence
   */
  async execute(
    request: UpdateProjectRequest
  ): Promise<UseCaseResult<UpdateProjectResponse>> {
    try {
      // Input validation
      const validationResult = this.validateRequest(request)
      if (!validationResult.isValid) {
        return createErrorResult(
          new ProjectValidationError("request", validationResult.error!)
        )
      }

      // Fetch existing project
      const existingProject = await this.projectRepository.findById(
        request.projectId
      )
      if (!existingProject) {
        return createErrorResult(
          new ProjectResourceNotFoundError("Project", request.projectId)
        )
      }

      // Permission check
      const hasPermission = await this.checkUpdatePermission(
        existingProject,
        request.userId
      )
      if (!hasPermission.allowed) {
        return createErrorResult(
          new ProjectPermissionError(
            "update project",
            request.userId,
            request.projectId
          )
        )
      }

      // Apply updates to project aggregate
      const updateResult = await this.applyUpdates(
        existingProject,
        request.updates,
        request.userId
      )
      if (!updateResult.success) {
        return createErrorResult(updateResult.error!)
      }

      const { updatedProject, updatedFields } = updateResult

      // Business rule validation for updated project
      const businessValidation =
        await this.validationService.validateProjectUpdate(
          existingProject,
          updatedProject
        )
      if (!businessValidation.isValid) {
        return createErrorResult(
          new ProjectBusinessRuleError(
            businessValidation.violations.join(", "),
            {
              projectId: request.projectId,
              updatedFields,
              userId: request.userId,
            }
          )
        )
      }

      // Persist the updated project
      await this.projectRepository.save(updatedProject)

      // Return successful result
      return createSuccessResult({
        project: updatedProject,
        updatedFields,
        validationWarnings: businessValidation.warnings || [],
      })
    } catch (error) {
      return createErrorResult(
        transformToApplicationError(error, "UpdateProjectUseCase", {
          projectId: request.projectId,
          userId: request.userId,
          updatedFields: Object.keys(request.updates),
        })
      )
    }
  }

  /**
   * Validates the incoming update request
   */
  private validateRequest(request: UpdateProjectRequest): {
    isValid: boolean
    error?: string
  } {
    // Project ID validation
    if (!request.projectId || request.projectId.trim().length === 0) {
      return { isValid: false, error: "Project ID is required" }
    }

    // User ID validation
    if (!request.userId || request.userId <= 0) {
      return { isValid: false, error: "Valid user ID is required" }
    }

    // Updates validation
    if (!request.updates || Object.keys(request.updates).length === 0) {
      return { isValid: false, error: "At least one field must be updated" }
    }

    const { updates } = request

    // Name validation
    if (updates.name !== undefined) {
      if (!updates.name || updates.name.trim().length === 0) {
        return { isValid: false, error: "Project name cannot be empty" }
      }
      if (updates.name.trim().length > 200) {
        return {
          isValid: false,
          error: "Project name cannot exceed 200 characters",
        }
      }
    }

    // Description validation
    if (
      updates.description !== undefined &&
      updates.description.length > 2000
    ) {
      return {
        isValid: false,
        error: "Project description cannot exceed 2000 characters",
      }
    }

    // Priority validation
    if (
      updates.priority &&
      !["Low", "Medium", "High", "Critical"].includes(updates.priority)
    ) {
      return { isValid: false, error: "Invalid priority level" }
    }

    // Budget validation
    if (updates.budgetData) {
      if (
        updates.budgetData.totalAmount !== undefined &&
        updates.budgetData.totalAmount <= 0
      ) {
        return { isValid: false, error: "Budget total amount must be positive" }
      }

      if (
        updates.budgetData.spentAmount !== undefined &&
        updates.budgetData.spentAmount < 0
      ) {
        return {
          isValid: false,
          error: "Budget spent amount cannot be negative",
        }
      }

      if (
        updates.budgetData.currency &&
        updates.budgetData.currency.length !== 3
      ) {
        return {
          isValid: false,
          error: "Valid 3-character currency code is required",
        }
      }
    }

    // Date validation
    if (updates.startDate && updates.endDate) {
      const startDate = new Date(updates.startDate)
      const endDate = new Date(updates.endDate)

      if (startDate >= endDate) {
        return { isValid: false, error: "End date must be after start date" }
      }
    }

    // Tags validation
    if (updates.tags) {
      if (updates.tags.length > 10) {
        return { isValid: false, error: "Maximum 10 tags allowed" }
      }

      const invalidTags = updates.tags.filter(
        (tag) => !tag || tag.trim().length === 0 || tag.length > 50
      )
      if (invalidTags.length > 0) {
        return {
          isValid: false,
          error: "Tags must be non-empty and under 50 characters",
        }
      }
    }

    return { isValid: true }
  }

  /**
   * Checks if user has permission to update the project
   */
  private async checkUpdatePermission(
    project: Project,
    userId: number
  ): Promise<{ allowed: boolean; reason?: string }> {
    // Project owner can always update
    if (project.ownerId === userId) {
      return { allowed: true }
    }

    // Check if user is a team member with appropriate role
    const userMember = project.members.find(
      (member) => member.userId === userId
    )
    if (userMember && userMember.role.canManageProject()) {
      return { allowed: true }
    }

    return {
      allowed: false,
      reason:
        "User must be project owner or team member with management privileges",
    }
  }

  /**
   * Applies updates to the project aggregate using domain methods
   */
  private async applyUpdates(
    project: Project,
    updates: UpdateProjectRequest["updates"],
    userId: number
  ): Promise<
    | {
        success: true
        updatedProject: Project
        updatedFields: string[]
      }
    | {
        success: false
        error: ProjectBusinessRuleError
      }
  > {
    const updatedFields: string[] = []
    let updatedProject = project

    try {
      // Basic info updates
      if (
        updates.name !== undefined ||
        updates.description !== undefined ||
        updates.priority !== undefined
      ) {
        const basicUpdates: {
          name?: string
          description?: string
          priority?: any
        } = {}

        // Only track fields that are actually being changed
        if (updates.name !== undefined) {
          basicUpdates.name = updates.name.trim()
          if (basicUpdates.name !== updatedProject.name) {
            updatedFields.push("name")
          }
        }

        if (updates.description !== undefined) {
          basicUpdates.description = updates.description?.trim()
          if (basicUpdates.description !== updatedProject.description) {
            updatedFields.push("description")
          }
        }

        if (updates.priority !== undefined) {
          basicUpdates.priority = updates.priority
          if (basicUpdates.priority !== updatedProject.priority) {
            updatedFields.push("priority")
          }
        }

        // Only update if there are actual changes
        if (updatedFields.length > 0) {
          updatedProject = updatedProject.updateProjectInfo(
            basicUpdates,
            userId
          )
        }
      }

      // Budget updates
      if (updates.budgetData) {
        const currentBudget = updatedProject.budget
        
        // Create new budget data with updates
        const newBudgetData = {
          totalAmount: updates.budgetData.totalAmount ?? currentBudget.totalAmount,
          spentAmount: updates.budgetData.spentAmount ?? currentBudget.spentAmount,
          currency: updates.budgetData.currency ?? currentBudget.currency,
          expenses: currentBudget.expenses,
          budgetCategories: currentBudget.budgetCategories,
        }

        // Create new budget instance
        const newBudget = ProjectBudget.create(newBudgetData)
        
        updatedProject = updatedProject.updateBudget(newBudget, userId, "Budget update via API")

        if (updates.budgetData.totalAmount !== undefined) updatedFields.push("budgetTotal")
        if (updates.budgetData.spentAmount !== undefined) updatedFields.push("budgetSpent")  
        if (updates.budgetData.currency !== undefined) updatedFields.push("budgetCurrency")
        if (updates.budgetData.allocatedAmount !== undefined) updatedFields.push("budgetAllocated")
      }

      // Timeline updates
      if (updates.startDate !== undefined || updates.endDate !== undefined) {
        updatedProject = updatedProject.updateTimeline(
          updates.startDate,
          updates.endDate,
          userId
        )

        if (updates.startDate !== undefined) updatedFields.push("startDate")
        if (updates.endDate !== undefined) updatedFields.push("endDate")
      }

      // Tags updates
      if (updates.tags !== undefined) {
        const cleanTags = updates.tags.map((tag) => tag.trim()).filter(Boolean)
        updatedProject = updatedProject.updateTags(cleanTags, userId)
        updatedFields.push("tags")
      }

      // Location updates
      if (updates.location !== undefined) {
        updatedProject = updatedProject.updateLocation(updates.location?.trim(), userId)
        updatedFields.push("location")
      }

      // Approval requirement updates
      if (updates.requiresApproval !== undefined) {
        // Note: requiresApproval is a computed property based on status and budget
        // For now, just track the field without functional impact
        // TODO: Implement proper business logic for approval requirements
        updatedFields.push("requiresApproval")
      }

      return {
        success: true,
        updatedProject,
        updatedFields,
      }
    } catch (error) {
      return {
        success: false,
        error: new ProjectBusinessRuleError(
          `Failed to apply updates: ${error instanceof Error ? error.message : "Unknown error"}`,
          { updatedFields, originalError: error }
        ),
      }
    }
  }
}
