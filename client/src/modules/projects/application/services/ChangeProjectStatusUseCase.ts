/**
 * Change Project Status Use Case
 *
 * Orchestrates project status transitions with validation of business rules,
 * state transition logic, and permission checks.
 */

import { ProjectValidationService } from "../../domain/domain-services/ProjectValidationService"
import { Project } from "../../domain/entities/Project"
import { IProjectRepository } from "../../domain/repositories/IProjectRepository"
import {
  ChangeProjectStatusRequest,
  ChangeProjectStatusResponse,
  createErrorResult,
  createSuccessResult,
  ProjectBusinessRuleError,
  ProjectOperationConflictError,
  ProjectPermissionError,
  ProjectResourceNotFoundError,
  ProjectValidationError,
  transformToApplicationError,
  UseCase,
  UseCaseResult,
} from "../types"

/**
 * Use case for changing project status with comprehensive state transition validation
 */
export class ChangeProjectStatusUseCase
  implements UseCase<ChangeProjectStatusRequest, ChangeProjectStatusResponse>
{
  constructor(
    private readonly projectRepository: IProjectRepository,
    private readonly validationService: ProjectValidationService
  ) {}

  /**
   * Executes project status change with validation and persistence
   */
  async execute(
    request: ChangeProjectStatusRequest
  ): Promise<UseCaseResult<ChangeProjectStatusResponse>> {
    try {
      // Input validation
      const validationResult = this.validateRequest(request)
      if (!validationResult.isValid) {
        return createErrorResult(
          new ProjectValidationError("request", validationResult.error!)
        )
      }

      // Fetch existing project
      const project = await this.projectRepository.findById(request.projectId)
      if (!project) {
        return createErrorResult(
          new ProjectResourceNotFoundError("Project", request.projectId)
        )
      }

      // Store previous status for response
      const previousStatus = project.status

      // Check if status change is needed
      if (previousStatus.status === request.newStatus) {
        return createSuccessResult({
          project,
          previousStatus,
          newStatus: previousStatus,
          transitionValidated: true,
        })
      }

      // Permission check
      const hasPermission = await this.checkStatusChangePermission(
        project,
        request.changedBy
      )
      if (!hasPermission.allowed) {
        return createErrorResult(
          new ProjectPermissionError(
            `change status from ${previousStatus.status} to ${request.newStatus}`,
            request.changedBy,
            request.projectId
          )
        )
      }

      // Validate status transition
      const transitionValidation = await this.validateStatusTransition(
        project,
        request.newStatus,
        request.force || false
      )

      if (!transitionValidation.allowed) {
        return createErrorResult(
          new ProjectOperationConflictError(
            `change status to ${request.newStatus}`,
            previousStatus.status,
            request.projectId
          )
        )
      }

      // Apply status change using appropriate domain method
      const updatedProject = await this.applyStatusChange(
        project,
        request.newStatus,
        request.changedBy,
        request.reason
      )

      if (!updatedProject.success) {
        return createErrorResult(updatedProject.error!)
      }

      // Business rule validation for new status
      const businessValidation =
        await this.validationService.validateProjectStatusChange(
          project,
          updatedProject.project
        )

      if (!businessValidation.isValid && !request.force) {
        return createErrorResult(
          new ProjectBusinessRuleError(
            businessValidation.errors.join(", "),
            {
              projectId: request.projectId,
              previousStatus: previousStatus.status,
              newStatus: request.newStatus,
              changedBy: request.changedBy,
            }
          )
        )
      }

      // Persist the updated project
      await this.projectRepository.save(updatedProject.project)

      // Return successful result
      return createSuccessResult({
        project: updatedProject.project,
        previousStatus,
        newStatus: updatedProject.project.status,
        transitionValidated: transitionValidation.validated,
      })
    } catch (error) {
      return createErrorResult(
        transformToApplicationError(error, "ChangeProjectStatusUseCase", {
          projectId: request.projectId,
          newStatus: request.newStatus,
          changedBy: request.changedBy,
          force: request.force,
        })
      )
    }
  }

  /**
   * Validates the incoming status change request
   */
  private validateRequest(request: ChangeProjectStatusRequest): {
    isValid: boolean
    error?: string
  } {
    // Project ID validation
    if (!request.projectId || request.projectId.trim().length === 0) {
      return { isValid: false, error: "Project ID is required" }
    }

    // User ID validation
    if (!request.changedBy || request.changedBy <= 0) {
      return { isValid: false, error: "Valid user ID is required" }
    }

    // Status validation
    const validStatuses = [
      "Draft",
      "Active",
      "Paused",
      "Completed",
      "Cancelled",
    ]
    if (!validStatuses.includes(request.newStatus)) {
      return {
        isValid: false,
        error: `Invalid status. Must be one of: ${validStatuses.join(", ")}`,
      }
    }

    // Reason validation
    if (request.reason && request.reason.length > 500) {
      return {
        isValid: false,
        error: "Status change reason cannot exceed 500 characters",
      }
    }

    return { isValid: true }
  }

  /**
   * Checks if user has permission to change project status
   */
  private async checkStatusChangePermission(
    project: Project,
    userId: number
  ): Promise<{ allowed: boolean; reason?: string }> {
    // Project owner can always change status
    if (project.ownerId === userId) {
      return { allowed: true }
    }

    // Check if user is a team member with appropriate role
    const userMember = project.members.find(
      (member) => member.userId === userId
    )
    if (userMember && userMember.role.canManageProject()) {
      return { allowed: true }
    }

    return {
      allowed: false,
      reason:
        "User must be project owner or have project management privileges",
    }
  }

  /**
   * Validates if the status transition is allowed
   */
  private async validateStatusTransition(
    project: Project,
    newStatus: string,
    force: boolean
  ): Promise<{ allowed: boolean; validated: boolean; reason?: string }> {
    const currentStatus = project.status.status

    // Define allowed transitions
    const allowedTransitions: Record<string, string[]> = {
      Draft: ["Active", "Cancelled"],
      Active: ["Paused", "Completed", "Cancelled"],
      Paused: ["Active", "Completed", "Cancelled"],
      Completed: [], // Completed projects generally cannot change status
      Cancelled: [], // Cancelled projects generally cannot change status
    }

    // Check if transition is normally allowed
    const isTransitionAllowed =
      allowedTransitions[currentStatus]?.includes(newStatus) || false

    if (!isTransitionAllowed && !force) {
      return {
        allowed: false,
        validated: false,
        reason: `Cannot transition from ${currentStatus} to ${newStatus}`,
      }
    }

    // Additional business rule validations
    if (newStatus === "Completed") {
      // Check if project can be completed
      const canComplete = await this.validateProjectCompletion(project)
      if (!canComplete.valid && !force) {
        return {
          allowed: false,
          validated: false,
          reason: `Project cannot be completed: ${canComplete.reasons.join(", ")}`,
        }
      }
    }

    if (newStatus === "Active") {
      // Check if project can be activated
      const canActivate = await this.validateProjectActivation(project)
      if (!canActivate.valid && !force) {
        return {
          allowed: false,
          validated: false,
          reason: `Project cannot be activated: ${canActivate.reasons.join(", ")}`,
        }
      }
    }

    return {
      allowed: true,
      validated: isTransitionAllowed,
    }
  }

  /**
   * Applies the status change using appropriate domain methods
   */
  private async applyStatusChange(
    project: Project,
    newStatus: string,
    changedBy: number,
    reason?: string
  ): Promise<
    | {
        success: true
        project: Project
      }
    | {
        success: false
        error: ProjectBusinessRuleError
      }
  > {
    try {
      let updatedProject: Project

      switch (newStatus) {
        case "Active":
          updatedProject = project.activateProject(changedBy, reason)
          break

        case "Paused":
          updatedProject = project.pauseProject(changedBy, reason)
          break

        case "Completed":
          updatedProject = project.completeProject(changedBy, reason)
          break

        case "Cancelled":
          updatedProject = project.cancelProject(changedBy, reason)
          break

        case "Draft":
          updatedProject = project.revertToDraft(changedBy, reason)
          break

        default:
          throw new Error(`Unsupported status transition to ${newStatus}`)
      }

      return {
        success: true,
        project: updatedProject,
      }
    } catch (error) {
      return {
        success: false,
        error: new ProjectBusinessRuleError(
          `Failed to change status: ${error instanceof Error ? error.message : "Unknown error"}`,
          { newStatus, changedBy, originalError: error }
        ),
      }
    }
  }

  /**
   * Validates if project can be completed
   */
  private async validateProjectCompletion(
    project: Project
  ): Promise<{ valid: boolean; reasons: string[] }> {
    const reasons: string[] = []

    // Check if all required team members are still active
    const expiredMembers = project.members.filter((member) =>
      member.hasExpired()
    )
    if (expiredMembers.length > 0) {
      reasons.push(
        `${expiredMembers.length} team members have expired assignments`
      )
    }

    // Check budget status (warning, not blocking)
    if (project.budget.isOverBudget()) {
      reasons.push("Project is over budget")
    }

    // Check if project has minimum required duration
    if (project.startDate && project.endDate) {
      const duration =
        new Date(project.endDate).getTime() -
        new Date(project.startDate).getTime()
      const minDurationDays = 1
      if (duration < minDurationDays * 24 * 60 * 60 * 1000) {
        reasons.push("Project duration is too short for completion")
      }
    }

    return {
      valid: reasons.length === 0,
      reasons,
    }
  }

  /**
   * Validates if project can be activated
   */
  private async validateProjectActivation(
    project: Project
  ): Promise<{ valid: boolean; reasons: string[] }> {
    const reasons: string[] = []

    // Check if project has minimum required information
    if (!project.description || project.description.trim().length === 0) {
      reasons.push("Project description is required for activation")
    }

    // Check if project has at least one team member
    if (project.members.length === 0) {
      reasons.push("At least one team member is required for activation")
    }

    // Check if project has budget information for non-internal projects
    if (project.clientId && project.budget.totalAmount === 0) {
      reasons.push("Budget information is required for client projects")
    }

    return {
      valid: reasons.length === 0,
      reasons,
    }
  }
}
