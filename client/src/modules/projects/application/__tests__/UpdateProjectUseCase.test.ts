/**
 * Update Project Use Case Tests
 *
 * Comprehensive tests for project update orchestration including validation,
 * permission checks, and business rule enforcement.
 */

import { beforeEach, describe, expect, it, Mock, vi } from "vitest"
import { ProjectValidationService } from "../../domain/domain-services/ProjectValidationService"
import { Project } from "../../domain/entities/Project"
import { ProjectMember } from "../../domain/entities/ProjectMember"
import { IProjectRepository } from "../../domain/repositories/IProjectRepository"
import { TeamRole } from "../../domain/value-objects/TeamRole"
import { UpdateProjectUseCase } from "../services/UpdateProjectUseCase"
import {
  isSuccessResult,
  ProjectBusinessRuleError,
  ProjectPermissionError,
  ProjectResourceNotFoundError,
  ProjectValidationError,
} from "../types/ApplicationErrors"
import { UpdateProjectRequest } from "../types/ProjectOperationTypes"

// Mock dependencies
const mockProjectRepository = {
  findById: vi.fn(),
  save: vi.fn(),
} as unknown as IProjectRepository

const mockValidationService = {
  validateProjectUpdate: vi.fn(),
} as unknown as ProjectValidationService

describe("UpdateProjectUseCase", () => {
  let useCase: UpdateProjectUseCase
  let mockFindById: Mock
  let mockSave: Mock
  let mockValidateProjectUpdate: Mock
  let existingProject: Project

  beforeEach(() => {
    vi.clearAllMocks()

    mockFindById = mockProjectRepository.findById as Mock
    mockSave = mockProjectRepository.save as Mock
    mockValidateProjectUpdate =
      mockValidationService.validateProjectUpdate as Mock

    useCase = new UpdateProjectUseCase(
      mockProjectRepository,
      mockValidationService
    )

    // Create a test project
    existingProject = Project.create({
      id: "test-project-id",
      name: "Original Project Name",
      description: "Original description",
      ownerId: 1,
      clientId: 100,
      priority: "Medium",
    })

    // Default mock implementations
    mockFindById.mockResolvedValue(existingProject)
    mockSave.mockResolvedValue(undefined)
    mockValidateProjectUpdate.mockResolvedValue({
      isValid: true,
      violations: [],
      warnings: [],
    })
  })

  describe("Input Validation", () => {
    it("should reject empty project ID", async () => {
      const request: UpdateProjectRequest = {
        projectId: "",
        userId: 1,
        updates: { name: "New Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain("Project ID is required")
      }
    })

    it("should reject invalid user ID", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 0,
        updates: { name: "New Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain("Valid user ID is required")
      }
    })

    it("should reject empty updates object", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: {},
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain(
          "At least one field must be updated"
        )
      }
    })

    it("should reject empty project name", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: { name: "" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain("Project name cannot be empty")
      }
    })

    it("should reject invalid budget amounts", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: {
          budgetData: {
            totalAmount: -500,
            spentAmount: -100,
          },
        },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain(
          "Budget total amount must be positive"
        )
      }
    })

    it("should reject invalid date range", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: {
          startDate: "2024-12-31",
          endDate: "2024-01-01",
        },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain(
          "End date must be after start date"
        )
      }
    })
  })

  describe("Project Not Found", () => {
    it("should return error when project does not exist", async () => {
      mockFindById.mockResolvedValue(null)

      const request: UpdateProjectRequest = {
        projectId: "non-existent-id",
        userId: 1,
        updates: { name: "New Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectResourceNotFoundError)
        expect(result.error.message).toContain(
          "Project with ID non-existent-id not found"
        )
      }
    })
  })

  describe("Permission Checks", () => {
    it("should allow project owner to update", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1, // Same as owner
        updates: { name: "Updated Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
    })

    it("should allow team member with management privileges to update", async () => {
      const managerRole = TeamRole.create({ role: "Project Manager" })
      // Test member creation removed as it was unused

      // Mock the role's permission check
      vi.spyOn(managerRole, "canManageProject").mockReturnValue(true)

      const projectWithManager = existingProject.addTeamMember(
        2,
        managerRole,
        1
      )
      mockFindById.mockResolvedValue(projectWithManager)

      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 2, // Manager user
        updates: { name: "Updated Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
    })

    it("should reject update from unauthorized user", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 999, // Different user
        updates: { name: "Updated Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectPermissionError)
        expect(result.error.message).toContain("update project")
      }
    })

    it("should reject update from regular team member without management privileges", async () => {
      const memberRole = TeamRole.create({ role: "Electrical Engineer" })
      // Test member creation removed as it was unused

      // Mock the role's permission check
      vi.spyOn(memberRole, "canManageProject").mockReturnValue(false)

      const projectWithMember = existingProject.addTeamMember(3, memberRole, 1)
      mockFindById.mockResolvedValue(projectWithMember)

      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 3, // Regular member
        updates: { name: "Updated Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectPermissionError)
      }
    })
  })

  describe("Business Rule Validation", () => {
    it("should reject update when business rules are violated", async () => {
      mockValidateProjectUpdate.mockResolvedValue({
        isValid: false,
        violations: ["Cannot update project name while project is completed"],
        warnings: [],
      })

      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: { name: "New Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectBusinessRuleError)
        expect(result.error.message).toContain(
          "Cannot update project name while project is completed"
        )
      }
    })

    it("should proceed with warnings but no violations", async () => {
      mockValidateProjectUpdate.mockResolvedValue({
        isValid: true,
        violations: [],
        warnings: ["Consider updating project timeline"],
      })

      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: { name: "New Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.validationWarnings).toContain(
          "Consider updating project timeline"
        )
      }
    })
  })

  describe("Successful Updates", () => {
    it("should update basic project information", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: {
          name: "Updated Project Name",
          description: "Updated description",
          priority: "High",
        },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.project.name).toBe("Updated Project Name")
        expect(result.data.project.description).toBe("Updated description")
        expect(result.data.project.priority).toBe("High")
        expect(result.data.updatedFields).toEqual([
          "name",
          "description",
          "priority",
        ])
      }

      expect(mockSave).toHaveBeenCalledWith(expect.any(Project))
    })

    it("should update budget information", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: {
          budgetData: {
            totalAmount: 75000,
            spentAmount: 25000,
            currency: "USD",
            allocatedAmount: 50000,
          },
        },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.project.budget.totalAmount).toBe(75000)
        expect(result.data.project.budget.spentAmount).toBe(25000)
        expect(result.data.project.budget.currency).toBe("USD")
        expect(result.data.updatedFields).toEqual([
          "budgetTotal",
          "budgetSpent",
          "budgetCurrency",
          "budgetAllocated",
        ])
      }
    })

    it("should update timeline information", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: {
          startDate: "2024-02-01T00:00:00Z",
          endDate: "2024-11-30T23:59:59Z",
        },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.project.startDate).toBe("2024-02-01T00:00:00Z")
        expect(result.data.project.endDate).toBe("2024-11-30T23:59:59Z")
        expect(result.data.updatedFields).toEqual(["startDate", "endDate"])
      }
    })

    it("should update tags and location", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: {
          tags: ["updated", "electrical", "design"],
          location: "Tampere, Finland",
        },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.project.tags).toEqual([
          "updated",
          "electrical",
          "design",
        ])
        expect(result.data.project.location).toBe("Tampere, Finland")
        expect(result.data.updatedFields).toEqual(["tags", "location"])
      }
    })

    it("should update approval requirement", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: {
          requiresApproval: false, // Test removing requirement  
        },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        // requiresApproval is computed from status and budget, not a settable property
        // So we just verify the field was tracked, not the actual state change
        expect(result.data.updatedFields).toEqual(["requiresApproval"])
      }
    })

    it("should only update changed fields", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: {
          name: "Original Project Name", // Same as existing
          description: "New description", // Different from existing
          priority: "Medium", // Same as existing
        },
      }

      // Mock the project to return specific values for comparison
      const projectWithDescription = Project.create({
        id: "test-project-id",
        name: "Original Project Name",
        description: "Original description",
        ownerId: 1,
        clientId: 100,
        priority: "Medium",
      })
      mockFindById.mockResolvedValue(projectWithDescription)

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.updatedFields).toEqual(["description"])
      }
    })

    it("should trim whitespace from string updates", async () => {
      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: {
          name: "  Trimmed Name  ",
          description: "  Trimmed Description  ",
          location: "  Trimmed Location  ",
        },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.project.name).toBe("Trimmed Name")
        expect(result.data.project.description).toBe("Trimmed Description")
        expect(result.data.project.location).toBe("Trimmed Location")
      }
    })
  })

  describe("Error Handling", () => {
    it("should handle repository errors", async () => {
      mockSave.mockRejectedValue(new Error("Database connection failed"))

      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: { name: "New Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error.message).toContain(
          "Use case UpdateProjectUseCase execution failed"
        )
      }
    })

    it("should handle domain method errors", async () => {
      // Mock a project method to throw an error  
      vi.spyOn(existingProject, "updateProjectInfo").mockImplementation(() => {
        throw new Error("Domain validation failed")
      })
      mockFindById.mockResolvedValue(existingProject)

      const request: UpdateProjectRequest = {
        projectId: "test-project-id",
        userId: 1,
        updates: { name: "New Name" },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectBusinessRuleError)
        expect(result.error.message).toContain("Failed to apply updates")
      }
    })
  })
})
