/**
 * Create Project Use Case Tests
 *
 * Comprehensive tests for project creation orchestration including validation,
 * business rule enforcement, and error handling.
 */

import { beforeEach, describe, expect, it, Mock, vi } from "vitest"
import { ProjectValidationService } from "../../domain/domain-services/ProjectValidationService"
import { Project } from "../../domain/entities/Project"
import { IProjectRepository } from "../../domain/repositories/IProjectRepository"
import { CreateProjectUseCase } from "../services/CreateProjectUseCase"
import {
  isSuccessResult,
  ProjectBusinessRuleError,
  ProjectValidationError,
} from "../types/ApplicationErrors"
import { CreateProjectRequest } from "../types/ProjectOperationTypes"

// Mock dependencies
const mockProjectRepository = {
  save: vi.fn(),
  findByCriteria: vi.fn(),
} as unknown as IProjectRepository

const mockValidationService = {
  validateProjectCreation: vi.fn(),
} as unknown as ProjectValidationService

describe("CreateProjectUseCase", () => {
  let useCase: CreateProjectUseCase
  let mockSave: Mock
  let mockFindByCriteria: Mock
  let mockValidateProjectCreation: Mock

  beforeEach(() => {
    vi.clearAllMocks()

    mockSave = mockProjectRepository.save as Mock
    mockFindByCriteria = mockProjectRepository.findByCriteria as Mock
    mockValidateProjectCreation =
      mockValidationService.validateProjectCreation as Mock

    useCase = new CreateProjectUseCase(
      mockProjectRepository,
      mockValidationService
    )

    // Default mock implementations
    mockSave.mockResolvedValue(undefined)
    mockFindByCriteria.mockResolvedValue({
      projects: [],
      totalCount: 0,
      hasMore: false,
    })
    mockValidateProjectCreation.mockResolvedValue(undefined) // Now returns void or throws
  })

  describe("Input Validation", () => {
    it("should reject empty project name", async () => {
      const request: CreateProjectRequest = {
        name: "",
        ownerId: 1,
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain("Project name is required")
      }
    })

    it("should reject project name that is too long", async () => {
      const request: CreateProjectRequest = {
        name: "a".repeat(201),
        ownerId: 1,
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain("cannot exceed 200 characters")
      }
    })

    it("should reject invalid owner ID", async () => {
      const request: CreateProjectRequest = {
        name: "Valid Project Name",
        ownerId: 0,
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain("Valid owner ID is required")
      }
    })

    it("should reject invalid budget data", async () => {
      const request: CreateProjectRequest = {
        name: "Valid Project Name",
        ownerId: 1,
        budgetData: {
          totalAmount: -100,
          currency: "EUR",
        },
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain(
          "Budget total amount must be positive"
        )
      }
    })

    it("should reject invalid date range", async () => {
      const request: CreateProjectRequest = {
        name: "Valid Project Name",
        ownerId: 1,
        startDate: "2024-12-31",
        endDate: "2024-01-01",
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain(
          "End date must be after start date"
        )
      }
    })

    it("should reject too many tags", async () => {
      const request: CreateProjectRequest = {
        name: "Valid Project Name",
        ownerId: 1,
        tags: Array(11).fill("tag"),
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectValidationError)
        expect(result.error.message).toContain("Maximum 10 tags allowed")
      }
    })
  })

  describe("Business Rule Validation", () => {
    it("should reject project creation when business rules are violated", async () => {
      const validationError = new Error("Project name must be unique within client")
      mockValidateProjectCreation.mockRejectedValue(validationError)

      const request: CreateProjectRequest = {
        name: "Valid Project Name",
        ownerId: 1,
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectBusinessRuleError)
        expect(result.error.message).toContain(
          "Project name must be unique within client"
        )
      }
    })

    it("should proceed with warnings but no violations", async () => {
      mockValidateProjectCreation.mockResolvedValue(undefined) // No exception = validation passes

      const request: CreateProjectRequest = {
        name: "Valid Project Name",
        ownerId: 1,
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.validationWarnings).toEqual([]) // No warnings currently implemented
      }
    })
  })

  describe("Duplicate Detection", () => {
    it("should reject project with existing external ID", async () => {
      const existingProject = Project.create({
        id: "existing-id",
        name: "Existing Project",
        ownerId: 1,
        externalProjectId: "EXT-123",
      })

      mockFindByCriteria.mockResolvedValue({
        projects: [existingProject],
        totalCount: 1,
        hasMore: false,
      })

      const request: CreateProjectRequest = {
        name: "New Project Name",
        ownerId: 1,
        externalProjectId: "EXT-123",
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectBusinessRuleError)
        expect(result.error.message).toContain(
          "External project ID EXT-123 already exists"
        )
      }
    })

    it("should reject project with same name and client", async () => {
      const existingProject = Project.create({
        id: "existing-id",
        name: "Duplicate Name",
        ownerId: 1,
        clientId: 100,
      })

      // Only one call for client check (no external ID in request)
      mockFindByCriteria.mockResolvedValueOnce({
        projects: [existingProject],
        totalCount: 1,
        hasMore: false,
      })

      const request: CreateProjectRequest = {
        name: "Duplicate Name",
        ownerId: 1,
        clientId: 100,
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectBusinessRuleError)
        expect(result.error.message).toContain(
          'Project with name "Duplicate Name" already exists for this client'
        )
      }
    })

    it("should allow duplicate names for different clients", async () => {
      mockFindByCriteria.mockResolvedValue({
        projects: [],
        totalCount: 0,
        hasMore: false,
      })

      const request: CreateProjectRequest = {
        name: "Duplicate Name",
        ownerId: 1,
        clientId: 200, // Different client
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
    })
  })

  describe("Successful Project Creation", () => {
    it("should create project with minimal data", async () => {
      const request: CreateProjectRequest = {
        name: "New Project",
        ownerId: 1,
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.project.name).toBe("New Project")
        expect(result.data.project.ownerId).toBe(1)
        expect(result.data.project.priority).toBe("Medium") // Default priority
      }

      expect(mockSave).toHaveBeenCalledWith(expect.any(Project))
    })

    it("should create project with complete data", async () => {
      // Ensure external ID check returns empty, then client check returns empty
      mockFindByCriteria
        .mockResolvedValueOnce({ projects: [], totalCount: 0, hasMore: false }) // External ID check
        .mockResolvedValueOnce({ projects: [], totalCount: 0, hasMore: false }) // Client check

      const request: CreateProjectRequest = {
        name: "Complete Project",
        description: "A complete project with all fields",
        ownerId: 1,
        clientId: 100,
        priority: "High",
        budgetData: {
          totalAmount: 50000,
          currency: "EUR",
        },
        startDate: "2024-01-01T00:00:00Z",
        endDate: "2024-12-31T23:59:59Z",
        tags: ["electrical", "design"],
        location: "Helsinki, Finland",
        externalProjectId: "EXT-456",
        requiresApproval: true,
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        const project = result.data.project
        expect(project.name).toBe("Complete Project")
        expect(project.description).toBe("A complete project with all fields")
        expect(project.ownerId).toBe(1)
        expect(project.clientId).toBe(100)
        expect(project.priority).toBe("High")
        expect(project.budget.totalAmount).toBe(50000)
        expect(project.budget.currency).toBe("EUR")
        expect(project.startDate).toBe("2024-01-01T00:00:00Z")
        expect(project.endDate).toBe("2024-12-31T23:59:59Z")
        expect(project.tags).toEqual(["electrical", "design"])
        expect(project.location).toBe("Helsinki, Finland")
        expect(project.externalProjectId).toBe("EXT-456")
        expect(project.requiresApproval()).toBe(false) // Draft status doesn't require approval
      }
    })

    it("should trim whitespace from string fields", async () => {
      const request: CreateProjectRequest = {
        name: "  Trimmed Project  ",
        description: "  Description with spaces  ",
        ownerId: 1,
        location: "  Location  ",
        tags: ["  tag1  ", "  tag2  "],
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        const project = result.data.project
        expect(project.name).toBe("Trimmed Project")
        expect(project.description).toBe("Description with spaces")
        expect(project.location).toBe("Location")
        expect(project.tags).toEqual(["tag1", "tag2"])
      }
    })

    it("should filter out empty tags", async () => {
      // This test doesn't have externalProjectId or clientId, so no duplicate checks should be called
      
      const request: CreateProjectRequest = {
        name: "Project with Tags",
        ownerId: 1,
        tags: [
          "valid-tag",
          "",
          "  ",
          "another-valid-tag",
          null as any,
          undefined as any,
        ],
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.project.tags).toEqual([
          "valid-tag",
          "another-valid-tag",
        ])
      }
    })
  })

  describe("Error Handling", () => {
    it("should handle repository save errors", async () => {
      mockSave.mockRejectedValue(new Error("Database connection failed"))

      const request: CreateProjectRequest = {
        name: "Valid Project Name",
        ownerId: 1,
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error.message).toContain(
          "Use case CreateProjectUseCase execution failed"
        )
      }
    })

    it("should handle validation service errors", async () => {
      mockValidateProjectCreation.mockRejectedValue(
        new Error("Validation service unavailable")
      )

      const request: CreateProjectRequest = {
        name: "Valid Project Name",
        ownerId: 1,
      }

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      if (!isSuccessResult(result)) {
        expect(result.error).toBeInstanceOf(ProjectBusinessRuleError)
        expect(result.error.message).toContain("Validation service unavailable")
      }
    })

    it("should handle duplicate check failures gracefully", async () => {
      mockFindByCriteria.mockRejectedValue(
        new Error("Search service unavailable")
      )

      const request: CreateProjectRequest = {
        name: "Valid Project Name",
        ownerId: 1,
        externalProjectId: "EXT-789",
      }

      // Should not block creation even if duplicate check fails
      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(true)
      if (isSuccessResult(result)) {
        expect(result.data.project.name).toBe("Valid Project Name")
      }
    })
  })

  describe("Data Sanitization", () => {
    it("should sanitize request data for logging", async () => {
      const request: CreateProjectRequest = {
        name: "Project with Sensitive Data",
        ownerId: 1,
        budgetData: {
          totalAmount: 100000,
          currency: "EUR",
        },
      }

      // Force an error to trigger logging
      mockSave.mockRejectedValue(new Error("Test error"))

      const result = await useCase.execute(request)

      expect(isSuccessResult(result)).toBe(false)
      // The sanitized data should not contain sensitive information
      // This would be verified through logging in a real scenario
    })
  })
})
