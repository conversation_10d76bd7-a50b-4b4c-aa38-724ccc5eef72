/**
 * Member Card Molecule
 *
 * Reusable team member card with status, role, and action display.
 * Used in team management and member lists.
 */

import React from "react"

import {
  AlertTriangle,
  Calendar,
  Clock,
  Crown,
  Timer,
  User,
} from "lucide-react"

import { cn } from "@/lib/utils"

import { Badge } from "@/components/ui/badge"
// import { Button } from "@/components/ui/button" // Unused

import { ProjectMember } from "../../domain/entities/ProjectMember"
import { ActionButton } from "../atoms"

export interface MemberCardProps {
  member: ProjectMember
  currentUserId?: number
  showActions?: boolean
  onActionClick?: (action: string, member: ProjectMember) => void
  compact?: boolean
  className?: string
}

// Member Status Badge Component
const MemberStatusBadge: React.FC<{
  member: ProjectMember
  size?: "sm" | "md"
}> = ({ member, size = "sm" }) => {
  if (!member.isActive) {
    return (
      <Badge variant="secondary" className={cn(size === "sm" && "text-xs")}>
        Inactive
      </Badge>
    )
  }

  if (member.hasExpired()) {
    return (
      <Badge
        variant="destructive"
        className={cn(size === "sm" && "text-xs", "flex items-center gap-1")}
      >
        <AlertTriangle className="h-3 w-3" />
        Expired
      </Badge>
    )
  }

  return (
    <Badge
      variant="default"
      className={cn(size === "sm" && "text-xs", "flex items-center gap-1")}
    >
      Active
    </Badge>
  )
}

// Role Badge Component
const RoleBadge: React.FC<{
  role: string
  canManageProject: boolean
  size?: "sm" | "md"
}> = ({ role, canManageProject, size = "sm" }) => {
  const getRoleBadgeVariant = (role: string) => {
    if (
      role.toLowerCase().includes("manager") ||
      role.toLowerCase().includes("lead")
    ) {
      return "default"
    }
    if (
      role.toLowerCase().includes("senior") ||
      role.toLowerCase().includes("architect")
    ) {
      return "secondary"
    }
    return "outline"
  }

  return (
    <Badge
      variant={getRoleBadgeVariant(role)}
      className={cn(size === "sm" && "text-xs")}
    >
      {canManageProject && <Crown className="mr-1 h-3 w-3" />}
      {role}
    </Badge>
  )
}

// Member Info Component
const MemberInfo: React.FC<{
  member: ProjectMember
  currentUserId?: number
  compact?: boolean
}> = ({ member, currentUserId, compact }) => (
  <div className="flex items-center gap-3">
    <div className="flex-shrink-0">
      <div className="bg-muted flex h-8 w-8 items-center justify-center rounded-full">
        <User className="text-muted-foreground h-4 w-4" />
      </div>
    </div>

    <div className="min-w-0 flex-1">
      <div className="flex items-center gap-2">
        <div className="text-sm font-medium">User ID: {member.userId}</div>
        {member.userId === currentUserId && (
          <Badge variant="outline" className="text-xs">
            You
          </Badge>
        )}
      </div>

      {!compact && (
        <div className="text-muted-foreground text-xs">
          Member ID: {member.id}
        </div>
      )}
    </div>
  </div>
)

// Assignment Details Component
const AssignmentDetails: React.FC<{
  member: ProjectMember
  compact?: boolean
}> = ({ member, compact }) => (
  <div className="space-y-2">
    <div className="text-muted-foreground flex items-center gap-4 text-xs">
      <div className="flex items-center gap-1">
        <Calendar className="h-3 w-3" />
        <span>Assigned {new Date(member.assignedAt).toLocaleDateString()}</span>
      </div>

      {member.expiresAt && (
        <div className="flex items-center gap-1">
          <Timer className="h-3 w-3" />
          <span>
            Expires {new Date(member.expiresAt).toLocaleDateString()}
            {member.hasExpired() && (
              <Badge variant="destructive" className="ml-1 text-xs">
                Expired
              </Badge>
            )}
          </span>
        </div>
      )}
    </div>

    {!compact && (
      <div className="text-muted-foreground text-xs">
        Assigned by User {member.assignedBy}
      </div>
    )}
  </div>
)

export const MemberCard: React.FC<MemberCardProps> = ({
  member,
  currentUserId,
  showActions = true,
  onActionClick,
  compact = false,
  className,
}) => {
  const handleActionClick = (action: string) => {
    onActionClick?.(action, member)
  }

  return (
    <div
      className={cn(
        "space-y-3 rounded-lg border p-4",
        "hover:bg-muted/50 transition-colors",
        member.hasExpired() && "border-destructive/30 bg-destructive/5",
        className
      )}
    >
      {/* Header with Member Info and Actions */}
      <div className="flex items-start justify-between">
        <MemberInfo
          member={member}
          currentUserId={currentUserId}
          compact={compact}
        />

        {showActions && onActionClick && (
          <ActionButton
            action="more"
            size="sm"
            onClick={() => handleActionClick("menu")}
            data-testid={`member-actions-${member.id}`}
          />
        )}
      </div>

      {/* Role and Status */}
      <div className="flex flex-wrap items-center gap-2">
        <RoleBadge
          role={member.role.role}
          canManageProject={member.role.canManageProject()}
          size={compact ? "sm" : "md"}
        />

        <MemberStatusBadge member={member} size={compact ? "sm" : "md"} />
      </div>

      {/* Assignment Details */}
      {!compact && <AssignmentDetails member={member} compact={compact} />}

      {/* Expired Warning */}
      {member.hasExpired() && (
        <div className="text-destructive bg-destructive/10 flex items-center gap-2 rounded p-2 text-xs">
          <Clock className="h-3 w-3" />
          <span>
            This member&apos;s assignment has expired and may need attention.
          </span>
        </div>
      )}
    </div>
  )
}

// List variant for table-like display
export const MemberCardCompact: React.FC<MemberCardProps> = (props) => (
  <MemberCard {...props} compact={true} className="p-2" />
)

// Grid layout for multiple member cards
export const MemberGrid: React.FC<{
  members: ProjectMember[]
  currentUserId?: number
  onActionClick?: (action: string, member: ProjectMember) => void
  loading?: boolean
  emptyState?: React.ReactNode
}> = ({ members, currentUserId, onActionClick, loading, emptyState }) => {
  if (loading) {
    return (
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="animate-pulse rounded-lg border p-4">
            <div className="flex items-center gap-3">
              <div className="bg-muted h-8 w-8 rounded-full" />
              <div className="flex-1 space-y-2">
                <div className="bg-muted h-4 rounded" />
                <div className="bg-muted h-3 w-3/4 rounded" />
              </div>
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (members.length === 0 && emptyState) {
    return <>{emptyState}</>
  }

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {members.map((member) => (
        <MemberCard
          key={member.id}
          member={member}
          currentUserId={currentUserId}
          onActionClick={onActionClick}
        />
      ))}
    </div>
  )
}
