"use client"

/**
 * Member Form Molecule
 *
 * Reusable form for adding and editing team members.
 * Used in team management dialogs and member operations.
 */
import React, { useState } from "react"

import { Loader2, Plus, X } from "lucide-react"

import { cn } from "@/lib/utils"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Textarea } from "@/components/ui/textarea"

import { FormInput } from "../atoms"

export interface MemberFormData {
  userId: number
  role: string
  reason?: string
  priority?: "Low" | "Medium" | "High"
  estimatedWorkload?: number
  skillsRequired?: string[]
  expiresAt?: string
  requiresApproval?: boolean
}

export interface MemberFormProps {
  initialData?: Partial<MemberFormData>
  onSubmit: (data: MemberFormData) => Promise<void>
  onCancel?: () => void
  loading?: boolean
  submitLabel?: string
  title?: string
  description?: string
  className?: string
}

const defaultFormData: MemberFormData = {
  userId: 0,
  role: "Developer",
  priority: "Medium",
  estimatedWorkload: 100,
  skillsRequired: [],
  requiresApproval: false,
}

const availableRoles = [
  "Project Manager",
  "Lead Developer",
  "Senior Developer",
  "Developer",
  "Designer",
  "QA Engineer",
  "Business Analyst",
  "Architect",
  "Consultant",
]

// Skills Input Component
const SkillsInput: React.FC<{
  skills: string[]
  onSkillAdd: (skill: string) => void
  onSkillRemove: (skill: string) => void
  disabled?: boolean
}> = ({ skills, onSkillAdd, onSkillRemove, disabled }) => {
  const [skillInput, setSkillInput] = useState("")

  const addSkill = () => {
    if (skillInput.trim() && !skills.includes(skillInput.trim())) {
      onSkillAdd(skillInput.trim())
      setSkillInput("")
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault()
      addSkill()
    }
  }

  return (
    <div className="space-y-2">
      <Label>Required Skills</Label>

      <div className="flex gap-2">
        <Input
          placeholder="Add skill"
          value={skillInput}
          onChange={(e) => setSkillInput(e.target.value)}
          onKeyPress={handleKeyPress}
          disabled={disabled}
        />
        <Button
          type="button"
          onClick={addSkill}
          variant="outline"
          disabled={disabled || !skillInput.trim()}
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>

      <div className="flex flex-wrap gap-2">
        {skills.map((skill) => (
          <Badge key={skill} variant="secondary" className="cursor-pointer">
            {skill}
            <button
              type="button"
              onClick={() => onSkillRemove(skill)}
              className="hover:text-destructive ml-1 text-xs"
              disabled={disabled}
            >
              <X className="h-2 w-2" />
            </button>
          </Badge>
        ))}
      </div>
    </div>
  )
}

export const MemberForm: React.FC<MemberFormProps> = ({
  initialData = {},
  onSubmit,
  onCancel,
  loading = false,
  submitLabel = "Add Member",
  title = "Add Team Member",
  description = "Assign a new member to this project with specific role and requirements.",
  className,
}) => {
  const [formData, setFormData] = useState<MemberFormData>({
    ...defaultFormData,
    ...initialData,
  })

  const [errors, setErrors] = useState<
    Partial<Record<keyof MemberFormData, string>>
  >({})

  const updateField = <K extends keyof MemberFormData>(
    field: K,
    value: MemberFormData[K]
  ) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    // Clear error when field is updated
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }))
    }
  }

  const addSkill = (skill: string) => {
    const newSkills = [...(formData.skillsRequired || []), skill]
    updateField("skillsRequired", newSkills)
  }

  const removeSkill = (skillToRemove: string) => {
    const newSkills = (formData.skillsRequired || []).filter(
      (skill) => skill !== skillToRemove
    )
    updateField("skillsRequired", newSkills)
  }

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof MemberFormData, string>> = {}

    if (!formData.userId || formData.userId <= 0) {
      newErrors.userId = "User ID is required and must be positive"
    }

    if (!formData.role) {
      newErrors.role = "Role is required"
    }

    if (
      formData.estimatedWorkload &&
      (formData.estimatedWorkload < 0 || formData.estimatedWorkload > 100)
    ) {
      newErrors.estimatedWorkload = "Workload must be between 0 and 100%"
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm() || loading) return

    try {
      await onSubmit(formData)
    } catch (error) {
      // Handle submission errors
      console.error("Form submission error:", error)
    }
  }

  const isValid = formData.userId > 0 && formData.role && !loading

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div>
        <h3 className="text-lg font-semibold">{title}</h3>
        {description && (
          <p className="text-muted-foreground mt-1 text-sm">{description}</p>
        )}
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        {/* Basic Information */}
        <div className="grid grid-cols-2 gap-4">
          <FormInput
            label="User ID"
            type="number"
            placeholder="Enter user ID"
            value={formData.userId || ""}
            onChange={(e) =>
              updateField("userId", parseInt(e.target.value) || 0)
            }
            error={errors.userId}
            required
            disabled={loading}
            data-testid="member-user-id-input"
          />

          <div>
            <Label htmlFor="role">Role *</Label>
            <Select
              value={formData.role}
              onValueChange={(value) => updateField("role", value)}
              disabled={loading}
            >
              <SelectTrigger data-testid="member-role-select">
                <SelectValue placeholder="Select role" />
              </SelectTrigger>
              <SelectContent>
                {availableRoles.map((role) => (
                  <SelectItem key={role} value={role}>
                    {role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.role && (
              <p className="text-destructive mt-1 text-sm">{errors.role}</p>
            )}
          </div>
        </div>

        {/* Assignment Details */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <Label htmlFor="priority">Priority</Label>
            <Select
              value={formData.priority}
              onValueChange={(value: "Low" | "Medium" | "High") =>
                updateField("priority", value)
              }
              disabled={loading}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Low">Low</SelectItem>
                <SelectItem value="Medium">Medium</SelectItem>
                <SelectItem value="High">High</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <FormInput
            label="Estimated Workload (%)"
            type="number"
            min="0"
            max="100"
            value={formData.estimatedWorkload || ""}
            onChange={(e) =>
              updateField("estimatedWorkload", parseInt(e.target.value) || 0)
            }
            error={errors.estimatedWorkload}
            disabled={loading}
          />
        </div>

        {/* Skills */}
        <SkillsInput
          skills={formData.skillsRequired || []}
          onSkillAdd={addSkill}
          onSkillRemove={removeSkill}
          disabled={loading}
        />

        {/* Optional Fields */}
        <FormInput
          label="Expiration Date (Optional)"
          type="date"
          value={formData.expiresAt || ""}
          onChange={(e) =>
            updateField("expiresAt", e.target.value || undefined)
          }
          min={new Date().toISOString().split("T")[0]}
          disabled={loading}
        />

        <div>
          <Label htmlFor="reason">Assignment Reason</Label>
          <Textarea
            id="reason"
            placeholder="Why is this member being assigned to the project?"
            value={formData.reason || ""}
            onChange={(e) => updateField("reason", e.target.value)}
            disabled={loading}
            data-testid="member-reason-input"
          />
        </div>

        <div className="flex items-center space-x-2">
          <Switch
            id="requiresApproval"
            checked={formData.requiresApproval}
            onCheckedChange={(checked) =>
              updateField("requiresApproval", checked)
            }
            disabled={loading}
          />
          <Label htmlFor="requiresApproval">Requires approval</Label>
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-4">
          {onCancel && (
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={loading}
            >
              Cancel
            </Button>
          )}

          <Button
            type="submit"
            disabled={!isValid}
            data-testid="assign-member-button"
          >
            {loading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {submitLabel}
          </Button>
        </div>
      </form>
    </div>
  )
}
