/**
 * Filter Panel Molecule
 *
 * Reusable filter panel with status, priority, and owner filters.
 * Used in project lists and other filterable data views.
 */

import React from "react"

import { Filter, X } from "lucide-react"

import { cn } from "@/lib/utils"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

import { PriorityBadge, StatusBadge } from "../atoms"

export interface FilterOption {
  value: string
  label: string
  count?: number
}

export interface ActiveFilters {
  status?: string[]
  priority?: string[]
  ownerId?: number
  [key: string]: any
}

export interface FilterPanelProps {
  activeFilters: ActiveFilters
  onFilterChange: (key: string, value: any) => void
  onClearFilters: () => void
  currentUserId?: number
  className?: string
  compact?: boolean
}

const statusOptions: FilterOption[] = [
  { value: "Draft", label: "Draft" },
  { value: "Active", label: "Active" },
  { value: "Paused", label: "Paused" },
  { value: "Completed", label: "Completed" },
  { value: "Cancelled", label: "Cancelled" },
]

const priorityOptions: FilterOption[] = [
  { value: "Low", label: "Low" },
  { value: "Medium", label: "Medium" },
  { value: "High", label: "High" },
  { value: "Critical", label: "Critical" },
]

export const FilterPanel: React.FC<FilterPanelProps> = ({
  activeFilters,
  onFilterChange,
  onClearFilters,
  currentUserId,
  className,
  compact = false,
}) => {
  const hasActiveFilters = Object.values(activeFilters).some((value) =>
    Array.isArray(value) ? value.length > 0 : value !== undefined
  )

  const gridColumns = compact ? "grid-cols-2" : "grid-cols-1 md:grid-cols-4"

  return (
    <div className={cn("bg-card space-y-4 rounded-lg border p-4", className)}>
      {/* Filter Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="text-muted-foreground h-4 w-4" />
          <span className="font-medium">Filters</span>
          {hasActiveFilters && (
            <Badge variant="secondary" className="text-xs">
              Active
            </Badge>
          )}
        </div>

        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="text-muted-foreground hover:text-foreground h-8 px-2"
          >
            <X className="mr-1 h-3 w-3" />
            Clear All
          </Button>
        )}
      </div>

      {/* Filter Controls */}
      <div className={cn("grid gap-4", gridColumns)}>
        {/* Status Filter */}
        <div>
          <label className="mb-2 block text-sm font-medium">Status</label>
          <Select
            value={activeFilters.status?.[0] || ""}
            onValueChange={(value) =>
              onFilterChange("status", value ? [value] : undefined)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="All statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All statuses</SelectItem>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-2">
                    <StatusBadge
                      status={option.value as any}
                      size="sm"
                      showIcon={false}
                    />
                    <span>{option.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Priority Filter */}
        <div>
          <label className="mb-2 block text-sm font-medium">Priority</label>
          <Select
            value={activeFilters.priority?.[0] || ""}
            onValueChange={(value) =>
              onFilterChange("priority", value ? [value] : undefined)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="All priorities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All priorities</SelectItem>
              {priorityOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  <div className="flex items-center gap-2">
                    <PriorityBadge
                      priority={option.value as any}
                      size="sm"
                      showIcon={false}
                    />
                    <span>{option.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Owner Filter */}
        <div>
          <label className="mb-2 block text-sm font-medium">Owner</label>
          <Select
            value={activeFilters.ownerId?.toString() || ""}
            onValueChange={(value) =>
              onFilterChange("ownerId", value ? parseInt(value) : undefined)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="All owners" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">All owners</SelectItem>
              {currentUserId && (
                <SelectItem value={currentUserId.toString()}>
                  My Projects
                </SelectItem>
              )}
            </SelectContent>
          </Select>
        </div>

        {/* Clear Button Column */}
        {!compact && (
          <div className="flex items-end">
            <Button
              variant="outline"
              onClick={onClearFilters}
              size="sm"
              disabled={!hasActiveFilters}
              className="w-full"
            >
              Clear Filters
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}

// Quick filter buttons molecule
export const QuickFilters: React.FC<{
  onFilterClick: (filterKey: string) => void
  activeFilter?: string
  className?: string
}> = ({ onFilterClick, activeFilter, className }) => {
  const quickFilters = [
    { key: "my-projects", label: "My Projects" },
    { key: "active-projects", label: "Active" },
    { key: "needs-attention", label: "Needs Attention" },
    { key: "over-budget", label: "Over Budget" },
  ]

  return (
    <div className={cn("flex flex-wrap gap-2", className)}>
      {quickFilters.map((filter) => (
        <Button
          key={filter.key}
          variant={activeFilter === filter.key ? "default" : "outline"}
          size="sm"
          onClick={() => onFilterClick(filter.key)}
          className="text-xs"
        >
          {filter.label}
        </Button>
      ))}
    </div>
  )
}
