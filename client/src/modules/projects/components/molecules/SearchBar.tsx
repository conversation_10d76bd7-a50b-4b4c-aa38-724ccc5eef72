"use client"

/**
 * Search Bar Molecule
 *
 * Reusable search input with debouncing, clear functionality, and consistent styling.
 * Used across project lists, team management, and component browsing.
 */
import React, { useCallback, useEffect, useState } from "react"

import { Loader2, Search, X } from "lucide-react"

import { cn } from "@/lib/utils"

import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"

export interface SearchBarProps {
  placeholder?: string
  value: string
  onChange: (value: string) => void
  debounceMs?: number
  showClearButton?: boolean
  loading?: boolean
  disabled?: boolean
  size?: "sm" | "md" | "lg"
  className?: string
  onFocus?: () => void
  onBlur?: () => void
}

const sizeConfig = {
  sm: {
    input: "h-8 text-sm pl-8 pr-8",
    icon: "h-3 w-3",
    iconContainer: "left-2",
    clearButton: "right-2 h-4 w-4",
  },
  md: {
    input: "h-10 text-sm pl-10 pr-10",
    icon: "h-4 w-4",
    iconContainer: "left-3",
    clearButton: "right-3 h-6 w-6",
  },
  lg: {
    input: "h-12 text-base pl-12 pr-12",
    icon: "h-5 w-5",
    iconContainer: "left-4",
    clearButton: "right-4 h-8 w-8",
  },
} as const

export const SearchBar: React.FC<SearchBarProps> = ({
  placeholder = "Search...",
  value,
  onChange,
  debounceMs = 300,
  showClearButton = true,
  loading = false,
  disabled = false,
  size = "md",
  className,
  onFocus,
  onBlur,
}) => {
  const [internalValue, setInternalValue] = useState(value)
  const [isFocused, setIsFocused] = useState(false)
  const sizeStyles = sizeConfig[size]

  // Debounced search effect
  useEffect(() => {
    const timer = setTimeout(() => {
      if (internalValue !== value) {
        onChange(internalValue)
      }
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [internalValue, debounceMs, onChange, value])

  // Sync external value changes
  useEffect(() => {
    if (value !== internalValue) {
      setInternalValue(value)
    }
  }, [value, internalValue])

  const handleClear = useCallback(() => {
    setInternalValue("")
    onChange("")
  }, [onChange])

  const handleFocus = useCallback(() => {
    setIsFocused(true)
    onFocus?.()
  }, [onFocus])

  const handleBlur = useCallback(() => {
    setIsFocused(false)
    onBlur?.()
  }, [onBlur])

  const showClear = showClearButton && internalValue.length > 0 && !loading

  return (
    <div className={cn("relative", className)}>
      {/* Search Icon */}
      <div
        className={cn(
          "pointer-events-none absolute inset-y-0 flex items-center",
          sizeStyles.iconContainer
        )}
      >
        {loading ? (
          <Loader2
            className={cn(
              sizeStyles.icon,
              "text-muted-foreground animate-spin"
            )}
          />
        ) : (
          <Search className={cn(sizeStyles.icon, "text-muted-foreground")} />
        )}
      </div>

      {/* Input */}
      <Input
        type="text"
        placeholder={placeholder}
        value={internalValue}
        onChange={(e) => setInternalValue(e.target.value)}
        onFocus={handleFocus}
        onBlur={handleBlur}
        disabled={disabled}
        className={cn(
          sizeStyles.input,
          "transition-all duration-200",
          isFocused && "ring-ring ring-2 ring-offset-2",
          showClear && "pr-12"
        )}
      />

      {/* Clear Button */}
      {showClear && (
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={handleClear}
          className={cn(
            "absolute inset-y-0 p-0 hover:bg-transparent",
            sizeStyles.clearButton,
            sizeStyles.iconContainer.replace("left", "right")
          )}
        >
          <X
            className={cn(
              sizeStyles.icon,
              "text-muted-foreground hover:text-foreground"
            )}
          />
        </Button>
      )}
    </div>
  )
}

// Hook for managing search state
export const useSearchBar = (initialValue = "", debounceMs = 300) => {
  const [searchValue, setSearchValue] = useState(initialValue)
  const [debouncedValue, setDebouncedValue] = useState(initialValue)

  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedValue(searchValue)
    }, debounceMs)

    return () => clearTimeout(timer)
  }, [searchValue, debounceMs])

  const clearSearch = useCallback(() => {
    setSearchValue("")
  }, [])

  return {
    searchValue,
    debouncedValue,
    setSearchValue,
    clearSearch,
    hasValue: searchValue.length > 0,
  }
}
