"use client"

/**
 * Bulk Action Bar Molecule
 *
 * Displays selected item count and available bulk actions.
 * Used in project lists, team management, and other multi-selection interfaces.
 */
import React, { useState } from "react"

import {
  Archive,
  CheckCircle,
  CheckSquare,
  Loader2,
  Pause,
  Play,
  Trash2,
  X,
} from "lucide-react"

import { cn } from "@/lib/utils"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export interface BulkAction {
  key: string
  label: string
  icon?: React.ReactNode
  variant?: "default" | "destructive" | "outline"
  requiresConfirmation?: boolean
}

export interface BulkActionBarProps {
  selectedCount: number
  totalCount?: number
  actions: BulkAction[]
  onActionClick: (actionKey: string) => void
  onClearSelection: () => void
  loading?: boolean
  disabled?: boolean
  className?: string
}

const defaultActions: BulkAction[] = [
  {
    key: "archive",
    label: "Archive",
    icon: <Archive className="h-3 w-3" />,
    variant: "outline",
    requiresConfirmation: true,
  },
  {
    key: "delete",
    label: "Delete",
    icon: <Trash2 className="h-3 w-3" />,
    variant: "destructive",
    requiresConfirmation: true,
  },
  {
    key: "activate",
    label: "Activate",
    icon: <Play className="h-3 w-3" />,
    variant: "outline",
  },
  {
    key: "pause",
    label: "Pause",
    icon: <Pause className="h-3 w-3" />,
    variant: "outline",
  },
  {
    key: "complete",
    label: "Complete",
    icon: <CheckCircle className="h-3 w-3" />,
    variant: "outline",
  },
]

export const BulkActionBar: React.FC<BulkActionBarProps> = ({
  selectedCount,
  totalCount,
  actions = defaultActions,
  onActionClick,
  onClearSelection,
  loading = false,
  disabled = false,
  className,
}) => {
  const [selectedAction, setSelectedAction] = useState<string>("")

  const handleActionApply = () => {
    if (selectedAction && !loading && !disabled) {
      onActionClick(selectedAction)
      setSelectedAction("") // Reset selection after action
    }
  }

  const handleClearSelection = () => {
    if (!loading && !disabled) {
      onClearSelection()
      setSelectedAction("")
    }
  }

  if (selectedCount === 0) {
    return null
  }

  return (
    <div
      className={cn(
        "bg-muted/50 flex items-center justify-between gap-4 rounded-lg border p-3",
        "animate-in slide-in-from-top-2 duration-200",
        className
      )}
    >
      {/* Selection Info */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-2">
          <CheckSquare className="text-primary h-4 w-4" />
          <Badge variant="secondary" className="font-medium">
            {selectedCount} selected
          </Badge>
          {totalCount && (
            <span className="text-muted-foreground text-sm">
              of {totalCount}
            </span>
          )}
        </div>

        <Button
          variant="ghost"
          size="sm"
          onClick={handleClearSelection}
          disabled={loading || disabled}
          className="hover:bg-background h-6 px-2 text-xs"
        >
          <X className="mr-1 h-3 w-3" />
          Clear
        </Button>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-2">
        <Select
          value={selectedAction}
          onValueChange={setSelectedAction}
          disabled={loading || disabled}
        >
          <SelectTrigger className="h-8 w-40">
            <SelectValue placeholder="Choose action..." />
          </SelectTrigger>
          <SelectContent>
            {actions.map((action) => (
              <SelectItem
                key={action.key}
                value={action.key}
                className="flex items-center gap-2"
              >
                <div className="flex items-center gap-2">
                  {action.icon}
                  <span>{action.label}</span>
                </div>
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Button
          onClick={handleActionApply}
          disabled={!selectedAction || loading || disabled}
          size="sm"
          variant={
            selectedAction &&
            actions.find((a) => a.key === selectedAction)?.variant ===
              "destructive"
              ? "destructive"
              : "default"
          }
          className="h-8"
        >
          {loading && <Loader2 className="mr-2 h-3 w-3 animate-spin" />}
          Apply
        </Button>
      </div>
    </div>
  )
}

// Hook for managing bulk selection state
export const useBulkSelection = <T extends { id: string | number }>(
  items: T[] = []
) => {
  const [selectedItems, setSelectedItems] = useState<T[]>([])

  const selectItem = (item: T) => {
    setSelectedItems((prev) =>
      prev.some((selected) => selected.id === item.id)
        ? prev.filter((selected) => selected.id !== item.id)
        : [...prev, item]
    )
  }

  const selectAll = () => {
    setSelectedItems(items)
  }

  const clearSelection = () => {
    setSelectedItems([])
  }

  const isSelected = (item: T) => {
    return selectedItems.some((selected) => selected.id === item.id)
  }

  const isAllSelected = () => {
    return items.length > 0 && selectedItems.length === items.length
  }

  const isIndeterminate = () => {
    return selectedItems.length > 0 && selectedItems.length < items.length
  }

  return {
    selectedItems,
    selectedCount: selectedItems.length,
    selectItem,
    selectAll,
    clearSelection,
    isSelected,
    isAllSelected,
    isIndeterminate,
  }
}
