/**
 * Loading Spinner Atom
 *
 * Consistent loading indicator with multiple variants and sizes.
 * Used across tables, forms, and async operations.
 */

import React from "react"

import { Loader2, RefreshCw } from "lucide-react"

import { cn } from "@/lib/utils"

export interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg" | "xl"
  text?: string
  variant?: "spinner" | "pulse" | "refresh"
  className?: string
  inline?: boolean
}

const sizeConfig = {
  sm: {
    icon: "h-3 w-3",
    text: "text-xs",
    container: "gap-1",
  },
  md: {
    icon: "h-4 w-4",
    text: "text-sm",
    container: "gap-2",
  },
  lg: {
    icon: "h-6 w-6",
    text: "text-base",
    container: "gap-2",
  },
  xl: {
    icon: "h-8 w-8",
    text: "text-lg",
    container: "gap-3",
  },
} as const

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = "md",
  text,
  variant = "spinner",
  className,
  inline = false,
}) => {
  const sizeStyles = sizeConfig[size]

  const Icon = variant === "refresh" ? RefreshCw : Loader2

  const containerClasses = cn(
    "flex items-center",
    sizeStyles.container,
    inline ? "inline-flex" : "justify-center",
    className
  )

  const iconClasses = cn(
    sizeStyles.icon,
    "animate-spin text-muted-foreground",
    variant === "pulse" && "animate-pulse"
  )

  return (
    <div className={containerClasses}>
      <Icon className={iconClasses} />
      {text && (
        <span className={cn(sizeStyles.text, "text-muted-foreground")}>
          {text}
        </span>
      )}
    </div>
  )
}

// Loading overlay component for full screen loading
export const LoadingOverlay: React.FC<{
  text?: string
  className?: string
}> = ({ text = "Loading...", className }) => (
  <div className={cn("flex items-center justify-center py-8", className)}>
    <LoadingSpinner size="lg" text={text} />
  </div>
)

// Inline loading component for buttons and small spaces
export const InlineLoading: React.FC<{
  text?: string
  size?: "sm" | "md"
}> = ({ text, size = "sm" }) => (
  <LoadingSpinner size={size} text={text} inline />
)
