/**
 * Empty State Atom
 *
 * Consistent empty state display with optional actions.
 * Used for empty tables, lists, and data views.
 */

import React from "react"

import {
  Database,
  FileText,
  Folder,
  PlusCircle,
  Search,
  Users,
} from "lucide-react"

import { cn } from "@/lib/utils"

import { Button } from "@/components/ui/button"

export type EmptyStateVariant =
  | "search"
  | "folder"
  | "users"
  | "documents"
  | "data"
  | "custom"

export interface EmptyStateProps {
  icon?: React.ReactNode
  title: string
  description?: string
  action?: React.ReactNode
  variant?: EmptyStateVariant
  size?: "sm" | "md" | "lg"
  className?: string
}

const variantConfig = {
  search: {
    icon: Search,
    color: "text-blue-400",
  },
  folder: {
    icon: Folder,
    color: "text-yellow-400",
  },
  users: {
    icon: Users,
    color: "text-green-400",
  },
  documents: {
    icon: FileText,
    color: "text-purple-400",
  },
  data: {
    icon: Database,
    color: "text-gray-400",
  },
  custom: {
    icon: null,
    color: "text-muted-foreground",
  },
} as const

const sizeConfig = {
  sm: {
    icon: "h-8 w-8",
    title: "text-base",
    description: "text-sm",
    container: "py-6",
    spacing: "space-y-2",
  },
  md: {
    icon: "h-12 w-12",
    title: "text-lg",
    description: "text-sm",
    container: "py-8",
    spacing: "space-y-3",
  },
  lg: {
    icon: "h-16 w-16",
    title: "text-xl",
    description: "text-base",
    container: "py-12",
    spacing: "space-y-4",
  },
} as const

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  description,
  action,
  variant = "data",
  size = "md",
  className,
}) => {
  const config = variantConfig[variant]
  const sizeStyles = sizeConfig[size]

  const IconComponent = icon ? null : config.icon

  return (
    <div className={cn("text-center", sizeStyles.container, className)}>
      <div className={cn("flex flex-col items-center", sizeStyles.spacing)}>
        {/* Icon */}
        <div className={cn(sizeStyles.icon, config.color)}>
          {icon ||
            (IconComponent && <IconComponent className="h-full w-full" />)}
        </div>

        {/* Content */}
        <div className={sizeStyles.spacing}>
          <h3 className={cn(sizeStyles.title, "text-foreground font-semibold")}>
            {title}
          </h3>

          {description && (
            <p
              className={cn(
                sizeStyles.description,
                "text-muted-foreground mx-auto max-w-md"
              )}
            >
              {description}
            </p>
          )}
        </div>

        {/* Action */}
        {action && <div className="pt-2">{action}</div>}
      </div>
    </div>
  )
}

// Predefined empty states for common scenarios
export const EmptyProjectList: React.FC<{
  onCreateProject?: () => void
}> = ({ onCreateProject }) => (
  <EmptyState
    variant="folder"
    title="No projects found"
    description="Get started by creating your first project or adjust your search filters."
    action={
      onCreateProject && (
        <Button onClick={onCreateProject}>
          <PlusCircle className="mr-2 h-4 w-4" />
          Create Project
        </Button>
      )
    }
  />
)

export const EmptySearchResults: React.FC<{
  searchQuery?: string
  onClearSearch?: () => void
}> = ({ searchQuery, onClearSearch }) => (
  <EmptyState
    variant="search"
    title="No results found"
    description={
      searchQuery
        ? `No projects match "${searchQuery}". Try adjusting your search terms.`
        : "No projects match your current filters."
    }
    action={
      onClearSearch && (
        <Button variant="outline" onClick={onClearSearch}>
          Clear Search
        </Button>
      )
    }
  />
)

export const EmptyTeamMembers: React.FC<{
  onAddMember?: () => void
}> = ({ onAddMember }) => (
  <EmptyState
    variant="users"
    title="No team members"
    description="This project doesn't have any team members yet. Add members to start collaborating."
    action={
      onAddMember && (
        <Button onClick={onAddMember}>
          <Users className="mr-2 h-4 w-4" />
          Add Team Member
        </Button>
      )
    }
    size="sm"
  />
)
