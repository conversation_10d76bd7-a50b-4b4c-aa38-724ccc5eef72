/**
 * Project Atoms Index
 *
 * Central export for all atomic components in the Projects module.
 * These are the smallest, most reusable UI elements.
 */

export {
  getStatusBadgeVariant,
  isValidProjectStatus,
  StatusBadge,
} from "./StatusBadge"
export type { ProjectStatus, StatusBadgeProps } from "./StatusBadge"

export {
  getPriorityBadgeVariant,
  getPriorityLevel,
  isValidProjectPriority,
  PriorityBadge,
} from "./PriorityBadge"
export type { PriorityBadgeProps, ProjectPriority } from "./PriorityBadge"

export { FormInput } from "./FormInput"
export type { FormInputProps } from "./FormInput"

export {
  ActionButton,
  getActionConfig,
  isValidActionType,
} from "./ActionButton"
export type { ActionButtonProps, ActionType } from "./ActionButton"

export { InlineLoading, LoadingOverlay, LoadingSpinner } from "./LoadingSpinner"
export type { LoadingSpinnerProps } from "./LoadingSpinner"

export {
  EmptyProjectList,
  EmptySearchResults,
  EmptyState,
  EmptyTeamMembers,
} from "./EmptyState"
export type { EmptyStateProps, EmptyStateVariant } from "./EmptyState"
