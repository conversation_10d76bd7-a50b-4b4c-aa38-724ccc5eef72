/**
 * Priority Badge Atom
 *
 * Reusable priority badge component with consistent styling and color coding.
 * Used across project lists, forms, and detail views.
 */

import React from "react"

import { Alert<PERSON>riangle, ArrowDown, ArrowUp, Minus } from "lucide-react"

import { cn } from "@/lib/utils"

import { Badge } from "@/components/ui/badge"

export type ProjectPriority = "Low" | "Medium" | "High" | "Critical"

export interface PriorityBadgeProps {
  priority: ProjectPriority
  size?: "sm" | "md" | "lg"
  variant?: "default" | "solid" | "outline"
  showIcon?: boolean
  className?: string
}

const priorityConfig = {
  Low: {
    variant: "secondary" as const,
    icon: ArrowDown,
    color: "text-blue-600",
    bgColor: "bg-blue-50 text-blue-700 border-blue-200",
  },
  Medium: {
    variant: "default" as const,
    icon: Minus,
    color: "text-gray-600",
    bgColor: "bg-gray-50 text-gray-700 border-gray-200",
  },
  High: {
    variant: "warning" as const,
    icon: ArrowUp,
    color: "text-orange-600",
    bgColor: "bg-orange-50 text-orange-700 border-orange-200",
  },
  Critical: {
    variant: "destructive" as const,
    icon: AlertTriangle,
    color: "text-red-600",
    bgColor: "bg-red-50 text-red-700 border-red-200",
  },
} as const

const sizeConfig = {
  sm: {
    badge: "text-xs px-1.5 py-0.5",
    icon: "h-2.5 w-2.5",
  },
  md: {
    badge: "text-sm px-2 py-1",
    icon: "h-3 w-3",
  },
  lg: {
    badge: "text-base px-3 py-1.5",
    icon: "h-4 w-4",
  },
} as const

export const PriorityBadge: React.FC<PriorityBadgeProps> = ({
  priority,
  size = "md",
  variant: variantOverride,
  showIcon = true,
  className,
}) => {
  const config = priorityConfig[priority]
  const sizeStyles = sizeConfig[size]
  const Icon = config.icon

  // Map custom variants to valid Badge variants
  const mapVariant = (variant: string) => {
    switch (variant) {
      case "warning":
        return "secondary"
      case "solid":
        return "default"
      default:
        return variant as "default" | "secondary" | "destructive" | "outline"
    }
  }
  const badgeVariant = mapVariant(variantOverride || config.variant)

  return (
    <Badge
      variant={badgeVariant}
      className={cn(
        sizeStyles.badge,
        "inline-flex items-center gap-1 font-medium",
        (variantOverride || config.variant) === "solid" && config.bgColor,
        className
      )}
    >
      {showIcon && <Icon className={cn(sizeStyles.icon, config.color)} />}
      {priority}
    </Badge>
  )
}

// Utility function for getting badge variant (used in existing components)
export const getPriorityBadgeVariant = (priority: ProjectPriority) => {
  return priorityConfig[priority]?.variant || "outline"
}

// Get priority level as number for sorting
export const getPriorityLevel = (priority: ProjectPriority): number => {
  const levels = { Low: 1, Medium: 2, High: 3, Critical: 4 }
  return levels[priority]
}

// Type guard for priority values
export const isValidProjectPriority = (
  priority: string
): priority is ProjectPriority => {
  return ["Low", "Medium", "High", "Critical"].includes(priority)
}
