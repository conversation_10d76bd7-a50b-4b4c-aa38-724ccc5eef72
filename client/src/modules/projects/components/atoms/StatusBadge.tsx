/**
 * Status Badge Atom
 *
 * Reusable status badge component with consistent styling and behavior.
 * Used across project lists, forms, and detail views.
 */

import React from "react"

import {
  CheckCircle,
  FileText,
  Pause,
  Play,
  XCircle,
} from "lucide-react"

import { cn } from "@/lib/utils"

import { Badge } from "@/components/ui/badge"

export type ProjectStatus =
  | "Draft"
  | "Active"
  | "Paused"
  | "Completed"
  | "Cancelled"

export interface StatusBadgeProps {
  status: ProjectStatus
  size?: "sm" | "md" | "lg"
  variant?: "default" | "solid" | "outline"
  showIcon?: boolean
  className?: string
}

const statusConfig = {
  Draft: {
    variant: "secondary" as const,
    icon: FileText,
    color: "text-muted-foreground",
  },
  Active: {
    variant: "default" as const,
    icon: Play,
    color: "text-green-600",
  },
  Paused: {
    variant: "warning" as const,
    icon: Pause,
    color: "text-yellow-600",
  },
  Completed: {
    variant: "success" as const,
    icon: CheckCircle,
    color: "text-green-700",
  },
  Cancelled: {
    variant: "destructive" as const,
    icon: XCircle,
    color: "text-red-600",
  },
} as const

const sizeConfig = {
  sm: {
    badge: "text-xs px-1.5 py-0.5",
    icon: "h-2.5 w-2.5",
  },
  md: {
    badge: "text-sm px-2 py-1",
    icon: "h-3 w-3",
  },
  lg: {
    badge: "text-base px-3 py-1.5",
    icon: "h-4 w-4",
  },
} as const

export const StatusBadge: React.FC<StatusBadgeProps> = ({
  status,
  size = "md",
  variant: variantOverride,
  showIcon = true,
  className,
}) => {
  const config = statusConfig[status]
  const sizeStyles = sizeConfig[size]
  const Icon = config.icon

  // Map custom variants to valid Badge variants
  const mapVariant = (variant: string) => {
    switch (variant) {
      case "success":
        return "default"
      case "warning":
        return "secondary"
      case "solid":
        return "default"
      default:
        return variant as "default" | "secondary" | "destructive" | "outline"
    }
  }
  const badgeVariant = mapVariant(variantOverride || config.variant)

  return (
    <Badge
      variant={badgeVariant}
      className={cn(
        sizeStyles.badge,
        "inline-flex items-center gap-1 font-medium",
        className
      )}
    >
      {showIcon && <Icon className={cn(sizeStyles.icon, config.color)} />}
      {status}
    </Badge>
  )
}

// Utility function for getting badge variant (used in existing components)
export const getStatusBadgeVariant = (status: ProjectStatus) => {
  return statusConfig[status]?.variant || "outline"
}

// Type guard for status values
export const isValidProjectStatus = (
  status: string
): status is ProjectStatus => {
  return ["Draft", "Active", "Paused", "Completed", "Cancelled"].includes(
    status
  )
}
