/**
 * Form Input Atom
 *
 * Enhanced form input with validation states, warnings, and consistent styling.
 * Supports all standard input types with domain-aware validation display.
 */

import React, { forwardRef } from "react"

import { AlertCircle, CheckCircle, Info } from "lucide-react"

import { cn } from "@/lib/utils"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export interface FormInputProps
  extends Omit<React.InputHTMLAttributes<HTMLInputElement>, "size"> {
  label: string
  error?: string
  warning?: string
  success?: string
  helpText?: string
  required?: boolean
  touched?: boolean
  size?: "sm" | "md" | "lg"
  variant?: "default" | "ghost" | "outline"
}

const sizeConfig = {
  sm: {
    input: "h-8 text-sm",
    label: "text-sm",
    message: "text-xs",
  },
  md: {
    input: "h-10 text-sm",
    label: "text-sm",
    message: "text-sm",
  },
  lg: {
    input: "h-12 text-base",
    label: "text-base",
    message: "text-sm",
  },
} as const

export const FormInput = forwardRef<HTMLInputElement, FormInputProps>(
  (
    {
      label,
      error,
      warning,
      success,
      helpText,
      required = false,
      touched = false,
      size = "md",
      variant = "default",
      className,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || `input-${label.toLowerCase().replace(/\s+/g, "-")}`
    const sizeStyles = sizeConfig[size]

    const hasError = error && touched
    const hasWarning = warning && touched && !hasError
    const hasSuccess = success && touched && !hasError && !hasWarning

    return (
      <div className="space-y-2">
        <Label
          htmlFor={inputId}
          className={cn(
            sizeStyles.label,
            "font-medium",
            hasError && "text-destructive",
            hasWarning && "text-yellow-700",
            hasSuccess && "text-green-700"
          )}
        >
          {label}
          {required && <span className="text-destructive ml-1">*</span>}
        </Label>

        <div className="relative">
          <Input
            ref={ref}
            id={inputId}
            className={cn(
              sizeStyles.input,
              hasError && "border-destructive focus-visible:ring-destructive",
              hasWarning && "border-yellow-400 focus-visible:ring-yellow-400",
              hasSuccess && "border-green-400 focus-visible:ring-green-400",
              "transition-colors",
              className
            )}
            {...props}
          />

          {/* Status Icon */}
          {(hasError || hasWarning || hasSuccess) && (
            <div className="absolute inset-y-0 right-0 flex items-center pr-3">
              {hasError && <AlertCircle className="text-destructive h-4 w-4" />}
              {hasWarning && <Info className="h-4 w-4 text-yellow-600" />}
              {hasSuccess && <CheckCircle className="h-4 w-4 text-green-600" />}
            </div>
          )}
        </div>

        {/* Messages */}
        <div className="space-y-1">
          {hasError && (
            <p
              className={cn(
                sizeStyles.message,
                "text-destructive flex items-center gap-1"
              )}
            >
              <AlertCircle className="h-3 w-3 flex-shrink-0" />
              {error}
            </p>
          )}

          {hasWarning && (
            <p
              className={cn(
                sizeStyles.message,
                "flex items-center gap-1 text-yellow-700"
              )}
            >
              <Info className="h-3 w-3 flex-shrink-0" />
              {warning}
            </p>
          )}

          {hasSuccess && (
            <p
              className={cn(
                sizeStyles.message,
                "flex items-center gap-1 text-green-700"
              )}
            >
              <CheckCircle className="h-3 w-3 flex-shrink-0" />
              {success}
            </p>
          )}

          {helpText && !hasError && !hasWarning && !hasSuccess && (
            <p className={cn(sizeStyles.message, "text-muted-foreground")}>
              {helpText}
            </p>
          )}
        </div>
      </div>
    )
  }
)

FormInput.displayName = "FormInput"
