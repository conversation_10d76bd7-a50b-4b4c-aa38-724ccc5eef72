/**
 * Action Button Atom
 *
 * Standardized action buttons with consistent icons, variants, and behavior.
 * Used across tables, forms, and action menus.
 */

import React from "react"

import {
  Archive,
  CheckCircle,
  Copy,
  Download,
  Edit,
  Eye,
  Loader2,
  MoreHorizontal,
  Pause,
  Play,
  Settings,
  Share,
  Trash2,
  Upload,
  UserMinus,
  UserPlus,
  XCircle,
} from "lucide-react"

import { cn } from "@/lib/utils"

import { But<PERSON> } from "@/components/ui/button"

export type ActionType =
  | "edit"
  | "delete"
  | "view"
  | "archive"
  | "activate"
  | "pause"
  | "complete"
  | "cancel"
  | "add-user"
  | "remove-user"
  | "more"
  | "download"
  | "upload"
  | "copy"
  | "share"
  | "settings"

export interface ActionButtonProps {
  action: ActionType
  size?: "sm" | "md" | "lg"
  variant?: "default" | "destructive" | "outline" | "ghost" | "secondary"
  display?: "icon" | "text" | "both"
  disabled?: boolean
  loading?: boolean
  className?: string
  onClick?: () => void
  children?: React.ReactNode
}

const actionConfig = {
  edit: {
    icon: Edit,
    label: "Edit",
    variant: "outline" as const,
    color: "text-blue-600",
  },
  delete: {
    icon: Trash2,
    label: "Delete",
    variant: "destructive" as const,
    color: "text-red-600",
  },
  view: {
    icon: Eye,
    label: "View",
    variant: "ghost" as const,
    color: "text-gray-600",
  },
  archive: {
    icon: Archive,
    label: "Archive",
    variant: "outline" as const,
    color: "text-orange-600",
  },
  activate: {
    icon: Play,
    label: "Activate",
    variant: "outline" as const,
    color: "text-green-600",
  },
  pause: {
    icon: Pause,
    label: "Pause",
    variant: "outline" as const,
    color: "text-yellow-600",
  },
  complete: {
    icon: CheckCircle,
    label: "Complete",
    variant: "outline" as const,
    color: "text-green-700",
  },
  cancel: {
    icon: XCircle,
    label: "Cancel",
    variant: "destructive" as const,
    color: "text-red-600",
  },
  "add-user": {
    icon: UserPlus,
    label: "Add User",
    variant: "outline" as const,
    color: "text-blue-600",
  },
  "remove-user": {
    icon: UserMinus,
    label: "Remove User",
    variant: "destructive" as const,
    color: "text-red-600",
  },
  more: {
    icon: MoreHorizontal,
    label: "More",
    variant: "ghost" as const,
    color: "text-gray-600",
  },
  download: {
    icon: Download,
    label: "Download",
    variant: "outline" as const,
    color: "text-gray-600",
  },
  upload: {
    icon: Upload,
    label: "Upload",
    variant: "outline" as const,
    color: "text-gray-600",
  },
  copy: {
    icon: Copy,
    label: "Copy",
    variant: "ghost" as const,
    color: "text-gray-600",
  },
  share: {
    icon: Share,
    label: "Share",
    variant: "outline" as const,
    color: "text-gray-600",
  },
  settings: {
    icon: Settings,
    label: "Settings",
    variant: "ghost" as const,
    color: "text-gray-600",
  },
} as const

const sizeConfig = {
  sm: {
    button: "h-8 px-2 text-xs",
    icon: "h-3 w-3",
    iconOnly: "h-8 w-8 p-0",
  },
  md: {
    button: "h-10 px-3 text-sm",
    icon: "h-4 w-4",
    iconOnly: "h-10 w-10 p-0",
  },
  lg: {
    button: "h-12 px-4 text-base",
    icon: "h-5 w-5",
    iconOnly: "h-12 w-12 p-0",
  },
} as const

export const ActionButton: React.FC<ActionButtonProps> = ({
  action,
  size = "md",
  variant: variantOverride,
  display = "icon",
  disabled = false,
  loading = false,
  className,
  onClick,
  children,
}) => {
  const config = actionConfig[action]
  const sizeStyles = sizeConfig[size]
  const Icon = config.icon

  const buttonVariant = variantOverride || config.variant
  const isIconOnly = display === "icon"
  const showText = display === "text" || display === "both"
  const showIcon = display === "icon" || display === "both"

  return (
    <Button
      variant={buttonVariant}
      size="sm"
      disabled={disabled || loading}
      onClick={onClick}
      className={cn(
        isIconOnly ? sizeStyles.iconOnly : sizeStyles.button,
        "transition-all duration-200",
        disabled && "cursor-not-allowed opacity-50",
        className
      )}
    >
      {loading ? (
        <Loader2 className={cn(sizeStyles.icon, "animate-spin")} />
      ) : (
        showIcon && <Icon className={cn(sizeStyles.icon, showText && "mr-2")} />
      )}

      {showText && (children || config.label)}
    </Button>
  )
}

// Utility for getting action configuration
export const getActionConfig = (action: ActionType) => {
  return actionConfig[action]
}

// Type guard for action types
export const isValidActionType = (action: string): action is ActionType => {
  return Object.keys(actionConfig).includes(action)
}
