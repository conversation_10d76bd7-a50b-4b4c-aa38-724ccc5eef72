"use client"

/**
 * Team Management Organism (Refactored with Atomic Design)
 *
 * Comprehensive team management using atomic design principles.
 * Composed of atoms and molecules for maximum reusability and consistency.
 */
import React, { useState } from "react"

import {
  AlertCircle,
  Clock,
  Crown,
  Edit,
  MoreHorizontal,
  Trash2,
  UserPlus,
  Users,
} from "lucide-react"

import { useAuth } from "@/hooks/useAuth"

import { Alert, AlertDescription } from "@/components/ui/alert"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Label } from "@/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Textarea } from "@/components/ui/textarea"

// Domain imports
import { ProjectMember } from "../../domain/entities/ProjectMember"
import { useProjectHooks } from "../../hooks/useProjectHooks"
import { useProjectNotifications } from "../../store/projectStore"
// Atomic components
import {
  ActionButton,
  EmptyTeamMembers,
  LoadingSpinner,
  StatusBadge,
} from "../atoms"
import {
  MemberCard,
  MemberForm,
  MemberFormData,
  MemberGrid,
} from "../molecules"

interface TeamManagementProps {
  projectId: string
  viewMode?: "table" | "grid"
  showAddButton?: boolean
  compact?: boolean
}

// Team Statistics Component
const TeamStatistics: React.FC<{
  members: ProjectMember[]
  expiredCount: number
}> = ({ members, expiredCount }) => (
  <div>
    <h2 className="flex items-center gap-2 text-2xl font-bold">
      <Users className="h-6 w-6" />
      Team Management
    </h2>
    <p className="text-muted-foreground">
      {members.length} member{members.length !== 1 ? "s" : ""}
      {expiredCount > 0 && ` • ${expiredCount} expired`}
    </p>
  </div>
)

// Member Actions Component
const MemberActions: React.FC<{
  member: ProjectMember
  onEdit: (member: ProjectMember) => void
  onRemove: (member: ProjectMember) => void
  canManage: boolean
}> = ({ member, onEdit, onRemove, canManage }) => {
  if (!canManage) return null

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="icon"
          data-testid={`member-actions-${member.id}`}
        >
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => onEdit(member)} disabled>
          <Edit className="mr-2 h-4 w-4" />
          Edit Role
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => onRemove(member)}
          className="text-destructive"
          data-testid={`remove-member-${member.id}`}
        >
          <Trash2 className="mr-2 h-4 w-4" />
          Remove Member
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

// Team Members Table Component
const TeamMembersTable: React.FC<{
  members: ProjectMember[]
  currentUserId?: number
  canManageTeam: boolean
  onMemberAction: (action: string, member: ProjectMember) => void
}> = ({ members, currentUserId, canManageTeam, onMemberAction }) => {
  const getRoleBadgeVariant = (role: string) => {
    if (
      role.toLowerCase().includes("manager") ||
      role.toLowerCase().includes("lead")
    ) {
      return "default"
    }
    if (
      role.toLowerCase().includes("senior") ||
      role.toLowerCase().includes("architect")
    ) {
      return "secondary"
    }
    return "outline"
  }

  const getMemberStatusBadge = (member: ProjectMember) => {
    if (!member.isActive) {
      return <StatusBadge status="Cancelled" size="sm" />
    }
    if (member.hasExpired()) {
      return <StatusBadge status="Cancelled" size="sm" />
    }
    return <StatusBadge status="Active" size="sm" />
  }

  return (
    <div className="rounded-lg border">
      <Table data-testid="team-members-table">
        <TableHeader>
          <TableRow>
            <TableHead>Member</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Assigned</TableHead>
            <TableHead>Expires</TableHead>
            {canManageTeam && <TableHead className="w-12">Actions</TableHead>}
          </TableRow>
        </TableHeader>
        <TableBody>
          {members.map((member) => (
            <TableRow
              key={member.id}
              data-testid={`team-member-row-${member.id}`}
            >
              <TableCell>
                <div className="flex items-center gap-2">
                  <div>
                    <div className="font-medium">
                      User ID: {member.userId}
                      {member.userId === currentUserId && (
                        <span className="text-muted-foreground bg-muted ml-2 rounded px-2 py-1 text-xs">
                          You
                        </span>
                      )}
                    </div>
                    <div className="text-muted-foreground text-sm">
                      Member ID: {member.id}
                    </div>
                  </div>
                </div>
              </TableCell>

              <TableCell>
                <div className="flex items-center gap-1">
                  {member.role.canManageProject() && (
                    <Crown className="h-3 w-3 text-yellow-600" />
                  )}
                  <span className="font-medium">{member.role.role}</span>
                </div>
              </TableCell>

              <TableCell>{getMemberStatusBadge(member)}</TableCell>

              <TableCell>
                <div className="text-sm">
                  {new Date(member.assignedAt).toLocaleDateString()}
                </div>
                <div className="text-muted-foreground text-xs">
                  by User {member.assignedBy}
                </div>
              </TableCell>

              <TableCell>
                {member.expiresAt ? (
                  <div className="text-sm">
                    {new Date(member.expiresAt).toLocaleDateString()}
                    {member.hasExpired() && (
                      <div className="text-destructive mt-1 text-xs">
                        Expired
                      </div>
                    )}
                  </div>
                ) : (
                  <span className="text-muted-foreground">No expiration</span>
                )}
              </TableCell>

              {canManageTeam && (
                <TableCell>
                  <MemberActions
                    member={member}
                    onEdit={(member) => onMemberAction("edit", member)}
                    onRemove={(member) => onMemberAction("remove", member)}
                    canManage={canManageTeam}
                  />
                </TableCell>
              )}
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
}

// Remove Member Dialog Component
const RemoveMemberDialog: React.FC<{
  member: ProjectMember | null
  open: boolean
  onOpenChange: (open: boolean) => void
  onConfirm: (reason?: string) => void
  loading: boolean
}> = ({ member, open, onOpenChange, onConfirm, loading }) => {
  const [removeReason, setRemoveReason] = useState("")

  const handleConfirm = () => {
    onConfirm(removeReason || undefined)
    setRemoveReason("")
  }

  const handleCancel = () => {
    onOpenChange(false)
    setRemoveReason("")
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Remove Team Member</DialogTitle>
          <DialogDescription>
            Are you sure you want to remove this member from the project? This
            action cannot be undone.
          </DialogDescription>
        </DialogHeader>

        {member && (
          <div className="space-y-4">
            <div className="bg-muted rounded p-3">
              <div className="font-medium">User ID: {member.userId}</div>
              <div className="text-muted-foreground text-sm">
                Role: {member.role.role}
              </div>
            </div>

            <div>
              <Label htmlFor="removeReason">
                Reason for removal (optional)
              </Label>
              <Textarea
                id="removeReason"
                placeholder="Why is this member being removed?"
                value={removeReason}
                onChange={(e) => setRemoveReason(e.target.value)}
                data-testid="remove-reason-input"
              />
            </div>
          </div>
        )}

        <DialogFooter>
          <Button variant="outline" onClick={handleCancel} disabled={loading}>
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={loading}
            data-testid="confirm-remove-member"
          >
            {loading && <LoadingSpinner size="sm" inline />}
            Remove Member
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}

// Main TeamManagement Component
export const TeamManagement: React.FC<TeamManagementProps> = ({
  projectId,
  viewMode = "table",
  showAddButton = true,
  compact = false,
}) => {
  const { user } = useAuth()
  const { notifications } = useProjectNotifications()

  // Domain-aware hooks
  const { useProjectTeam } = useProjectHooks()
  const {
    assignMember,
    removeMember,
    isAssigning,
    isRemoving,
    getProjectMembers,
    isUserProjectMember,
    getUserProjectRole,
    getExpiredMembers,
    canUserManageTeam,
    hasAnyError,
    assignError,
    removeError,
  } = useProjectTeam()

  // Local state
  const [showAddDialog, setShowAddDialog] = useState(false)
  const [showRemoveDialog, setShowRemoveDialog] = useState(false)
  const [selectedMember, setSelectedMember] = useState<ProjectMember | null>(
    null
  )

  // Get current project data
  const members = getProjectMembers(projectId)
  const expiredMembers = getExpiredMembers(projectId)
  const canManageTeam = user?.id ? canUserManageTeam(projectId, user.id) : false
  const userRole = user?.id ? getUserProjectRole(projectId, user.id) : null

  // Handle member assignment
  const handleAssignMember = async (formData: MemberFormData) => {
    if (!user?.id) return

    const result = await assignMember({
      projectId,
      userId: formData.userId,
      role: formData.role,
      assignedBy: user.id,
      expiresAt: formData.expiresAt,
      assignmentContext: {
        reason: formData.reason,
        priority: formData.priority,
        skillsRequired: formData.skillsRequired,
        estimatedWorkload: formData.estimatedWorkload,
        requiresApproval: formData.requiresApproval,
      },
    })

    if (result.success) {
      setShowAddDialog(false)
    }
  }

  // Handle member removal
  const handleRemoveMember = async (reason?: string) => {
    if (!selectedMember || !user?.id) return

    const result = await removeMember({
      projectId,
      memberId: selectedMember.id,
      removedBy: user.id,
      reason,
    })

    if (result.success) {
      setShowRemoveDialog(false)
      setSelectedMember(null)
    }
  }

  // Handle member actions
  const handleMemberAction = (action: string, member: ProjectMember) => {
    switch (action) {
      case "remove":
        setSelectedMember(member)
        setShowRemoveDialog(true)
        break
      case "edit":
        // Future implementation
        console.log("Edit member:", member)
        break
    }
  }

  // Error state
  if (hasAnyError) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Error managing team members:{" "}
          {assignError?.message || removeError?.message || "Unknown error"}
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex items-center justify-between">
        <TeamStatistics
          members={members}
          expiredCount={expiredMembers.length}
        />

        {canManageTeam && showAddButton && (
          <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
            <DialogTrigger asChild>
              <Button data-testid="add-team-member-button">
                <UserPlus className="mr-2 h-4 w-4" />
                Add Member
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <MemberForm
                onSubmit={handleAssignMember}
                onCancel={() => setShowAddDialog(false)}
                loading={isAssigning}
                submitLabel="Assign Member"
              />
            </DialogContent>
          </Dialog>
        )}
      </div>

      {/* Expired Members Warning */}
      {expiredMembers.length > 0 && (
        <Alert variant="destructive">
          <Clock className="h-4 w-4" />
          <AlertDescription>
            {expiredMembers.length} team member
            {expiredMembers.length !== 1 ? "s have" : " has"} expired
            assignments. Consider extending or removing them.
          </AlertDescription>
        </Alert>
      )}

      {/* Team Members Display */}
      {members.length === 0 ? (
        <EmptyTeamMembers
          onAddMember={canManageTeam ? () => setShowAddDialog(true) : undefined}
        />
      ) : viewMode === "grid" ? (
        <MemberGrid
          members={members}
          currentUserId={user?.id}
          onActionClick={(action, member) => {
            if (action === "menu") {
              handleMemberAction("remove", member)
            }
          }}
        />
      ) : (
        <TeamMembersTable
          members={members}
          currentUserId={user?.id}
          canManageTeam={canManageTeam}
          onMemberAction={handleMemberAction}
        />
      )}

      {/* Remove Member Dialog */}
      <RemoveMemberDialog
        member={selectedMember}
        open={showRemoveDialog}
        onOpenChange={setShowRemoveDialog}
        onConfirm={handleRemoveMember}
        loading={isRemoving}
      />
    </div>
  )
}
