/**
 * Project Operations Hook
 *
 * Domain-aware React hook for core project CRUD operations.
 * Integrates application services with React Query and provides
 * optimistic updates and comprehensive error handling.
 */

import { useCallback } from "react"

import type {
  ArchiveProjectRequest,
  CreateProjectRequest,
  UpdateProjectRequest,
} from "../application"

import { useMutation, useQueryClient } from "@tanstack/react-query"

import {
  ArchiveProjectUseCase,
  CreateProjectUseCase,
  isSuccessResult,
  UpdateProjectUseCase,
} from "../application"
import { Project } from "../domain/entities/Project"
import { projectQueryKeys } from "../infrastructure"
import { useProjectStore } from "../store/projectStore"

/**
 * Configuration for project operations
 */
interface ProjectOperationsConfig {
  enableOptimisticUpdates?: boolean
  showSuccessNotifications?: boolean
  showErrorNotifications?: boolean
  onSuccess?: (operation: string, data: any) => void
  onError?: (operation: string, error: any) => void
}

/**
 * Result type for operation hooks
 */
interface OperationResult<T> {
  success: boolean
  data?: T
  error?: string
  details?: Record<string, any>
}

/**
 * Hook for project operations with domain awareness
 */
export function useProjectOperations(
  useCases: {
    createProjectUseCase: CreateProjectUseCase
    updateProjectUseCase: UpdateProjectUseCase
    archiveProjectUseCase: ArchiveProjectUseCase
  },
  config: ProjectOperationsConfig = {}
) {
  const queryClient = useQueryClient()
  const { addNotification, setSelectedProject, clearSelection, selectedProject } =
    useProjectStore()

  // Create project mutation
  const createProjectMutation = useMutation({
    mutationFn: async (
      request: CreateProjectRequest
    ): Promise<OperationResult<Project>> => {
      const result = await useCases.createProjectUseCase.execute(request)

      if (isSuccessResult(result)) {
        return {
          success: true,
          data: result.data.project,
          details: {
            validationWarnings: result.data.validationWarnings,
          },
        }
      } else {
        return {
          success: false,
          error: result.error.message,
          details: result.error.details,
        }
      }
    },
    onSuccess: (result, variables) => {
      if (result.success && result.data) {
        // Update cache optimistically
        queryClient.setQueryData(
          projectQueryKeys.detail(result.data.id),
          result.data
        )

        // Invalidate list queries to include new project
        queryClient.invalidateQueries({ queryKey: projectQueryKeys.lists() })
        queryClient.invalidateQueries({
          queryKey: projectQueryKeys.byOwner(variables.ownerId),
        })

        // Set as selected project
        setSelectedProject(result.data)

        // Show success notification
        if (config.showSuccessNotifications !== false) {
          addNotification({
            type: "success",
            message: `Project "${result.data.name}" created successfully`,
            details: result.details?.validationWarnings?.length
              ? `${result.details.validationWarnings.length} warnings`
              : undefined,
          })
        }

        // Custom success callback
        config.onSuccess?.("create", result.data)
      } else {
        // Handle creation failure
        if (config.showErrorNotifications !== false) {
          addNotification({
            type: "error",
            message: `Failed to create project: ${result.error}`,
            details: result.details,
          })
        }

        config.onError?.("create", result.error)
      }
    },
    onError: (error) => {
      if (config.showErrorNotifications !== false) {
        addNotification({
          type: "error",
          message: "Project creation failed",
          details: error.message,
        })
      }

      config.onError?.("create", error)
    },
  })

  // Update project mutation
  const updateProjectMutation = useMutation({
    mutationFn: async (
      request: UpdateProjectRequest
    ): Promise<OperationResult<Project>> => {
      const result = await useCases.updateProjectUseCase.execute(request)

      if (isSuccessResult(result)) {
        return {
          success: true,
          data: result.data.project,
          details: {
            updatedFields: result.data.updatedFields,
            validationWarnings: result.data.validationWarnings,
          },
        }
      } else {
        return {
          success: false,
          error: result.error.message,
          details: result.error.details,
        }
      }
    },
    onMutate: async (variables) => {
      if (!config.enableOptimisticUpdates) return

      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: projectQueryKeys.detail(variables.projectId),
      })

      // Snapshot previous value
      const previousProject = queryClient.getQueryData(
        projectQueryKeys.detail(variables.projectId)
      )

      // Optimistically update
      if (previousProject) {
        queryClient.setQueryData(
          projectQueryKeys.detail(variables.projectId),
          (old: Project) => {
            // Apply optimistic updates based on the update request
            const { updates } = variables
            let updated = old
            
            // Apply basic info updates using updateBasicInfo (no permissions check)
            if (updates.name !== undefined || updates.description !== undefined) {
              const name = updates.name !== undefined ? updates.name : updated.name
              const description = updates.description !== undefined ? updates.description : updated.description
              updated = updated.updateBasicInfo(name, description)
            }
            
            // For other updates like priority, we'd need separate methods or skip optimistic updates
            // For now, handle them individually if needed

            return updated
          }
        )
      }

      return { previousProject }
    },
    onSuccess: (result, variables, context) => {
      if (result.success && result.data) {
        // Update cache with server response
        queryClient.setQueryData(
          projectQueryKeys.detail(variables.projectId),
          result.data
        )

        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: projectQueryKeys.byOwner(result.data.ownerId),
        })
        if (result.data.clientId) {
          queryClient.invalidateQueries({
            queryKey: projectQueryKeys.byClient(result.data.clientId),
          })
        }

        // Update selected project if it's the one being updated
        if (selectedProject?.id === result.data.id) {
          setSelectedProject(result.data)
        }

        // Show success notification
        if (config.showSuccessNotifications !== false) {
          const updatedFieldsText =
            result.details?.updatedFields?.join(", ") || "project"
          addNotification({
            type: "success",
            message: `Updated ${updatedFieldsText} successfully`,
            details: result.details?.validationWarnings?.length
              ? `${result.details.validationWarnings.length} warnings`
              : undefined,
          })
        }

        config.onSuccess?.("update", result.data)
      } else {
        // Revert optimistic update on failure
        if (context?.previousProject) {
          queryClient.setQueryData(
            projectQueryKeys.detail(variables.projectId),
            context.previousProject
          )
        }

        if (config.showErrorNotifications !== false) {
          addNotification({
            type: "error",
            message: `Failed to update project: ${result.error}`,
            details: result.details,
          })
        }

        config.onError?.("update", result.error)
      }
    },
    onError: (error, variables, context) => {
      // Revert optimistic update
      if (context?.previousProject) {
        queryClient.setQueryData(
          projectQueryKeys.detail(variables.projectId),
          context.previousProject
        )
      }

      if (config.showErrorNotifications !== false) {
        addNotification({
          type: "error",
          message: "Project update failed",
          details: error.message,
        })
      }

      config.onError?.("update", error)
    },
  })

  // Archive projects mutation
  const archiveProjectsMutation = useMutation({
    mutationFn: async (
      request: ArchiveProjectRequest
    ): Promise<OperationResult<string[]>> => {
      const result = await useCases.archiveProjectUseCase.execute(request)

      if (isSuccessResult(result)) {
        return {
          success: true,
          data: result.data.archivedProjectIds,
          details: {
            skippedProjectIds: result.data.skippedProjectIds,
            validationIssues: result.data.validationIssues,
          },
        }
      } else {
        return {
          success: false,
          error: result.error.message,
          details: result.error.details,
        }
      }
    },
    onSuccess: (result, variables) => {
      if (result.success && result.data) {
        // Remove archived projects from cache
        result.data.forEach((projectId) => {
          queryClient.removeQueries({
            queryKey: projectQueryKeys.detail(projectId),
          })
        })

        // Invalidate list queries
        queryClient.invalidateQueries({ queryKey: projectQueryKeys.lists() })

        // Clear selection if archived project was selected
        if (selectedProject && result.data.includes(selectedProject.id)) {
          clearSelection()
        }

        // Show success notification
        if (config.showSuccessNotifications !== false) {
          const count = result.data.length
          const skippedCount = result.details?.skippedProjectIds?.length || 0
          let message = `${count} project${count === 1 ? "" : "s"} archived successfully`

          if (skippedCount > 0) {
            message += `, ${skippedCount} skipped`
          }

          addNotification({
            type: "success",
            message,
            details: result.details?.validationIssues
              ? "Some validation issues found"
              : undefined,
          })
        }

        config.onSuccess?.("archive", result.data)
      } else {
        if (config.showErrorNotifications !== false) {
          addNotification({
            type: "error",
            message: `Failed to archive projects: ${result.error}`,
            details: result.details,
          })
        }

        config.onError?.("archive", result.error)
      }
    },
    onError: (error) => {
      if (config.showErrorNotifications !== false) {
        addNotification({
          type: "error",
          message: "Project archival failed",
          details: error.message,
        })
      }

      config.onError?.("archive", error)
    },
  })

  // Convenience methods
  const createProject = useCallback(
    async (
      request: CreateProjectRequest
    ): Promise<OperationResult<Project>> => {
      const result = await createProjectMutation.mutateAsync(request)
      return result
    },
    [createProjectMutation]
  )

  const updateProject = useCallback(
    async (
      request: UpdateProjectRequest
    ): Promise<OperationResult<Project>> => {
      const result = await updateProjectMutation.mutateAsync(request)
      return result
    },
    [updateProjectMutation]
  )

  const archiveProjects = useCallback(
    async (
      request: ArchiveProjectRequest
    ): Promise<OperationResult<string[]>> => {
      const result = await archiveProjectsMutation.mutateAsync(request)
      return result
    },
    [archiveProjectsMutation]
  )

  return {
    // Mutation functions
    createProject,
    updateProject,
    archiveProjects,

    // Loading states
    isCreating: createProjectMutation.isPending,
    isUpdating: updateProjectMutation.isPending,
    isArchiving: archiveProjectsMutation.isPending,

    // Error states
    createError: createProjectMutation.error,
    updateError: updateProjectMutation.error,
    archiveError: archiveProjectsMutation.error,

    // Reset functions
    resetCreateState: createProjectMutation.reset,
    resetUpdateState: updateProjectMutation.reset,
    resetArchiveState: archiveProjectsMutation.reset,

    // Combined states for UI convenience
    isAnyOperationPending:
      createProjectMutation.isPending ||
      updateProjectMutation.isPending ||
      archiveProjectsMutation.isPending,

    hasAnyError: !!(
      createProjectMutation.error ||
      updateProjectMutation.error ||
      archiveProjectsMutation.error
    ),
  }
}
