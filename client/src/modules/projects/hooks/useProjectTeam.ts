/**
 * Project Team Management Hook
 *
 * Domain-aware React hook for team member operations.
 * Handles member assignment, removal, and role management with validation.
 */

import { useCallback } from "react"

import type { AssignMemberRequest } from "../application"

import { useMutation, useQueryClient } from "@tanstack/react-query"

import { AssignMemberToProjectUseCase, isSuccessResult } from "../application"
import { Project } from "../domain/entities/Project"
import { ProjectMember } from "../domain/entities/ProjectMember"
import { projectQueryKeys } from "../infrastructure"
import { useProjectStore } from "../store/projectStore"

/**
 * Configuration for team operations
 */
interface TeamOperationsConfig {
  enableOptimisticUpdates?: boolean
  showSuccessNotifications?: boolean
  showErrorNotifications?: boolean
  onSuccess?: (operation: string, data: any) => void
  onError?: (operation: string, error: any) => void
}

/**
 * Result type for team operations
 */
interface TeamOperationResult<T> {
  success: boolean
  data?: T
  error?: string
  details?: Record<string, any>
}

/**
 * Hook for project team management operations
 */
export function useProjectTeam(
  useCases: {
    assignMemberToProjectUseCase: AssignMemberToProjectUseCase
  },
  config: TeamOperationsConfig = {}
) {
  const queryClient = useQueryClient()
  const { addNotification, addActivity } = useProjectStore()

  // Assign member mutation
  const assignMemberMutation = useMutation({
    mutationFn: async (
      request: AssignMemberRequest
    ): Promise<
      TeamOperationResult<{ project: Project; member: ProjectMember }>
    > => {
      const result =
        await useCases.assignMemberToProjectUseCase.execute(request)

      if (isSuccessResult(result)) {
        return {
          success: true,
          data: {
            project: result.data.project,
            member: result.data.member,
          },
          details: {
            assignmentId: result.data.assignmentId,
          },
        }
      } else {
        return {
          success: false,
          error: result.error.message,
          details: result.error.details,
        }
      }
    },
    onMutate: async (variables) => {
      if (!config.enableOptimisticUpdates) return

      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: projectQueryKeys.detail(variables.projectId),
      })

      // Snapshot previous value
      const previousProject = queryClient.getQueryData<Project>(
        projectQueryKeys.detail(variables.projectId)
      )

      // Optimistically add member
      if (previousProject) {
        queryClient.setQueryData(
          projectQueryKeys.detail(variables.projectId),
          (old: Project) => {
            // Create optimistic member (simplified)
            const optimisticMember = ProjectMember.create({
              id: `temp-${Date.now()}`,
              userId: variables.userId,
              projectId: variables.projectId,
              role: { role: variables.role } as any,
              assignedAt: new Date().toISOString(),
              assignedBy: variables.assignedBy,
              isActive: true,
            })

            return old.addTeamMember(optimisticMember, variables.assignedBy)
          }
        )
      }

      return { previousProject }
    },
    onSuccess: (result, variables, context) => {
      if (result.success && result.data) {
        // Update cache with server response
        queryClient.setQueryData(
          projectQueryKeys.detail(variables.projectId),
          result.data.project
        )

        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: projectQueryKeys.byOwner(result.data.project.ownerId),
        })

        // Add to recent activity
        addActivity({
          type: "member_added",
          projectId: variables.projectId,
          projectName: result.data.project.name,
          userId: variables.assignedBy,
          details: {
            newMemberId: result.data.member.id,
            newMemberUserId: variables.userId,
            role: variables.role,
          },
        })

        // Show success notification
        if (config.showSuccessNotifications !== false) {
          addNotification({
            type: "success",
            message: `Team member assigned successfully`,
            details: `Role: ${variables.role}`,
          })
        }

        config.onSuccess?.("assign-member", result.data)
      } else {
        // Revert optimistic update on failure
        if (context?.previousProject) {
          queryClient.setQueryData(
            projectQueryKeys.detail(variables.projectId),
            context.previousProject
          )
        }

        if (config.showErrorNotifications !== false) {
          addNotification({
            type: "error",
            message: `Failed to assign team member: ${result.error}`,
            details: result.details,
          })
        }

        config.onError?.("assign-member", result.error)
      }
    },
    onError: (error, variables, context) => {
      // Revert optimistic update
      if (context?.previousProject) {
        queryClient.setQueryData(
          projectQueryKeys.detail(variables.projectId),
          context.previousProject
        )
      }

      if (config.showErrorNotifications !== false) {
        addNotification({
          type: "error",
          message: "Team member assignment failed",
          details: error.message,
        })
      }

      config.onError?.("assign-member", error)
    },
  })

  // Remove member mutation (simplified - would need a proper use case)
  const removeMemberMutation = useMutation({
    mutationFn: async (request: {
      projectId: string
      memberId: string
      removedBy: number
      reason?: string
    }): Promise<TeamOperationResult<Project>> => {
      // This would use a RemoveMemberFromProjectUseCase
      // For now, we'll simulate the operation
      const project = queryClient.getQueryData<Project>(
        projectQueryKeys.detail(request.projectId)
      )
      if (!project) {
        throw new Error("Project not found")
      }

      const updatedProject = project.removeTeamMember(
        request.memberId,
        request.removedBy,
        request.reason
      )

      return {
        success: true,
        data: updatedProject,
      }
    },
    onSuccess: (result, variables) => {
      if (result.success && result.data) {
        // Update cache
        queryClient.setQueryData(
          projectQueryKeys.detail(variables.projectId),
          result.data
        )

        // Add to recent activity
        addActivity({
          type: "member_removed",
          projectId: variables.projectId,
          projectName: result.data.name,
          userId: variables.removedBy,
          details: {
            removedMemberId: variables.memberId,
            reason: variables.reason,
          },
        })

        // Show success notification
        if (config.showSuccessNotifications !== false) {
          addNotification({
            type: "success",
            message: "Team member removed successfully",
          })
        }

        config.onSuccess?.("remove-member", result.data)
      }
    },
    onError: (error) => {
      if (config.showErrorNotifications !== false) {
        addNotification({
          type: "error",
          message: "Failed to remove team member",
          details: error.message,
        })
      }

      config.onError?.("remove-member", error)
    },
  })

  // Convenience methods
  const assignMember = useCallback(
    async (
      request: AssignMemberRequest
    ): Promise<
      TeamOperationResult<{ project: Project; member: ProjectMember }>
    > => {
      return await assignMemberMutation.mutateAsync(request)
    },
    [assignMemberMutation]
  )

  const removeMember = useCallback(
    async (request: {
      projectId: string
      memberId: string
      removedBy: number
      reason?: string
    }): Promise<TeamOperationResult<Project>> => {
      return await removeMemberMutation.mutateAsync(request)
    },
    [removeMemberMutation]
  )

  // Utility functions
  const getProjectMembers = useCallback(
    (projectId: string): ProjectMember[] => {
      const project = queryClient.getQueryData<Project>(
        projectQueryKeys.detail(projectId)
      )
      return project?.members || []
    },
    [queryClient]
  )

  const isUserProjectMember = useCallback(
    (projectId: string, userId: number): boolean => {
      const members = getProjectMembers(projectId)
      return members.some(
        (member) => member.userId === userId && member.isActive
      )
    },
    [getProjectMembers]
  )

  const getUserProjectRole = useCallback(
    (projectId: string, userId: number): string | null => {
      const members = getProjectMembers(projectId)
      const member = members.find(
        (member) => member.userId === userId && member.isActive
      )
      return member?.role.role || null
    },
    [getProjectMembers]
  )

  const getExpiredMembers = useCallback(
    (projectId: string): ProjectMember[] => {
      const members = getProjectMembers(projectId)
      return members.filter((member) => member.hasExpired())
    },
    [getProjectMembers]
  )

  const canUserManageTeam = useCallback(
    (projectId: string, userId: number): boolean => {
      const project = queryClient.getQueryData<Project>(
        projectQueryKeys.detail(projectId)
      )
      if (!project) return false

      // Project owner can always manage team
      if (project.ownerId === userId) return true

      // Check if user has management role
      const userMember = project.members.find(
        (member) => member.userId === userId && member.isActive
      )
      return userMember?.role.canManageProject() || false
    },
    [queryClient]
  )

  return {
    // Mutation functions
    assignMember,
    removeMember,

    // Loading states
    isAssigning: assignMemberMutation.isPending,
    isRemoving: removeMemberMutation.isPending,

    // Error states
    assignError: assignMemberMutation.error,
    removeError: removeMemberMutation.error,

    // Reset functions
    resetAssignState: assignMemberMutation.reset,
    resetRemoveState: removeMemberMutation.reset,

    // Utility functions
    getProjectMembers,
    isUserProjectMember,
    getUserProjectRole,
    getExpiredMembers,
    canUserManageTeam,

    // Combined states
    isAnyOperationPending:
      assignMemberMutation.isPending || removeMemberMutation.isPending,

    hasAnyError: !!(assignMemberMutation.error || removeMemberMutation.error),
  }
}
