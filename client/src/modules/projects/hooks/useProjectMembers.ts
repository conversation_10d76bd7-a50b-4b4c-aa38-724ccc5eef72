import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import {
  addProjectMember,
  getProjectMembers,
  removeProjectMember,
  updateProjectMember,
} from "../api/projectMemberApi"
import {
  ProjectMember,
  ProjectMemberCreate,
  ProjectMemberUpdate,
} from "@/types/api"

export const useProjectMembers = (projectId: number) => {
  const queryClient = useQueryClient()

  const {
    data: members,
    isLoading,
    error,
  } = useQuery<ProjectMember[], Error>({
    queryKey: ["projects", projectId, "members"],
    queryFn: () => getProjectMembers(projectId),
  })

  const addMemberMutation = useMutation<
    ProjectMember,
    Error,
    ProjectMemberCreate
  >({
    mutationFn: (memberData) => addProjectMember(projectId, memberData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["projects", projectId, "members"],
      })
    },
  })

  const removeMemberMutation = useMutation<void, Error, number>({
    mutationFn: (userId) => removeProjectMember(projectId, userId),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["projects", projectId, "members"],
      })
    },
  })

  const updateMemberMutation = useMutation<
    ProjectMember,
    Error,
    { memberId: number; memberData: ProjectMemberUpdate }
  >({
    mutationFn: ({ memberId, memberData }) =>
      updateProjectMember(memberId, memberData),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["projects", projectId, "members"],
      })
    },
  })

  return {
    members,
    isLoading,
    error,
    addMember: addMemberMutation.mutate,
    removeMember: removeMemberMutation.mutate,
    updateMember: updateMemberMutation.mutate,
  }
}
