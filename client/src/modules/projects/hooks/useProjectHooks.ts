/**
 * Project Hooks Factory
 *
 * Factory for creating properly configured domain-aware hooks with dependencies.
 * Provides a single entry point for all project-related hooks.
 */

import { useMemo } from "react"

import type { ProjectHooksFactory } from "."

import {
  createProjectHooks,
  defaultHookConfigs,
} from "."
import {
  ArchiveProjectUseCase,
  AssignMemberToProjectUseCase,
  ChangeProjectStatusUseCase,
  CreateProjectUseCase,
  UpdateProjectUseCase,
  ValidateProjectUseCase,
} from "../application"
import { ProjectValidationService } from "../domain/domain-services/ProjectValidationService"
import { TeamManagementService } from "../domain/domain-services/TeamManagementService"
import { useProjectRepository } from "../infrastructure"

/**
 * Configuration for the hooks factory
 */
export interface ProjectHooksConfig {
  enableOptimisticUpdates?: boolean
  showNotifications?: boolean
  enableAutoRefresh?: boolean
  refreshInterval?: number
}

/**
 * Default configuration
 */
const defaultConfig: ProjectHooksConfig = {
  enableOptimisticUpdates: true,
  showNotifications: true,
  enableAutoRefresh: false,
  refreshInterval: 30000,
}

/**
 * Main hook that provides access to all project-related hooks
 * with proper dependency injection and configuration
 */
export function useProjectHooks(config: Partial<ProjectHooksConfig> = {}) {
  const finalConfig = useMemo(() => ({ ...defaultConfig, ...config }), [config])

  // Infrastructure dependencies
  const repository = useProjectRepository()

  // Domain services (would normally be injected via DI container)
  const validationService = useMemo(() => new ProjectValidationService(), [])
  const teamManagementService = useMemo(() => new TeamManagementService(), [])

  // Use cases (would normally be injected via DI container)
  const useCases = useMemo(
    () => ({
      createProjectUseCase: new CreateProjectUseCase(
        repository.repository,
        validationService
      ),
      updateProjectUseCase: new UpdateProjectUseCase(
        repository.repository,
        validationService
      ),
      assignMemberToProjectUseCase: new AssignMemberToProjectUseCase(
        repository.repository,
        teamManagementService
      ),
      changeProjectStatusUseCase: new ChangeProjectStatusUseCase(
        repository.repository,
        validationService
      ),
      archiveProjectUseCase: new ArchiveProjectUseCase(
        repository.repository,
        validationService
      ),
      validateProjectUseCase: new ValidateProjectUseCase(
        repository.repository,
        validationService
      ),
    }),
    [repository.repository, validationService, teamManagementService]
  )

  // Hook factory configuration
  const factory: ProjectHooksFactory = useMemo(
    () => ({
      repository,
      useCases,
      services: {
        validationService,
      },
    }),
    [repository, useCases, validationService]
  )

  // Create configured hooks
  const hooks = useMemo(() => createProjectHooks(factory), [factory])

  // Hook configurations based on global config
  const operationsConfig = useMemo(
    () => ({
      ...defaultHookConfigs.operations,
      enableOptimisticUpdates: finalConfig.enableOptimisticUpdates,
      showSuccessNotifications: finalConfig.showNotifications,
      showErrorNotifications: finalConfig.showNotifications,
    }),
    [finalConfig]
  )

  const selectionConfig = useMemo(
    () => ({
      ...defaultHookConfigs.selection,
      enableAutoRefresh: finalConfig.enableAutoRefresh,
      refreshInterval: finalConfig.refreshInterval,
    }),
    [finalConfig]
  )

  const teamConfig = useMemo(
    () => ({
      ...defaultHookConfigs.team,
      enableOptimisticUpdates: finalConfig.enableOptimisticUpdates,
      showSuccessNotifications: finalConfig.showNotifications,
      showErrorNotifications: finalConfig.showNotifications,
    }),
    [finalConfig]
  )

  const statusConfig = useMemo(
    () => ({
      ...defaultHookConfigs.status,
      enableOptimisticUpdates: finalConfig.enableOptimisticUpdates,
      showSuccessNotifications: finalConfig.showNotifications,
      showErrorNotifications: finalConfig.showNotifications,
    }),
    [finalConfig]
  )

  return {
    // Core hooks with configuration
    useProjectOperations: (customConfig?: any) =>
      hooks.useProjectOperations({ ...operationsConfig, ...customConfig }),

    useProjectSelection: (initialFilters?: any, customConfig?: any) =>
      hooks.useProjectSelection(initialFilters, {
        ...selectionConfig,
        ...customConfig,
      }),

    useProjectForm: (initialProject?: any, customConfig?: any) =>
      hooks.useProjectForm(initialProject, {
        ...defaultHookConfigs.form,
        ...customConfig,
      }),

    useProjectTeam: (customConfig?: any) =>
      hooks.useProjectTeam({ ...teamConfig, ...customConfig }),

    useProjectStatus: (customConfig?: any) =>
      hooks.useProjectStatus({ ...statusConfig, ...customConfig }),

    // Utility hooks
    useProjectManagement: (projectId: string, userId: number) => {
      // This would use the composition utility from the hooks index
      const operations = hooks.useProjectOperations(operationsConfig)
      const team = hooks.useProjectTeam(teamConfig)
      const status = hooks.useProjectStatus(statusConfig)

      return {
        // Operations
        ...operations,

        // Team management
        assignMember: team.assignMember,
        removeMember: team.removeMember,
        canUserManageTeam: () => team.canUserManageTeam(projectId, userId),

        // Status management
        changeStatus: status.changeStatus,
        activateProject: () => status.activateProject(projectId, userId),
        pauseProject: () => status.pauseProject(projectId, userId),
        completeProject: () => status.completeProject(projectId, userId),
        cancelProject: () => status.cancelProject(projectId, userId),

        // Combined states
        isBusy:
          operations.isAnyOperationPending ||
          team.isAnyOperationPending ||
          status.isChangingStatus,

        hasErrors:
          operations.hasAnyError || team.hasAnyError || status.hasError,
      }
    },

    // Raw dependencies for advanced usage
    repository,
    useCases,
    services: {
      validationService,
      teamManagementService,
    },
  }
}

/**
 * Convenience hook for project list views
 */
export function useProjectListHooks(
  filters?: any,
  config?: Partial<ProjectHooksConfig>
) {
  const { useProjectSelection } = useProjectHooks(config)

  return useProjectSelection(filters, {
    selectionMode: "multiple",
    pagination: {
      pageSize: 20,
      sortBy: "updatedAt",
      sortOrder: "desc",
      enableInfiniteScroll: false,
    },
  })
}

/**
 * Convenience hook for project detail views
 */
export function useProjectDetailHooks(
  projectId: string,
  userId: number,
  config?: Partial<ProjectHooksConfig>
) {
  const { useProjectManagement } = useProjectHooks(config)

  return useProjectManagement(projectId, userId)
}

/**
 * Convenience hook for project form views
 */
export function useProjectFormHooks(
  initialProject?: any,
  config?: Partial<ProjectHooksConfig>
) {
  const { useProjectForm, useProjectOperations } = useProjectHooks(config)

  const form = useProjectForm(initialProject, {
    mode: initialProject ? "edit" : "create",
    validateOnChange: true,
    validateOnBlur: true,
    showWarnings: true,
  })

  const operations = useProjectOperations()

  return {
    ...form,
    ...operations,

    // Convenience submit method
    submitForm: async (userId: number) => {
      if (form.mode === "create") {
        const request = form.toCreateRequest()
        return await operations.createProject(request)
      } else {
        const request = form.toUpdateRequest(userId)
        if (request) {
          return await operations.updateProject(request)
        }
        return { success: false, error: "Invalid update request" }
      }
    },
  }
}
