/**
 * Project Selection Hook
 *
 * Domain-aware React hook for project selection, filtering, and query management.
 * Integrates with React Query for data fetching and Zustand for UI state management.
 */

import { useCallback, useMemo } from "react"

import { useInfiniteQuery, useQuery } from "@tanstack/react-query"

import { Project } from "../domain/entities/Project"
import {
  ProjectPaginationOptions,
  ProjectSearchCriteria,
} from "../domain/repositories/IProjectRepository"
import { projectQueryKeys, useProjectRepository } from "../infrastructure"
import { useProjectStore } from "../store/projectStore"

/**
 * Selection filters for project queries
 */
export interface ProjectSelectionFilters {
  // User-based filters
  ownerId?: number
  clientId?: number
  memberUserId?: number

  // Status and priority filters
  status?: string[]
  priority?: string[]

  // Date filters
  createdAfter?: string
  createdBefore?: string
  updatedAfter?: string
  updatedBefore?: string

  // Budget filters
  budgetMin?: number
  budgetMax?: number
  currency?: string
  isOverBudget?: boolean

  // Team filters
  hasExpiredMembers?: boolean
  minTeamSize?: number
  maxTeamSize?: number

  // Search filters
  searchTerm?: string
  tags?: string[]
  location?: string

  // Special filters
  requiresApproval?: boolean
  requiresAttention?: boolean
}

/**
 * Pagination configuration
 */
export interface PaginationConfig {
  pageSize: number
  sortBy: "name" | "createdAt" | "updatedAt" | "priority" | "status"
  sortOrder: "asc" | "desc"
  enableInfiniteScroll: boolean
}

/**
 * Selection mode configuration
 */
export type SelectionMode = "none" | "single" | "multiple"

/**
 * Hook configuration
 */
interface ProjectSelectionConfig {
  selectionMode: SelectionMode
  pagination: PaginationConfig
  enableAutoRefresh?: boolean
  refreshInterval?: number
  enableRealTimeUpdates?: boolean
}

/**
 * Default configuration
 */
const defaultConfig: ProjectSelectionConfig = {
  selectionMode: "single",
  pagination: {
    pageSize: 20,
    sortBy: "updatedAt",
    sortOrder: "desc",
    enableInfiniteScroll: false,
  },
  enableAutoRefresh: false,
  refreshInterval: 30000, // 30 seconds
  enableRealTimeUpdates: false,
}

/**
 * Hook for project selection and filtering
 */
export function useProjectSelection(
  repository: ReturnType<typeof useProjectRepository>,
  initialFilters: ProjectSelectionFilters = {},
  config: Partial<ProjectSelectionConfig> = {}
) {
  const finalConfig = { ...defaultConfig, ...config }
  const {
    filters,
    selectedProjectIds,
    selectedProject,
    updateFilters,
    selectProject,
    selectProjects,
    clearSelection,
    toggleProjectSelection,
  } = useProjectStore()

  // Merge initial filters with store filters
  const activeFilters = useMemo(
    () => ({
      ...initialFilters,
      ...filters,
    }),
    [initialFilters, filters]
  )

  // Convert filters to search criteria
  const searchCriteria = useMemo((): ProjectSearchCriteria => {
    const criteria: ProjectSearchCriteria = {}

    if (activeFilters.ownerId) criteria.ownerId = activeFilters.ownerId
    if (activeFilters.clientId) criteria.clientId = activeFilters.clientId
    if (activeFilters.memberUserId)
      criteria.memberUserId = activeFilters.memberUserId

    if (activeFilters.status?.length) criteria.status = activeFilters.status[0] // API typically supports single status
    if (activeFilters.priority?.length)
      criteria.priority = activeFilters.priority[0]

    if (activeFilters.createdAfter)
      criteria.createdAfter = activeFilters.createdAfter
    if (activeFilters.createdBefore)
      criteria.createdBefore = activeFilters.createdBefore

    if (activeFilters.budgetMin) criteria.budgetMin = activeFilters.budgetMin
    if (activeFilters.budgetMax) criteria.budgetMax = activeFilters.budgetMax
    if (activeFilters.currency) criteria.currency = activeFilters.currency
    if (activeFilters.isOverBudget !== undefined)
      criteria.isOverBudget = activeFilters.isOverBudget

    if (activeFilters.tags?.length) criteria.tags = activeFilters.tags
    if (activeFilters.location) criteria.location = activeFilters.location

    if (activeFilters.hasExpiredMembers !== undefined) {
      criteria.hasExpiredMembers = activeFilters.hasExpiredMembers
    }

    if (activeFilters.requiresApproval !== undefined) {
      criteria.requiresApproval = activeFilters.requiresApproval
    }

    return criteria
  }, [activeFilters])

  // Pagination options
  const paginationOptions = useMemo(
    (): ProjectPaginationOptions => ({
      limit: finalConfig.pagination.pageSize,
      offset: 0, // Will be managed by React Query
      sortBy: finalConfig.pagination.sortBy,
      sortOrder: finalConfig.pagination.sortOrder,
    }),
    [finalConfig.pagination]
  )

  // Main projects query
  const projectsQuery = useQuery({
    queryKey: projectQueryKeys.search(searchCriteria),
    queryFn: () =>
      repository.repository.findByCriteria(searchCriteria, paginationOptions),
    refetchInterval: finalConfig.enableAutoRefresh
      ? finalConfig.refreshInterval
      : false,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  })

  // Infinite query for infinite scroll
  const infiniteProjectsQuery = useInfiniteQuery({
    queryKey: [...projectQueryKeys.search(searchCriteria), "infinite"],
    queryFn: ({ pageParam = 0 }) => {
      const options = {
        ...paginationOptions,
        offset: pageParam * paginationOptions.limit!,
      }
      return repository.repository.findByCriteria(searchCriteria, options)
    },
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.hasMore ? allPages.length : undefined
    },
    initialPageParam: 0,
    enabled: finalConfig.pagination.enableInfiniteScroll,
  })

  // Use appropriate query based on configuration
  const activeQuery = finalConfig.pagination.enableInfiniteScroll
    ? infiniteProjectsQuery
    : projectsQuery

  // Extract projects from query result
  const projects = useMemo(() => {
    if (finalConfig.pagination.enableInfiniteScroll) {
      return (
        infiniteProjectsQuery.data?.pages.flatMap((page) => page.projects) || []
      )
    } else {
      return projectsQuery.data?.projects || []
    }
  }, [
    projectsQuery.data,
    infiniteProjectsQuery.data,
    finalConfig.pagination.enableInfiniteScroll,
  ])

  // Selected projects (resolved from IDs)
  const selectedProjects = useMemo(() => {
    if (finalConfig.selectionMode === "none") return []

    return projects.filter((project) => selectedProjectIds.includes(project.id))
  }, [projects, selectedProjectIds, finalConfig.selectionMode])

  // Filter management
  const updateFilter = useCallback(
    <K extends keyof ProjectSelectionFilters>(
      key: K,
      value: ProjectSelectionFilters[K]
    ) => {
      updateFilters({ [key]: value })
    },
    [updateFilters]
  )

  const clearFilters = useCallback(() => {
    updateFilters({})
  }, [updateFilters])

  const resetToInitialFilters = useCallback(() => {
    updateFilters(initialFilters)
  }, [updateFilters, initialFilters])

  // Selection management
  const handleProjectSelect = useCallback(
    (project: Project) => {
      if (finalConfig.selectionMode === "none") return

      if (finalConfig.selectionMode === "single") {
        selectProject(project)
      } else {
        toggleProjectSelection(project.id)
      }
    },
    [finalConfig.selectionMode, selectProject, toggleProjectSelection]
  )

  const handleMultipleSelect = useCallback(
    (projectIds: string[]) => {
      if (finalConfig.selectionMode === "multiple") {
        selectProjects(projectIds)
      }
    },
    [finalConfig.selectionMode, selectProjects]
  )

  // Quick filter presets
  const applyQuickFilter = useCallback(
    (preset: string) => {
      switch (preset) {
        case "my-projects":
          // This would need userId from context or props
          updateFilters({ ownerId: 0 }) // Replace with actual user ID
          break

        case "active-projects":
          updateFilters({ status: ["Active"] })
          break

        case "overdue-projects":
          updateFilters({ requiresAttention: true })
          break

        case "over-budget":
          updateFilters({ isOverBudget: true })
          break

        case "needs-attention":
          updateFilters({ requiresAttention: true })
          break

        case "completed-projects":
          updateFilters({ status: ["Completed"] })
          break

        default:
          clearFilters()
      }
    },
    [updateFilters, clearFilters]
  )

  // Search functionality
  const search = useCallback(
    (searchTerm: string) => {
      updateFilter("searchTerm", searchTerm)
    },
    [updateFilter]
  )

  // Statistics based on current results
  const statistics = useMemo(() => {
    const totalCount = finalConfig.pagination.enableInfiniteScroll
      ? infiniteProjectsQuery.data?.pages[0]?.totalCount || 0
      : projectsQuery.data?.totalCount || 0

    return {
      totalProjects: totalCount,
      visibleProjects: projects.length,
      selectedCount: selectedProjects.length,
      hasMore: finalConfig.pagination.enableInfiniteScroll
        ? infiniteProjectsQuery.hasNextPage
        : projectsQuery.data?.hasMore || false,
    }
  }, [
    projects.length,
    selectedProjects.length,
    projectsQuery.data,
    infiniteProjectsQuery.data,
    infiniteProjectsQuery.hasNextPage,
    finalConfig.pagination.enableInfiniteScroll,
  ])

  return {
    // Data
    projects,
    selectedProjects,
    selectedProject,
    statistics,

    // Query states
    isLoading: activeQuery.isLoading,
    isFetching: activeQuery.isFetching,
    isError: activeQuery.isError,
    error: activeQuery.error,

    // Infinite scroll specific
    hasNextPage: infiniteProjectsQuery.hasNextPage,
    isFetchingNextPage: infiniteProjectsQuery.isFetchingNextPage,
    fetchNextPage: infiniteProjectsQuery.fetchNextPage,

    // Filters
    activeFilters,
    updateFilter,
    updateFilters,
    clearFilters,
    resetToInitialFilters,
    applyQuickFilter,

    // Search
    search,

    // Selection
    selectProject: handleProjectSelect,
    selectProjects: handleMultipleSelect,
    clearSelection,
    toggleSelection: toggleProjectSelection,

    // Utilities
    refetch: activeQuery.refetch,
    refresh: () => activeQuery.refetch(),

    // Configuration
    selectionMode: finalConfig.selectionMode,
    pagination: finalConfig.pagination,
  }
}
