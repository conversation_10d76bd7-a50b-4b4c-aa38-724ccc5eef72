/**
 * Project Form Hook
 *
 * Domain-aware React hook for project form management with validation.
 * Provides form state management, domain validation, and submission handling.
 */

import { useCallback, useEffect, useState } from "react"

import { useDebounce } from "use-debounce"

import { CreateProjectRequest, UpdateProjectRequest } from "../application"
import { ProjectValidationService } from "../domain/domain-services/ProjectValidationService"
import { Project } from "../domain/entities/Project"

/**
 * Form field types
 */
export interface ProjectFormData {
  // Basic information
  name: string
  description?: string
  priority: "Low" | "Medium" | "High" | "Critical"

  // Ownership and client
  ownerId: number
  clientId?: number

  // Budget information
  budgetTotal?: number
  budgetCurrency?: string

  // Timeline
  startDate?: string
  endDate?: string

  // Additional information
  tags: string[]
  location?: string
  externalProjectId?: string
  requiresApproval: boolean
}

/**
 * Form validation result
 */
interface ValidationResult {
  isValid: boolean
  errors: Record<string, string[]>
  warnings: Record<string, string[]>
  fieldErrors: Record<keyof ProjectFormData, string[]>
}

/**
 * Form mode configuration
 */
export type FormMode = "create" | "edit" | "view"

/**
 * Form configuration options
 */
interface ProjectFormConfig {
  mode: FormMode
  enableAutoSave?: boolean
  autoSaveDelay?: number
  validateOnChange?: boolean
  validateOnBlur?: boolean
  showWarnings?: boolean
  enableOptimisticValidation?: boolean
}

/**
 * Form submission result
 */
interface SubmissionResult {
  success: boolean
  data?: Project
  errors?: Record<string, string[]>
  warnings?: string[]
}

/**
 * Default form configuration
 */
const defaultConfig: ProjectFormConfig = {
  mode: "create",
  enableAutoSave: false,
  autoSaveDelay: 2000,
  validateOnChange: true,
  validateOnBlur: true,
  showWarnings: true,
  enableOptimisticValidation: true,
}

/**
 * Initial form data
 */
const createInitialFormData = (project?: Project): ProjectFormData => ({
  name: project?.name || "",
  description: project?.description || "",
  priority: project?.priority || "Medium",
  ownerId: project?.ownerId || 0,
  clientId: project?.clientId,
  budgetTotal: project?.budget.totalAmount || undefined,
  budgetCurrency: project?.budget.currency || "EUR",
  startDate: project?.startDate || "",
  endDate: project?.endDate || "",
  tags: project?.tags || [],
  location: project?.location || "",
  externalProjectId: project?.externalProjectId || "",
  requiresApproval: project?.requiresApproval() || false,
})

/**
 * Hook for project form management
 */
export function useProjectForm(
  initialProject?: Project,
  validationService?: ProjectValidationService,
  config: Partial<ProjectFormConfig> = {}
) {
  const finalConfig = { ...defaultConfig, ...config }

  // Form state
  const [formData, setFormData] = useState<ProjectFormData>(() =>
    createInitialFormData(initialProject)
  )
  const [isDirty, setIsDirty] = useState(false)
  const [touchedFields, setTouchedFields] = useState<
    Set<keyof ProjectFormData>
  >(new Set())
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [validationResult, setValidationResult] = useState<ValidationResult>({
    isValid: true,
    errors: {},
    warnings: {},
    fieldErrors: {} as Record<keyof ProjectFormData, string[]>,
  })

  // Debounced form data for validation
  const [debouncedFormData] = useDebounce(formData, 300)

  // Reset form to initial state
  const resetForm = useCallback(() => {
    setFormData(createInitialFormData(initialProject))
    setIsDirty(false)
    setTouchedFields(new Set())
    setValidationResult({
      isValid: true,
      errors: {},
      warnings: {},
      fieldErrors: {} as Record<keyof ProjectFormData, string[]>,
    })
  }, [initialProject])

  // Update form field
  const updateField = useCallback(
    <K extends keyof ProjectFormData>(field: K, value: ProjectFormData[K]) => {
      setFormData((prev) => ({ ...prev, [field]: value }))
      setIsDirty(true)

      if (finalConfig.validateOnChange) {
        setTouchedFields((prev) => new Set(prev).add(field))
      }
    },
    [finalConfig.validateOnChange]
  )

  // Mark field as touched
  const touchField = useCallback((field: keyof ProjectFormData) => {
    setTouchedFields((prev) => new Set(prev).add(field))
  }, [])

  // Validate form data
  const validateForm = useCallback(
    async (
      data: ProjectFormData,
      validateAllFields = false
    ): Promise<ValidationResult> => {
      const errors: Record<string, string[]> = {}
      const warnings: Record<string, string[]> = {}
      const fieldErrors: Record<keyof ProjectFormData, string[]> = {} as any

      // Initialize field errors
      Object.keys(data).forEach((key) => {
        fieldErrors[key as keyof ProjectFormData] = []
      })

      // Basic field validation
      if (!data.name.trim()) {
        fieldErrors.name.push("Project name is required")
      } else if (data.name.length > 200) {
        fieldErrors.name.push("Project name cannot exceed 200 characters")
      }

      if (data.ownerId <= 0) {
        fieldErrors.ownerId.push("Valid owner is required")
      }

      if (data.description && data.description.length > 2000) {
        fieldErrors.description.push(
          "Description cannot exceed 2000 characters"
        )
      }

      if (data.clientId && data.clientId <= 0) {
        fieldErrors.clientId.push("Client ID must be positive when provided")
      }

      // Budget validation
      if (data.budgetTotal !== undefined) {
        if (data.budgetTotal <= 0) {
          fieldErrors.budgetTotal.push("Budget amount must be positive")
        }

        if (!data.budgetCurrency || data.budgetCurrency.length !== 3) {
          fieldErrors.budgetCurrency.push(
            "Valid 3-character currency code is required"
          )
        }
      }

      // Date validation
      if (data.startDate && data.endDate) {
        const startDate = new Date(data.startDate)
        const endDate = new Date(data.endDate)

        if (startDate >= endDate) {
          fieldErrors.endDate.push("End date must be after start date")
        }
      }

      // Tags validation
      if (data.tags.length > 10) {
        fieldErrors.tags.push("Maximum 10 tags allowed")
      }

      const invalidTags = data.tags.filter(
        (tag) => !tag.trim() || tag.length > 50
      )
      if (invalidTags.length > 0) {
        fieldErrors.tags.push("Tags must be non-empty and under 50 characters")
      }

      // Domain validation if service is available
      if (validationService && (validateAllFields || touchedFields.size > 0)) {
        try {
          // Create temporary project for validation
          const createData = {
            id: initialProject?.id || "temp",
            name: data.name,
            description: data.description,
            ownerId: data.ownerId,
            clientId: data.clientId,
            priority: data.priority,
            budgetData: data.budgetTotal
              ? {
                  totalAmount: data.budgetTotal,
                  currency: data.budgetCurrency || "EUR",
                }
              : undefined,
            startDate: data.startDate,
            endDate: data.endDate,
            tags: data.tags.filter(Boolean),
            location: data.location,
            externalProjectId: data.externalProjectId,
            requiresApproval: data.requiresApproval,
          }

          const tempProject = Project.create(createData)

          // Validate with domain service
          const domainValidation =
            await validationService.validateProjectCreation(tempProject)

          domainValidation.violations.forEach((violation) => {
            const field = violation.field as keyof ProjectFormData
            if (field && fieldErrors[field]) {
              fieldErrors[field].push(violation)
            } else {
              if (!errors.general) errors.general = []
              errors.general.push(violation)
            }
          })

          domainValidation.warnings.forEach((warning) => {
            if (!warnings.general) warnings.general = []
            warnings.general.push(warning)
          })
        } catch (domainError) {
          if (!errors.general) errors.general = []
          errors.general.push("Domain validation failed")
        }
      }

      // Determine overall validity
      const hasFieldErrors = Object.values(fieldErrors).some(
        (errs) => errs.length > 0
      )
      const hasGeneralErrors = Object.values(errors).some(
        (errs) => errs.length > 0
      )

      return {
        isValid: !hasFieldErrors && !hasGeneralErrors,
        errors,
        warnings,
        fieldErrors,
      }
    },
    [validationService, touchedFields, initialProject]
  )

  // Auto-validation effect
  useEffect(() => {
    if (finalConfig.validateOnChange && isDirty) {
      validateForm(debouncedFormData).then(setValidationResult)
    }
  }, [debouncedFormData, finalConfig.validateOnChange, isDirty, validateForm])

  // Convert form data to create/update request
  const toCreateRequest = useCallback(
    (): CreateProjectRequest => ({
      name: formData.name,
      description: formData.description || undefined,
      ownerId: formData.ownerId,
      clientId: formData.clientId || undefined,
      priority: formData.priority,
      budgetData: formData.budgetTotal
        ? {
            totalAmount: formData.budgetTotal,
            currency: formData.budgetCurrency || "EUR",
          }
        : undefined,
      startDate: formData.startDate || undefined,
      endDate: formData.endDate || undefined,
      tags: formData.tags.filter(Boolean),
      location: formData.location || undefined,
      externalProjectId: formData.externalProjectId || undefined,
      requiresApproval: formData.requiresApproval,
    }),
    [formData]
  )

  const toUpdateRequest = useCallback(
    (userId: number): UpdateProjectRequest | null => {
      if (!initialProject) return null

      return {
        projectId: initialProject.id,
        userId,
        updates: {
          name:
            formData.name !== initialProject.name ? formData.name : undefined,
          description:
            formData.description !== initialProject.description
              ? formData.description
              : undefined,
          priority:
            formData.priority !== initialProject.priority
              ? formData.priority
              : undefined,
          budgetData: (() => {
            const budgetChanged =
              formData.budgetTotal !== initialProject.budget.totalAmount ||
              formData.budgetCurrency !== initialProject.budget.currency

            return budgetChanged
              ? {
                  totalAmount: formData.budgetTotal,
                  currency: formData.budgetCurrency,
                }
              : undefined
          })(),
          startDate:
            formData.startDate !== initialProject.startDate
              ? formData.startDate
              : undefined,
          endDate:
            formData.endDate !== initialProject.endDate
              ? formData.endDate
              : undefined,
          tags:
            JSON.stringify(formData.tags) !==
            JSON.stringify(initialProject.tags)
              ? formData.tags
              : undefined,
          location:
            formData.location !== initialProject.location
              ? formData.location
              : undefined,
          requiresApproval:
            formData.requiresApproval !== initialProject.requiresApproval()
              ? formData.requiresApproval
              : undefined,
        },
      }
    },
    [formData, initialProject]
  )

  // Submit form
  const submit = useCallback(
    async (
      submitFn: (
        data: CreateProjectRequest | UpdateProjectRequest
      ) => Promise<SubmissionResult>,
      userId?: number
    ): Promise<SubmissionResult> => {
      setIsSubmitting(true)

      try {
        // Validate all fields before submission
        const validation = await validateForm(formData, true)
        setValidationResult(validation)

        if (!validation.isValid) {
          return {
            success: false,
            errors: { ...validation.errors, ...validation.fieldErrors },
          }
        }

        // Prepare request based on mode
        let request: CreateProjectRequest | UpdateProjectRequest

        if (finalConfig.mode === "create") {
          request = toCreateRequest()
        } else if (finalConfig.mode === "edit" && userId) {
          const updateRequest = toUpdateRequest(userId)
          if (!updateRequest) {
            return {
              success: false,
              errors: {
                general: [
                  "Cannot create update request - original project not found",
                ],
              },
            }
          }
          request = updateRequest
        } else {
          return {
            success: false,
            errors: { general: ["Invalid form mode or missing user ID"] },
          }
        }

        // Submit the request
        const result = await submitFn(request)

        if (result.success) {
          setIsDirty(false)
          setTouchedFields(new Set())
        }

        return result
      } finally {
        setIsSubmitting(false)
      }
    },
    [formData, finalConfig.mode, validateForm, toCreateRequest, toUpdateRequest]
  )

  // Get field error
  const getFieldError = useCallback(
    (field: keyof ProjectFormData): string | undefined => {
      const errors = validationResult.fieldErrors[field]
      return errors && errors.length > 0 ? errors[0] : undefined
    },
    [validationResult.fieldErrors]
  )

  // Get field warning
  const getFieldWarning = useCallback(
    (field: keyof ProjectFormData): string | undefined => {
      const warnings = validationResult.warnings[field as string]
      return warnings && warnings.length > 0 ? warnings[0] : undefined
    },
    [validationResult.warnings]
  )

  // Check if field has been touched
  const isFieldTouched = useCallback(
    (field: keyof ProjectFormData): boolean => {
      return touchedFields.has(field)
    },
    [touchedFields]
  )

  return {
    // Form data
    formData,
    updateField,
    resetForm,

    // Validation
    validationResult,
    isValid: validationResult.isValid,
    getFieldError,
    getFieldWarning,

    // Field state
    touchField,
    isFieldTouched,
    touchedFields: Array.from(touchedFields),

    // Form state
    isDirty,
    isSubmitting,
    mode: finalConfig.mode,

    // Actions
    submit,
    toCreateRequest,
    toUpdateRequest,

    // Utilities
    validateForm: () => validateForm(formData, true),

    // Helper getters
    hasErrors: !validationResult.isValid,
    errorCount: Object.values(validationResult.fieldErrors).reduce(
      (count, errors) => count + errors.length,
      0
    ),
    warningCount: Object.values(validationResult.warnings).reduce(
      (count, warnings) => count + warnings.length,
      0
    ),
  }
}
