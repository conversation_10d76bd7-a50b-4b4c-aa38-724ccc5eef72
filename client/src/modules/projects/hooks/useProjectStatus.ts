/**
 * Project Status Management Hook
 *
 * Domain-aware React hook for project status transitions.
 * Handles status changes with business rule validation and permission checks.
 */

import { useCallback } from "react"

import type { ChangeProjectStatusRequest } from "../application"

import { useMutation, useQueryClient } from "@tanstack/react-query"

import { ChangeProjectStatusUseCase, isSuccessResult } from "../application"
import { Project } from "../domain/entities/Project"
import { ProjectStatus } from "../domain/value-objects/ProjectStatus"
import { projectQueryKeys } from "../infrastructure"
import { useProjectStore } from "../store/projectStore"

/**
 * Configuration for status operations
 */
interface StatusOperationsConfig {
  enableOptimisticUpdates?: boolean
  showSuccessNotifications?: boolean
  showErrorNotifications?: boolean
  onSuccess?: (operation: string, data: any) => void
  onError?: (operation: string, error: any) => void
}

/**
 * Result type for status operations
 */
interface StatusOperationResult {
  success: boolean
  project?: Project
  previousStatus?: ProjectStatus
  newStatus?: ProjectStatus
  error?: string
  details?: Record<string, any>
}

/**
 * Status transition configuration
 */
interface StatusTransition {
  from: string
  to: string
  label: string
  requiresConfirmation?: boolean
  confirmationMessage?: string
  icon?: string
  variant?: "default" | "warning" | "destructive"
}

/**
 * Predefined status transitions
 */
const STATUS_TRANSITIONS: StatusTransition[] = [
  {
    from: "Draft",
    to: "Active",
    label: "Activate Project",
    icon: "play",
    variant: "default",
  },
  {
    from: "Active",
    to: "Paused",
    label: "Pause Project",
    icon: "pause",
    variant: "warning",
  },
  {
    from: "Paused",
    to: "Active",
    label: "Resume Project",
    icon: "play",
    variant: "default",
  },
  {
    from: "Active",
    to: "Completed",
    label: "Complete Project",
    requiresConfirmation: true,
    confirmationMessage:
      "Are you sure you want to mark this project as completed? This action cannot be easily undone.",
    icon: "check",
    variant: "default",
  },
  {
    from: "Draft",
    to: "Cancelled",
    label: "Cancel Project",
    requiresConfirmation: true,
    confirmationMessage: "Are you sure you want to cancel this project?",
    icon: "x",
    variant: "destructive",
  },
  {
    from: "Active",
    to: "Cancelled",
    label: "Cancel Project",
    requiresConfirmation: true,
    confirmationMessage: "Are you sure you want to cancel this active project?",
    icon: "x",
    variant: "destructive",
  },
  {
    from: "Paused",
    to: "Cancelled",
    label: "Cancel Project",
    requiresConfirmation: true,
    confirmationMessage: "Are you sure you want to cancel this project?",
    icon: "x",
    variant: "destructive",
  },
]

/**
 * Hook for project status management
 */
export function useProjectStatus(
  useCases: {
    changeProjectStatusUseCase: ChangeProjectStatusUseCase
  },
  config: StatusOperationsConfig = {}
) {
  const queryClient = useQueryClient()
  const { addNotification, addActivity, openConfirmDialog } = useProjectStore()

  // Change status mutation
  const changeStatusMutation = useMutation({
    mutationFn: async (
      request: ChangeProjectStatusRequest
    ): Promise<StatusOperationResult> => {
      const result = await useCases.changeProjectStatusUseCase.execute(request)

      if (isSuccessResult(result)) {
        return {
          success: true,
          project: result.data.project,
          previousStatus: result.data.previousStatus,
          newStatus: result.data.newStatus,
          details: {
            transitionValidated: result.data.transitionValidated,
          },
        }
      } else {
        return {
          success: false,
          error: result.error.message,
          details: result.error.details,
        }
      }
    },
    onMutate: async (variables) => {
      if (!config.enableOptimisticUpdates) return

      // Cancel outgoing refetches
      await queryClient.cancelQueries({
        queryKey: projectQueryKeys.detail(variables.projectId),
      })

      // Snapshot previous value
      const previousProject = queryClient.getQueryData<Project>(
        projectQueryKeys.detail(variables.projectId)
      )

      // Optimistically update status
      if (previousProject) {
        queryClient.setQueryData(
          projectQueryKeys.detail(variables.projectId),
          (old: Project) => {
            // Apply optimistic status change
            const newStatus = ProjectStatus.create({
              status: variables.newStatus,
            })
            return { ...old, status: newStatus }
          }
        )
      }

      return { previousProject }
    },
    onSuccess: (result, variables, context) => {
      if (result.success && result.project) {
        // Update cache with server response
        queryClient.setQueryData(
          projectQueryKeys.detail(variables.projectId),
          result.project
        )

        // Invalidate related queries
        queryClient.invalidateQueries({
          queryKey: projectQueryKeys.byStatus(variables.newStatus),
        })
        if (result.previousStatus) {
          queryClient.invalidateQueries({
            queryKey: projectQueryKeys.byStatus(result.previousStatus.status),
          })
        }

        // Add to recent activity
        addActivity({
          type: "status_changed",
          projectId: variables.projectId,
          projectName: result.project.name,
          userId: variables.changedBy,
          details: {
            previousStatus: result.previousStatus?.status,
            newStatus: result.newStatus?.status,
            reason: variables.reason,
          },
        })

        // Show success notification
        if (config.showSuccessNotifications !== false) {
          const statusText = `${result.previousStatus?.status} → ${result.newStatus?.status}`
          addNotification({
            type: "success",
            message: `Project status changed successfully`,
            details: statusText,
          })
        }

        config.onSuccess?.("change-status", result)
      } else {
        // Revert optimistic update on failure
        if (context?.previousProject) {
          queryClient.setQueryData(
            projectQueryKeys.detail(variables.projectId),
            context.previousProject
          )
        }

        if (config.showErrorNotifications !== false) {
          addNotification({
            type: "error",
            message: `Failed to change project status: ${result.error}`,
            details: result.details,
          })
        }

        config.onError?.("change-status", result.error)
      }
    },
    onError: (error, variables, context) => {
      // Revert optimistic update
      if (context?.previousProject) {
        queryClient.setQueryData(
          projectQueryKeys.detail(variables.projectId),
          context.previousProject
        )
      }

      if (config.showErrorNotifications !== false) {
        addNotification({
          type: "error",
          message: "Project status change failed",
          details: error.message,
        })
      }

      config.onError?.("change-status", error)
    },
  })

  // Get available transitions for a project
  const getAvailableTransitions = useCallback(
    (project: Project): StatusTransition[] => {
      const currentStatus = project.status.status
      return STATUS_TRANSITIONS.filter(
        (transition) => transition.from === currentStatus
      )
    },
    []
  )

  // Check if transition is available
  const canTransitionTo = useCallback(
    (project: Project, newStatus: string): boolean => {
      const availableTransitions = getAvailableTransitions(project)
      return availableTransitions.some((t) => t.to === newStatus)
    },
    [getAvailableTransitions]
  )

  // Get transition configuration
  const getTransition = useCallback(
    (fromStatus: string, toStatus: string): StatusTransition | undefined => {
      return STATUS_TRANSITIONS.find(
        (t) => t.from === fromStatus && t.to === toStatus
      )
    },
    []
  )

  // Change status with optional confirmation
  const changeStatus = useCallback(
    async (
      projectId: string,
      newStatus: string,
      changedBy: number,
      reason?: string,
      force?: boolean
    ): Promise<StatusOperationResult> => {
      const project = queryClient.getQueryData<Project>(
        projectQueryKeys.detail(projectId)
      )
      if (!project) {
        return {
          success: false,
          error: "Project not found",
        }
      }

      const transition = getTransition(project.status.status, newStatus)

      // Show confirmation dialog if required
      if (transition?.requiresConfirmation && !force) {
        return new Promise((resolve) => {
          openConfirmDialog({
            isOpen: true,
            title: transition.label,
            message:
              transition.confirmationMessage ||
              `Are you sure you want to change the status to ${newStatus}?`,
            confirmText: transition.label,
            variant: transition.variant,
            onConfirm: async () => {
              const result = await changeStatusMutation.mutateAsync({
                projectId,
                newStatus,
                changedBy,
                reason,
                force: true,
              })
              resolve(result)
            },
            onCancel: () => {
              resolve({
                success: false,
                error: "User cancelled operation",
              })
            },
          })
        })
      }

      // Execute status change
      return await changeStatusMutation.mutateAsync({
        projectId,
        newStatus,
        changedBy,
        reason,
        force,
      })
    },
    [queryClient, getTransition, openConfirmDialog, changeStatusMutation]
  )

  // Convenience methods for specific status changes
  const activateProject = useCallback(
    (projectId: string, changedBy: number, reason?: string) =>
      changeStatus(projectId, "Active", changedBy, reason),
    [changeStatus]
  )

  const pauseProject = useCallback(
    (projectId: string, changedBy: number, reason?: string) =>
      changeStatus(projectId, "Paused", changedBy, reason),
    [changeStatus]
  )

  const completeProject = useCallback(
    (projectId: string, changedBy: number, reason?: string) =>
      changeStatus(projectId, "Completed", changedBy, reason),
    [changeStatus]
  )

  const cancelProject = useCallback(
    (projectId: string, changedBy: number, reason?: string) =>
      changeStatus(projectId, "Cancelled", changedBy, reason),
    [changeStatus]
  )

  // Utility functions
  const getProjectStatus = useCallback(
    (projectId: string): ProjectStatus | null => {
      const project = queryClient.getQueryData<Project>(
        projectQueryKeys.detail(projectId)
      )
      return project?.status || null
    },
    [queryClient]
  )

  const isProjectInStatus = useCallback(
    (projectId: string, status: string): boolean => {
      const projectStatus = getProjectStatus(projectId)
      return projectStatus?.status === status
    },
    [getProjectStatus]
  )

  const getStatusHistory = useCallback(
    (
      projectId: string
    ): Array<{ status: string; timestamp: string; reason?: string }> => {
      // This would typically come from the project's status history
      // For now, we'll return an empty array
      return []
    },
    []
  )

  return {
    // Mutation functions
    changeStatus,
    activateProject,
    pauseProject,
    completeProject,
    cancelProject,

    // Loading state
    isChangingStatus: changeStatusMutation.isPending,

    // Error state
    changeStatusError: changeStatusMutation.error,

    // Reset function
    resetStatusState: changeStatusMutation.reset,

    // Utility functions
    getAvailableTransitions,
    canTransitionTo,
    getTransition,
    getProjectStatus,
    isProjectInStatus,
    getStatusHistory,

    // Configuration
    statusTransitions: STATUS_TRANSITIONS,

    // State information
    hasError: !!changeStatusMutation.error,
  }
}
