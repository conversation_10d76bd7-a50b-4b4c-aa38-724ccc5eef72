# Projects Module Hooks

This directory contains domain-aware React hooks that provide a clean, idiomatic React interface for components to interact with the Projects module's domain logic.

## Directory Structure

```
hooks/
├── useProjectOperations.ts     # Core project CRUD operations
├── useProjectSelection.ts      # Project selection and filtering
├── useProjectForm.ts          # Form management with validation
├── useProjectTeam.ts          # Team member management
├── useProjectStatus.ts        # Project status transitions
├── useProjectValidation.ts    # Project validation workflows
├── useProjectSearch.ts        # Search and query capabilities
├── __tests__/                # Hook behavior tests
└── index.ts                   # Hook exports
```

## Design Principles

### Domain-Aware
All hooks work exclusively with domain entities and value objects, maintaining the domain model integrity at the presentation layer.

### React Query Integration
Hooks leverage React Query for data fetching, caching, and synchronization with server state.

### Zustand Integration
Local UI state and cross-component coordination is managed through Zustand stores.

### Error Handling
Comprehensive error handling with user-friendly error messages and recovery strategies.

### Loading States
Proper loading state management with optimistic updates where appropriate.

## Hook Categories

### Operation Hooks
- **useProjectOperations**: Core CRUD operations (create, update, delete)
- **useProjectStatus**: Status transition operations
- **useProjectTeam**: Team member management operations

### Query Hooks
- **useProjectSelection**: Project filtering and selection
- **useProjectSearch**: Advanced search capabilities
- **useProjectValidation**: Validation and business rule checking

### Form Hooks
- **useProjectForm**: Form state management with domain validation
- Form validation using domain business rules
- Optimistic updates and error recovery

## Usage Patterns

### Basic Operation
```typescript
const { createProject, isCreating, error } = useProjectOperations()

const handleCreate = async (data) => {
  const result = await createProject(data)
  if (result.success) {
    // Handle success
  }
}
```

### Form Management
```typescript
const { 
  formData, 
  errors, 
  isValid, 
  updateField, 
  submit 
} = useProjectForm(initialData)
```

### Selection and Filtering
```typescript
const { 
  selectedProjects, 
  filters, 
  updateFilters, 
  clearSelection 
} = useProjectSelection()
```