/**
 * Project Operations Hook Tests
 *
 * Comprehensive tests for the useProjectOperations hook including
 * React Query integration, optimistic updates, and error handling.
 */

import React from "react"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { renderHook, waitFor } from "@testing-library/react"
import { beforeEach, describe, expect, it, Mock, vi } from "vitest"

import {
  ArchiveProjectUseCase,
  CreateProjectUseCase,
  UpdateProjectUseCase,
} from "../../application"
import { Project } from "../../domain/entities/Project"
import { useProjectStore } from "../../store/projectStore"
import { useProjectOperations } from "../useProjectOperations"

// Mock the store
vi.mock("../../store/projectStore", () => ({
  useProjectStore: vi.fn(() => ({
    addNotification: vi.fn(),
    setSelectedProject: vi.fn(),
    clearSelection: vi.fn(),
    getState: vi.fn(() => ({
      selectedProject: null,
    })),
  })),
}))

// Mock use cases
const mockCreateProjectUseCase = {
  execute: vi.fn(),
} as unknown as CreateProjectUseCase

const mockUpdateProjectUseCase = {
  execute: vi.fn(),
} as unknown as UpdateProjectUseCase

const mockArchiveProjectUseCase = {
  execute: vi.fn(),
} as unknown as ArchiveProjectUseCase

describe("useProjectOperations", () => {
  let queryClient: QueryClient
  let mockAddNotification: Mock
  let mockSetSelectedProject: Mock
  let mockClearSelection: Mock

  // Wrapper component for React Query
  const createWrapper = () => {
    const TestWrapper = ({ children }: { children: React.ReactNode }) => (
      <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
    )
    TestWrapper.displayName = 'TestWrapper'
    return TestWrapper
  }

  beforeEach(() => {
    vi.clearAllMocks()

    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
        mutations: {
          retry: false,
        },
      },
    })

    mockAddNotification = vi.fn()
    mockSetSelectedProject = vi.fn()
    mockClearSelection = vi.fn()

    // Setup store mock
    const mockStoreReturn = {
      addNotification: mockAddNotification,
      setSelectedProject: mockSetSelectedProject,
      clearSelection: mockClearSelection,
      getState: vi.fn(() => ({
        selectedProject: null,
      })),
    }

    ;(useProjectStore as Mock).mockReturnValue(mockStoreReturn)
    mockStoreReturn.getState.mockReturnValue({ selectedProject: null })
  })

  describe("Project Creation", () => {
    it("should create project successfully", async () => {
      const testProject = Project.create({
        id: "test-project-id",
        name: "Test Project",
        ownerId: 1,
      })

      const mockExecute = mockCreateProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: true,
        data: {
          project: testProject,
          validationWarnings: [],
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations({
            createProjectUseCase: mockCreateProjectUseCase,
            updateProjectUseCase: mockUpdateProjectUseCase,
            archiveProjectUseCase: mockArchiveProjectUseCase,
          }),
        { wrapper: createWrapper() }
      )

      const createResult = await result.current.createProject({
        name: "Test Project",
        ownerId: 1,
      })

      expect(createResult.success).toBe(true)
      expect(createResult.data).toEqual(testProject)
      expect(mockExecute).toHaveBeenCalledWith({
        name: "Test Project",
        ownerId: 1,
      })
      expect(mockSetSelectedProject).toHaveBeenCalledWith(testProject)
      expect(mockAddNotification).toHaveBeenCalledWith({
        type: "success",
        message: 'Project "Test Project" created successfully',
        details: undefined,
      })
    })

    it("should handle creation errors", async () => {
      const mockExecute = mockCreateProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: false,
        error: {
          message: "Project name already exists",
          details: { field: "name" },
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations({
            createProjectUseCase: mockCreateProjectUseCase,
            updateProjectUseCase: mockUpdateProjectUseCase,
            archiveProjectUseCase: mockArchiveProjectUseCase,
          }),
        { wrapper: createWrapper() }
      )

      const createResult = await result.current.createProject({
        name: "Duplicate Project",
        ownerId: 1,
      })

      expect(createResult.success).toBe(false)
      expect(createResult.error).toBe("Project name already exists")
      expect(mockAddNotification).toHaveBeenCalledWith({
        type: "error",
        message: "Failed to create project: Project name already exists",
        details: { field: "name" },
      })
    })

    it("should show validation warnings", async () => {
      const testProject = Project.create({
        id: "test-project-id",
        name: "Test Project",
        ownerId: 1,
      })

      const mockExecute = mockCreateProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: true,
        data: {
          project: testProject,
          validationWarnings: ["Consider adding a project description"],
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations({
            createProjectUseCase: mockCreateProjectUseCase,
            updateProjectUseCase: mockUpdateProjectUseCase,
            archiveProjectUseCase: mockArchiveProjectUseCase,
          }),
        { wrapper: createWrapper() }
      )

      await result.current.createProject({
        name: "Test Project",
        ownerId: 1,
      })

      expect(mockAddNotification).toHaveBeenCalledWith({
        type: "success",
        message: 'Project "Test Project" created successfully',
        details: "1 warnings",
      })
    })
  })

  describe("Project Updates", () => {
    it("should update project successfully", async () => {
      const updatedProject = Project.create({
        id: "test-project-id",
        name: "Updated Project Name",
        ownerId: 1,
      })

      const mockExecute = mockUpdateProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: true,
        data: {
          project: updatedProject,
          updatedFields: ["name"],
          validationWarnings: [],
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations({
            createProjectUseCase: mockCreateProjectUseCase,
            updateProjectUseCase: mockUpdateProjectUseCase,
            archiveProjectUseCase: mockArchiveProjectUseCase,
          }),
        { wrapper: createWrapper() }
      )

      const updateResult = await result.current.updateProject({
        projectId: "test-project-id",
        userId: 1,
        updates: { name: "Updated Project Name" },
      })

      expect(updateResult.success).toBe(true)
      expect(updateResult.data).toEqual(updatedProject)
      expect(mockAddNotification).toHaveBeenCalledWith({
        type: "success",
        message: "Updated name successfully",
        details: undefined,
      })
    })

    it("should handle update errors with optimistic rollback", async () => {
      // Set up existing project in cache
      const existingProject = Project.create({
        id: "test-project-id",
        name: "Original Name",
        ownerId: 1,
      })

      queryClient.setQueryData(
        ["projects", "detail", "test-project-id"],
        existingProject
      )

      const mockExecute = mockUpdateProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: false,
        error: {
          message: "Permission denied",
          details: { userId: 1 },
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations(
            {
              createProjectUseCase: mockCreateProjectUseCase,
              updateProjectUseCase: mockUpdateProjectUseCase,
              archiveProjectUseCase: mockArchiveProjectUseCase,
            },
            { enableOptimisticUpdates: true }
          ),
        { wrapper: createWrapper() }
      )

      const updateResult = await result.current.updateProject({
        projectId: "test-project-id",
        userId: 1,
        updates: { name: "New Name" },
      })

      expect(updateResult.success).toBe(false)
      expect(updateResult.error).toBe("Permission denied")

      // Verify cache was rolled back
      const cachedProject = queryClient.getQueryData([
        "projects",
        "detail",
        "test-project-id",
      ])
      expect(cachedProject).toEqual(existingProject)

      expect(mockAddNotification).toHaveBeenCalledWith({
        type: "error",
        message: "Failed to update project: Permission denied",
        details: { userId: 1 },
      })
    })

    it("should update selected project when it matches updated project", async () => {
      const originalProject = Project.create({
        id: "test-project-id",
        name: "Original Name",
        ownerId: 1,
      })

      const updatedProject = Project.create({
        id: "test-project-id",
        name: "Updated Name",
        ownerId: 1,
      })

      // Mock store to return the original project as selected
      const mockGetState = vi.fn(() => ({
        selectedProject: originalProject,
      }))

      ;(useProjectStore as Mock).mockReturnValue({
        addNotification: mockAddNotification,
        setSelectedProject: mockSetSelectedProject,
        clearSelection: mockClearSelection,
        selectedProject: originalProject,
        getState: mockGetState,
      })

      const mockExecute = mockUpdateProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: true,
        data: {
          project: updatedProject,
          updatedFields: ["name"],
          validationWarnings: [],
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations({
            createProjectUseCase: mockCreateProjectUseCase,
            updateProjectUseCase: mockUpdateProjectUseCase,
            archiveProjectUseCase: mockArchiveProjectUseCase,
          }),
        { wrapper: createWrapper() }
      )

      await result.current.updateProject({
        projectId: "test-project-id",
        userId: 1,
        updates: { name: "Updated Name" },
      })

      expect(mockSetSelectedProject).toHaveBeenCalledWith(updatedProject)
    })
  })

  describe("Project Archival", () => {
    it("should archive projects successfully", async () => {
      const mockExecute = mockArchiveProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: true,
        data: {
          archivedProjectIds: ["project-1", "project-2"],
          skippedProjectIds: [],
          validationIssues: undefined,
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations({
            createProjectUseCase: mockCreateProjectUseCase,
            updateProjectUseCase: mockUpdateProjectUseCase,
            archiveProjectUseCase: mockArchiveProjectUseCase,
          }),
        { wrapper: createWrapper() }
      )

      const archiveResult = await result.current.archiveProjects({
        projectIds: ["project-1", "project-2"],
        archivedBy: 1,
      })

      expect(archiveResult.success).toBe(true)
      expect(archiveResult.data).toEqual(["project-1", "project-2"])
      expect(mockAddNotification).toHaveBeenCalledWith({
        type: "success",
        message: "2 projects archived successfully",
        details: undefined,
      })
    })

    it("should handle partial archival with skipped projects", async () => {
      const mockExecute = mockArchiveProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: true,
        data: {
          archivedProjectIds: ["project-1"],
          skippedProjectIds: ["project-2"],
          validationIssues: [
            {
              projectId: "project-2",
              issues: ["Project is not completed"],
            },
          ],
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations({
            createProjectUseCase: mockCreateProjectUseCase,
            updateProjectUseCase: mockUpdateProjectUseCase,
            archiveProjectUseCase: mockArchiveProjectUseCase,
          }),
        { wrapper: createWrapper() }
      )

      const archiveResult = await result.current.archiveProjects({
        projectIds: ["project-1", "project-2"],
        archivedBy: 1,
      })

      expect(archiveResult.success).toBe(true)
      expect(archiveResult.data).toEqual(["project-1"])
      expect(mockAddNotification).toHaveBeenCalledWith({
        type: "success",
        message: "1 project archived successfully, 1 skipped",
        details: "Some validation issues found",
      })
    })

    it("should clear selection when selected project is archived", async () => {
      const selectedProject = Project.create({
        id: "project-1",
        name: "Selected Project",
        ownerId: 1,
      })

      const mockGetState = vi.fn(() => ({
        selectedProject,
      }))

      ;(useProjectStore as Mock).mockReturnValue({
        addNotification: mockAddNotification,
        setSelectedProject: mockSetSelectedProject,
        clearSelection: mockClearSelection,
        selectedProject,
        getState: mockGetState,
      })

      const mockExecute = mockArchiveProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: true,
        data: {
          archivedProjectIds: ["project-1"],
          skippedProjectIds: [],
          validationIssues: undefined,
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations({
            createProjectUseCase: mockCreateProjectUseCase,
            updateProjectUseCase: mockUpdateProjectUseCase,
            archiveProjectUseCase: mockArchiveProjectUseCase,
          }),
        { wrapper: createWrapper() }
      )

      await result.current.archiveProjects({
        projectIds: ["project-1"],
        archivedBy: 1,
      })

      expect(mockClearSelection).toHaveBeenCalled()
    })
  })

  describe("Loading States", () => {
    it("should track loading states correctly", async () => {
      let resolveCreate: (value: any) => void
      const createPromise = new Promise((resolve) => {
        resolveCreate = resolve
      })

      const mockExecute = mockCreateProjectUseCase.execute as Mock
      mockExecute.mockReturnValue(createPromise)

      const { result } = renderHook(
        () =>
          useProjectOperations({
            createProjectUseCase: mockCreateProjectUseCase,
            updateProjectUseCase: mockUpdateProjectUseCase,
            archiveProjectUseCase: mockArchiveProjectUseCase,
          }),
        { wrapper: createWrapper() }
      )

      // Start operation
      const createPromiseResult = result.current.createProject({
        name: "Test Project",
        ownerId: 1,
      })

      // Check loading state
      await waitFor(() => {
        expect(result.current.isCreating).toBe(true)
        expect(result.current.isAnyOperationPending).toBe(true)
      })

      // Complete operation
      resolveCreate!({
        success: true,
        data: {
          project: Project.create({
            id: "test-id",
            name: "Test Project",
            ownerId: 1,
          }),
          validationWarnings: [],
        },
      })

      await waitFor(() => {
        expect(result.current.isCreating).toBe(false)
        expect(result.current.isAnyOperationPending).toBe(false)
      })

      await createPromiseResult
    })
  })

  describe("Configuration Options", () => {
    it("should respect notification configuration", async () => {
      const testProject = Project.create({
        id: "test-project-id",
        name: "Test Project",
        ownerId: 1,
      })

      const mockExecute = mockCreateProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: true,
        data: {
          project: testProject,
          validationWarnings: [],
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations(
            {
              createProjectUseCase: mockCreateProjectUseCase,
              updateProjectUseCase: mockUpdateProjectUseCase,
              archiveProjectUseCase: mockArchiveProjectUseCase,
            },
            { showSuccessNotifications: false }
          ),
        { wrapper: createWrapper() }
      )

      await result.current.createProject({
        name: "Test Project",
        ownerId: 1,
      })

      expect(mockAddNotification).not.toHaveBeenCalled()
    })

    it("should call custom success callback", async () => {
      const testProject = Project.create({
        id: "test-project-id",
        name: "Test Project",
        ownerId: 1,
      })

      const mockOnSuccess = vi.fn()
      const mockExecute = mockCreateProjectUseCase.execute as Mock
      mockExecute.mockResolvedValue({
        success: true,
        data: {
          project: testProject,
          validationWarnings: [],
        },
      })

      const { result } = renderHook(
        () =>
          useProjectOperations(
            {
              createProjectUseCase: mockCreateProjectUseCase,
              updateProjectUseCase: mockUpdateProjectUseCase,
              archiveProjectUseCase: mockArchiveProjectUseCase,
            },
            { onSuccess: mockOnSuccess }
          ),
        { wrapper: createWrapper() }
      )

      await result.current.createProject({
        name: "Test Project",
        ownerId: 1,
      })

      expect(mockOnSuccess).toHaveBeenCalledWith("create", testProject)
    })
  })
})
