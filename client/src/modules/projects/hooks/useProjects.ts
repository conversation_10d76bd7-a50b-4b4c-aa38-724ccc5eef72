import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"

import {
  createProject,
  deleteProject,
  getProject,
  getProjects,
  updateProject,
} from "../api/projectApi"
import { Project, ProjectCreate, ProjectUpdate } from "@/types/api"

export const useProjects = () => {
  const queryClient = useQueryClient()

  const {
    data: projects,
    isLoading,
    error,
  } = useQuery<Project[], Error>({
    queryKey: ["projects", "list"],
    queryFn: getProjects,
  })

  const createProjectMutation = useMutation<Project, Error, ProjectCreate>({
    mutationFn: createProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projects", "list"] })
    },
  })

  const updateProjectMutation = useMutation<
    Project,
    Error,
    { projectId: number; projectData: ProjectUpdate }
  >({
    mutationFn: ({ projectId, projectData }) =>
      updateProject(projectId, projectData),
    onSuccess: (_, { projectId }) => {
      queryClient.invalidateQueries({ queryKey: ["projects", "list"] })
      queryClient.invalidateQueries({
        queryKey: ["projects", "detail", projectId],
      })
    },
  })

  const deleteProjectMutation = useMutation<void, Error, number>({
    mutationFn: deleteProject,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["projects", "list"] })
    },
  })

  return {
    projects,
    isLoading,
    error,
    createProject: createProjectMutation.mutate,
    updateProject: updateProjectMutation.mutate,
    deleteProject: deleteProjectMutation.mutate,
  }
}

export const useProject = (projectId: number) => {
  const {
    data: project,
    isLoading,
    error,
  } = useQuery<Project, Error>({
    queryKey: ["projects", "detail", projectId],
    queryFn: () => getProject(projectId),
  })

  return { project, isLoading, error }
}
