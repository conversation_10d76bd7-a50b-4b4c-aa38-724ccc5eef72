/**
 * Projects Module Hooks Index
 *
 * Centralized exports for all project-related React hooks.
 * Provides domain-aware hooks for UI components.
 */

// Import hooks with different names to avoid circular references in factory
import { useProjectForm as useProjectFormImpl } from "./useProjectForm"
import { useProjectOperations as useProjectOperationsImpl } from "./useProjectOperations"
import { useProjectSelection as useProjectSelectionImpl } from "./useProjectSelection"
import { useProjectStatus as useProjectStatusImpl } from "./useProjectStatus"
import { useProjectTeam as useProjectTeamImpl } from "./useProjectTeam"

// Core operation hooks
export { useProjectOperations } from "./useProjectOperations"
export { useProjectSelection } from "./useProjectSelection"
export { useProjectForm } from "./useProjectForm"

// Specialized operation hooks
export { useProjectTeam } from "./useProjectTeam"
export { useProjectStatus } from "./useProjectStatus"

// Factory hooks
export { useProjectHooks, useProjectListHooks } from "./useProjectHooks"

// Store-specific hooks (re-exported from store)
export {
  useProjectStore,
  useProjectSelection as useProjectSelectionStore,
  useProjectFilters,
  useProjectViewState,
  useProjectNotifications,
  useProjectDialogs,
  useProjectForm as useProjectFormStore,
  useProjectActivity,
} from "../store/projectStore"

// Hook configuration types
export type {
  ProjectSelectionFilters,
  PaginationConfig,
  SelectionMode,
} from "./useProjectSelection"

export type { ProjectFormData, FormMode } from "./useProjectForm"

// Store types
export type {
  Notification,
  ProjectViewState,
  ProjectFormState,
  DialogState,
  RecentActivity,
} from "../store/projectStore"

/**
 * Hook factory for creating configured hooks with dependencies
 */
export interface ProjectHooksFactory {
  // Repository dependencies
  repository: any // ProjectRepository instance

  // Use case dependencies
  useCases: {
    createProjectUseCase: any
    updateProjectUseCase: any
    archiveProjectUseCase: any
    assignMemberToProjectUseCase: any
    changeProjectStatusUseCase: any
  }

  // Service dependencies
  services: {
    validationService?: any
  }
}

/**
 * Create configured project hooks with dependencies
 */
export function createProjectHooks(factory: ProjectHooksFactory) {
  return {
    useProjectOperations: (config?: any) =>
      useProjectOperationsImpl(
        {
          createProjectUseCase: factory.useCases.createProjectUseCase,
          updateProjectUseCase: factory.useCases.updateProjectUseCase,
          archiveProjectUseCase: factory.useCases.archiveProjectUseCase,
        },
        config
      ),

    useProjectSelection: (initialFilters?: any, config?: any) =>
      useProjectSelectionImpl(factory.repository, initialFilters, config),

    useProjectForm: (initialProject?: any, config?: any) =>
      useProjectFormImpl(
        initialProject,
        factory.services.validationService,
        config
      ),

    useProjectTeam: (config?: any) =>
      useProjectTeamImpl(
        {
          assignMemberToProjectUseCase:
            factory.useCases.assignMemberToProjectUseCase,
        },
        config
      ),

    useProjectStatus: (config?: any) =>
      useProjectStatusImpl(
        {
          changeProjectStatusUseCase:
            factory.useCases.changeProjectStatusUseCase,
        },
        config
      ),
  }
}

/**
 * Default hook configurations
 */
export const defaultHookConfigs = {
  operations: {
    enableOptimisticUpdates: true,
    showSuccessNotifications: true,
    showErrorNotifications: true,
  },

  selection: {
    selectionMode: "single" as const,
    pagination: {
      pageSize: 20,
      sortBy: "updatedAt" as const,
      sortOrder: "desc" as const,
      enableInfiniteScroll: false,
    },
    enableAutoRefresh: false,
    refreshInterval: 30000,
  },

  form: {
    mode: "create" as const,
    enableAutoSave: false,
    autoSaveDelay: 2000,
    validateOnChange: true,
    validateOnBlur: true,
    showWarnings: true,
    enableOptimisticValidation: true,
  },

  team: {
    enableOptimisticUpdates: true,
    showSuccessNotifications: true,
    showErrorNotifications: true,
  },

  status: {
    enableOptimisticUpdates: true,
    showSuccessNotifications: true,
    showErrorNotifications: true,
  },
}

/**
 * Hook composition utilities
 */
export function useProjectManagement(
  projectId: string,
  userId: number,
  factory: ProjectHooksFactory
) {
  const operations = useProjectOperations(
    {
      createProjectUseCase: factory.useCases.createProjectUseCase,
      updateProjectUseCase: factory.useCases.updateProjectUseCase,
      archiveProjectUseCase: factory.useCases.archiveProjectUseCase,
    },
    defaultHookConfigs.operations
  )

  const team = useProjectTeam(
    {
      assignMemberToProjectUseCase:
        factory.useCases.assignMemberToProjectUseCase,
    },
    defaultHookConfigs.team
  )

  const status = useProjectStatus(
    {
      changeProjectStatusUseCase: factory.useCases.changeProjectStatusUseCase,
    },
    defaultHookConfigs.status
  )

  return {
    // Operations
    ...operations,

    // Team management
    assignMember: team.assignMember,
    removeMember: team.removeMember,
    canUserManageTeam: () => team.canUserManageTeam(projectId, userId),

    // Status management
    changeStatus: status.changeStatus,
    activateProject: () => status.activateProject(projectId, userId),
    pauseProject: () => status.pauseProject(projectId, userId),
    completeProject: () => status.completeProject(projectId, userId),
    cancelProject: () => status.cancelProject(projectId, userId),

    // Combined loading states
    isBusy:
      operations.isAnyOperationPending ||
      team.isAnyOperationPending ||
      status.isChangingStatus,

    // Combined error states
    hasErrors: operations.hasAnyError || team.hasAnyError || status.hasError,
  }
}

/**
 * Hook for project list management
 */
export function useProjectList(
  filters?: any,
  factory?: Partial<ProjectHooksFactory>
) {
  const selection = useProjectSelection(
    factory?.repository,
    filters,
    defaultHookConfigs.selection
  )

  const {
    filters: activeFilters,
    updateFilters,
    clearFilters,
  } = useProjectFilters()
  const { viewState, setLayout, setDensity, setGroupBy } = useProjectViewState()

  return {
    // Data
    projects: selection.projects,
    selectedProjects: selection.selectedProjects,
    statistics: selection.statistics,

    // Loading states
    isLoading: selection.isLoading,
    isFetching: selection.isFetching,

    // Filtering
    activeFilters,
    updateFilters,
    clearFilters,
    search: selection.search,
    applyQuickFilter: selection.applyQuickFilter,

    // Selection
    selectProject: selection.selectProject,
    clearSelection: selection.clearSelection,

    // View management
    viewState,
    setLayout,
    setDensity,
    setGroupBy,

    // Pagination
    hasNextPage: selection.hasNextPage,
    fetchNextPage: selection.fetchNextPage,

    // Utilities
    refresh: selection.refresh,
  }
}
