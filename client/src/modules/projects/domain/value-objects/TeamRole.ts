/**
 * TeamRole Value Object
 *
 * Encapsulates team member roles with permission-based business logic.
 * Ensures immutability and proper authorization for project operations.
 */

import { UserRoleEnum } from "@/types/api"

export interface TeamRoleData {
  role: UserRoleEnum
  description?: string
  permissions?: string[]
}

/**
 * Permission categories for granular access control
 */
export type PermissionType =
  | "project.view"
  | "project.edit"
  | "project.delete"
  | "project.manage"
  | "team.view"
  | "team.add"
  | "team.remove"
  | "team.manage"
  | "components.view"
  | "components.edit"
  | "components.manage"
  | "calculations.view"
  | "calculations.run"
  | "calculations.edit"
  | "reports.view"
  | "reports.generate"
  | "reports.export"
  | "financials.view"
  | "financials.edit"
  | "design.view"
  | "design.edit"
  | "design.approve"
  | "system.admin"

export class TeamRole {
  private constructor(
    private readonly _role: UserRoleEnum,
    private readonly _description?: string,
    private readonly _customPermissions?: string[]
  ) {
    this.validateRole()
  }

  public static create(data: TeamRoleData): Team<PERSON>ole {
    return new TeamRole(data.role, data.description, data.permissions)
  }

  public static createAdministrator(): TeamRole {
    return new Team<PERSON>ole(
      "Administrator",
      "Full system access with all permissions"
    )
  }

  public static createProjectManager(): TeamRole {
    return new TeamRole(
      "Project Manager",
      "Project oversight and team management"
    )
  }

  public static createViewer(): TeamRole {
    return new TeamRole("Viewer", "Read-only access to project information")
  }

  // Getters
  public get role(): UserRoleEnum {
    return this._role
  }

  public get description(): string | undefined {
    return this._description
  }

  public get customPermissions(): string[] | undefined {
    return this._customPermissions
  }

  // Core Permission Queries
  public hasPermission(permission: PermissionType): boolean {
    const rolePermissions = this.getPermissions()
    return rolePermissions.includes(permission)
  }

  public getPermissions(): PermissionType[] {
    // Custom permissions override default role permissions
    if (this._customPermissions && this._customPermissions.length > 0) {
      return this._customPermissions as PermissionType[]
    }

    return this.getDefaultPermissions()
  }

  private getDefaultPermissions(): PermissionType[] {
    switch (this._role) {
      case "Administrator":
        return [
          "project.view",
          "project.edit",
          "project.delete",
          "project.manage",
          "team.view",
          "team.add",
          "team.remove",
          "team.manage",
          "components.view",
          "components.edit",
          "components.manage",
          "calculations.view",
          "calculations.run",
          "calculations.edit",
          "reports.view",
          "reports.generate",
          "reports.export",
          "financials.view",
          "financials.edit",
          "design.view",
          "design.edit",
          "design.approve",
          "system.admin",
        ]

      case "Project Manager":
        return [
          "project.view",
          "project.edit",
          "project.manage",
          "team.view",
          "team.add",
          "team.remove",
          "team.manage",
          "components.view",
          "components.edit",
          "calculations.view",
          "calculations.run",
          "reports.view",
          "reports.generate",
          "reports.export",
          "financials.view",
          "financials.edit",
          "design.view",
          "design.approve",
        ]

      case "Lead Engineer":
        return [
          "project.view",
          "project.edit",
          "team.view",
          "components.view",
          "components.edit",
          "components.manage",
          "calculations.view",
          "calculations.run",
          "calculations.edit",
          "reports.view",
          "reports.generate",
          "design.view",
          "design.edit",
          "design.approve",
        ]

      case "Electrical Engineer":
        return [
          "project.view",
          "team.view",
          "components.view",
          "components.edit",
          "calculations.view",
          "calculations.run",
          "reports.view",
          "reports.generate",
          "design.view",
          "design.edit",
        ]

      case "Automation Engineer":
        return [
          "project.view",
          "team.view",
          "components.view",
          "components.edit",
          "calculations.view",
          "calculations.run",
          "reports.view",
          "reports.generate",
          "design.view",
          "design.edit",
        ]

      case "Instrumentation Engineer":
        return [
          "project.view",
          "team.view",
          "components.view",
          "components.edit",
          "calculations.view",
          "calculations.run",
          "reports.view",
          "reports.generate",
          "design.view",
          "design.edit",
        ]

      case "CAD Operator":
        return [
          "project.view",
          "team.view",
          "components.view",
          "design.view",
          "design.edit",
          "reports.view",
        ]

      case "Viewer":
        return [
          "project.view",
          "team.view",
          "components.view",
          "calculations.view",
          "reports.view",
          "design.view",
        ]

      case "Guest":
        return ["project.view", "reports.view"]

      case "Client":
        return ["project.view", "reports.view", "financials.view"]

      case "Supplier":
        return ["components.view", "reports.view"]

      default:
        return []
    }
  }

  // Business Logic Queries
  public canManageTeam(): boolean {
    return this.hasPermission("team.manage")
  }

  public canEditProject(): boolean {
    return this.hasPermission("project.edit")
  }

  public canDeleteProject(): boolean {
    return this.hasPermission("project.delete")
  }

  public canManageProject(): boolean {
    return this.hasPermission("project.manage")
  }

  public canViewFinancials(): boolean {
    return this.hasPermission("financials.view")
  }

  public canViewBudget(): boolean {
    return this.hasPermission("financials.view")
  }

  public canEditFinancials(): boolean {
    return this.hasPermission("financials.edit")
  }

  public hasDesignPermissions(): boolean {
    return (
      this.hasPermission("design.edit") || this.hasPermission("design.approve")
    )
  }

  public canApproveDesign(): boolean {
    return this.hasPermission("design.approve")
  }

  public canRunCalculations(): boolean {
    return this.hasPermission("calculations.run")
  }

  public canEditCalculations(): boolean {
    return this.hasPermission("calculations.edit")
  }

  public canManageComponents(): boolean {
    return this.hasPermission("components.manage")
  }

  public canGenerateReports(): boolean {
    return this.hasPermission("reports.generate")
  }

  public canExportReports(): boolean {
    return this.hasPermission("reports.export")
  }

  public isSystemAdmin(): boolean {
    return this.hasPermission("system.admin")
  }

  public canAddTeamMembers(): boolean {
    return this.hasPermission("team.add")
  }

  public canRemoveTeamMembers(): boolean {
    return this.hasPermission("team.remove")
  }

  // Role Hierarchy Queries
  public isManagerialRole(): boolean {
    return ["Administrator", "Project Manager"].includes(this._role)
  }

  public isTechnicalRole(): boolean {
    return [
      "Lead Engineer",
      "Electrical Engineer",
      "Automation Engineer",
      "Instrumentation Engineer",
      "CAD Operator",
    ].includes(this._role)
  }

  public isExternalRole(): boolean {
    return ["Client", "Supplier", "Guest"].includes(this._role)
  }

  public isReadOnlyRole(): boolean {
    return (
      ["Viewer", "Guest"].includes(this._role) ||
      (!this.canEditProject() && !this.canManageProject())
    )
  }

  public getHierarchyLevel(): number {
    const hierarchy: Record<UserRoleEnum, number> = {
      Administrator: 10,
      "Project Manager": 9,
      "Lead Engineer": 8,
      "Electrical Engineer": 7,
      "Automation Engineer": 6,
      "Instrumentation Engineer": 6,
      "CAD Operator": 5,
      Viewer: 3,
      Client: 2,
      Guest: 1,
      Supplier: 1,
    }

    return hierarchy[this._role] || 0
  }

  public hasHigherAuthorityThan(otherRole: TeamRole): boolean {
    return this.getHierarchyLevel() > otherRole.getHierarchyLevel()
  }

  public getAssignableRoles(): UserRoleEnum[] {
    const allRoles: UserRoleEnum[] = [
      "Administrator",
      "Project Manager",
      "Lead Engineer",
      "Electrical Engineer",
      "Automation Engineer",
      "Instrumentation Engineer",
      "CAD Operator",
      "Viewer",
      "Guest",
      "Client",
      "Supplier",
    ]

    return allRoles.filter(role => {
      const targetRole = TeamRole.create({ role })
      return this.canAssignRole(targetRole)
    })
  }

  // Business Operation Validation
  public canAssignRole(targetRole: TeamRole): boolean {
    // Only administrators can assign administrator roles
    if (targetRole._role === "Administrator") {
      return this._role === "Administrator"
    }

    // Project managers can assign roles below their level
    if (this._role === "Project Manager") {
      return !["Administrator", "Project Manager"].includes(targetRole._role)
    }

    // Lead engineers can assign technical roles below their level
    if (this._role === "Lead Engineer") {
      return ["Electrical Engineer", "CAD Operator", "Viewer"].includes(
        targetRole._role
      )
    }

    // Administrators can assign any role
    return this._role === "Administrator"
  }

  public canModifyProjectSettings(): boolean {
    return this.canManageProject() || this.isSystemAdmin()
  }

  public canAccessProjectBudget(): boolean {
    return (
      this.canViewFinancials() &&
      ["Administrator", "Project Manager", "Client"].includes(this._role)
    )
  }

  // Role Transformation Methods
  public withCustomPermissions(permissions: PermissionType[]): TeamRole {
    return new TeamRole(this._role, this._description, permissions)
  }

  public withDescription(description: string): TeamRole {
    return new TeamRole(this._role, description, this._customPermissions)
  }

  // Value Object Equality
  public equals(other: TeamRole): boolean {
    return (
      this._role === other._role &&
      this._description === other._description &&
      JSON.stringify(this._customPermissions?.sort()) ===
        JSON.stringify(other._customPermissions?.sort())
    )
  }

  public toString(): string {
    return this._role
  }

  // Private Methods
  private validateRole(): void {
    const validRoles: UserRoleEnum[] = [
      "Electrical Engineer", "Administrator", "Project Manager", "Lead Engineer", "Automation Engineer", "Instrumentation Engineer", "CAD Operator", "Viewer", "Guest", "Client", "Supplier",
    ]

    if (!validRoles.includes(this._role)) {
      throw new Error(`Invalid team role: ${this._role}`)
    }
  }
}
