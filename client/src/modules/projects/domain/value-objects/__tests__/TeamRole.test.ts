/**
 * TeamRole Value Object Unit Tests
 *
 * Comprehensive test coverage for team roles and permission logic.
 * Tests cover all role types, permissions, and business queries.
 */

import { describe, expect, test } from "vitest"
import { PermissionType, TeamRole, TeamRoleType } from "../TeamRole"

describe("TeamRole Value Object", () => {
  describe("Creation and Validation", () => {
    test("should create a valid team role", () => {
      const role = TeamRole.create({
        role: "Project Manager",
        description: "Manages project execution",
      })

      expect(role.role).toBe("Project Manager")
      expect(role.description).toBe("Manages project execution")
    })

    test("should create administrator role with factory method", () => {
      const admin = TeamRole.createAdministrator()

      expect(admin.role).toBe("Administrator")
      expect(admin.description).toBe("Full system access with all permissions")
    })

    test("should create project manager role with factory method", () => {
      const pm = TeamRole.createProjectManager()

      expect(pm.role).toBe("Project Manager")
      expect(pm.description).toBe("Project oversight and team management")
    })

    test("should create viewer role with factory method", () => {
      const viewer = TeamRole.createViewer()

      expect(viewer.role).toBe("Viewer")
      expect(viewer.description).toBe("Read-only access to project information")
    })

    test("should throw error for invalid role", () => {
      expect(() => {
        TeamRole.create({
          role: "InvalidRole" as TeamRoleType,
        })
      }).toThrow("Invalid team role: InvalidRole")
    })

    test("should handle missing optional fields", () => {
      const role = TeamRole.create({
        role: "Electrical Engineer",
      })

      expect(role.role).toBe("Electrical Engineer")
      expect(role.description).toBeUndefined()
      expect(role.customPermissions).toBeUndefined()
    })

    test("should create role with custom permissions", () => {
      const customPermissions = ["project.view", "components.edit"]
      const role = TeamRole.create({
        role: "CAD Operator",
        permissions: customPermissions,
      })

      expect(role.customPermissions).toEqual(customPermissions)
    })
  })

  describe("Permission System", () => {
    test("should return correct permissions for Administrator", () => {
      const admin = TeamRole.createAdministrator()
      const permissions = admin.getPermissions()

      expect(permissions).toContain("system.admin")
      expect(permissions).toContain("project.manage")
      expect(permissions).toContain("team.manage")
      expect(permissions).toContain("design.approve")
      expect(permissions.length).toBeGreaterThan(15)
    })

    test("should return correct permissions for Project Manager", () => {
      const pm = TeamRole.createProjectManager()
      const permissions = pm.getPermissions()

      expect(permissions).toContain("project.manage")
      expect(permissions).toContain("team.manage")
      expect(permissions).toContain("financials.edit")
      expect(permissions).toContain("design.approve")
      expect(permissions).not.toContain("system.admin")
    })

    test("should return correct permissions for Lead Engineer", () => {
      const lead = TeamRole.create({ role: "Lead Engineer" })
      const permissions = lead.getPermissions()

      expect(permissions).toContain("design.approve")
      expect(permissions).toContain("calculations.edit")
      expect(permissions).toContain("components.manage")
      expect(permissions).not.toContain("team.manage")
      expect(permissions).not.toContain("project.delete")
    })

    test("should return correct permissions for Electrical Engineer", () => {
      const designer = TeamRole.create({ role: "Electrical Engineer" })
      const permissions = designer.getPermissions()

      expect(permissions).toContain("design.edit")
      expect(permissions).toContain("calculations.run")
      expect(permissions).toContain("components.edit")
      expect(permissions).not.toContain("design.approve")
      expect(permissions).not.toContain("team.manage")
    })

    test("should return correct permissions for Viewer", () => {
      const viewer = TeamRole.createViewer()
      const permissions = viewer.getPermissions()

      expect(permissions).toContain("project.view")
      expect(permissions).toContain("components.view")
      expect(permissions).toContain("design.view")
      expect(permissions).not.toContain("project.edit")
      expect(permissions).not.toContain("team.manage")
    })

    test("should return correct permissions for Client", () => {
      const client = TeamRole.create({ role: "Client" })
      const permissions = client.getPermissions()

      expect(permissions).toContain("project.view")
      expect(permissions).toContain("financials.view")
      expect(permissions).toContain("reports.view")
      expect(permissions).not.toContain("project.edit")
      expect(permissions).not.toContain("team.view")
    })

    test("should return correct permissions for Guest", () => {
      const guest = TeamRole.create({ role: "Guest" })
      const permissions = guest.getPermissions()

      expect(permissions).toContain("project.view")
      expect(permissions).toContain("reports.view")
      expect(permissions).not.toContain("team.view")
      expect(permissions).not.toContain("components.view")
      expect(permissions.length).toBe(2)
    })

    test("should use custom permissions when provided", () => {
      const customPermissions: PermissionType[] = [
        "project.view",
        "reports.generate",
      ]
      const role = TeamRole.create({
        role: "CAD Operator",
        permissions: customPermissions,
      })

      const permissions = role.getPermissions()
      expect(permissions).toEqual(customPermissions)
    })

    test("should validate individual permissions correctly", () => {
      const pm = TeamRole.createProjectManager()

      expect(pm.hasPermission("project.manage")).toBe(true)
      expect(pm.hasPermission("team.manage")).toBe(true)
      expect(pm.hasPermission("system.admin")).toBe(false)
    })
  })

  describe("Business Logic Queries", () => {
    test("should correctly identify team management capability", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()
      const designer = TeamRole.create({ role: "Electrical Engineer" })

      expect(admin.canManageTeam()).toBe(true)
      expect(pm.canManageTeam()).toBe(true)
      expect(designer.canManageTeam()).toBe(false)
    })

    test("should correctly identify project editing capability", () => {
      const pm = TeamRole.createProjectManager()
      const lead = TeamRole.create({ role: "Lead Engineer" })
      const viewer = TeamRole.createViewer()

      expect(pm.canEditProject()).toBe(true)
      expect(lead.canEditProject()).toBe(true)
      expect(viewer.canEditProject()).toBe(false)
    })

    test("should correctly identify project management capability", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()
      const designer = TeamRole.create({ role: "Electrical Engineer" })

      expect(admin.canManageProject()).toBe(true)
      expect(pm.canManageProject()).toBe(true)
      expect(designer.canManageProject()).toBe(false)
    })

    test("should correctly identify financial access", () => {
      const pm = TeamRole.createProjectManager()
      const client = TeamRole.create({ role: "Client" })
      const designer = TeamRole.create({ role: "Electrical Engineer" })

      expect(pm.canViewFinancials()).toBe(true)
      expect(pm.canEditFinancials()).toBe(true)
      expect(client.canViewFinancials()).toBe(true)
      expect(client.canEditFinancials()).toBe(false)
      expect(designer.canViewFinancials()).toBe(false)
    })

    test("should correctly identify design permissions", () => {
      const lead = TeamRole.create({ role: "Lead Engineer" })
      const designer = TeamRole.create({ role: "Electrical Engineer" })
      const viewer = TeamRole.createViewer()

      expect(lead.hasDesignPermissions()).toBe(true)
      expect(lead.canApproveDesign()).toBe(true)
      expect(designer.hasDesignPermissions()).toBe(true)
      expect(designer.canApproveDesign()).toBe(false)
      expect(viewer.hasDesignPermissions()).toBe(false)
    })

    test("should correctly identify calculation permissions", () => {
      const lead = TeamRole.create({ role: "Lead Engineer" })
      const designer = TeamRole.create({ role: "Electrical Engineer" })
      const viewer = TeamRole.createViewer()

      expect(lead.canRunCalculations()).toBe(true)
      expect(lead.canEditCalculations()).toBe(true)
      expect(designer.canRunCalculations()).toBe(true)
      expect(designer.canEditCalculations()).toBe(false)
      expect(viewer.canRunCalculations()).toBe(false)
    })

    test("should correctly identify component management permissions", () => {
      const admin = TeamRole.createAdministrator()
      const lead = TeamRole.create({ role: "Lead Engineer" })
      const designer = TeamRole.create({ role: "Electrical Engineer" })

      expect(admin.canManageComponents()).toBe(true)
      expect(lead.canManageComponents()).toBe(true)
      expect(designer.canManageComponents()).toBe(false)
    })

    test("should correctly identify report permissions", () => {
      const pm = TeamRole.createProjectManager()
      const designer = TeamRole.create({ role: "Electrical Engineer" })
      const viewer = TeamRole.createViewer()

      expect(pm.canGenerateReports()).toBe(true)
      expect(pm.canExportReports()).toBe(true)
      expect(designer.canGenerateReports()).toBe(true)
      expect(designer.canExportReports()).toBe(false)
      expect(viewer.canGenerateReports()).toBe(false)
    })

    test("should correctly identify system admin privileges", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()

      expect(admin.isSystemAdmin()).toBe(true)
      expect(pm.isSystemAdmin()).toBe(false)
    })

    test("should correctly identify team member management capabilities", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()
      const designer = TeamRole.create({ role: "Electrical Engineer" })

      expect(admin.canAddTeamMembers()).toBe(true)
      expect(admin.canRemoveTeamMembers()).toBe(true)
      expect(pm.canAddTeamMembers()).toBe(true)
      expect(pm.canRemoveTeamMembers()).toBe(true)
      expect(designer.canAddTeamMembers()).toBe(false)
      expect(designer.canRemoveTeamMembers()).toBe(false)
    })
  })

  describe("Role Hierarchy and Classification", () => {
    test("should correctly identify managerial roles", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()
      const lead = TeamRole.create({ role: "Lead Engineer" })

      expect(admin.isManagerialRole()).toBe(true)
      expect(pm.isManagerialRole()).toBe(true)
      expect(lead.isManagerialRole()).toBe(false)
    })

    test("should correctly identify technical roles", () => {
      const lead = TeamRole.create({ role: "Lead Engineer" })
      const designer = TeamRole.create({ role: "Electrical Engineer" })
      const mechanical = TeamRole.create({ role: "Automation Engineer" })
      const cad = TeamRole.create({ role: "CAD Operator" })
      const pm = TeamRole.createProjectManager()

      expect(lead.isTechnicalRole()).toBe(true)
      expect(designer.isTechnicalRole()).toBe(true)
      expect(mechanical.isTechnicalRole()).toBe(true)
      expect(cad.isTechnicalRole()).toBe(true)
      expect(pm.isTechnicalRole()).toBe(false)
    })

    test("should correctly identify external roles", () => {
      const client = TeamRole.create({ role: "Client" })
      const supplier = TeamRole.create({ role: "Supplier" })
      const guest = TeamRole.create({ role: "Guest" })
      const designer = TeamRole.create({ role: "Electrical Engineer" })

      expect(client.isExternalRole()).toBe(true)
      expect(supplier.isExternalRole()).toBe(true)
      expect(guest.isExternalRole()).toBe(true)
      expect(designer.isExternalRole()).toBe(false)
    })

    test("should correctly identify read-only roles", () => {
      const viewer = TeamRole.createViewer()
      const guest = TeamRole.create({ role: "Guest" })
      const pm = TeamRole.createProjectManager()

      expect(viewer.isReadOnlyRole()).toBe(true)
      expect(guest.isReadOnlyRole()).toBe(true)
      expect(pm.isReadOnlyRole()).toBe(false)
    })

    test("should correctly compare role authority", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()
      const lead = TeamRole.create({ role: "Lead Engineer" })
      const designer = TeamRole.create({ role: "Electrical Engineer" })
      const viewer = TeamRole.createViewer()

      expect(admin.hasHigherAuthorityThan(pm)).toBe(true)
      expect(pm.hasHigherAuthorityThan(lead)).toBe(true)
      expect(lead.hasHigherAuthorityThan(designer)).toBe(true)
      expect(designer.hasHigherAuthorityThan(viewer)).toBe(true)
      expect(viewer.hasHigherAuthorityThan(admin)).toBe(false)
    })
  })

  describe("Role Assignment Validation", () => {
    test("should validate administrator assignment correctly", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()
      const targetAdmin = TeamRole.createAdministrator()

      expect(admin.canAssignRole(targetAdmin)).toBe(true)
      expect(pm.canAssignRole(targetAdmin)).toBe(false)
    })

    test("should validate project manager assignment correctly", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()
      const designer = TeamRole.create({ role: "Electrical Engineer" })
      const targetPM = TeamRole.createProjectManager()

      expect(admin.canAssignRole(targetPM)).toBe(true)
      expect(pm.canAssignRole(targetPM)).toBe(false)
      expect(designer.canAssignRole(targetPM)).toBe(false)
    })

    test("should validate technical role assignment correctly", () => {
      const pm = TeamRole.createProjectManager()
      const lead = TeamRole.create({ role: "Lead Engineer" })
      const designer = TeamRole.create({ role: "Electrical Engineer" })
      const targetDesigner = TeamRole.create({ role: "Electrical Engineer" })

      expect(pm.canAssignRole(targetDesigner)).toBe(true)
      expect(lead.canAssignRole(targetDesigner)).toBe(true)
      expect(designer.canAssignRole(targetDesigner)).toBe(false)
    })

    test("should validate viewer assignment correctly", () => {
      const lead = TeamRole.create({ role: "Lead Engineer" })
      const designer = TeamRole.create({ role: "Electrical Engineer" })
      const viewer = TeamRole.createViewer()

      expect(lead.canAssignRole(viewer)).toBe(true)
      expect(designer.canAssignRole(viewer)).toBe(false)
    })
  })

  describe("Business Operation Validation", () => {
    test("should validate project settings modification", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()
      const designer = TeamRole.create({ role: "Electrical Engineer" })

      expect(admin.canModifyProjectSettings()).toBe(true)
      expect(pm.canModifyProjectSettings()).toBe(true)
      expect(designer.canModifyProjectSettings()).toBe(false)
    })

    test("should validate project budget access", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()
      const client = TeamRole.create({ role: "Client" })
      const designer = TeamRole.create({ role: "Electrical Engineer" })

      expect(admin.canAccessProjectBudget()).toBe(true)
      expect(pm.canAccessProjectBudget()).toBe(true)
      expect(client.canAccessProjectBudget()).toBe(true)
      expect(designer.canAccessProjectBudget()).toBe(false)
    })
  })

  describe("Role Transformation", () => {
    test("should create role with custom permissions", () => {
      const original = TeamRole.create({ role: "CAD Operator" })
      const customPermissions: PermissionType[] = ["project.edit", "team.view"]
      const modified = original.withCustomPermissions(customPermissions)

      expect(modified.role).toBe("CAD Operator")
      expect(modified.getPermissions()).toEqual(customPermissions)
      expect(original).not.toBe(modified) // Immutability
    })

    test("should create role with updated description", () => {
      const original = TeamRole.create({ role: "Electrical Engineer" })
      const modified = original.withDescription("Senior Electrical Engineer")

      expect(modified.role).toBe("Electrical Engineer")
      expect(modified.description).toBe("Senior Electrical Engineer")
      expect(original).not.toBe(modified) // Immutability
    })
  })

  describe("Value Object Properties", () => {
    test("should maintain immutability", () => {
      const original = TeamRole.create({ role: "Project Manager" })
      const modified = original.withDescription("Updated description")

      expect(original.description).toBeUndefined()
      expect(modified.description).toBe("Updated description")
      expect(original).not.toBe(modified)
    })

    test("should implement equality correctly", () => {
      const role1 = TeamRole.create({
        role: "Electrical Engineer",
        description: "Test description",
        permissions: ["project.view", "design.edit"],
      })

      const role2 = TeamRole.create({
        role: "Electrical Engineer",
        description: "Test description",
        permissions: ["design.edit", "project.view"], // Different order
      })

      const role3 = TeamRole.create({
        role: "Project Manager",
        description: "Different role",
      })

      expect(role1.equals(role2)).toBe(true) // Same permissions, different order
      expect(role1.equals(role3)).toBe(false)
    })

    test("should convert to string correctly", () => {
      const admin = TeamRole.createAdministrator()
      const pm = TeamRole.createProjectManager()

      expect(admin.toString()).toBe("Administrator")
      expect(pm.toString()).toBe("Project Manager")
    })
  })

  describe("Edge Cases", () => {
    test("should handle empty custom permissions", () => {
      const role = TeamRole.create({
        role: "Electrical Engineer",
        permissions: [],
      })

      // Should fall back to default permissions
      const permissions = role.getPermissions()
      expect(permissions.length).toBeGreaterThan(0)
    })

    test("should handle roles with equal authority", () => {
      const mechanical = TeamRole.create({ role: "Automation Engineer" })
      const instrumentation = TeamRole.create({
        role: "Instrumentation Engineer",
      })

      expect(mechanical.hasHigherAuthorityThan(instrumentation)).toBe(false)
      expect(instrumentation.hasHigherAuthorityThan(mechanical)).toBe(false)
    })

    test("should handle all role types without errors", () => {
      const allRoles: TeamRoleType[] = [
        "Administrator",
        "Project Manager",
        "Lead Engineer",
        "Electrical Engineer",
        "Automation Engineer",
        "Instrumentation Engineer",
        "CAD Operator",
        "Viewer",
        "Guest",
        "Client",
        "Supplier",
      ]

      allRoles.forEach((roleType) => {
        expect(() => {
          const role = TeamRole.create({ role: roleType })
          role.getPermissions()
          role.canManageTeam()
          role.hasDesignPermissions()
        }).not.toThrow()
      })
    })
  })
})
