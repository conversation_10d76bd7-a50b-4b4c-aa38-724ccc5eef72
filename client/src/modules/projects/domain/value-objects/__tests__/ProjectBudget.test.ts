/**
 * ProjectBudget Value Object Unit Tests
 *
 * Comprehensive test coverage for project budget and financial logic.
 * Tests cover all financial calculations, expense management, and business queries.
 */

import { describe, expect, test } from "vitest"
import { BudgetCategory, ProjectBudget, ProjectExpense } from "../ProjectBudget"

describe("ProjectBudget Value Object", () => {
  const sampleExpense: Omit<ProjectExpense, "id"> = {
    description: "Test equipment",
    amount: 1500,
    currency: "EUR",
    category: "Equipment",
    date: "2024-01-15T00:00:00Z",
    approvedBy: 1,
  }

  const sampleCategory: BudgetCategory = {
    name: "Equipment",
    allocatedAmount: 5000,
    spentAmount: 1500,
    currency: "EUR",
  }

  describe("Creation and Validation", () => {
    test("should create a valid project budget", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 2500,
        currency: "EUR",
      })

      expect(budget.totalAmount).toBe(10000)
      expect(budget.spentAmount).toBe(2500)
      expect(budget.currency).toBe("EUR")
      expect(budget.expenses).toEqual([])
      expect(budget.budgetCategories).toEqual([])
    })

    test("should create empty budget with factory method", () => {
      const budget = ProjectBudget.createEmpty("USD")

      expect(budget.totalAmount).toBe(0)
      expect(budget.spentAmount).toBe(0)
      expect(budget.currency).toBe("USD")
    })

    test("should create budget with amount using factory method", () => {
      const budget = ProjectBudget.createWithAmount(15000, "GBP")

      expect(budget.totalAmount).toBe(15000)
      expect(budget.spentAmount).toBe(0)
      expect(budget.currency).toBe("GBP")
    })

    test("should use default values for optional fields", () => {
      const budget = ProjectBudget.create({
        totalAmount: 5000,
        currency: "EUR",
      })

      expect(budget.spentAmount).toBe(0)
      expect(budget.expenses).toEqual([])
      expect(budget.budgetCategories).toEqual([])
    })

    test("should throw error for negative total amount", () => {
      expect(() => {
        ProjectBudget.create({
          totalAmount: -1000,
          currency: "EUR",
        })
      }).toThrow("Total budget amount cannot be negative")
    })

    test("should throw error for negative spent amount", () => {
      expect(() => {
        ProjectBudget.create({
          totalAmount: 10000,
          spentAmount: -500,
          currency: "EUR",
        })
      }).toThrow("Spent amount cannot be negative")
    })

    test("should throw error for invalid currency", () => {
      expect(() => {
        ProjectBudget.create({
          totalAmount: 10000,
          currency: "INVALID",
        })
      }).toThrow("Invalid currency code: INVALID")
    })

    test("should create budget with expenses and categories", () => {
      const expenses: ProjectExpense[] = [
        {
          ...sampleExpense,
          id: "expense_1",
        },
      ]

      const budget = ProjectBudget.create({
        totalAmount: 10000,
        currency: "EUR",
        expenses,
        budgetCategories: [sampleCategory],
      })

      expect(budget.expenses).toHaveLength(1)
      expect(budget.budgetCategories).toHaveLength(1)
    })
  })

  describe("Financial Calculations", () => {
    test("should calculate remaining amount correctly", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 3500,
        currency: "EUR",
      })

      expect(budget.getRemainingAmount()).toBe(6500)
    })

    test("should handle negative remaining amount", () => {
      const budget = ProjectBudget.create({
        totalAmount: 5000,
        spentAmount: 7000,
        currency: "EUR",
      })

      expect(budget.getRemainingAmount()).toBe(0) // Should not go negative
    })

    test("should calculate utilization percentage correctly", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 2500,
        currency: "EUR",
      })

      expect(budget.getUtilizationPercentage()).toBe(25)
    })

    test("should handle zero total amount for utilization", () => {
      const budget = ProjectBudget.createEmpty("EUR")

      expect(budget.getUtilizationPercentage()).toBe(0)
    })

    test("should cap utilization at 100%", () => {
      const budget = ProjectBudget.create({
        totalAmount: 5000,
        spentAmount: 7500,
        currency: "EUR",
      })

      expect(budget.getUtilizationPercentage()).toBe(100) // Capped at 100%
    })

    test("should calculate overspend amount correctly", () => {
      const budget = ProjectBudget.create({
        totalAmount: 5000,
        spentAmount: 6500,
        currency: "EUR",
      })

      expect(budget.getOverspendAmount()).toBe(1500)
    })

    test("should return zero overspend for within-budget projects", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 7500,
        currency: "EUR",
      })

      expect(budget.getOverspendAmount()).toBe(0)
    })

    test("should calculate tax amount correctly", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      expect(budget.calculateTaxAmount(0.21)).toBe(2100) // 21% VAT
      expect(budget.calculateTaxAmount(0.1)).toBe(1000) // 10% tax
    })

    test("should throw error for invalid tax rate", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      expect(() => budget.calculateTaxAmount(-0.1)).toThrow(
        "Tax rate must be between 0 and 1"
      )
      expect(() => budget.calculateTaxAmount(1.5)).toThrow(
        "Tax rate must be between 0 and 1"
      )
    })

    test("should calculate net amount including tax", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      expect(budget.calculateNetAmount(0.21)).toBe(12100) // 10000 + 2100 tax
    })
  })

  describe("Expense Management", () => {
    test("should add expense correctly", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const updatedBudget = budget.addExpense(sampleExpense)

      expect(updatedBudget.expenses).toHaveLength(1)
      expect(updatedBudget.spentAmount).toBe(1500)
      expect(updatedBudget.expenses[0].description).toBe("Test equipment")
      expect(updatedBudget.expenses[0].id).toBeDefined()
      expect(budget).not.toBe(updatedBudget) // Immutability
    })

    test("should generate unique expense IDs", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const updated1 = budget.addExpense(sampleExpense)
      const updated2 = updated1.addExpense({
        ...sampleExpense,
        description: "Second expense",
      })

      const ids = updated2.expenses.map((e) => e.id)
      expect(new Set(ids).size).toBe(2) // All IDs should be unique
    })

    test("should remove expense correctly", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const withExpense = budget.addExpense(sampleExpense)
      const expenseId = withExpense.expenses[0].id
      const withoutExpense = withExpense.removeExpense(expenseId)

      expect(withoutExpense.expenses).toHaveLength(0)
      expect(withoutExpense.spentAmount).toBe(0)
    })

    test("should throw error when removing non-existent expense", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      expect(() => {
        budget.removeExpense("non-existent-id")
      }).toThrow("Expense with ID non-existent-id not found")
    })

    test("should update expense correctly", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const withExpense = budget.addExpense(sampleExpense)
      const expenseId = withExpense.expenses[0].id

      const updated = withExpense.updateExpense(expenseId, {
        description: "Updated equipment",
        amount: 2000,
      })

      expect(updated.expenses[0].description).toBe("Updated equipment")
      expect(updated.expenses[0].amount).toBe(2000)
      expect(updated.spentAmount).toBe(2000)
    })

    test("should throw error when updating non-existent expense", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      expect(() => {
        budget.updateExpense("non-existent-id", { amount: 100 })
      }).toThrow("Expense with ID non-existent-id not found")
    })

    test("should handle currency conversion in expenses", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const usdExpense = {
        ...sampleExpense,
        currency: "USD",
        amount: 1100, // Should convert to ~1000 EUR (simplified rate)
      }

      const updated = budget.addExpense(usdExpense)

      // Note: Exact conversion depends on exchange rates in convertCurrency method
      expect(updated.spentAmount).toBeGreaterThan(0)
      expect(updated.expenses[0].currency).toBe("USD")
    })
  })

  describe("Budget Modification", () => {
    test("should increase budget correctly", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const increased = budget.increaseBudget(5000, "Additional funding")

      expect(increased.totalAmount).toBe(15000)
      expect(increased.spentAmount).toBe(0) // Unchanged
    })

    test("should throw error for non-positive increase", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      expect(() => {
        budget.increaseBudget(0)
      }).toThrow("Budget increase amount must be positive")

      expect(() => {
        budget.increaseBudget(-1000)
      }).toThrow("Budget increase amount must be positive")
    })

    test("should decrease budget correctly", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const decreased = budget.decreaseBudget(3000, "Budget cut")

      expect(decreased.totalAmount).toBe(7000)
      expect(decreased.spentAmount).toBe(0) // Unchanged
    })

    test("should throw error when decreasing budget below zero", () => {
      const budget = ProjectBudget.createWithAmount(5000, "EUR")

      expect(() => {
        budget.decreaseBudget(6000)
      }).toThrow("Cannot decrease budget below zero")
    })

    test("should set budget amount correctly", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const updated = budget.setBudget(15000)

      expect(updated.totalAmount).toBe(15000)
      expect(updated.spentAmount).toBe(0) // Unchanged
    })

    test("should throw error for negative budget amount", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      expect(() => {
        budget.setBudget(-1000)
      }).toThrow("Budget amount cannot be negative")
    })
  })

  describe("Currency Management", () => {
    test("should handle same currency conversion", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      expect(budget.convertCurrency(1000, "EUR", "EUR")).toBe(1000)
    })

    test("should convert currency with exchange rates", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      // Based on simplified exchange rates in the implementation
      const usdAmount = budget.convertCurrency(1000, "EUR", "USD")
      expect(usdAmount).toBe(1100) // 1000 * 1.1
    })

    test("should convert entire budget to different currency", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 2000,
        currency: "EUR",
      }).addExpense(sampleExpense)

      const usdBudget = budget.convertToCurrency("USD")

      expect(usdBudget.currency).toBe("USD")
      expect(usdBudget.totalAmount).toBe(11000) // 10000 * 1.1
      expect(usdBudget.spentAmount).toBeGreaterThan(2000) // Converted amount
      expect(usdBudget.expenses[0].currency).toBe("USD")
    })

    test("should return same budget when converting to same currency", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const converted = budget.convertToCurrency("EUR")

      expect(converted).toBe(budget) // Should return same instance
    })
  })

  describe("Category Management", () => {
    test("should add budget category correctly", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const withCategory = budget.addBudgetCategory(sampleCategory)

      expect(withCategory.budgetCategories).toHaveLength(1)
      expect(withCategory.budgetCategories[0].name).toBe("Equipment")
    })

    test("should throw error for duplicate category", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        currency: "EUR",
        budgetCategories: [sampleCategory],
      })

      expect(() => {
        budget.addBudgetCategory(sampleCategory)
      }).toThrow("Budget category 'Equipment' already exists")
    })

    test("should update budget category correctly", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        currency: "EUR",
        budgetCategories: [sampleCategory],
      })

      const updated = budget.updateBudgetCategory("Equipment", {
        allocatedAmount: 7500,
        spentAmount: 2000,
      })

      const updatedCategory = updated.budgetCategories[0]
      expect(updatedCategory.allocatedAmount).toBe(7500)
      expect(updatedCategory.spentAmount).toBe(2000)
      expect(updatedCategory.name).toBe("Equipment") // Unchanged
    })

    test("should throw error when updating non-existent category", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      expect(() => {
        budget.updateBudgetCategory("NonExistent", { allocatedAmount: 1000 })
      }).toThrow("Budget category 'NonExistent' not found")
    })
  })

  describe("Business Logic Queries", () => {
    test("should correctly determine if within budget", () => {
      const withinBudget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 7500,
        currency: "EUR",
      })

      const overBudget = ProjectBudget.create({
        totalAmount: 5000,
        spentAmount: 6500,
        currency: "EUR",
      })

      expect(withinBudget.isWithinBudget()).toBe(true)
      expect(overBudget.isWithinBudget()).toBe(false)
    })

    test("should correctly determine if over budget", () => {
      const withinBudget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 7500,
        currency: "EUR",
      })

      const overBudget = ProjectBudget.create({
        totalAmount: 5000,
        spentAmount: 6500,
        currency: "EUR",
      })

      expect(withinBudget.isOverBudget()).toBe(false)
      expect(overBudget.isOverBudget()).toBe(true)
    })

    test("should determine if near budget limit", () => {
      const nearLimit = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 9200, // 92%
        currency: "EUR",
      })

      const notNearLimit = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 5000, // 50%
        currency: "EUR",
      })

      expect(nearLimit.isNearBudgetLimit(0.9)).toBe(true)
      expect(notNearLimit.isNearBudgetLimit(0.9)).toBe(false)
    })

    test("should handle zero budget for near limit check", () => {
      const zeroBudget = ProjectBudget.createEmpty("EUR")

      expect(zeroBudget.isNearBudgetLimit()).toBe(false)
    })

    test("should determine if has expenses", () => {
      const withExpenses = ProjectBudget.createWithAmount(
        10000,
        "EUR"
      ).addExpense(sampleExpense)
      const withoutExpenses = ProjectBudget.createWithAmount(10000, "EUR")

      expect(withExpenses.hasExpenses()).toBe(true)
      expect(withoutExpenses.hasExpenses()).toBe(false)
    })

    test("should determine if has budget categories", () => {
      const withCategories = ProjectBudget.create({
        totalAmount: 10000,
        currency: "EUR",
        budgetCategories: [sampleCategory],
      })
      const withoutCategories = ProjectBudget.createWithAmount(10000, "EUR")

      expect(withCategories.hasBudgetCategories()).toBe(true)
      expect(withoutCategories.hasBudgetCategories()).toBe(false)
    })

    test("should determine if can afford amount", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 3000,
        currency: "EUR",
      })

      expect(budget.canAfford(5000)).toBe(true) // 7000 remaining
      expect(budget.canAfford(8000)).toBe(false) // Only 7000 remaining
    })

    test("should determine if requires approval", () => {
      const normalBudget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 2000,
        currency: "EUR",
      })

      const nearLimitBudget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 8600, // 86%
        currency: "EUR",
      })

      expect(normalBudget.requiresApproval(500)).toBe(false)
      expect(normalBudget.requiresApproval(1500)).toBe(true) // Above threshold
      expect(nearLimitBudget.requiresApproval(100)).toBe(true) // Near limit
    })
  })

  describe("Expense Analysis", () => {
    const expenses: ProjectExpense[] = [
      {
        id: "1",
        description: "Equipment A",
        amount: 1000,
        currency: "EUR",
        category: "Equipment",
        date: "2024-01-15T00:00:00Z",
      },
      {
        id: "2",
        description: "Equipment B",
        amount: 2000,
        currency: "EUR",
        category: "Equipment",
        date: "2024-01-20T00:00:00Z",
      },
      {
        id: "3",
        description: "Software License",
        amount: 500,
        currency: "EUR",
        category: "Software",
        date: "2024-01-25T00:00:00Z",
      },
    ]

    test("should get expenses by category", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        currency: "EUR",
        expenses,
      })

      const equipmentExpenses = budget.getExpensesByCategory("Equipment")
      expect(equipmentExpenses).toHaveLength(2)
      expect(equipmentExpenses.every((e) => e.category === "Equipment")).toBe(
        true
      )
    })

    test("should get expenses by date range", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        currency: "EUR",
        expenses,
      })

      const rangeExpenses = budget.getExpensesByDateRange(
        "2024-01-15T00:00:00Z",
        "2024-01-20T00:00:00Z"
      )

      expect(rangeExpenses).toHaveLength(2)
    })

    test("should get total expenses by category", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        currency: "EUR",
        expenses,
      })

      const categoryTotals = budget.getTotalExpensesByCategory()

      expect(categoryTotals["Equipment"]).toBe(3000) // 1000 + 2000
      expect(categoryTotals["Software"]).toBe(500)
    })

    test("should get largest expenses", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        currency: "EUR",
        expenses,
      })

      const largest = budget.getLargestExpenses(2)

      expect(largest).toHaveLength(2)
      expect(largest[0].amount).toBe(2000) // Equipment B
      expect(largest[1].amount).toBe(1000) // Equipment A
    })
  })

  describe("Formatting and Display", () => {
    test("should format amount correctly", () => {
      const budget = ProjectBudget.createWithAmount(12345.67, "EUR")

      expect(budget.formatAmount()).toBe("€12,345.67")
      expect(budget.formatAmount(5000)).toBe("€5,000.00")
    })

    test("should format different currencies correctly", () => {
      const usdBudget = ProjectBudget.createWithAmount(12345.67, "USD")
      const gbpBudget = ProjectBudget.createWithAmount(12345.67, "GBP")

      expect(usdBudget.formatAmount()).toBe("$12,345.67")
      expect(gbpBudget.formatAmount()).toBe("£12,345.67")
    })

    test("should provide comprehensive budget summary", () => {
      const budget = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 7500,
        currency: "EUR",
      })

      const summary = budget.getSummary()

      expect(summary.total).toBe("€10,000.00")
      expect(summary.spent).toBe("€7,500.00")
      expect(summary.remaining).toBe("€2,500.00")
      expect(summary.utilization).toBe("75.0%")
      expect(summary.status).toBe("within_budget")
    })

    test("should show correct status in summary", () => {
      const nearLimit = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 9200,
        currency: "EUR",
      })

      const overBudget = ProjectBudget.create({
        totalAmount: 5000,
        spentAmount: 6000,
        currency: "EUR",
      })

      expect(nearLimit.getSummary().status).toBe("near_limit")
      expect(overBudget.getSummary().status).toBe("over_budget")
    })

    test("should convert to string correctly", () => {
      const budget = ProjectBudget.createWithAmount(12345.67, "USD")

      expect(budget.toString()).toBe("$12,345.67")
    })
  })

  describe("Value Object Properties", () => {
    test("should maintain immutability", () => {
      const original = ProjectBudget.createWithAmount(10000, "EUR")
      const modified = original.addExpense(sampleExpense)

      expect(original.spentAmount).toBe(0)
      expect(modified.spentAmount).toBe(1500)
      expect(original).not.toBe(modified)
    })

    test("should implement equality correctly", () => {
      const budget1 = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 2500,
        currency: "EUR",
      })

      const budget2 = ProjectBudget.create({
        totalAmount: 10000,
        spentAmount: 2500,
        currency: "EUR",
      })

      const budget3 = ProjectBudget.create({
        totalAmount: 15000,
        spentAmount: 2500,
        currency: "EUR",
      })

      expect(budget1.equals(budget2)).toBe(true)
      expect(budget1.equals(budget3)).toBe(false)
    })

    test("should return expense copies for immutability", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR").addExpense(
        sampleExpense
      )

      const expenses = budget.expenses
      expenses.push({
        id: "fake",
        description: "Should not affect original",
        amount: 100,
        currency: "EUR",
        category: "Test",
        date: "2024-01-01T00:00:00Z",
      })

      expect(budget.expenses).toHaveLength(1) // Original unchanged
    })
  })

  describe("Edge Cases and Error Handling", () => {
    test("should handle expenses with invalid currency", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")

      expect(() => {
        budget.addExpense({
          ...sampleExpense,
          currency: "INVALID",
        })
      }).toThrow("Invalid expense currency: INVALID")
    })

    test("should handle multiple currency conversions", () => {
      const budget = ProjectBudget.createWithAmount(10000, "EUR")
      const updated = budget
        .addExpense({ ...sampleExpense, currency: "USD", amount: 1100 })
        .addExpense({ ...sampleExpense, currency: "GBP", amount: 850 })

      expect(updated.expenses).toHaveLength(2)
      expect(updated.spentAmount).toBeGreaterThan(0)
    })

    test("should handle zero amounts gracefully", () => {
      const budget = ProjectBudget.createEmpty("EUR")

      expect(budget.getRemainingAmount()).toBe(0)
      expect(budget.getUtilizationPercentage()).toBe(0)
      expect(budget.getOverspendAmount()).toBe(0)
      expect(budget.canAfford(100)).toBe(false)
    })

    test("should handle precision in financial calculations", () => {
      const budget = ProjectBudget.create({
        totalAmount: 100.33,
        spentAmount: 67.89,
        currency: "EUR",
      })

      expect(budget.getRemainingAmount()).toBeCloseTo(32.44, 2)
      expect(budget.getUtilizationPercentage()).toBeCloseTo(67.67, 2)
    })
  })
})
