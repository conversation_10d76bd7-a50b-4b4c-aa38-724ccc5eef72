/**
 * ProjectStatus Value Object Unit Tests
 *
 * Comprehensive test coverage for project status transitions and business logic.
 * Tests cover all state transitions, business queries, and edge cases.
 */

import { describe, expect, test } from "vitest"
import { ProjectStatus, ProjectStatusType } from "../ProjectStatus"

describe("ProjectStatus Value Object", () => {
  describe("Creation and Validation", () => {
    test("should create a valid project status", () => {
      const status = ProjectStatus.create({
        status: "Active",
        reason: "Project started",
        changedBy: 1,
      })

      expect(status.status).toBe("Active")
      expect(status.reason).toBe("Project started")
      expect(status.changedBy).toBe(1)
      expect(status.changedAt).toBeDefined()
    })

    test("should create draft status with default values", () => {
      const status = ProjectStatus.createDraft()

      expect(status.status).toBe("Draft")
      expect(status.reason).toBe("Initial creation")
      expect(status.changedAt).toBeDefined()
    })

    test("should throw error for invalid status", () => {
      expect(() => {
        ProjectStatus.create({
          status: "InvalidStatus" as ProjectStatusType,
        })
      }).toThrow("Invalid project status: InvalidStatus")
    })

    test("should handle missing optional fields", () => {
      const status = ProjectStatus.create({
        status: "Draft",
      })

      expect(status.status).toBe("Draft")
      expect(status.reason).toBeUndefined()
      expect(status.changedBy).toBeUndefined()
      expect(status.changedAt).toBeDefined()
    })
  })

  describe("State Transitions", () => {
    test("should transition from Draft to Active", () => {
      const draft = ProjectStatus.createDraft()
      const active = draft.toActive("Ready to start", 1)

      expect(active.status).toBe("Active")
      expect(active.reason).toBe("Ready to start")
      expect(active.changedBy).toBe(1)
      expect(active).not.toBe(draft) // Immutability check
    })

    test("should transition from Draft to Planning", () => {
      const draft = ProjectStatus.createDraft()
      const planning = draft.toPlanning("Starting planning phase", 1)

      expect(planning.status).toBe("Planning")
      expect(planning.reason).toBe("Starting planning phase")
    })

    test("should transition through project lifecycle", () => {
      let status = ProjectStatus.createDraft()

      status = status.toPlanning("Begin planning", 1)
      expect(status.status).toBe("Planning")

      status = status.toDesignInProgress("Design started", 1)
      expect(status.status).toBe("Design In Progress")

      status = status.toProcurementInProgress("Procurement started", 1)
      expect(status.status).toBe("Procurement In Progress")

      status = status.toConstructionInProgress("Construction started", 1)
      expect(status.status).toBe("Construction In Progress")

      status = status.toCommissioning("Commissioning started", 1)
      expect(status.status).toBe("Commissioning")

      status = status.toCompleted("Project finished", 1)
      expect(status.status).toBe("Completed")

      status = status.toArchived("Final archival", 1)
      expect(status.status).toBe("Archived")
    })

    test("should allow On Hold transition from active states", () => {
      const active = ProjectStatus.create({ status: "Active" })
      const onHold = active.toOnHold("Temporary pause", 1)

      expect(onHold.status).toBe("On Hold")
      expect(onHold.reason).toBe("Temporary pause")
    })

    test("should allow resuming from On Hold", () => {
      const onHold = ProjectStatus.create({ status: "On Hold" })
      const active = onHold.toActive("Resuming work", 1)

      expect(active.status).toBe("Active")
      expect(active.reason).toBe("Resuming work")
    })

    test("should handle cancellation from various states", () => {
      const planning = ProjectStatus.create({ status: "Planning" })
      const cancelled = planning.toCancelled("Budget cut", 1)

      expect(cancelled.status).toBe("Cancelled")
      expect(cancelled.reason).toBe("Budget cut")
    })

    test("should allow cancelled projects to return to draft", () => {
      const cancelled = ProjectStatus.create({ status: "Cancelled" })
      const draft = cancelled.toDraft("Restarting project", 1)

      expect(draft.status).toBe("Draft")
      expect(draft.reason).toBe("Restarting project")
    })
  })

  describe("Invalid Transitions", () => {
    test("should throw error for invalid transition from Draft", () => {
      const draft = ProjectStatus.createDraft()

      expect(() => {
        draft.toCompleted("Cannot complete from draft", 1)
      }).toThrow(/Invalid status transition from 'Draft' to 'Completed'/)
    })

    test("should throw error for invalid transition from Archived", () => {
      const archived = ProjectStatus.create({ status: "Archived" })

      expect(() => {
        archived.toActive("Cannot reactivate archived", 1)
      }).toThrow(/Invalid status transition from 'Archived' to 'Active'/)
    })

    test("should throw error for backward transitions", () => {
      const construction = ProjectStatus.create({
        status: "Construction In Progress",
      })

      expect(() => {
        construction.toPlanning("Going back to planning", 1)
      }).toThrow(/Invalid status transition/)
    })

    test("should throw error for skipping phases", () => {
      const draft = ProjectStatus.createDraft()

      expect(() => {
        draft.toCommissioning("Skipping phases", 1)
      }).toThrow(/Invalid status transition/)
    })
  })

  describe("Valid Transitions Query", () => {
    test("should return correct valid transitions for Draft", () => {
      const draft = ProjectStatus.createDraft()
      const validTransitions = draft.getValidTransitions()

      expect(validTransitions).toEqual(["Active", "Planning", "Cancelled"])
    })

    test("should return correct valid transitions for Active", () => {
      const active = ProjectStatus.create({ status: "Active" })
      const validTransitions = active.getValidTransitions()

      expect(validTransitions).toEqual([
        "Design In Progress",
        "On Hold",
        "Cancelled",
        "Completed",
      ])
    })

    test("should return empty array for Archived", () => {
      const archived = ProjectStatus.create({ status: "Archived" })
      const validTransitions = archived.getValidTransitions()

      expect(validTransitions).toEqual([])
    })

    test("should return multiple options for On Hold", () => {
      const onHold = ProjectStatus.create({ status: "On Hold" })
      const validTransitions = onHold.getValidTransitions()

      expect(validTransitions).toContain("Active")
      expect(validTransitions).toContain("Planning")
      expect(validTransitions).toContain("Cancelled")
      expect(validTransitions.length).toBeGreaterThan(3)
    })
  })

  describe("Business Queries", () => {
    test("should correctly identify terminal states", () => {
      const archived = ProjectStatus.create({ status: "Archived" })
      const active = ProjectStatus.create({ status: "Active" })

      expect(archived.isTerminalState()).toBe(true)
      expect(active.isTerminalState()).toBe(false)
    })

    test("should correctly identify active status", () => {
      const active = ProjectStatus.create({ status: "Active" })
      const draft = ProjectStatus.createDraft()

      expect(active.isActive()).toBe(true)
      expect(draft.isActive()).toBe(false)
    })

    test("should correctly identify in-progress states", () => {
      const testCases = [
        { status: "Active" as ProjectStatusType, expected: true },
        { status: "Design In Progress" as ProjectStatusType, expected: true },
        {
          status: "Procurement In Progress" as ProjectStatusType,
          expected: true,
        },
        {
          status: "Construction In Progress" as ProjectStatusType,
          expected: true,
        },
        { status: "Commissioning" as ProjectStatusType, expected: true },
        { status: "Draft" as ProjectStatusType, expected: false },
        { status: "On Hold" as ProjectStatusType, expected: false },
        { status: "Completed" as ProjectStatusType, expected: false },
      ]

      testCases.forEach(({ status, expected }) => {
        const projectStatus = ProjectStatus.create({ status })
        expect(projectStatus.isInProgress()).toBe(expected)
      })
    })

    test("should correctly identify completed status", () => {
      const completed = ProjectStatus.create({ status: "Completed" })
      const active = ProjectStatus.create({ status: "Active" })

      expect(completed.isCompleted()).toBe(true)
      expect(active.isCompleted()).toBe(false)
    })

    test("should correctly identify cancelled status", () => {
      const cancelled = ProjectStatus.create({ status: "Cancelled" })
      const active = ProjectStatus.create({ status: "Active" })

      expect(cancelled.isCancelled()).toBe(true)
      expect(active.isCancelled()).toBe(false)
    })

    test("should correctly identify on hold status", () => {
      const onHold = ProjectStatus.create({ status: "On Hold" })
      const active = ProjectStatus.create({ status: "Active" })

      expect(onHold.isOnHold()).toBe(true)
      expect(active.isOnHold()).toBe(false)
    })

    test("should correctly identify draft status", () => {
      const draft = ProjectStatus.createDraft()
      const active = ProjectStatus.create({ status: "Active" })

      expect(draft.isDraft()).toBe(true)
      expect(active.isDraft()).toBe(false)
    })

    test("should correctly identify planning status", () => {
      const planning = ProjectStatus.create({ status: "Planning" })
      const active = ProjectStatus.create({ status: "Active" })

      expect(planning.isPlanning()).toBe(true)
      expect(active.isPlanning()).toBe(false)
    })
  })

  describe("Business Logic Queries", () => {
    test("should determine if project can be edited", () => {
      const editableStatuses = [
        "Draft",
        "Planning",
        "Active",
        "Design In Progress",
      ]
      const nonEditableStatuses = [
        "Completed",
        "Cancelled",
        "Archived",
        "On Hold",
      ]

      editableStatuses.forEach((status) => {
        const projectStatus = ProjectStatus.create({
          status: status as ProjectStatusType,
        })
        expect(projectStatus.canBeEdited()).toBe(true)
      })

      nonEditableStatuses.forEach((status) => {
        const projectStatus = ProjectStatus.create({
          status: status as ProjectStatusType,
        })
        expect(projectStatus.canBeEdited()).toBe(false)
      })
    })

    test("should determine if members can be added", () => {
      const archived = ProjectStatus.create({ status: "Archived" })
      const cancelled = ProjectStatus.create({ status: "Cancelled" })
      const active = ProjectStatus.create({ status: "Active" })

      expect(archived.canAddMembers()).toBe(false)
      expect(cancelled.canAddMembers()).toBe(false)
      expect(active.canAddMembers()).toBe(true)
    })

    test("should determine if approval is required", () => {
      const approvalStatuses = [
        "Planning",
        "Design In Progress",
        "Commissioning",
      ]
      const nonApprovalStatuses = ["Draft", "Active", "Completed", "Cancelled"]

      approvalStatuses.forEach((status) => {
        const projectStatus = ProjectStatus.create({
          status: status as ProjectStatusType,
        })
        expect(projectStatus.requiresApproval()).toBe(true)
      })

      nonApprovalStatuses.forEach((status) => {
        const projectStatus = ProjectStatus.create({
          status: status as ProjectStatusType,
        })
        expect(projectStatus.requiresApproval()).toBe(false)
      })
    })
  })

  describe("Transition Validation", () => {
    test("should validate allowed transitions", () => {
      const draft = ProjectStatus.createDraft()

      expect(draft.canTransitionTo("Active")).toBe(true)
      expect(draft.canTransitionTo("Planning")).toBe(true)
      expect(draft.canTransitionTo("Cancelled")).toBe(true)
      expect(draft.canTransitionTo("Completed")).toBe(false)
      expect(draft.canTransitionTo("Archived")).toBe(false)
    })

    test("should validate complex transition paths", () => {
      const status = ProjectStatus.create({ status: "Design In Progress" })

      expect(status.canTransitionTo("Procurement In Progress")).toBe(true)
      expect(status.canTransitionTo("Active")).toBe(true)
      expect(status.canTransitionTo("On Hold")).toBe(true)
      expect(status.canTransitionTo("Cancelled")).toBe(true)
      expect(status.canTransitionTo("Completed")).toBe(false)
      expect(status.canTransitionTo("Draft")).toBe(false)
    })
  })

  describe("Value Object Properties", () => {
    test("should maintain immutability", () => {
      const original = ProjectStatus.createDraft()
      const modified = original.toActive("Activating", 1)

      expect(original.status).toBe("Draft")
      expect(modified.status).toBe("Active")
      expect(original).not.toBe(modified)
    })

    test("should implement equality correctly", () => {
      const status1 = ProjectStatus.create({
        status: "Active",
        reason: "Test reason",
        changedBy: 1,
        changedAt: "2024-01-01T00:00:00Z",
      })

      const status2 = ProjectStatus.create({
        status: "Active",
        reason: "Test reason",
        changedBy: 1,
        changedAt: "2024-01-01T00:00:00Z",
      })

      const status3 = ProjectStatus.create({
        status: "Draft",
        reason: "Different reason",
        changedBy: 2,
        changedAt: "2024-01-02T00:00:00Z",
      })

      expect(status1.equals(status2)).toBe(true)
      expect(status1.equals(status3)).toBe(false)
    })

    test("should convert to string correctly", () => {
      const active = ProjectStatus.create({ status: "Active" })
      const designInProgress = ProjectStatus.create({
        status: "Design In Progress",
      })

      expect(active.toString()).toBe("Active")
      expect(designInProgress.toString()).toBe("Design In Progress")
    })

    test("should use default reason when not provided", () => {
      const draft = ProjectStatus.createDraft()
      const active = draft.toActive()

      expect(active.reason).toBe("Project activated")
    })
  })

  describe("Edge Cases", () => {
    test("should handle transitions with all parameters", () => {
      const draft = ProjectStatus.createDraft()
      const active = draft.toActive("Custom reason", 999)

      expect(active.status).toBe("Active")
      expect(active.reason).toBe("Custom reason")
      expect(active.changedBy).toBe(999)
      expect(active.changedAt).toBeDefined()
    })

    test("should handle transitions with partial parameters", () => {
      const draft = ProjectStatus.createDraft()
      const active = draft.toActive("Only reason")

      expect(active.status).toBe("Active")
      expect(active.reason).toBe("Only reason")
      expect(active.changedBy).toBeUndefined()
      expect(active.changedAt).toBeDefined()
    })

    test("should generate timestamp for transitions", () => {
      const draft = ProjectStatus.createDraft()
      const before = new Date().toISOString()
      const active = draft.toActive()
      const after = new Date().toISOString()

      expect(active.changedAt).toBeDefined()
      expect(active.changedAt! >= before).toBe(true)
      expect(active.changedAt! <= after).toBe(true)
    })
  })
})
