/**
 * ProjectBudget Value Object
 *
 * Encapsulates project budget with financial business logic.
 * Ensures immutability and proper financial calculations.
 *
 * Handles multi-currency budgets, expense tracking, and utilization analysis.
 */

export interface ProjectBudgetData {
  totalAmount: number
  spentAmount?: number
  currency: string
  allocatedAmount?: number
  expenses?: ProjectExpense[]
  budgetCategories?: BudgetCategory[]
}

export interface ProjectExpense {
  id: string
  description: string
  amount: number
  currency: string
  category: string
  date: string
  approvedBy?: number
  receiptUrl?: string
}

export interface BudgetCategory {
  name: string
  allocatedAmount: number
  spentAmount: number
  currency: string
}

export type CurrencyCode =
  | "USD"
  | "EUR"
  | "GBP"
  | "JPY"
  | "CAD"
  | "AUD"
  | "CHF"
  | "CNY"
  | "SEK"
  | "NOK"

export class ProjectBudget {
  private constructor(
    private readonly _totalAmount: number,
    private readonly _spentAmount: number,
    private readonly _currency: string,
    private readonly _allocatedAmount: number,
    private readonly _expenses: ProjectExpense[],
    private readonly _budgetCategories: BudgetCategory[]
  ) {
    this.validateBudget()
  }

  public static create(data: ProjectBudgetData): ProjectBudget {
    return new ProjectBudget(
      data.totalAmount,
      data.spentAmount || 0,
      data.currency,
      data.allocatedAmount || data.totalAmount,
      data.expenses || [],
      data.budgetCategories || []
    )
  }

  public static createEmpty(currency: string = "EUR"): ProjectBudget {
    return new ProjectBudget(0, 0, currency, 0, [], [])
  }

  public static createWithAmount(
    totalAmount: number,
    currency: string = "EUR"
  ): ProjectBudget {
    return new ProjectBudget(totalAmount, 0, currency, totalAmount, [], [])
  }

  // Getters
  public get totalAmount(): number {
    return this._totalAmount
  }

  public get spentAmount(): number {
    return this._spentAmount
  }

  public get currency(): string {
    return this._currency
  }

  public get allocatedAmount(): number {
    return this._allocatedAmount
  }

  public get expenses(): ProjectExpense[] {
    return this._expenses ? [...this._expenses] : [] // Return copy for immutability
  }

  public get budgetCategories(): BudgetCategory[] {
    return this._budgetCategories ? [...this._budgetCategories] : [] // Return copy for immutability
  }

  // Financial Calculation Methods
  public getRemainingAmount(): number {
    return Math.max(0, this._totalAmount - this._spentAmount)
  }

  public getUtilizationPercentage(): number {
    if (this._totalAmount === 0) return 0
    return Math.min(100, (this._spentAmount / this._totalAmount) * 100)
  }

  public getOverspendAmount(): number {
    return Math.max(0, this._spentAmount - this._totalAmount)
  }

  public calculateTaxAmount(taxRate: number): number {
    if (taxRate < 0 || taxRate > 1) {
      throw new Error("Tax rate must be between 0 and 1")
    }
    return this._totalAmount * taxRate
  }

  public calculateNetAmount(taxRate: number): number {
    return this._totalAmount + this.calculateTaxAmount(taxRate)
  }

  // Expense Management Methods
  public addExpense(expense: Omit<ProjectExpense, "id">): ProjectBudget {
    const newExpense: ProjectExpense = {
      ...expense,
      id: this.generateExpenseId(),
    }

    // Validate expense currency matches budget currency or is convertible
    this.validateExpenseCurrency(expense.currency)

    const convertedAmount = this.convertCurrency(
      expense.amount,
      expense.currency,
      this._currency
    )
    const newSpentAmount = this._spentAmount + convertedAmount
    const newExpenses = [...this._expenses, newExpense]

    return new ProjectBudget(
      this._totalAmount,
      newSpentAmount,
      this._currency,
      this._allocatedAmount,
      newExpenses,
      this._budgetCategories
    )
  }

  public removeExpense(expenseId: string): ProjectBudget {
    const expenseToRemove = this._expenses.find((e) => e.id === expenseId)
    if (!expenseToRemove) {
      throw new Error(`Expense with ID ${expenseId} not found`)
    }

    const convertedAmount = this.convertCurrency(
      expenseToRemove.amount,
      expenseToRemove.currency,
      this._currency
    )
    const newSpentAmount = Math.max(0, this._spentAmount - convertedAmount)
    const newExpenses = this._expenses.filter((e) => e.id !== expenseId)

    return new ProjectBudget(
      this._totalAmount,
      newSpentAmount,
      this._currency,
      this._allocatedAmount,
      newExpenses,
      this._budgetCategories
    )
  }

  public updateExpense(
    expenseId: string,
    updates: Partial<Omit<ProjectExpense, "id">>
  ): ProjectBudget {
    const expenseIndex = this._expenses.findIndex((e) => e.id === expenseId)
    if (expenseIndex === -1) {
      throw new Error(`Expense with ID ${expenseId} not found`)
    }

    const currentExpense = this._expenses[expenseIndex]
    const updatedExpense = { ...currentExpense, ...updates }

    // Recalculate spent amount
    const oldConvertedAmount = this.convertCurrency(
      currentExpense.amount,
      currentExpense.currency,
      this._currency
    )
    const newConvertedAmount = this.convertCurrency(
      updatedExpense.amount,
      updatedExpense.currency,
      this._currency
    )
    const spentDifference = newConvertedAmount - oldConvertedAmount
    const newSpentAmount = Math.max(0, this._spentAmount + spentDifference)

    const newExpenses = [...this._expenses]
    newExpenses[expenseIndex] = updatedExpense

    return new ProjectBudget(
      this._totalAmount,
      newSpentAmount,
      this._currency,
      this._allocatedAmount,
      newExpenses,
      this._budgetCategories
    )
  }

  // Budget Modification Methods
  public increaseBudget(amount: number, reason?: string): ProjectBudget {
    if (amount <= 0) {
      throw new Error("Budget increase amount must be positive")
    }

    return new ProjectBudget(
      this._totalAmount + amount,
      this._spentAmount,
      this._currency,
      this._allocatedAmount + amount,
      this._expenses,
      this._budgetCategories
    )
  }

  public decreaseBudget(amount: number, reason?: string): ProjectBudget {
    if (amount <= 0) {
      throw new Error("Budget decrease amount must be positive")
    }

    const newTotalAmount = this._totalAmount - amount
    if (newTotalAmount < 0) {
      throw new Error("Cannot decrease budget below zero")
    }

    const newAllocatedAmount = Math.max(0, this._allocatedAmount - amount);

    return new ProjectBudget(
      newTotalAmount,
      this._spentAmount,
      this._currency,
      newAllocatedAmount,
      this._expenses,
      this._budgetCategories
    )
  }

  public setBudget(newAmount: number): ProjectBudget {
    if (newAmount < 0) {
      throw new Error("Budget amount cannot be negative")
    }

    return new ProjectBudget(
      newAmount,
      this._spentAmount,
      this._currency,
      newAmount,
      this._expenses,
      this._budgetCategories
    )
  }

  // Currency Management Methods
  public convertCurrency(
    amount: number,
    fromCurrency: string,
    toCurrency: string
  ): number {
    if (fromCurrency === toCurrency) return amount

    // Simplified currency conversion - in real implementation, use actual exchange rates
    const exchangeRates: Record<string, Record<string, number>> = {
      EUR: { USD: 1.1, GBP: 0.85, JPY: 130, CAD: 1.35 },
      USD: { EUR: 0.91, GBP: 0.77, JPY: 118, CAD: 1.23 },
      GBP: { EUR: 1.18, USD: 1.3, JPY: 153, CAD: 1.6 },
    }

    const rate = exchangeRates[fromCurrency]?.[toCurrency]
    if (!rate) {
      // Default to 1:1 if rate not found (should integrate with real exchange rate service)
      console.warn(
        `Exchange rate not found for ${fromCurrency} to ${toCurrency}, using 1:1`
      )
      return amount
    }

    return amount * rate
  }

  public convertToCurrency(targetCurrency: string): ProjectBudget {
    if (targetCurrency === this._currency) return this

    const newTotalAmount = this.convertCurrency(
      this._totalAmount,
      this._currency,
      targetCurrency
    )
    const newSpentAmount = this.convertCurrency(
      this._spentAmount,
      this._currency,
      targetCurrency
    )

    // Convert all expenses
    const newExpenses = this._expenses.map((expense) => ({
      ...expense,
      amount: this.convertCurrency(
        expense.amount,
        expense.currency,
        targetCurrency
      ),
      currency: targetCurrency,
    }))

    // Convert budget categories
    const newBudgetCategories = this._budgetCategories.map((category) => ({
      ...category,
      allocatedAmount: this.convertCurrency(
        category.allocatedAmount,
        category.currency,
        targetCurrency
      ),
      spentAmount: this.convertCurrency(
        category.spentAmount,
        category.currency,
        targetCurrency
      ),
      currency: targetCurrency,
    }))

    return new ProjectBudget(
      newTotalAmount,
      newSpentAmount,
      targetCurrency,
      this._allocatedAmount,
      newExpenses,
      newBudgetCategories
    )
  }

  // Category Management Methods
  public addBudgetCategory(category: BudgetCategory): ProjectBudget {
    const existingCategory = this._budgetCategories.find(
      (c) => c.name === category.name
    )
    if (existingCategory) {
      throw new Error(`Budget category '${category.name}' already exists`)
    }

    const newCategories = [...this._budgetCategories, category]
    return new ProjectBudget(
      this._totalAmount,
      this._spentAmount,
      this._currency,
      this._allocatedAmount,
      this._expenses,
      newCategories
    )
  }

  public updateBudgetCategory(
    categoryName: string,
    updates: Partial<BudgetCategory>
  ): ProjectBudget {
    const categoryIndex = this._budgetCategories.findIndex(
      (c) => c.name === categoryName
    )
    if (categoryIndex === -1) {
      throw new Error(`Budget category '${categoryName}' not found`)
    }

    const newCategories = [...this._budgetCategories]
    newCategories[categoryIndex] = {
      ...newCategories[categoryIndex],
      ...updates,
    }

    return new ProjectBudget(
      this._totalAmount,
      this._spentAmount,
      this._currency,
      this._allocatedAmount,
      this._expenses,
      newCategories
    )
  }

  // Business Logic Queries
  public isWithinBudget(): boolean {
    return this._spentAmount <= this._totalAmount
  }

  public isOverBudget(): boolean {
    return this._spentAmount > this._totalAmount
  }

  public isNearBudgetLimit(threshold: number = 0.9): boolean {
    if (this._totalAmount === 0) return false
    return this.getUtilizationPercentage() >= threshold * 100
  }

  public hasExpenses(): boolean {
    return this._expenses.length > 0
  }

  public hasBudgetCategories(): boolean {
    return this._budgetCategories.length > 0
  }

  public canAfford(amount: number): boolean {
    return this.getRemainingAmount() >= amount
  }

  public requiresApproval(
    amount: number,
    approvalThreshold: number = 1000
  ): boolean {
    return amount >= approvalThreshold || this.isNearBudgetLimit(0.85)
  }

  // Expense Analysis Methods
  public getExpensesByCategory(category: string): ProjectExpense[] {
    return this._expenses.filter((expense) => expense.category === category)
  }

  public getExpensesByDateRange(
    startDate: string,
    endDate: string
  ): ProjectExpense[] {
    return this._expenses.filter(
      (expense) => expense.date >= startDate && expense.date <= endDate
    )
  }

  public getTotalExpensesByCategory(): Record<string, number> {
    const categoryTotals: Record<string, number> = {}

    this._expenses.forEach((expense) => {
      const convertedAmount = this.convertCurrency(
        expense.amount,
        expense.currency,
        this._currency
      )
      categoryTotals[expense.category] =
        (categoryTotals[expense.category] || 0) + convertedAmount
    })

    return categoryTotals
  }

  public getLargestExpenses(limit: number = 5): ProjectExpense[] {
    return [...this._expenses]
      .sort((a, b) => {
        const amountA = this.convertCurrency(
          a.amount,
          a.currency,
          this._currency
        )
        const amountB = this.convertCurrency(
          b.amount,
          b.currency,
          this._currency
        )
        return amountB - amountA
      })
      .slice(0, limit)
  }

  // Validation and Formatting Methods
  public formatAmount(amount: number = this._totalAmount): string {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: this._currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount)
  }

  public getSummary(): {
    total: string
    spent: string
    remaining: string
    utilization: string
    status: "within_budget" | "near_limit" | "over_budget"
  } {
    const utilization = this.getUtilizationPercentage()
    let status: "within_budget" | "near_limit" | "over_budget"

    if (this.isOverBudget()) {
      status = "over_budget"
    } else if (this.isNearBudgetLimit(0.9)) {
      status = "near_limit"
    } else {
      status = "within_budget"
    }

    return {
      total: this.formatAmount(this._totalAmount),
      spent: this.formatAmount(this._spentAmount),
      remaining: this.formatAmount(this.getRemainingAmount()),
      utilization: `${utilization.toFixed(1)}%`,
      status,
    }
  }

  // Value Object Equality
  public equals(other: ProjectBudget): boolean {
    return (
      this._totalAmount === other._totalAmount &&
      this._spentAmount === other._spentAmount &&
      this._currency === other._currency &&
      this._expenses.length === other._expenses.length &&
      this._budgetCategories.length === other._budgetCategories.length &&
      JSON.stringify(
        this._expenses.sort((a, b) => a.id.localeCompare(b.id))
      ) ===
        JSON.stringify(other._expenses.sort((a, b) => a.id.localeCompare(b.id)))
    )
  }

  public toString(): string {
    return this.formatAmount()
  }

  // Private Methods
  private validateBudget(): void {
    if (this._totalAmount < 0) {
      throw new Error("Total budget amount cannot be negative")
    }

    if (this._spentAmount < 0) {
      throw new Error("Spent amount cannot be negative")
    }

    if (!this.isValidCurrency(this._currency)) {
      throw new Error(`Invalid currency code: ${this._currency}`)
    }

    // Validate expenses
    if (this._expenses) {
      this._expenses.forEach((expense) => {
        if (expense.amount < 0) {
          throw new Error(`Expense amount cannot be negative: ${expense.id}`)
        }
        if (!this.isValidCurrency(expense.currency)) {
          throw new Error(`Invalid expense currency: ${expense.currency}`)
        }
      })
    }
  }

  private isValidCurrency(currency: string): boolean {
    const validCurrencies = [
      "USD",
      "EUR",
      "GBP",
      "JPY",
      "CAD",
      "AUD",
      "CHF",
      "CNY",
      "SEK",
      "NOK",
    ]
    return validCurrencies.includes(currency)
  }

  private validateExpenseCurrency(currency: string): void {
    if (!this.isValidCurrency(currency)) {
      throw new Error(`Invalid expense currency: ${currency}`)
    }
  }

  private generateExpenseId(): string {
    return `expense_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
  }
}