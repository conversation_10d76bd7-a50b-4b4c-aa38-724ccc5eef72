/**
 * ProjectStatus Value Object
 *
 * Encapsulates project status with business rules for state transitions.
 * Ensures immutability and proper lifecycle management.
 */

import { ProjectStatusEnum } from "@/types/api"

export interface ProjectStatusData {
  status: ProjectStatusEnum
  reason?: string
  changedBy?: number
  changedAt?: string
}

export class ProjectStatus {
  private constructor(
    private readonly _status: ProjectStatusEnum,
    private readonly _reason?: string,
    private readonly _changedBy?: number,
    private readonly _changedAt?: string
  ) {
    this.validateStatus()
  }

  public static create(data: ProjectStatusData): ProjectStatus {
    return new ProjectStatus(
      data.status,
      data.reason,
      data.changedBy,
      data.changedAt || new Date().toISOString()
    )
  }

  public static createDraft(): ProjectStatus {
    return new ProjectStatus(
      "Draft",
      "Initial creation",
      undefined,
      new Date().toISOString()
    )
  }

  // Getters
  public get status(): ProjectStatusEnum {
    return this._status
  }

  public get reason(): string | undefined {
    return this._reason
  }

  public get changedBy(): number | undefined {
    return this._changedBy
  }

  public get changedAt(): string | undefined {
    return this._changedAt
  }

  public get lastTransitionAt(): string | undefined {
    return this._changedAt
  }

  // State Transition Methods
  public toDraft(reason?: string, changedBy?: number): ProjectStatus {
    this.validateTransition("Draft")
    return new ProjectStatus(
      "Draft",
      reason || "Reverted to draft",
      changedBy,
      new Date().toISOString()
    )
  }

  public toActive(reason?: string, changedBy?: number): ProjectStatus {
    this.validateTransition("Active")
    return new ProjectStatus(
      "Active",
      reason || "Project activated",
      changedBy,
      new Date().toISOString()
    )
  }

  public toPlanning(reason?: string, changedBy?: number): ProjectStatus {
    this.validateTransition("Planning")
    return new ProjectStatus(
      "Planning",
      reason || "Moved to planning phase",
      changedBy,
      new Date().toISOString()
    )
  }

  public toDesignInProgress(
    reason?: string,
    changedBy?: number
  ): ProjectStatus {
    this.validateTransition("Design In Progress")
    return new ProjectStatus(
      "Design In Progress",
      reason || "Design phase started",
      changedBy,
      new Date().toISOString()
    )
  }

  public toProcurementInProgress(
    reason?: string,
    changedBy?: number
  ): ProjectStatus {
    this.validateTransition("Procurement In Progress")
    return new ProjectStatus(
      "Procurement In Progress",
      reason || "Procurement phase started",
      changedBy,
      new Date().toISOString()
    )
  }

  public toConstructionInProgress(
    reason?: string,
    changedBy?: number
  ): ProjectStatus {
    this.validateTransition("Construction In Progress")
    return new ProjectStatus(
      "Construction In Progress",
      reason || "Construction phase started",
      changedBy,
      new Date().toISOString()
    )
  }

  public toCommissioning(reason?: string, changedBy?: number): ProjectStatus {
    this.validateTransition("Commissioning")
    return new ProjectStatus(
      "Commissioning",
      reason || "Commissioning phase started",
      changedBy,
      new Date().toISOString()
    )
  }

  public toOnHold(reason?: string, changedBy?: number): ProjectStatus {
    this.validateTransition("On Hold")
    return new ProjectStatus(
      "On Hold",
      reason || "Project placed on hold",
      changedBy,
      new Date().toISOString()
    )
  }

  public toCompleted(reason?: string, changedBy?: number): ProjectStatus {
    this.validateTransition("Completed")
    return new ProjectStatus(
      "Completed",
      reason || "Project completed successfully",
      changedBy,
      new Date().toISOString()
    )
  }

  public toCancelled(reason?: string, changedBy?: number): ProjectStatus {
    this.validateTransition("Cancelled")
    return new ProjectStatus(
      "Cancelled",
      reason || "Project cancelled",
      changedBy,
      new Date().toISOString()
    )
  }

  public toArchived(reason?: string, changedBy?: number): ProjectStatus {
    this.validateTransition("Archived")
    return new ProjectStatus(
      "Archived",
      reason || "Project archived",
      changedBy,
      new Date().toISOString()
    )
  }

  // Business Logic Methods
  public canTransitionTo(targetStatus: ProjectStatusEnum): boolean {
    const validTransitions = this.getValidTransitions()
    return validTransitions.includes(targetStatus)
  }

  public getValidTransitions(): ProjectStatusEnum[] {
    switch (this._status) {
      case "Draft":
        return ["Active", "Planning", "Cancelled"]

      case "Planning":
        return ["Active", "Design In Progress", "On Hold", "Cancelled"]

      case "Active":
        return ["Design In Progress", "On Hold", "Cancelled", "Completed"]

      case "Design In Progress":
        return ["Procurement In Progress", "Active", "On Hold", "Cancelled"]

      case "Procurement In Progress":
        return [
          "Construction In Progress",
          "Design In Progress",
          "On Hold",
          "Cancelled",
        ]

      case "Construction In Progress":
        return [
          "Commissioning",
          "Procurement In Progress",
          "On Hold",
          "Cancelled",
        ]

      case "Commissioning":
        return ["Completed", "Construction In Progress", "On Hold", "Cancelled"]

      case "On Hold":
        return [
          "Active",
          "Planning",
          "Design In Progress",
          "Procurement In Progress",
          "Construction In Progress",
          "Commissioning",
          "Cancelled",
        ]

      case "Completed":
        return ["Archived"]

      case "Cancelled":
        return ["Draft", "Archived"]

      case "Archived":
        return [] // Terminal state

      default:
        return []
    }
  }

  // Business Queries
  public isTerminalState(): boolean {
    return this._status === "Archived"
  }

  public isActive(): boolean {
    return this._status === "Active"
  }

  public isInProgress(): boolean {
    return [
      "Active",
      "Design In Progress",
      "Procurement In Progress",
      "Construction In Progress",
      "Commissioning",
    ].includes(this._status)
  }

  public isCompleted(): boolean {
    return this._status === "Completed"
  }

  public isCancelled(): boolean {
    return this._status === "Cancelled"
  }

  public isOnHold(): boolean {
    return this._status === "On Hold"
  }

  public isDraft(): boolean {
    return this._status === "Draft"
  }

  public isPlanning(): boolean {
    return this._status === "Planning"
  }

  public canBeEdited(): boolean {
    return ["Draft", "Planning", "Active", "Design In Progress"].includes(
      this._status
    )
  }

  public canAddMembers(): boolean {
    return !this.isTerminalState() && !this.isCancelled()
  }

  public requiresApproval(): boolean {
    return ["Planning", "Design In Progress", "Commissioning"].includes(
      this._status
    )
  }

  // Value Object Equality
  public equals(other: ProjectStatus): boolean {
    return (
      this._status === other._status &&
      this._reason === other._reason &&
      this._changedBy === other._changedBy &&
      this._changedAt === other._changedAt
    )
  }

  public toString(): string {
    return this._status
  }

  // Private Methods
  private validateStatus(): void {
    const validStatuses: ProjectStatusEnum[] = [
      "Draft",
      "Active",
      "On Hold",
      "Completed",
      "Cancelled",
      "Archived",
      "Planning",
      "Design In Progress",
      "Procurement In Progress",
      "Construction In Progress",
      "Commissioning",
    ]

    if (!validStatuses.includes(this._status)) {
      throw new Error(`Invalid project status: ${this._status}`)
    }
  }

  private validateTransition(targetStatus: ProjectStatusEnum): void {
    if (!this.canTransitionTo(targetStatus)) {
      throw new Error(
        `Invalid status transition from '${this._status}' to '${targetStatus}'. ` +
          `Valid transitions: [${this.getValidTransitions().join(", ")}]`
      )
    }
  }
}
