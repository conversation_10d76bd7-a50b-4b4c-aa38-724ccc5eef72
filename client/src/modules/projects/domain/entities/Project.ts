/**
 * Project Entity (Aggregate Root)
 *
 * The main aggregate root for the Project Management bounded context.
 * Orchestrates ProjectMembers, ProjectStatus, and ProjectBudget to maintain
 * consistency and enforce business invariants within the aggregate boundary.
 *
 * Implements the full project lifecycle from creation to archival,
 * team management, budget control, and offline mode capabilities.
 */

import { UserRoleEnum } from "@/types/api"

import {
  ProjectBudget,
  ProjectBudgetData,
} from "../value-objects/ProjectBudget"
import { ProjectStatus } from "../value-objects/ProjectStatus"
import { TeamRole } from "../value-objects/TeamRole"
import { ProjectMember } from "./ProjectMember"

export interface ProjectData {
  id: string
  name: string
  description?: string
  clientId?: number
  ownerId: number
  status: ProjectStatus
  budget: ProjectBudget
  members: ProjectMember[]
  createdAt: string
  updatedAt: string
  startDate?: string
  endDate?: string
  isOfflineMode?: boolean
  tags?: string[]
  priority?: "Low" | "Medium" | "High" | "Critical"
  location?: string
  externalProjectId?: string
}

export interface CreateProjectData {
  id: string
  name: string
  description?: string
  clientId?: number
  ownerId: number
  budgetData?: ProjectBudgetData
  startDate?: string
  endDate?: string
  tags?: string[]
  priority?: "Low" | "Medium" | "High" | "Critical"
  location?: string
  externalProjectId?: string
}

export class Project {
  private constructor(
    private readonly _id: string,
    private _name: string,
    private _description: string | undefined,
    private readonly _clientId: number | undefined,
    private readonly _ownerId: number,
    private _status: ProjectStatus,
    private _budget: ProjectBudget,
    private _members: ProjectMember[],
    private readonly _createdAt: string,
    private _updatedAt: string,
    private _startDate: string | undefined,
    private _endDate: string | undefined,
    private _isOfflineMode: boolean,
    private _tags: string[],
    private _priority: "Low" | "Medium" | "High" | "Critical",
    private _location: string | undefined,
    private _externalProjectId: string | undefined
  ) {
    this.validateProject()
    this.enforceInvariants()
  }

  public static create(data: CreateProjectData): Project {
    // Validate input data first
    if (!data.ownerId || data.ownerId <= 0) {
      throw new Error("Owner ID must be a positive number")
    }

    const now = new Date().toISOString()
    const status = ProjectStatus.createDraft()
    const budget = data.budgetData
      ? ProjectBudget.create(data.budgetData)
      : ProjectBudget.createEmpty()

    // Create owner as first team member with administrator role
    const ownerMember = ProjectMember.createNew(
      `${data.id}_owner`,
      data.ownerId,
      data.id,
      TeamRole.createAdministrator()
    )

    return new Project(
      data.id,
      data.name,
      data.description,
      data.clientId,
      data.ownerId,
      status,
      budget,
      [ownerMember],
      now,
      now,
      data.startDate,
      data.endDate,
      false, // Default offline mode to false
      data.tags || [],
      data.priority || "Medium",
      data.location,
      data.externalProjectId
    )
  }

  public static restore(data: ProjectData): Project {
    return new Project(
      data.id,
      data.name,
      data.description,
      data.clientId,
      data.ownerId,
      data.status,
      data.budget,
      data.members,
      data.createdAt,
      data.updatedAt,
      data.startDate,
      data.endDate,
      data.isOfflineMode || false,
      data.tags || [],
      data.priority || "Medium",
      data.location,
      data.externalProjectId
    )
  }

  // Getters
  public get id(): string {
    return this._id
  }

  public get name(): string {
    return this._name
  }

  public get description(): string | undefined {
    return this._description
  }

  public get clientId(): number | undefined {
    return this._clientId
  }

  public get ownerId(): number {
    return this._ownerId
  }

  public get status(): ProjectStatus {
    return this._status
  }

  public get budget(): ProjectBudget {
    return this._budget
  }

  public get members(): ProjectMember[] {
    return [...this._members] // Return copy for immutability
  }

  public get createdAt(): string {
    return this._createdAt
  }

  public get updatedAt(): string {
    return this._updatedAt
  }

  public get startDate(): string | undefined {
    return this._startDate
  }

  public get endDate(): string | undefined {
    return this._endDate
  }

  public get isOfflineMode(): boolean {
    return this._isOfflineMode
  }

  public get tags(): string[] {
    return [...this._tags]
  }

  public get priority(): "Low" | "Medium" | "High" | "Critical" {
    return this._priority
  }

  public get location(): string | undefined {
    return this._location
  }

  public get externalProjectId(): string | undefined {
    return this._externalProjectId
  }

  // Project Lifecycle Methods

  /**
   * Activates the project from draft or planning state
   * Validates project readiness and transitions status
   */
  public activateProject(activatedBy: number, reason?: string): Project {
    if (!this.canBeActivated()) {
      throw new Error("Project cannot be activated in current state")
    }

    const activator = this.getMemberByUserId(activatedBy)
    if (!activator || !activator.canManageProject()) {
      throw new Error("User does not have permission to activate project")
    }

    const newStatus = this._status.toActive(reason, activatedBy)

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      newStatus,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Completes the project from active or in-progress states
   * Validates completion readiness and handles final processing
   */
  public completeProject(completedBy: number, reason?: string): Project {
    if (!this.canBeCompleted()) {
      throw new Error("Project cannot be completed in current state")
    }

    const completer = this.getMemberByUserId(completedBy)
    if (!completer || !completer.canManageProject()) {
      throw new Error("User does not have permission to complete project")
    }

    const newStatus = this._status.toCompleted(reason, completedBy)

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      newStatus,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Archives the completed project for long-term storage
   * Final state transition with comprehensive validation
   */
  public archiveProject(archivedBy: number, reason?: string): Project {
    if (!this.canBeArchived()) {
      throw new Error("Project cannot be archived in current state")
    }

    const archiver = this.getMemberByUserId(archivedBy)
    if (!archiver || !archiver.canManageProject()) {
      throw new Error("User does not have permission to archive project")
    }

    const newStatus = this._status.toArchived(reason, archivedBy)

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      newStatus,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Puts project on hold temporarily
   * Preserves state for later resumption
   */
  public putOnHold(heldBy: number, reason?: string): Project {
    if (!this.canBePutOnHold()) {
      throw new Error("Project cannot be put on hold in current state")
    }

    const holder = this.getMemberByUserId(heldBy)
    if (!holder || !holder.canManageProject()) {
      throw new Error("User does not have permission to put project on hold")
    }

    const newStatus = this._status.toOnHold(reason, heldBy)

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      newStatus,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Cancels the project from various states
   * Handles cleanup and final disposition
   */
  public cancelProject(cancelledBy: number, reason?: string): Project {
    if (!this.canBeCancelled()) {
      throw new Error("Project cannot be cancelled in current state")
    }

    const canceller = this.getMemberByUserId(cancelledBy)
    if (!canceller || !canceller.canManageProject()) {
      throw new Error("User does not have permission to cancel project")
    }

    const newStatus = this._status.toCancelled(reason, cancelledBy)

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      newStatus,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  // Team Management Methods

  /**
   * Adds a new team member to the project
   * Validates permissions and member constraints
   */
  public addTeamMember(
    userId: number,
    role: TeamRole,
    addedBy: number,
    expiresAt?: string,
    notes?: string
  ): Project {
    // Check status first (but not member limit)
    if (!this._status.canAddMembers()) {
      throw new Error("Cannot add members to project in current state")
    }

    const adder = this.getMemberByUserId(addedBy)
    if (!adder || !adder.canManageTeam()) {
      throw new Error("User does not have permission to add team members")
    }

    if (!adder.canAssignRole(role)) {
      throw new Error(
        `User does not have permission to assign role '${role.role}'`
      )
    }

    // Check if user is already a member
    if (this.getMemberByUserId(userId)) {
      throw new Error("User is already a member of this project")
    }

    // Validate member limit based on project constraints
    if (this._members.length >= this.getMaxMemberLimit()) {
      throw new Error("Project has reached maximum member limit")
    }

    const newMember = ProjectMember.createNew(
      `${this._id}_${userId}_${Date.now()}`,
      userId,
      this._id,
      role,
      addedBy,
      expiresAt
    )

    const updatedMember = notes ? newMember.addNotes(notes, addedBy) : newMember
    const newMembers = [...this._members, updatedMember]

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      this._status,
      this._budget,
      newMembers,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Removes a team member from the project
   * Validates permissions and business constraints
   */
  public removeTeamMember(
    userId: number,
    removedBy: number,
    reason?: string
  ): Project {
    const remover = this.getMemberByUserId(removedBy)
    if (!remover || !remover.canManageTeam()) {
      throw new Error("User does not have permission to remove team members")
    }

    const memberToRemove = this.getMemberByUserId(userId)
    if (!memberToRemove) {
      throw new Error("User is not a member of this project")
    }

    // Cannot remove project owner
    if (userId === this._ownerId) {
      throw new Error("Cannot remove project owner from project")
    }

    // Cannot remove if it would leave no administrators
    if (
      memberToRemove.role.role === "Administrator" &&
      this.getAdministratorCount() <= 1
    ) {
      throw new Error("Cannot remove last administrator from project")
    }

    const newMembers = this._members.filter(
      (member) => member.userId !== userId
    )

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      this._status,
      this._budget,
      newMembers,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Updates a team member's role
   * Validates permissions and role transition rules
   */
  public updateMemberRole(
    userId: number,
    newRole: TeamRole,
    updatedBy: number,
    reason?: string
  ): Project {
    const updater = this.getMemberByUserId(updatedBy)
    if (!updater || !updater.canManageTeam()) {
      throw new Error("User does not have permission to update member roles")
    }

    if (!updater.canAssignRole(newRole)) {
      throw new Error(
        `User does not have permission to assign role '${newRole.role}'`
      )
    }

    const memberToUpdate = this.getMemberByUserId(userId)
    if (!memberToUpdate) {
      throw new Error("User is not a member of this project")
    }

    // Special validation for owner role changes
    if (userId === this._ownerId && newRole.role !== "Administrator") {
      throw new Error("Project owner must maintain administrator privileges")
    }

    const updatedMember = memberToUpdate.assignRole(newRole, updatedBy, reason)
    const newMembers = this._members.map((member) =>
      member.userId === userId ? updatedMember : member
    )

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      this._status,
      this._budget,
      newMembers,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  // Budget Management Methods

  /**
   * Updates the project budget
   * Validates permissions and budget constraints
   */
  public updateBudget(
    newBudget: ProjectBudget,
    updatedBy: number,
    reason?: string
  ): Project {
    const updater = this.getMemberByUserId(updatedBy)
    if (!updater || !updater.canViewFinancials()) {
      throw new Error("User does not have permission to update project budget")
    }

    // Additional validation for budget changes in active projects
    if (
      this.isActive() &&
      newBudget.totalAmount < this._budget.totalAmount * 0.5
    ) {
      throw new Error("Cannot reduce budget by more than 50% in active project")
    }

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      this._status,
      newBudget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Increases the project budget
   * Simplified method for budget expansion
   */
  public increaseBudget(
    amount: number,
    increasedBy: number,
    reason?: string
  ): Project {
    const updater = this.getMemberByUserId(increasedBy)
    if (!updater || !updater.canViewFinancials()) {
      throw new Error(
        "User does not have permission to increase project budget"
      )
    }

    const newBudget = this._budget.increaseBudget(amount, reason)

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      this._status,
      newBudget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  // Project Configuration Methods

  /**
   * Toggles offline mode for the project
   * Enables/disables offline capabilities
   */
  public toggleOfflineMode(toggledBy: number, reason?: string): Project {
    const toggler = this.getMemberByUserId(toggledBy)
    if (!toggler || !toggler.canManageProject()) {
      throw new Error("User does not have permission to toggle offline mode")
    }

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      this._status,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      !this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Updates project metadata
   * Handles name, description, and other basic properties
   */
  public updateProjectInfo(
    updates: {
      name?: string
      description?: string
      startDate?: string
      endDate?: string
      tags?: string[]
      priority?: "Low" | "Medium" | "High" | "Critical"
      location?: string
    },
    updatedBy: number
  ): Project {
    const updater = this.getMemberByUserId(updatedBy)
    if (!updater || !updater.canEditProject()) {
      throw new Error(
        "User does not have permission to update project information"
      )
    }

    // Validate dates if provided
    if (
      updates.startDate &&
      updates.endDate &&
      updates.startDate >= updates.endDate
    ) {
      throw new Error("Start date must be before end date")
    }

    return new Project(
      this._id,
      updates.name ?? this._name,
      updates.description ?? this._description,
      this._clientId,
      this._ownerId,
      this._status,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      updates.startDate ?? this._startDate,
      updates.endDate ?? this._endDate,
      this._isOfflineMode,
      updates.tags ?? this._tags,
      updates.priority ?? this._priority,
      updates.location ?? this._location,
      this._externalProjectId
    )
  }

  // Business Queries

  /**
   * Checks if the project is currently active
   * Delegates to status value object
   */
  public isActive(): boolean {
    return this._status.isActive()
  }

  /**
   * Checks if the project is completed
   * Delegates to status value object
   */
  public isCompleted(): boolean {
    return this._status.isCompleted()
  }

  /**
   * Checks if the project is cancelled
   * Delegates to status value object
   */
  public isCancelled(): boolean {
    return this._status.isCancelled()
  }

  /**
   * Checks if the project is on hold
   * Delegates to status value object
   */
  public isOnHold(): boolean {
    return this._status.isOnHold()
  }

  /**
   * Checks if members can be added to the project
   * Based on status and business rules
   */
  public canAddMembers(): boolean {
    return this._status.canAddMembers() && !this.hasReachedMemberLimit()
  }

  /**
   * Checks if the project is over budget
   * Delegates to budget value object
   */
  public isOverBudget(): boolean {
    return this._budget.isOverBudget()
  }

  /**
   * Checks if the project has expired members
   * Scans all members for expiration status
   */
  public hasExpiredMembers(): boolean {
    return this._members.some((member) => member.hasExpired())
  }

  /**
   * Checks if the project has members approaching expiration
   * Useful for proactive management
   */
  public hasMembersApproachingExpiration(warningDays: number = 7): boolean {
    return this._members.some((member) =>
      member.isApproachingExpiration(warningDays)
    )
  }

  /**
   * Checks if the project requires approval for current operations
   * Based on status and other factors
   */
  public requiresApproval(): boolean {
    return this._status.requiresApproval() || this.isOverBudget()
  }

  // Member Query Methods

  /**
   * Gets a member by user ID
   * Returns undefined if not found
   */
  public getMemberByUserId(userId: number): ProjectMember | undefined {
    return this._members.find(
      (member) => member.userId === userId && member.isActive
    )
  }

  /**
   * Gets all active members
   * Filters out inactive and expired members
   */
  public getActiveMembers(): ProjectMember[] {
    return this._members.filter((member) => member.isActive)
  }

  /**
   * Gets members by role type
   * Useful for role-based operations
   */
  public getMembersByRole(role: UserRoleEnum): ProjectMember[] {
    return this._members.filter(
      (member) => member.role.role === role && member.isActive
    )
  }

  /**
   * Gets the project owner as a member
   * Always returns the owner member
   */
  public getOwner(): ProjectMember {
    const owner = this.getMemberByUserId(this._ownerId)
    if (!owner) {
      throw new Error(
        "Project owner not found in member list - data integrity issue"
      )
    }
    return owner
  }

  /**
   * Gets all administrators
   * Critical for permission validation
   */
  public getAdministrators(): ProjectMember[] {
    return this.getMembersByRole("Administrator")
  }

  /**
   * Gets count of administrators
   * Used for validation rules
   */
  public getAdministratorCount(): number {
    return this.getAdministrators().length
  }

  // Aggregate Summary Methods

  /**
   * Gets comprehensive project summary
   * Useful for reporting and dashboard views
   */
  public getProjectSummary(): {
    id: string
    name: string
    status: string
    memberCount: number
    activeMemberCount: number
    budgetSummary: ReturnType<ProjectBudget["getSummary"]>
    hasExpiredMembers: boolean
    requiresApproval: boolean
    isOfflineMode: boolean
    priority: string
    daysActive: number
  } {
    return {
      id: this._id,
      name: this._name,
      status: this._status.status,
      memberCount: this._members.length,
      activeMemberCount: this.getActiveMembers().length,
      budgetSummary: this._budget.getSummary(),
      hasExpiredMembers: this.hasExpiredMembers(),
      requiresApproval: this.requiresApproval(),
      isOfflineMode: this._isOfflineMode,
      priority: this._priority,
      daysActive: this.getDaysActive(),
    }
  }

  /**
   * Gets days since project was created
   * Useful for tracking project age
   */
  public getDaysActive(): number {
    const createdDate = new Date(this._createdAt)
    const currentDate = new Date()
    const diffTime = currentDate.getTime() - createdDate.getTime()
    return Math.floor(diffTime / (1000 * 60 * 60 * 24))
  }

  // Private Validation Methods

  private validateProject(): void {
    if (!this._id || this._id.trim() === "") {
      throw new Error("Project ID cannot be empty")
    }

    if (!this._name || this._name.trim() === "") {
      throw new Error("Project name cannot be empty")
    }

    if (!this._ownerId || this._ownerId <= 0) {
      throw new Error("Owner ID must be a positive number")
    }

    if (!this._createdAt || !this.isValidDate(this._createdAt)) {
      throw new Error("Created date must be a valid ISO date string")
    }

    if (!this._updatedAt || !this.isValidDate(this._updatedAt)) {
      throw new Error("Updated date must be a valid ISO date string")
    }

    if (this._startDate && !this.isValidDate(this._startDate)) {
      throw new Error("Start date must be a valid ISO date string")
    }

    if (this._endDate && !this.isValidDate(this._endDate)) {
      throw new Error("End date must be a valid ISO date string")
    }

    if (this._startDate && this._endDate && this._startDate >= this._endDate) {
      throw new Error("Start date must be before end date")
    }
  }

  private enforceInvariants(): void {
    // Ensure project owner is always in the member list
    const ownerMember = this.getMemberByUserId(this._ownerId)
    if (!ownerMember) {
      throw new Error("Project owner must be a member of the project")
    }

    // Ensure project owner has administrator role
    if (ownerMember.role.role !== "Administrator") {
      throw new Error("Project owner must have administrator role")
    }

    // Ensure at least one administrator exists
    if (this.getAdministratorCount() < 1) {
      throw new Error("Project must have at least one administrator")
    }

    // Validate member uniqueness
    const userIds = this._members.map((member) => member.userId)
    const uniqueUserIds = new Set(userIds)
    if (userIds.length !== uniqueUserIds.size) {
      throw new Error("Project cannot have duplicate members")
    }
  }

  // Private Business Logic Methods

  private canBeActivated(): boolean {
    return this._status.canTransitionTo("Active")
  }

  private canBeCompleted(): boolean {
    return this._status.canTransitionTo("Completed")
  }

  /**
   * Pauses the project from active state
   */
  public pauseProject(pausedBy: number, reason?: string): Project {
    if (!this.isActive()) {
      throw new Error("Project can only be paused from active state")
    }

    const pauser = this.getMemberByUserId(pausedBy)
    if (!pauser || !pauser.canManageProject()) {
      throw new Error("User does not have permission to pause project")
    }

    const newStatus = this._status.toOnHold(reason, pausedBy)

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      newStatus,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Reverts project back to draft state
   */
  public revertToDraft(revertedBy: number, reason?: string): Project {
    if (this.isActive() || this.isCompleted()) {
      throw new Error("Cannot revert active or completed project to draft")
    }

    const reverter = this.getMemberByUserId(revertedBy)
    if (!reverter || !reverter.canManageProject()) {
      throw new Error("User does not have permission to revert project")
    }

    const newStatus = ProjectStatus.createDraft()

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      newStatus,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Updates basic project information
   */
  public updateBasicInfo(
    name: string,
    description?: string,
    updatedBy?: number
  ): Project {
    if (updatedBy) {
      const updater = this.getMemberByUserId(updatedBy)
      if (!updater || !updater.canManageProject()) {
        throw new Error("User does not have permission to update project")
      }
    }

    return new Project(
      this._id,
      name.trim(),
      description?.trim(),
      this._clientId,
      this._ownerId,
      this._status,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Updates project timeline dates
   */
  public updateTimeline(
    startDate?: string,
    endDate?: string,
    updatedBy?: number
  ): Project {
    if (updatedBy) {
      const updater = this.getMemberByUserId(updatedBy)
      if (!updater || !updater.canManageProject()) {
        throw new Error(
          "User does not have permission to update project timeline"
        )
      }
    }

    // Validate dates if provided
    if (startDate && endDate) {
      const start = new Date(startDate)
      const end = new Date(endDate)
      if (start >= end) {
        throw new Error("End date must be after start date")
      }
    }

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      this._status,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      startDate,
      endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Updates project tags
   */
  public updateTags(tags: string[], updatedBy?: number): Project {
    if (updatedBy) {
      const updater = this.getMemberByUserId(updatedBy)
      if (!updater || !updater.canManageProject()) {
        throw new Error("User does not have permission to update project tags")
      }
    }

    const cleanTags = tags.map((tag) => tag.trim()).filter(Boolean)

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      this._status,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      cleanTags,
      this._priority,
      this._location,
      this._externalProjectId
    )
  }

  /**
   * Updates project location
   */
  public updateLocation(location?: string, updatedBy?: number): Project {
    if (updatedBy) {
      const updater = this.getMemberByUserId(updatedBy)
      if (!updater || !updater.canManageProject()) {
        throw new Error(
          "User does not have permission to update project location"
        )
      }
    }

    return new Project(
      this._id,
      this._name,
      this._description,
      this._clientId,
      this._ownerId,
      this._status,
      this._budget,
      this._members,
      this._createdAt,
      new Date().toISOString(),
      this._startDate,
      this._endDate,
      this._isOfflineMode,
      this._tags,
      this._priority,
      location?.trim(),
      this._externalProjectId
    )
  }

  /**
   * Removes approval requirement (if conditions allow)
   */
  public removeApprovalRequirement(
    removedBy: number,
    reason?: string
  ): Project {
    const remover = this.getMemberByUserId(removedBy)
    if (!remover || !remover.canManageProject()) {
      throw new Error(
        "User does not have permission to remove approval requirement"
      )
    }

    if (this._priority === "Critical") {
      throw new Error("Critical projects always require approval")
    }

    // For this simplified version, approval requirement is based on priority
    // so we just return the same project
    return this
  }

  private canBeArchived(): boolean {
    return this._status.canTransitionTo("Archived")
  }

  private canBePutOnHold(): boolean {
    return this._status.canTransitionTo("On Hold")
  }

  private canBeCancelled(): boolean {
    return this._status.canTransitionTo("Cancelled")
  }

  private hasReachedMemberLimit(): boolean {
    return this._members.length >= this.getMaxMemberLimit()
  }

  private getMaxMemberLimit(): number {
    // Business rule: different limits based on project type/priority
    switch (this._priority) {
      case "Critical":
        return 50
      case "High":
        return 30
      case "Medium":
        return 20
      case "Low":
        return 10
      default:
        return 20
    }
  }

  private isValidDate(dateString: string): boolean {
    const date = new Date(dateString)
    return (
      date instanceof Date && !isNaN(date.getTime()) && dateString.includes("T")
    )
  }

  // Entity Equality (based on identity)
  public equals(other: Project): boolean {
    return this._id === other._id
  }

  public toString(): string {
    return `Project(${this._id}, ${this._name}, ${this._status.status}, Members: ${this._members.length})`
  }
}
