/**
 * Projects Domain Entities
 *
 * Exports all domain entities for the Projects module.
 * Entities represent objects with identity and lifecycle within the domain.
 */

export { ProjectMember, type ProjectMemberData } from "./ProjectMember"
export { Project, type ProjectData, type CreateProjectData } from "./Project"

// Future entities will be exported here:
// export { ProjectPortfolio, type ProjectPortfolioData } from './ProjectPortfolio'
