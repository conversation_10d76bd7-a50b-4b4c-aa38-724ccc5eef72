/**
 * Project Entity (Aggregate Root) Unit Tests
 *
 * Comprehensive test coverage for the Project aggregate root.
 * Tests cover all business methods, aggregate consistency, and invariants.
 */

import { beforeEach, describe, expect, test } from "vitest"
import { ProjectBudget } from "../../value-objects/ProjectBudget"
import { ProjectStatus } from "../../value-objects/ProjectStatus"
import { TeamRole } from "../../value-objects/TeamRole"
import { CreateProjectData, Project, ProjectData } from "../Project"
import { ProjectMember } from "../ProjectMember"

describe("Project Aggregate Root", () => {
  let sampleCreateData: CreateProjectData
  let sampleProjectData: ProjectData
  let adminRole: TeamRole
  let designerRole: TeamRole
  let viewerRole: TeamRole

  beforeEach(() => {
    adminRole = TeamRole.createAdministrator()
    designerRole = TeamRole.create({ role: "Electrical Engineer" })
    viewerRole = TeamRole.createViewer()

    sampleCreateData = {
      id: "project_001",
      name: "Smart Building Electrical System",
      description: "Complete electrical design for smart building",
      clientId: 1001,
      ownerId: 2001,
      budgetData: {
        totalAmount: 100000,
        currency: "EUR",
      },
      startDate: "2024-02-01T00:00:00Z",
      endDate: "2024-12-31T23:59:59Z",
      tags: ["electrical", "smart-building"],
      priority: "High",
      location: "Amsterdam, Netherlands",
    }

    const status = ProjectStatus.createDraft()
    const budget = ProjectBudget.createWithAmount(100000, "EUR")
    const ownerMember = ProjectMember.createNew(
      "project_001_owner",
      2001,
      "project_001",
      adminRole
    )

    sampleProjectData = {
      id: "project_001",
      name: "Smart Building Electrical System",
      description: "Complete electrical design for smart building",
      clientId: 1001,
      ownerId: 2001,
      status,
      budget,
      members: [ownerMember],
      createdAt: "2024-01-01T10:00:00Z",
      updatedAt: "2024-01-01T10:00:00Z",
      startDate: "2024-02-01T00:00:00Z",
      endDate: "2024-12-31T23:59:59Z",
      isOfflineMode: false,
      tags: ["electrical", "smart-building"],
      priority: "High",
      location: "Amsterdam, Netherlands",
    }
  })

  describe("Creation and Validation", () => {
    test("should create a new project with create factory method", () => {
      const project = Project.create(sampleCreateData)

      expect(project.id).toBe("project_001")
      expect(project.name).toBe("Smart Building Electrical System")
      expect(project.description).toBe(
        "Complete electrical design for smart building"
      )
      expect(project.clientId).toBe(1001)
      expect(project.ownerId).toBe(2001)
      expect(project.status.status).toBe("Draft")
      expect(project.budget.totalAmount).toBe(100000)
      expect(project.members).toHaveLength(1) // Owner automatically added
      expect(project.createdAt).toBeDefined()
      expect(project.updatedAt).toBeDefined()
      expect(project.isOfflineMode).toBe(false)
      expect(project.tags).toEqual(["electrical", "smart-building"])
      expect(project.priority).toBe("High")
    })

    test("should create project with minimal data", () => {
      const minimalData: CreateProjectData = {
        id: "project_002",
        name: "Minimal Project",
        ownerId: 2002,
      }

      const project = Project.create(minimalData)

      expect(project.id).toBe("project_002")
      expect(project.name).toBe("Minimal Project")
      expect(project.ownerId).toBe(2002)
      expect(project.description).toBeUndefined()
      expect(project.clientId).toBeUndefined()
      expect(project.priority).toBe("Medium") // Default value
      expect(project.tags).toEqual([]) // Default empty array
      expect(project.members).toHaveLength(1) // Owner added
      expect(project.getOwner().role.role).toBe("Administrator")
    })

    test("should restore project from existing data", () => {
      const project = Project.restore(sampleProjectData)

      expect(project.id).toBe("project_001")
      expect(project.name).toBe("Smart Building Electrical System")
      expect(project.status.status).toBe("Draft")
      expect(project.members).toHaveLength(1)
      expect(project.createdAt).toBe("2024-01-01T10:00:00Z")
    })

    test("should throw error for empty project ID", () => {
      expect(() => {
        Project.create({
          ...sampleCreateData,
          id: "",
        })
      }).toThrow("Project ID cannot be empty")
    })

    test("should throw error for empty project name", () => {
      expect(() => {
        Project.create({
          ...sampleCreateData,
          name: "",
        })
      }).toThrow("Project name cannot be empty")
    })

    test("should throw error for invalid owner ID", () => {
      expect(() => {
        Project.create({
          ...sampleCreateData,
          ownerId: 0,
        })
      }).toThrow("Owner ID must be a positive number")
    })

    test("should enforce owner is administrator invariant", () => {
      const invalidData = {
        ...sampleProjectData,
        members: [
          ProjectMember.createNew("invalid", 2001, "project_001", designerRole),
        ],
      }

      expect(() => {
        Project.restore(invalidData)
      }).toThrow("Project owner must have administrator role")
    })

    test("should enforce at least one administrator invariant", () => {
      const invalidData = {
        ...sampleProjectData,
        members: [
          ProjectMember.createNew("invalid", 3001, "project_001", designerRole),
        ],
      }

      expect(() => {
        Project.restore(invalidData)
      }).toThrow("Project owner must be a member of the project")
    })
  })

  describe("Project Lifecycle Management", () => {
    test("should activate project from draft state", () => {
      const project = Project.create(sampleCreateData)
      const activated = project.activateProject(2001, "Ready to start")

      expect(activated.status.status).toBe("Active")
      expect(activated.status.reason).toBe("Ready to start")
      expect(new Date(activated.updatedAt).getTime()).toBeGreaterThanOrEqual(new Date(project.updatedAt).getTime()) // Updated timestamp
      expect(project).not.toBe(activated) // Immutability
    })

    test("should throw error when non-admin tries to activate", () => {
      const project = Project.create(sampleCreateData).addTeamMember(
        3001,
        designerRole,
        2001
      )

      expect(() => {
        project.activateProject(3001, "Should fail")
      }).toThrow("User does not have permission to activate project")
    })

    test("should complete project from active state", () => {
      const project = Project.create(sampleCreateData).activateProject(2001)
      const completed = project.completeProject(2001, "All work done")

      expect(completed.status.status).toBe("Completed")
      expect(completed.status.reason).toBe("All work done")
    })

    test("should archive completed project", () => {
      const project = Project.create(sampleCreateData)
        .activateProject(2001)
        .completeProject(2001)
      const archived = project.archiveProject(2001, "Final archival")

      expect(archived.status.status).toBe("Archived")
      expect(archived.status.reason).toBe("Final archival")
    })

    test("should put project on hold", () => {
      const project = Project.create(sampleCreateData).activateProject(2001)
      const onHold = project.putOnHold(2001, "Waiting for client approval")

      expect(onHold.status.status).toBe("On Hold")
      expect(onHold.status.reason).toBe("Waiting for client approval")
    })

    test("should cancel project", () => {
      const project = Project.create(sampleCreateData)
      const cancelled = project.cancelProject(2001, "Budget constraints")

      expect(cancelled.status.status).toBe("Cancelled")
      expect(cancelled.status.reason).toBe("Budget constraints")
    })

    test("should throw error for invalid state transitions", () => {
      const project = Project.create(sampleCreateData)

      expect(() => {
        project.completeProject(2001) // Can't complete from draft
      }).toThrow("Project cannot be completed in current state")
    })
  })

  describe("Team Management", () => {
    test("should add team member to project", () => {
      const project = Project.create(sampleCreateData)
      const withMember = project.addTeamMember(
        3001,
        designerRole,
        2001,
        undefined,
        "New designer"
      )

      expect(withMember.members).toHaveLength(2)
      const newMember = withMember.getMemberByUserId(3001)
      expect(newMember).toBeDefined()
      expect(newMember!.role.role).toBe("Electrical Engineer")
      expect(newMember!.notes).toContain("New designer")
      expect(project).not.toBe(withMember) // Immutability
    })

    test("should add member with expiration date", () => {
      const project = Project.create(sampleCreateData)
      const expirationDate = "2026-06-30T23:59:59Z"
      const withMember = project.addTeamMember(
        3001,
        designerRole,
        2001,
        expirationDate
      )

      const newMember = withMember.getMemberByUserId(3001)
      expect(newMember!.expiresAt).toBe(expirationDate)
    })

    test("should throw error when non-manager tries to add member", () => {
      const project = Project.create(sampleCreateData).addTeamMember(
        3001,
        designerRole,
        2001
      )

      expect(() => {
        project.addTeamMember(3002, viewerRole, 3001) // Designer trying to add member
      }).toThrow("User does not have permission to add team members")
    })

    test("should throw error when adding duplicate member", () => {
      const project = Project.create(sampleCreateData).addTeamMember(
        3001,
        designerRole,
        2001
      )

      expect(() => {
        project.addTeamMember(3001, viewerRole, 2001) // Same user again
      }).toThrow("User is already a member of this project")
    })

    test("should throw error when project cannot add members", () => {
      const archivedProject = Project.restore({
        ...sampleProjectData,
        status: ProjectStatus.create({ status: "Archived" }),
      })

      expect(() => {
        archivedProject.addTeamMember(3001, designerRole, 2001)
      }).toThrow("Cannot add members to project in current state")
    })

    test("should remove team member from project", () => {
      const project = Project.create(sampleCreateData).addTeamMember(
        3001,
        designerRole,
        2001
      )
      const withoutMember = project.removeTeamMember(
        3001,
        2001,
        "No longer needed"
      )

      expect(withoutMember.members).toHaveLength(1) // Only owner remains
      expect(withoutMember.getMemberByUserId(3001)).toBeUndefined()
    })

    test("should throw error when trying to remove project owner", () => {
      const project = Project.create(sampleCreateData)

      expect(() => {
        project.removeTeamMember(2001, 2001) // Trying to remove owner
      }).toThrow("Cannot remove project owner from project")
    })

    test("should throw error when removing last administrator", () => {
      // The business rule enforces that owner cannot be removed,
      // which takes precedence over "last administrator" rule
      const project = Project.create(sampleCreateData)

      expect(() => {
        project.removeTeamMember(2001, 2001) // Owner trying to remove themselves (only admin)
      }).toThrow("Cannot remove project owner from project")
    })

    test("should update member role", () => {
      const project = Project.create(sampleCreateData).addTeamMember(
        3001,
        designerRole,
        2001
      )
      const pmRole = TeamRole.createProjectManager()
      const updated = project.updateMemberRole(3001, pmRole, 2001, "Promotion")

      const updatedMember = updated.getMemberByUserId(3001)
      expect(updatedMember!.role.role).toBe("Project Manager")
      expect(updatedMember!.notes).toContain("Promotion")
    })

    test("should enforce owner administrator role constraint", () => {
      const project = Project.create(sampleCreateData)

      expect(() => {
        project.updateMemberRole(2001, designerRole, 2001) // Owner losing admin
      }).toThrow("Project owner must maintain administrator privileges")
    })
  })

  describe("Budget Management", () => {
    test("should update project budget", () => {
      const project = Project.create(sampleCreateData)
      const newBudget = ProjectBudget.createWithAmount(150000, "EUR")
      const updated = project.updateBudget(
        newBudget,
        2001,
        "Budget increase approved"
      )

      expect(updated.budget.totalAmount).toBe(150000)
      expect(new Date(updated.updatedAt).getTime()).toBeGreaterThanOrEqual(new Date(project.updatedAt).getTime())
    })

    test("should throw error when non-financial user updates budget", () => {
      const project = Project.create(sampleCreateData).addTeamMember(
        3001,
        viewerRole,
        2001
      )

      const newBudget = ProjectBudget.createWithAmount(150000, "EUR")
      expect(() => {
        project.updateBudget(newBudget, 3001)
      }).toThrow("User does not have permission to update project budget")
    })

    test("should throw error for excessive budget reduction in active project", () => {
      const project = Project.create(sampleCreateData).activateProject(2001)
      const reducedBudget = ProjectBudget.createWithAmount(40000, "EUR") // 60% reduction

      expect(() => {
        project.updateBudget(reducedBudget, 2001)
      }).toThrow("Cannot reduce budget by more than 50% in active project")
    })

    test("should increase budget successfully", () => {
      const project = Project.create(sampleCreateData)
      const increased = project.increaseBudget(
        25000,
        2001,
        "Additional funding"
      )

      expect(increased.budget.totalAmount).toBe(125000)
    })
  })

  describe("Project Configuration", () => {
    test("should toggle offline mode", () => {
      const project = Project.create(sampleCreateData)
      const offlineEnabled = project.toggleOfflineMode(
        2001,
        "Enable offline capabilities"
      )

      expect(offlineEnabled.isOfflineMode).toBe(true)
      expect(project.isOfflineMode).toBe(false) // Original unchanged

      const offlineDisabled = offlineEnabled.toggleOfflineMode(
        2001,
        "Disable offline mode"
      )
      expect(offlineDisabled.isOfflineMode).toBe(false)
    })

    test("should update project information", () => {
      const project = Project.create(sampleCreateData)
      const updates = {
        name: "Updated Smart Building Project",
        description: "Updated description with more details",
        tags: ["electrical", "smart-building", "iot"],
        priority: "Critical" as const,
        location: "Rotterdam, Netherlands",
      }

      const updated = project.updateProjectInfo(updates, 2001)

      expect(updated.name).toBe("Updated Smart Building Project")
      expect(updated.description).toBe("Updated description with more details")
      expect(updated.tags).toEqual(["electrical", "smart-building", "iot"])
      expect(updated.priority).toBe("Critical")
      expect(updated.location).toBe("Rotterdam, Netherlands")
    })

    test("should validate date ranges in project updates", () => {
      const project = Project.create(sampleCreateData)

      expect(() => {
        project.updateProjectInfo(
          {
            startDate: "2024-12-01T00:00:00Z",
            endDate: "2024-06-01T00:00:00Z", // End before start
          },
          2001
        )
      }).toThrow("Start date must be before end date")
    })

    test("should throw error when non-editor updates project info", () => {
      const project = Project.create(sampleCreateData).addTeamMember(
        3001,
        viewerRole,
        2001
      )

      expect(() => {
        project.updateProjectInfo({ name: "New Name" }, 3001)
      }).toThrow("User does not have permission to update project information")
    })
  })

  describe("Business Queries", () => {
    test("should correctly identify project status", () => {
      let project = Project.create(sampleCreateData)
      expect(project.isActive()).toBe(false)
      expect(project.isCompleted()).toBe(false)
      expect(project.isCancelled()).toBe(false)
      expect(project.isOnHold()).toBe(false)

      project = project.activateProject(2001)
      expect(project.isActive()).toBe(true)

      project = project.completeProject(2001)
      expect(project.isCompleted()).toBe(true)
    })

    test("should determine if members can be added", () => {
      let project = Project.create(sampleCreateData)
      expect(project.canAddMembers()).toBe(true)

      project = project.activateProject(2001)
      expect(project.canAddMembers()).toBe(true)

      const archived = Project.restore({
        ...sampleProjectData,
        status: ProjectStatus.create({ status: "Archived" }),
      })
      expect(archived.canAddMembers()).toBe(false)
    })

    test("should identify over budget status", () => {
      const overBudget = ProjectBudget.create({
        totalAmount: 100000,
        spentAmount: 120000,
        currency: "EUR",
      })

      const project = Project.restore({
        ...sampleProjectData,
        budget: overBudget,
      })

      expect(project.isOverBudget()).toBe(true)
    })

    test("should identify expired members", () => {
      const expiredMember = ProjectMember.create({
        id: "expired_member",
        userId: 3001,
        projectId: "project_001",
        role: designerRole,
        assignedAt: "2024-01-01T10:00:00Z",
        expiresAt: "2024-06-30T23:59:59Z", // Past date (relative to 2025)
      })

      const project = Project.restore({
        ...sampleProjectData,
        members: [...sampleProjectData.members, expiredMember],
      })

      expect(project.hasExpiredMembers()).toBe(true)
    })

    test("should identify members approaching expiration", () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 3) // 3 days from now

      const approachingMember = ProjectMember.create({
        id: "approaching_member",
        userId: 3001,
        projectId: "project_001",
        role: designerRole,
        assignedAt: "2024-01-01T10:00:00Z",
        expiresAt: futureDate.toISOString(),
      })

      const project = Project.restore({
        ...sampleProjectData,
        members: [...sampleProjectData.members, approachingMember],
      })

      expect(project.hasMembersApproachingExpiration(7)).toBe(true)
      expect(project.hasMembersApproachingExpiration(2)).toBe(false)
    })

    test("should determine approval requirements", () => {
      let project = Project.create(sampleCreateData)
      expect(project.requiresApproval()).toBe(false)

      // Move to planning state which requires approval
      project = Project.restore({
        ...sampleProjectData,
        status: ProjectStatus.create({ status: "Planning" }),
      })
      expect(project.requiresApproval()).toBe(true)
    })
  })

  describe("Member Query Methods", () => {
    test("should get member by user ID", () => {
      const project = Project.create(sampleCreateData).addTeamMember(
        3001,
        designerRole,
        2001
      )

      const member = project.getMemberByUserId(3001)
      expect(member).toBeDefined()
      expect(member!.userId).toBe(3001)

      const nonExistent = project.getMemberByUserId(9999)
      expect(nonExistent).toBeUndefined()
    })

    test("should get active members only", () => {
      const inactiveMember = ProjectMember.create({
        id: "inactive_member",
        userId: 3001,
        projectId: "project_001",
        role: designerRole,
        assignedAt: "2024-01-01T10:00:00Z",
        isActive: false,
      })

      const project = Project.restore({
        ...sampleProjectData,
        members: [...sampleProjectData.members, inactiveMember],
      })

      const activeMembers = project.getActiveMembers()
      expect(activeMembers).toHaveLength(1) // Only owner is active
      expect(activeMembers.every((m) => m.isActive)).toBe(true)
    })

    test("should get members by role", () => {
      const project = Project.create(sampleCreateData)
        .addTeamMember(3001, designerRole, 2001)
        .addTeamMember(3002, designerRole, 2001)
        .addTeamMember(3003, viewerRole, 2001)

      const designers = project.getMembersByRole("Electrical Engineer")
      expect(designers).toHaveLength(2)

      const admins = project.getMembersByRole("Administrator")
      expect(admins).toHaveLength(1) // Only owner
    })

    test("should get project owner", () => {
      const project = Project.create(sampleCreateData)
      const owner = project.getOwner()

      expect(owner.userId).toBe(2001)
      expect(owner.role.role).toBe("Administrator")
    })

    test("should get administrators", () => {
      const project = Project.create(sampleCreateData).addTeamMember(
        3001,
        adminRole,
        2001
      )

      const admins = project.getAdministrators()
      expect(admins).toHaveLength(2) // Owner + added admin
      expect(project.getAdministratorCount()).toBe(2)
    })
  })

  describe("Aggregate Summary and Reporting", () => {
    test("should provide comprehensive project summary", () => {
      const project = Project.create(sampleCreateData)
        .addTeamMember(3001, designerRole, 2001)
        .addTeamMember(3002, viewerRole, 2001)

      const summary = project.getProjectSummary()

      expect(summary.id).toBe("project_001")
      expect(summary.name).toBe("Smart Building Electrical System")
      expect(summary.status).toBe("Draft")
      expect(summary.memberCount).toBe(3)
      expect(summary.activeMemberCount).toBe(3)
      expect(summary.budgetSummary.total).toBe("€100,000.00")
      expect(summary.hasExpiredMembers).toBe(false)
      expect(summary.requiresApproval).toBe(false)
      expect(summary.isOfflineMode).toBe(false)
      expect(summary.priority).toBe("High")
      expect(typeof summary.daysActive).toBe("number")
    })

    test("should calculate days active correctly", () => {
      const pastDate = new Date()
      pastDate.setDate(pastDate.getDate() - 10)

      const project = Project.restore({
        ...sampleProjectData,
        createdAt: pastDate.toISOString(),
      })

      const daysActive = project.getDaysActive()
      expect(daysActive).toBe(10)
    })
  })

  describe("Member Limit Validation", () => {
    test("should respect member limits based on priority", () => {
      const lowPriorityProject = Project.create({
        ...sampleCreateData,
        priority: "Low",
      })

      // Low priority has limit of 10 members
      // Add 9 more members (owner already exists)
      let project = lowPriorityProject
      for (let i = 1; i <= 9; i++) {
        project = project.addTeamMember(3000 + i, designerRole, 2001)
      }

      expect(project.members).toHaveLength(10)

      // Should fail to add 11th member
      expect(() => {
        project.addTeamMember(4000, designerRole, 2001)
      }).toThrow("Project has reached maximum member limit")
    })

    test("should have different limits for different priorities", () => {
      const criticalProject = Project.create({
        ...sampleCreateData,
        priority: "Critical",
      })

      // Critical priority should allow up to 50 members
      expect(criticalProject.canAddMembers()).toBe(true)
    })
  })

  describe("Aggregate Invariants", () => {
    test("should maintain owner administrator invariant across operations", () => {
      const project = Project.create(sampleCreateData).addTeamMember(
        3001,
        adminRole,
        2001
      )

      // Owner should always be admin
      const owner = project.getOwner()
      expect(owner.role.role).toBe("Administrator")

      // Should have at least one admin
      expect(project.getAdministratorCount()).toBeGreaterThanOrEqual(1)
    })

    test("should prevent duplicate members", () => {
      const duplicateMember = ProjectMember.createNew(
        "duplicate",
        2001,
        "project_001",
        designerRole
      )
      const invalidData = {
        ...sampleProjectData,
        members: [...sampleProjectData.members, duplicateMember],
      }

      expect(() => {
        Project.restore(invalidData)
      }).toThrow("Project cannot have duplicate members")
    })

    test("should maintain aggregate consistency across state changes", () => {
      let project = Project.create(sampleCreateData).addTeamMember(
        3001,
        designerRole,
        2001
      )

      // Verify invariants are maintained through lifecycle
      project = project.activateProject(2001)
      expect(project.getOwner().role.role).toBe("Administrator")

      project = project.putOnHold(2001)
      expect(project.getAdministratorCount()).toBeGreaterThanOrEqual(1)

      project = project.activateProject(2001)
      expect(project.getMemberByUserId(2001)).toBeDefined()
    })
  })

  describe("Entity Properties", () => {
    test("should maintain entity identity", () => {
      const project1 = Project.create(sampleCreateData)
      const project2 = project1.activateProject(2001)

      expect(project1.equals(project2)).toBe(true) // Same ID
    })

    test("should distinguish different entities", () => {
      const project1 = Project.create(sampleCreateData)
      const project2 = Project.create({
        ...sampleCreateData,
        id: "project_002",
      })

      expect(project1.equals(project2)).toBe(false) // Different IDs
    })

    test("should convert to string correctly", () => {
      const project = Project.create(sampleCreateData)
      const stringRepresentation = project.toString()

      expect(stringRepresentation).toContain("project_001")
      expect(stringRepresentation).toContain("Smart Building Electrical System")
      expect(stringRepresentation).toContain("Draft")
      expect(stringRepresentation).toContain("Members: 1")
    })

    test("should maintain immutability across all operations", () => {
      const original = Project.create(sampleCreateData)
      const modified = original
        .activateProject(2001)
        .addTeamMember(3001, designerRole, 2001)
        .increaseBudget(25000, 2001)
        .toggleOfflineMode(2001)

      expect(original.status.status).toBe("Draft")
      expect(modified.status.status).toBe("Active")
      expect(original.members).toHaveLength(1)
      expect(modified.members).toHaveLength(2)
      expect(original.budget.totalAmount).toBe(100000)
      expect(modified.budget.totalAmount).toBe(125000)
      expect(original.isOfflineMode).toBe(false)
      expect(modified.isOfflineMode).toBe(true)
      expect(original).not.toBe(modified)
    })
  })

  describe("Edge Cases and Error Handling", () => {
    test("should handle empty budgets gracefully", () => {
      const project = Project.create({
        ...sampleCreateData,
        budgetData: undefined,
      })

      expect(project.budget.totalAmount).toBe(0)
      expect(project.isOverBudget()).toBe(false)
    })

    test("should validate date strings strictly", () => {
      expect(() => {
        Project.create({
          ...sampleCreateData,
          startDate: "2024-01-01", // Missing time component
        })
      }).toThrow("Start date must be a valid ISO date string")
    })

    test("should handle member operations on empty team gracefully", () => {
      const project = Project.create(sampleCreateData)

      expect(project.getMemberByUserId(9999)).toBeUndefined()
      expect(project.getMembersByRole("Electrical Engineer")).toHaveLength(0)
      expect(project.hasExpiredMembers()).toBe(false)
    })

    test("should maintain data integrity under concurrent-like operations", () => {
      let project = Project.create(sampleCreateData)

      // Simulate multiple operations that could cause inconsistency
      project = project
        .addTeamMember(3001, designerRole, 2001)
        .addTeamMember(3002, viewerRole, 2001)
        .activateProject(2001)
        .updateMemberRole(3001, TeamRole.createProjectManager(), 2001)
        .increaseBudget(50000, 2001)

      // Verify all invariants still hold
      expect(project.getOwner().role.role).toBe("Administrator")
      expect(project.getAdministratorCount()).toBeGreaterThanOrEqual(1)
      expect(project.getMemberByUserId(3001)!.role.role).toBe("Project Manager")
      expect(project.budget.totalAmount).toBe(150000)
      expect(project.status.status).toBe("Active")
    })
  })
})
