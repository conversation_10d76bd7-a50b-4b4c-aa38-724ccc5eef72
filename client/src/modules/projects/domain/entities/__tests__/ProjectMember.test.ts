/**
 * ProjectMember Entity Unit Tests
 *
 * Comprehensive test coverage for project member entity logic.
 * Tests cover all business methods, queries, and edge cases.
 */

import { beforeEach, describe, expect, test } from "vitest"
import { TeamRole } from "../../value-objects/TeamRole"
import { ProjectMember, ProjectMemberData } from "../ProjectMember"

describe("ProjectMember Entity", () => {
  let sampleMemberData: ProjectMemberData
  let adminRole: TeamRole
  let viewerRole: TeamRole
  let designerRole: TeamRole

  beforeEach(() => {
    adminRole = TeamRole.createAdministrator()
    viewerRole = TeamRole.createViewer()
    designerRole = TeamRole.create({ role: "Electrical Engineer" })

    sampleMemberData = {
      id: "member_001",
      userId: 1001,
      projectId: "project_001",
      role: designerRole,
      assignedAt: "2024-01-01T10:00:00Z",
      assignedBy: 2001,
      expiresAt: "2025-12-31T23:59:59Z",
      isActive: true,
      lastActivity: "2024-01-15T14:30:00Z",
      notes: "Initial assignment",
    }
  })

  describe("Creation and Validation", () => {
    test("should create a valid project member", () => {
      const member = ProjectMember.create(sampleMemberData)

      expect(member.id).toBe("member_001")
      expect(member.userId).toBe(1001)
      expect(member.projectId).toBe("project_001")
      expect(member.role.role).toBe("Electrical Engineer")
      expect(member.assignedAt).toBe("2024-01-01T10:00:00Z")
      expect(member.assignedBy).toBe(2001)
      expect(member.expiresAt).toBe("2025-12-31T23:59:59Z")
      expect(member.isActive).toBe(true)
      expect(member.lastActivity).toBe("2024-01-15T14:30:00Z")
      expect(member.notes).toBe("Initial assignment")
    })

    test("should create new member with factory method", () => {
      const member = ProjectMember.createNew(
        "member_002",
        1002,
        "project_002",
        adminRole,
        2002,
        "2025-12-31T23:59:59Z"
      )

      expect(member.id).toBe("member_002")
      expect(member.userId).toBe(1002)
      expect(member.projectId).toBe("project_002")
      expect(member.role.role).toBe("Administrator")
      expect(member.assignedBy).toBe(2002)
      expect(member.expiresAt).toBe("2025-12-31T23:59:59Z")
      expect(member.isActive).toBe(true)
      expect(member.assignedAt).toBeDefined()
      expect(member.lastActivity).toBeUndefined()
      expect(member.notes).toBeUndefined()
    })

    test("should use default values for optional fields", () => {
      const minimalData: ProjectMemberData = {
        id: "member_003",
        userId: 1003,
        projectId: "project_003",
        role: viewerRole,
        assignedAt: "2024-01-01T10:00:00Z",
      }

      const member = ProjectMember.create(minimalData)

      expect(member.assignedBy).toBeUndefined()
      expect(member.expiresAt).toBeUndefined()
      expect(member.isActive).toBe(true)
      expect(member.lastActivity).toBeUndefined()
      expect(member.notes).toBeUndefined()
    })

    test("should throw error for empty member ID", () => {
      expect(() => {
        ProjectMember.create({
          ...sampleMemberData,
          id: "",
        })
      }).toThrow("Project member ID cannot be empty")
    })

    test("should throw error for invalid user ID", () => {
      expect(() => {
        ProjectMember.create({
          ...sampleMemberData,
          userId: 0,
        })
      }).toThrow("User ID must be a positive number")

      expect(() => {
        ProjectMember.create({
          ...sampleMemberData,
          userId: -1,
        })
      }).toThrow("User ID must be a positive number")
    })

    test("should throw error for empty project ID", () => {
      expect(() => {
        ProjectMember.create({
          ...sampleMemberData,
          projectId: "",
        })
      }).toThrow("Project ID cannot be empty")
    })

    test("should throw error for invalid assignment date", () => {
      expect(() => {
        ProjectMember.create({
          ...sampleMemberData,
          assignedAt: "invalid-date",
        })
      }).toThrow("Assignment date must be a valid ISO date string")
    })

    test("should throw error for invalid expiration date", () => {
      expect(() => {
        ProjectMember.create({
          ...sampleMemberData,
          expiresAt: "invalid-date",
        })
      }).toThrow("Expiration date must be a valid ISO date string")
    })

    test("should throw error when expiration is before assignment", () => {
      expect(() => {
        ProjectMember.create({
          ...sampleMemberData,
          assignedAt: "2024-01-01T10:00:00Z",
          expiresAt: "2023-12-31T23:59:59Z",
        })
      }).toThrow("Expiration date must be after assignment date")
    })
  })

  describe("Role Assignment", () => {
    test("should assign new role correctly", () => {
      const member = ProjectMember.create(sampleMemberData)
      const newRole = TeamRole.createProjectManager()
      const updatedMember = member.assignRole(newRole, 2001, "Promotion")

      expect(updatedMember.role.role).toBe("Project Manager")
      expect(updatedMember.lastActivity).toBeDefined()
      expect(updatedMember.lastActivity).not.toBe(member.lastActivity)
      expect(updatedMember.notes).toContain(
        "Role changed to Project Manager: Promotion"
      )
      expect(member).not.toBe(updatedMember) // Immutability
    })

    test("should assign role without reason", () => {
      const member = ProjectMember.create(sampleMemberData)
      const newRole = TeamRole.createViewer()
      const updatedMember = member.assignRole(newRole, 2001)

      expect(updatedMember.role.role).toBe("Viewer")
      expect(updatedMember.notes).toBe("Initial assignment") // Unchanged
    })

    test("should assign role without assigner", () => {
      const member = ProjectMember.create({
        ...sampleMemberData,
        notes: undefined,
      })
      const newRole = TeamRole.createViewer()
      const updatedMember = member.assignRole(newRole)

      expect(updatedMember.role.role).toBe("Viewer")
      expect(updatedMember.lastActivity).toBeDefined()
    })

    test("should validate role assignment permissions", () => {
      const member = ProjectMember.create(sampleMemberData)
      const adminRole = TeamRole.createAdministrator()

      // This would normally check if assignerId 3001 has permissions
      // For now, we test the method signature and basic validation
      expect(() => {
        member.assignRole(adminRole, 3001, "Should validate permissions")
      }).not.toThrow() // Basic test - full validation would be in domain service
    })
  })

  describe("Member Lifecycle Management", () => {
    test("should deactivate member correctly", () => {
      const member = ProjectMember.create(sampleMemberData)
      const deactivated = member.deactivate(2001, "Performance issues")

      expect(deactivated.isActive).toBe(false)
      expect(deactivated.lastActivity).toBeDefined()
      expect(deactivated.lastActivity).not.toBe(member.lastActivity)
      expect(deactivated.notes).toContain(
        "Deactivated by 2001: Performance issues"
      )
      expect(member).not.toBe(deactivated) // Immutability
    })

    test("should deactivate without reason", () => {
      const member = ProjectMember.create(sampleMemberData)
      const deactivated = member.deactivate(2001)

      expect(deactivated.isActive).toBe(false)
      expect(deactivated.notes).toContain("Deactivated by 2001")
      expect(deactivated.notes).not.toContain(":")
    })

    test("should deactivate without specifying user", () => {
      const member = ProjectMember.create(sampleMemberData)
      const deactivated = member.deactivate()

      expect(deactivated.isActive).toBe(false)
      expect(deactivated.notes).toContain("Deactivated by system")
    })

    test("should throw error when deactivating already inactive member", () => {
      const member = ProjectMember.create({
        ...sampleMemberData,
        isActive: false,
      })

      expect(() => {
        member.deactivate(2001, "Already inactive")
      }).toThrow("Project member is already inactive")
    })

    test("should reactivate member correctly", () => {
      const inactiveMember = ProjectMember.create({
        ...sampleMemberData,
        isActive: false,
      })
      const reactivated = inactiveMember.reactivate(2001, "Issue resolved")

      expect(reactivated.isActive).toBe(true)
      expect(reactivated.lastActivity).toBeDefined()
      expect(reactivated.notes).toContain("Reactivated by 2001: Issue resolved")
      expect(inactiveMember).not.toBe(reactivated) // Immutability
    })

    test("should throw error when reactivating already active member", () => {
      const member = ProjectMember.create(sampleMemberData)

      expect(() => {
        member.reactivate(2001, "Already active")
      }).toThrow("Project member is already active")
    })
  })

  describe("Expiration Management", () => {
    test("should extend expiration correctly", () => {
      const member = ProjectMember.create(sampleMemberData)
      const newExpiration = "2026-06-30T23:59:59Z" // Later than 2025-12-31
      const extended = member.extendExpiration(
        newExpiration,
        2001,
        "Contract extension"
      )

      expect(extended.expiresAt).toBe(newExpiration)
      expect(extended.lastActivity).toBeDefined()
      expect(extended.notes).toContain(
        `Expiration extended to ${newExpiration} by 2001: Contract extension`
      )
      expect(member).not.toBe(extended) // Immutability
    })

    test("should extend expiration without reason", () => {
      const member = ProjectMember.create(sampleMemberData)
      const newExpiration = "2026-06-30T23:59:59Z" // Later than 2025-12-31
      const extended = member.extendExpiration(newExpiration, 2001)

      expect(extended.expiresAt).toBe(newExpiration)
      expect(extended.notes).toContain(
        `Expiration extended to ${newExpiration} by 2001`
      )
      // Check that the extension note doesn't have a reason (no colon after user ID)
      expect(extended.notes).not.toContain(`by 2001:`)
    })

    test("should throw error for invalid expiration date", () => {
      const member = ProjectMember.create(sampleMemberData)

      expect(() => {
        member.extendExpiration("invalid-date", 2001)
      }).toThrow("Invalid expiration date format")
    })

    test("should throw error when new expiration is not later", () => {
      const member = ProjectMember.create(sampleMemberData)
      const earlierDate = "2025-06-30T23:59:59Z" // Earlier than current expiration (2025-12-31)

      expect(() => {
        member.extendExpiration(earlierDate, 2001)
      }).toThrow(
        "New expiration date must be later than current expiration date"
      )
    })

    test("should handle member without existing expiration", () => {
      const memberWithoutExpiration = ProjectMember.create({
        ...sampleMemberData,
        expiresAt: undefined,
      })
      const newExpiration = "2026-06-30T23:59:59Z"
      const extended = memberWithoutExpiration.extendExpiration(
        newExpiration,
        2001
      )

      expect(extended.expiresAt).toBe(newExpiration)
    })
  })

  describe("Activity Management", () => {
    test("should update activity timestamp", () => {
      const member = ProjectMember.create(sampleMemberData)
      const updated = member.updateActivity()

      expect(updated.lastActivity).toBeDefined()
      expect(updated.lastActivity).not.toBe(member.lastActivity)
      expect(member).not.toBe(updated) // Immutability
    })

    test("should add notes correctly", () => {
      const member = ProjectMember.create(sampleMemberData)
      const withNotes = member.addNotes("Performance review completed", 2001)

      expect(withNotes.notes).toContain("Performance review completed")
      expect(withNotes.notes).toContain("User 2001")
      expect(withNotes.notes).toContain(new Date().toISOString().split("T")[0]) // Date
      expect(member).not.toBe(withNotes) // Immutability
    })

    test("should add notes without specifying user", () => {
      const member = ProjectMember.create({
        ...sampleMemberData,
        notes: undefined,
      })
      const withNotes = member.addNotes("System generated note")

      expect(withNotes.notes).toContain("System generated note")
      expect(withNotes.notes).not.toContain("User")
    })

    test("should append to existing notes", () => {
      const member = ProjectMember.create(sampleMemberData)
      const withNotes = member.addNotes("Additional note", 2001)

      expect(withNotes.notes).toContain("Initial assignment")
      expect(withNotes.notes).toContain("Additional note")
    })
  })

  describe("Business Queries - Status", () => {
    test("should correctly identify active member", () => {
      const activeMember = ProjectMember.create(sampleMemberData)
      expect(activeMember.isActive).toBe(true)
    })

    test("should correctly identify inactive member", () => {
      const inactiveMember = ProjectMember.create({
        ...sampleMemberData,
        isActive: false,
      })
      expect(inactiveMember.isActive).toBe(false)
    })

    test("should correctly identify expired member", () => {
      const expiredMember = ProjectMember.create({
        ...sampleMemberData,
        assignedAt: "2022-01-01T10:00:00Z", // Earlier assignment
        expiresAt: "2023-01-01T00:00:00Z", // Past date but after assignment
      })
      expect(expiredMember.hasExpired()).toBe(true)
      expect(expiredMember.isActive).toBe(false) // Should be false due to expiration
    })

    test("should handle member without expiration", () => {
      const memberWithoutExpiration = ProjectMember.create({
        ...sampleMemberData,
        expiresAt: undefined,
      })
      expect(memberWithoutExpiration.hasExpired()).toBe(false)
      expect(memberWithoutExpiration.isActive).toBe(true)
    })

    test("should correctly identify approaching expiration", () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 3) // 3 days from now

      const approachingExpiration = ProjectMember.create({
        ...sampleMemberData,
        expiresAt: futureDate.toISOString(),
      })

      expect(approachingExpiration.isApproachingExpiration(7)).toBe(true)
      expect(approachingExpiration.isApproachingExpiration(2)).toBe(false)
    })

    test("should handle custom warning period for expiration", () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 15) // 15 days from now

      const member = ProjectMember.create({
        ...sampleMemberData,
        expiresAt: futureDate.toISOString(),
      })

      expect(member.isApproachingExpiration(30)).toBe(true)
      expect(member.isApproachingExpiration(10)).toBe(false)
    })
  })

  describe("Business Queries - Permissions", () => {
    test("should delegate project management permission to role", () => {
      const adminMember = ProjectMember.create({
        ...sampleMemberData,
        role: adminRole,
      })
      const viewerMember = ProjectMember.create({
        ...sampleMemberData,
        role: viewerRole,
      })

      expect(adminMember.canManageProject()).toBe(true)
      expect(viewerMember.canManageProject()).toBe(false)
    })

    test("should delegate team management permission to role", () => {
      const adminMember = ProjectMember.create({
        ...sampleMemberData,
        role: adminRole,
      })
      const designerMember = ProjectMember.create({
        ...sampleMemberData,
        role: designerRole,
      })

      expect(adminMember.canManageTeam()).toBe(true)
      expect(designerMember.canManageTeam()).toBe(false)
    })

    test("should delegate project editing permission to role", () => {
      const adminMember = ProjectMember.create({
        ...sampleMemberData,
        role: adminRole,
      })
      const viewerMember = ProjectMember.create({
        ...sampleMemberData,
        role: viewerRole,
      })

      expect(adminMember.canEditProject()).toBe(true)
      expect(viewerMember.canEditProject()).toBe(false)
    })

    test("should delegate financial permissions to role", () => {
      const pmRole = TeamRole.createProjectManager()
      const pmMember = ProjectMember.create({
        ...sampleMemberData,
        role: pmRole,
      })
      const designerMember = ProjectMember.create({
        ...sampleMemberData,
        role: designerRole,
      })

      expect(pmMember.canViewFinancials()).toBe(true)
      expect(designerMember.canViewFinancials()).toBe(false)
    })

    test("should delegate design permissions to role", () => {
      const leadRole = TeamRole.create({ role: "Lead Engineer" })
      const leadMember = ProjectMember.create({
        ...sampleMemberData,
        role: leadRole,
      })
      const viewerMember = ProjectMember.create({
        ...sampleMemberData,
        role: viewerRole,
      })

      expect(leadMember.canApproveDesign()).toBe(true)
      expect(leadMember.hasDesignPermissions()).toBe(true)
      expect(viewerMember.canApproveDesign()).toBe(false)
      expect(viewerMember.hasDesignPermissions()).toBe(false)
    })

    test("should require active status for permissions", () => {
      const inactiveAdmin = ProjectMember.create({
        ...sampleMemberData,
        role: adminRole,
        isActive: false,
      })

      expect(inactiveAdmin.canManageProject()).toBe(false)
      expect(inactiveAdmin.canManageTeam()).toBe(false)
      expect(inactiveAdmin.canEditProject()).toBe(false)
    })

    test("should require non-expired status for permissions", () => {
      const expiredAdmin = ProjectMember.create({
        ...sampleMemberData,
        role: adminRole,
        assignedAt: "2022-01-01T10:00:00Z", // Earlier assignment
        expiresAt: "2023-01-01T00:00:00Z", // Past date but after assignment
      })

      expect(expiredAdmin.canManageProject()).toBe(false)
      expect(expiredAdmin.canManageTeam()).toBe(false)
      expect(expiredAdmin.canEditProject()).toBe(false)
    })
  })

  describe("Time-based Calculations", () => {
    test("should calculate days until expiration", () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 10)

      const member = ProjectMember.create({
        ...sampleMemberData,
        expiresAt: futureDate.toISOString(),
      })

      const daysUntilExpiration = member.getDaysUntilExpiration()
      expect(daysUntilExpiration).toBe(10)
    })

    test("should return null for member without expiration", () => {
      const member = ProjectMember.create({
        ...sampleMemberData,
        expiresAt: undefined,
      })

      expect(member.getDaysUntilExpiration()).toBeNull()
    })

    test("should calculate membership duration", () => {
      const pastDate = new Date()
      pastDate.setDate(pastDate.getDate() - 30)

      const member = ProjectMember.create({
        ...sampleMemberData,
        assignedAt: pastDate.toISOString(),
      })

      const duration = member.getMembershipDuration()
      expect(duration).toBe(30)
    })

    test("should handle same-day assignment", () => {
      const member = ProjectMember.createNew(
        "member_new",
        1001,
        "project_001",
        designerRole
      )

      const duration = member.getMembershipDuration()
      expect(duration).toBe(0)
    })

    test("should identify inactive member based on activity threshold", () => {
      const oldDate = new Date()
      oldDate.setDate(oldDate.getDate() - 45)

      const inactiveMember = ProjectMember.create({
        ...sampleMemberData,
        lastActivity: oldDate.toISOString(),
      })

      expect(inactiveMember.isInactive(30)).toBe(true)
      expect(inactiveMember.isInactive(60)).toBe(false)
    })

    test("should use assignment date when no activity recorded", () => {
      const oldDate = new Date()
      oldDate.setDate(oldDate.getDate() - 45)

      const member = ProjectMember.create({
        ...sampleMemberData,
        assignedAt: oldDate.toISOString(),
        lastActivity: undefined,
      })

      expect(member.isInactive(30)).toBe(true)
      expect(member.isInactive(60)).toBe(false)
    })
  })

  describe("Status Summary", () => {
    test("should provide comprehensive status summary", () => {
      const member = ProjectMember.create(sampleMemberData)
      const summary = member.getStatusSummary()

      expect(summary.isActive).toBe(true)
      expect(summary.hasExpired).toBe(false)
      expect(summary.roleName).toBe("Electrical Engineer")
      expect(summary.lastActivity).toBe("2024-01-15T14:30:00Z")
      expect(summary.membershipDuration).toBeGreaterThanOrEqual(0)
      expect(typeof summary.isApproachingExpiration).toBe("boolean")
    })

    test("should handle member without expiration in summary", () => {
      const member = ProjectMember.create({
        ...sampleMemberData,
        expiresAt: undefined,
      })
      const summary = member.getStatusSummary()

      expect(summary.daysUntilExpiration).toBeNull()
      expect(summary.isApproachingExpiration).toBe(false)
    })

    test("should handle expired member in summary", () => {
      const member = ProjectMember.create({
        ...sampleMemberData,
        assignedAt: "2022-01-01T10:00:00Z", // Earlier assignment
        expiresAt: "2023-01-01T00:00:00Z", // Past date but after assignment
      })
      const summary = member.getStatusSummary()

      expect(summary.hasExpired).toBe(true)
      expect(summary.isActive).toBe(false)
      expect(summary.daysUntilExpiration).toBeLessThan(0)
    })
  })

  describe("Entity Properties", () => {
    test("should maintain entity identity", () => {
      const member1 = ProjectMember.create(sampleMemberData)
      const member2 = ProjectMember.create({
        ...sampleMemberData,
        notes: "Different notes",
      })

      expect(member1.equals(member2)).toBe(true) // Same ID
    })

    test("should distinguish different entities", () => {
      const member1 = ProjectMember.create(sampleMemberData)
      const member2 = ProjectMember.create({
        ...sampleMemberData,
        id: "member_002",
      })

      expect(member1.equals(member2)).toBe(false) // Different IDs
    })

    test("should convert to string correctly", () => {
      const member = ProjectMember.create(sampleMemberData)
      const stringRepresentation = member.toString()

      expect(stringRepresentation).toContain("member_001")
      expect(stringRepresentation).toContain("1001")
      expect(stringRepresentation).toContain("Electrical Engineer")
      expect(stringRepresentation).toContain("true")
    })

    test("should maintain immutability across operations", () => {
      const original = ProjectMember.create(sampleMemberData)
      const modified = original
        .assignRole(adminRole, 2001)
        .updateActivity()
        .addNotes("Test note")

      expect(original.role.role).toBe("Electrical Engineer")
      expect(modified.role.role).toBe("Administrator")
      expect(original.notes).toBe("Initial assignment")
      expect(modified.notes).toContain("Test note")
      expect(original).not.toBe(modified)
    })
  })

  describe("Edge Cases", () => {
    test("should handle role assignment validation gracefully", () => {
      const member = ProjectMember.create(sampleMemberData)
      const newRole = TeamRole.createAdministrator()

      // Basic validation should not throw for valid inputs
      expect(() => {
        member.canAssignRole(newRole)
      }).not.toThrow()
    })

    test("should handle expiration calculations near boundaries", () => {
      const now = new Date()
      const member = ProjectMember.create({
        ...sampleMemberData,
        expiresAt: now.toISOString(),
      })

      // Should handle current time gracefully
      expect(typeof member.getDaysUntilExpiration()).toBe("number")
      expect(typeof member.hasExpired()).toBe("boolean")
    })

    test("should handle very long notes", () => {
      const member = ProjectMember.create(sampleMemberData)
      const longNote = "A".repeat(1000)
      const withLongNotes = member.addNotes(longNote)

      expect(withLongNotes.notes).toContain(longNote)
      expect(withLongNotes.notes!.length).toBeGreaterThan(1000)
    })

    test("should handle assignment date validation strictly", () => {
      // Test with various date formats that should fail
      const invalidDates = [
        "2024-01-01", // Missing time
        "2024-01-01 10:00:00", // Wrong format
        "01/01/2024", // Wrong format
        "", // Empty
        "not-a-date", // Invalid
      ]

      invalidDates.forEach((invalidDate) => {
        expect(() => {
          ProjectMember.create({
            ...sampleMemberData,
            assignedAt: invalidDate,
          })
        }).toThrow()
      })
    })
  })
})
