/**
 * ProjectMember Entity
 *
 * Represents a team member within a project with role-based permissions.
 * Encapsulates member lifecycle, role management, and permission validation.
 *
 * An entity with identity, mutable state, and business behavior.
 * Leverages the TeamRole value object for permission logic.
 */

import { TeamRole } from "../value-objects/TeamRole"

export interface ProjectMemberData {
  id: string
  userId: number
  projectId: string
  role: TeamRole
  assignedAt: string
  assignedBy?: number
  expiresAt?: string
  isActive?: boolean
  lastActivity?: string
  notes?: string
}

export interface CreateProjectMemberData {
  userId: number
  projectId: string
  role: TeamRole
  assignedBy?: number
  expiresAt?: string
}

export class ProjectMember {
  private constructor(
    private readonly _id: string,
    private readonly _userId: number,
    private readonly _projectId: string,
    private _role: TeamRole,
    private readonly _assignedAt: string,
    private readonly _assignedBy?: number,
    private _expiresAt?: string,
    private _isActive: boolean = true,
    private _lastActivity?: string,
    private _notes?: string
  ) {
    this.validateMember()
  }

  public static create(data: ProjectMemberData): ProjectMember {
    return new ProjectMember(
      data.id,
      data.userId,
      data.projectId,
      data.role,
      data.assignedAt,
      data.assignedBy,
      data.expiresAt,
      data.isActive ?? true,
      data.lastActivity,
      data.notes
    )
  }

  public static createNew(
    id: string,
    userId: number,
    projectId: string,
    role: TeamRole,
    assignedBy?: number,
    expiresAt?: string
  ): ProjectMember {
    return new ProjectMember(
      id,
      userId,
      projectId,
      role,
      new Date().toISOString(),
      assignedBy,
      expiresAt,
      true,
      undefined,
      undefined
    )
  }

  // Identity and Core Properties
  public get id(): string {
    return this._id
  }

  public get userId(): number {
    return this._userId
  }

  public get projectId(): string {
    return this._projectId
  }

  public get role(): TeamRole {
    return this._role
  }

  public get assignedAt(): string {
    return this._assignedAt
  }

  public get assignedBy(): number | undefined {
    return this._assignedBy
  }

  public get expiresAt(): string | undefined {
    return this._expiresAt
  }

  public get isActive(): boolean {
    if (!this._isActive) return false
    if (!this._expiresAt) return true
    return new Date(this._expiresAt) > new Date()
  }

  public get lastActivity(): string | undefined {
    return this._lastActivity
  }

  public get notes(): string | undefined {
    return this._notes
  }

  // Business Logic Methods

  /**
   * Assigns a new role to the project member
   * Validates role assignment permissions and updates role
   */
  public assignRole(
    newRole: TeamRole,
    assignedBy?: number,
    reason?: string
  ): ProjectMember {
    // Validate that the assigner has permission to assign this role
    if (assignedBy && !this.canBeAssignedRoleBy(newRole, assignedBy)) {
      throw new Error(
        `User ${assignedBy} does not have permission to assign role '${newRole.role}'`
      )
    }

    // Create updated member with new role
    return new ProjectMember(
      this._id,
      this._userId,
      this._projectId,
      newRole,
      this._assignedAt,
      this._assignedBy,
      this._expiresAt,
      this._isActive,
      new Date().toISOString(), // Update last activity
      reason
        ? `${this._notes || ""}\nRole changed to ${newRole.role}: ${reason}`.trim()
        : this._notes
    )
  }

  /**
   * Deactivates the project member
   * Sets member to inactive and updates activity timestamp
   */
  public deactivate(deactivatedBy?: number, reason?: string): ProjectMember {
    if (!this._isActive) {
      throw new Error("Project member is already inactive")
    }

    const deactivationNote = `Deactivated by ${deactivatedBy || "system"}${reason ? `: ${reason}` : ""}`
    const updatedNotes = this._notes
      ? `${this._notes}\n${deactivationNote}`
      : deactivationNote

    return new ProjectMember(
      this._id,
      this._userId,
      this._projectId,
      this._role,
      this._assignedAt,
      this._assignedBy,
      this._expiresAt,
      false, // Set to inactive
      new Date().toISOString(),
      updatedNotes
    )
  }

  /**
   * Reactivates the project member
   * Sets member to active and updates activity timestamp
   */
  public reactivate(reactivatedBy?: number, reason?: string): ProjectMember {
    if (this._isActive) {
      throw new Error("Project member is already active")
    }

    const reactivationNote = `Reactivated by ${reactivatedBy || "system"}${reason ? `: ${reason}` : ""}`
    const updatedNotes = this._notes
      ? `${this._notes}\n${reactivationNote}`
      : reactivationNote

    return new ProjectMember(
      this._id,
      this._userId,
      this._projectId,
      this._role,
      this._assignedAt,
      this._assignedBy,
      this._expiresAt,
      true, // Set to active
      new Date().toISOString(),
      updatedNotes
    )
  }

  /**
   * Extends the expiration date of the project member
   * Updates expiration and activity timestamps
   */
  public extendExpiration(
    newExpirationDate: string,
    extendedBy?: number,
    reason?: string
  ): ProjectMember {
    if (!this.isValidDate(newExpirationDate)) {
      throw new Error("Invalid expiration date format")
    }

    if (this._expiresAt && newExpirationDate <= this._expiresAt) {
      throw new Error(
        "New expiration date must be later than current expiration date"
      )
    }

    const extensionNote = `Expiration extended to ${newExpirationDate} by ${extendedBy || "system"}${reason ? `: ${reason}` : ""}`
    const updatedNotes = this._notes
      ? `${this._notes}\n${extensionNote}`
      : extensionNote

    return new ProjectMember(
      this._id,
      this._userId,
      this._projectId,
      this._role,
      this._assignedAt,
      this._assignedBy,
      newExpirationDate,
      this._isActive,
      new Date().toISOString(),
      updatedNotes
    )
  }

  /**
   * Updates the last activity timestamp
   * Used to track member engagement
   */
  public updateActivity(): ProjectMember {
    return new ProjectMember(
      this._id,
      this._userId,
      this._projectId,
      this._role,
      this._assignedAt,
      this._assignedBy,
      this._expiresAt,
      this._isActive,
      new Date().toISOString(),
      this._notes
    )
  }

  /**
   * Adds notes to the project member
   * Appends to existing notes with timestamp
   */
  public addNotes(notes: string, addedBy?: number): ProjectMember {
    const timestamp = new Date().toISOString().split("T")[0] // Date only
    const noteEntry = `[${timestamp}${addedBy ? ` - User ${addedBy}` : ""}] ${notes}`
    const updatedNotes = this._notes
      ? `${this._notes}\n${noteEntry}`
      : noteEntry

    return new ProjectMember(
      this._id,
      this._userId,
      this._projectId,
      this._role,
      this._assignedAt,
      this._assignedBy,
      this._expiresAt,
      this._isActive,
      this._lastActivity,
      updatedNotes
    )
  }

  // Business Queries

  /**
   * Checks if the member is currently active and valid
   * Considers both active flag and expiration
   */
  public isCurrentlyActive(): boolean {
    return this._isActive && !this.hasExpired()
  }

  /**
   * Checks if the member's assignment has expired
   * Returns true if expiration date has passed
   */
  public hasExpired(): boolean {
    if (!this._expiresAt) return false
    return new Date(this._expiresAt) < new Date()
  }

  /**
   * Checks if the member is approaching expiration
   * Returns true if expiration is within the warning period
   */
  public isApproachingExpiration(warningDays: number = 7): boolean {
    if (!this._expiresAt) return false

    const expirationDate = new Date(this._expiresAt)
    const warningDate = new Date()
    warningDate.setDate(warningDate.getDate() + warningDays)

    return expirationDate <= warningDate && expirationDate > new Date()
  }

  /**
   * Determines if the member can manage the project
   * Delegates to role permissions
   */
  public canManageProject(): boolean {
    return this.isCurrentlyActive() && this._role.canManageProject()
  }

  /**
   * Determines if the member can manage team members
   * Delegates to role permissions
   */
  public canManageTeam(): boolean {
    return this.isCurrentlyActive() && this._role.canManageTeam()
  }

  /**
   * Determines if the member can edit project details
   * Delegates to role permissions
   */
  public canEditProject(): boolean {
    return this.isCurrentlyActive() && this._role.canEditProject()
  }

  /**
   * Determines if the member can view financial information
   * Delegates to role permissions
   */
  public canViewFinancials(): boolean {
    return this.isCurrentlyActive() && this._role.canViewFinancials()
  }

  /**
   * Determines if the member can approve designs
   * Delegates to role permissions
   */
  public canApproveDesign(): boolean {
    return this.isCurrentlyActive() && this._role.canApproveDesign()
  }

  /**
   * Determines if the member has design permissions
   * Delegates to role permissions
   */
  public hasDesignPermissions(): boolean {
    return this.isCurrentlyActive() && this._role.hasDesignPermissions()
  }

  /**
   * Checks if the member can assign a specific role to others
   * Requires active status and role assignment permissions
   */
  public canAssignRole(targetRole: TeamRole): boolean {
    return this.isCurrentlyActive() && this._role.canAssignRole(targetRole)
  }

  /**
   * Validates if this member can be assigned a role by another user
   * Basic validation - full logic would involve checking assigner's permissions
   */
  public canBeAssignedRoleBy(newRole: TeamRole, assignerId: number): boolean {
    // This is a simplified validation
    // In a real implementation, you would fetch the assigner's role and validate
    // For now, we'll assume basic validation
    return this.isCurrentlyActive()
  }

  /**
   * Gets the number of days until expiration
   * Returns null if no expiration date set
   */
  public getDaysUntilExpiration(): number | null {
    if (!this._expiresAt) return null

    const expirationDate = new Date(this._expiresAt)
    const currentDate = new Date()
    const diffTime = expirationDate.getTime() - currentDate.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

    return diffDays
  }

  /**
   * Gets the duration of membership in days
   * Calculates from assignment date to current date
   */
  public getMembershipDuration(): number {
    const assignedDate = new Date(this._assignedAt)
    const currentDate = new Date()
    const diffTime = currentDate.getTime() - assignedDate.getTime()
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24))

    return Math.max(0, diffDays)
  }

  /**
   * Checks if member has been inactive for a specified period
   * Returns true if last activity is older than threshold
   */
  public isInactive(inactivityThresholdDays: number = 30): boolean {
    if (!this._lastActivity) {
      // If no activity recorded, check against assignment date
      return this.getMembershipDuration() > inactivityThresholdDays
    }

    const lastActivityDate = new Date(this._lastActivity)
    const thresholdDate = new Date()
    thresholdDate.setDate(thresholdDate.getDate() - inactivityThresholdDays)

    return lastActivityDate < thresholdDate
  }

  // Utility Methods

  /**
   * Gets a summary of the member's status
   * Useful for reporting and dashboard views
   */
  public getStatusSummary(): {
    isActive: boolean
    hasExpired: boolean
    isApproachingExpiration: boolean
    daysUntilExpiration: number | null
    membershipDuration: number
    roleName: string
    lastActivity: string | undefined
  } {
    return {
      isActive: this.isCurrentlyActive(),
      hasExpired: this.hasExpired(),
      isApproachingExpiration: this.isApproachingExpiration(),
      daysUntilExpiration: this.getDaysUntilExpiration(),
      membershipDuration: this.getMembershipDuration(),
      roleName: this._role.role,
      lastActivity: this._lastActivity,
    }
  }

  // Entity Equality (based on identity)
  public equals(other: ProjectMember): boolean {
    return this._id === other._id
  }

  public toString(): string {
    return `ProjectMember(${this._id}, User: ${this._userId}, Role: ${this._role.role}, Active: ${this._isActive})`
  }

  // Private Methods

  private validateMember(): void {
    if (!this._id || this._id.trim() === "") {
      throw new Error("Project member ID cannot be empty")
    }

    if (!this._userId || this._userId <= 0) {
      throw new Error("User ID must be a positive number")
    }

    if (!this._projectId || this._projectId.trim() === "") {
      throw new Error("Project ID cannot be empty")
    }

    if (!this._assignedAt || !this.isValidDate(this._assignedAt)) {
      throw new Error("Assignment date must be a valid ISO date string")
    }

    if (this._expiresAt && !this.isValidDate(this._expiresAt)) {
      throw new Error("Expiration date must be a valid ISO date string")
    }

    if (this._expiresAt && this._expiresAt <= this._assignedAt) {
      throw new Error("Expiration date must be after assignment date")
    }

    if (this._lastActivity && !this.isValidDate(this._lastActivity)) {
      throw new Error("Last activity date must be a valid ISO date string")
    }
  }

  private isValidDate(dateString: string): boolean {
    const date = new Date(dateString)
    return (
      date instanceof Date && !isNaN(date.getTime()) && dateString.includes("T")
    )
  }
}
