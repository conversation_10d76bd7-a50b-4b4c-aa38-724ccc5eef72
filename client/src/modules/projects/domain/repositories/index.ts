/**
 * Projects Domain Repositories
 *
 * Exports all repository interfaces for the Projects domain.
 * Repository interfaces define contracts for aggregate persistence
 * and enable infrastructure-agnostic domain logic.
 */

export {
  IProjectRepository,
  ProjectSearchCriteria,
  ProjectPaginationOptions,
  ProjectQueryResult,
  ProjectRepositoryError,
  ProjectNotFoundError,
  ProjectDuplicateError,
  ProjectPersistenceError,
} from "./IProjectRepository"

// Future repository interfaces will be exported here:
// export { IProjectPortfolioRepository } from './IProjectPortfolioRepository'
