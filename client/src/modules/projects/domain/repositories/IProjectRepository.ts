/**
 * IProjectRepository Interface
 *
 * Defines the contract for Project aggregate persistence within the Repository pattern.
 * This interface abstracts data access operations for the Project aggregate root,
 * enabling infrastructure-agnostic domain logic and testability through dependency injection.
 *
 * Following DDD principles, this repository works exclusively with Project aggregates
 * and maintains aggregate boundary consistency during persistence operations.
 */

import { Project } from "../entities/Project"

/**
 * Search criteria for project queries
 * Enables flexible project filtering and querying
 */
export interface ProjectSearchCriteria {
  name?: string
  clientId?: number
  ownerId?: number
  status?: string
  priority?: "Low" | "Medium" | "High" | "Critical"
  tags?: string[]
  isOfflineMode?: boolean
  createdAfter?: string
  createdBefore?: string
  budgetMin?: number
  budgetMax?: number
  currency?: string
  memberUserId?: number
  location?: string
  hasExpiredMembers?: boolean
  requiresApproval?: boolean
}

/**
 * Pagination options for project lists
 * Supports efficient data retrieval for large datasets
 */
export interface ProjectPaginationOptions {
  offset?: number
  limit?: number
  sortBy?: "name" | "createdAt" | "updatedAt" | "status" | "priority"
  sortOrder?: "asc" | "desc"
}

/**
 * Project query result with pagination metadata
 * Provides both data and pagination context
 */
export interface ProjectQueryResult {
  projects: Project[]
  totalCount: number
  offset: number
  limit: number
  hasMore: boolean
}

/**
 * Repository interface for Project aggregate persistence
 *
 * This interface defines all persistence operations for Project aggregates,
 * ensuring that the domain layer remains infrastructure-agnostic while
 * providing comprehensive data access capabilities.
 */
export interface IProjectRepository {
  /**
   * Persists a Project aggregate to storage
   * Handles both new project creation and existing project updates
   *
   * @param project - The Project aggregate to persist
   * @returns Promise<void> - Resolves when persistence is complete
   * @throws Error if persistence fails or violates business constraints
   */
  save(project: Project): Promise<void>

  /**
   * Retrieves a Project aggregate by its unique identifier
   * Returns the complete aggregate with all associated entities
   *
   * @param id - The unique project identifier
   * @returns Promise<Project | null> - The project if found, null otherwise
   * @throws Error if data access fails
   */
  findById(id: string): Promise<Project | null>

  /**
   * Retrieves multiple Project aggregates by their identifiers
   * Efficient batch retrieval for multiple projects
   *
   * @param ids - Array of project identifiers
   * @returns Promise<Project[]> - Array of found projects (may be partial)
   * @throws Error if data access fails
   */
  findByIds(ids: string[]): Promise<Project[]>

  /**
   * Retrieves all Project aggregates owned by a specific user
   * Common query pattern for user-specific project management
   *
   * @param ownerId - The user ID of the project owner
   * @param options - Optional pagination and sorting options
   * @returns Promise<ProjectQueryResult> - Paginated results with metadata
   * @throws Error if data access fails
   */
  findByOwnerId(
    ownerId: number,
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult>

  /**
   * Retrieves all Project aggregates for a specific client
   * Enables client-focused project management and reporting
   *
   * @param clientId - The client identifier
   * @param options - Optional pagination and sorting options
   * @returns Promise<ProjectQueryResult> - Paginated results with metadata
   * @throws Error if data access fails
   */
  findByClientId(
    clientId: number,
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult>

  /**
   * Retrieves Project aggregates where a user is a team member
   * Supports user dashboard and collaboration views
   *
   * @param userId - The team member's user ID
   * @param options - Optional pagination and sorting options
   * @returns Promise<ProjectQueryResult> - Paginated results with metadata
   * @throws Error if data access fails
   */
  findByMemberUserId(
    userId: number,
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult>

  /**
   * Retrieves Project aggregates matching specific search criteria
   * Enables flexible querying with multiple filter combinations
   *
   * @param criteria - Search criteria for project filtering
   * @param options - Optional pagination and sorting options
   * @returns Promise<ProjectQueryResult> - Paginated results with metadata
   * @throws Error if data access fails
   */
  findByCriteria(
    criteria: ProjectSearchCriteria,
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult>

  /**
   * Retrieves all Project aggregates with optional pagination
   * Administrative function for complete project oversight
   *
   * @param options - Optional pagination and sorting options
   * @returns Promise<ProjectQueryResult> - Paginated results with metadata
   * @throws Error if data access fails
   */
  findAll(options?: ProjectPaginationOptions): Promise<ProjectQueryResult>

  /**
   * Retrieves Project aggregates requiring immediate attention
   * Business query for dashboard alerts and notifications
   *
   * @param userId - Optional user ID to filter for user-relevant projects
   * @returns Promise<Project[]> - Projects requiring attention
   * @throws Error if data access fails
   */
  findProjectsRequiringAttention(userId?: number): Promise<Project[]>

  /**
   * Retrieves Project aggregates with expired team members
   * Maintenance query for team member lifecycle management
   *
   * @param options - Optional pagination and sorting options
   * @returns Promise<ProjectQueryResult> - Projects with expired members
   * @throws Error if data access fails
   */
  findProjectsWithExpiredMembers(
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult>

  /**
   * Retrieves Project aggregates that are over budget
   * Financial monitoring query for budget management
   *
   * @param options - Optional pagination and sorting options
   * @returns Promise<ProjectQueryResult> - Projects exceeding budget
   * @throws Error if data access fails
   */
  findOverBudgetProjects(
    options?: ProjectPaginationOptions
  ): Promise<ProjectQueryResult>

  /**
   * Checks if a project with the given ID exists
   * Efficient existence check without full aggregate loading
   *
   * @param id - The project identifier to check
   * @returns Promise<boolean> - True if project exists, false otherwise
   * @throws Error if data access fails
   */
  exists(id: string): Promise<boolean>

  /**
   * Counts Project aggregates matching specific criteria
   * Efficient counting for analytics and reporting
   *
   * @param criteria - Optional search criteria for filtering
   * @returns Promise<number> - Total count of matching projects
   * @throws Error if data access fails
   */
  count(criteria?: ProjectSearchCriteria): Promise<number>

  /**
   * Removes a Project aggregate from storage
   * Permanent deletion with aggregate boundary respect
   *
   * @param id - The project identifier to remove
   * @returns Promise<void> - Resolves when removal is complete
   * @throws Error if project not found or removal fails
   */
  remove(id: string): Promise<void>

  /**
   * Removes multiple Project aggregates from storage
   * Efficient batch deletion for administrative operations
   *
   * @param ids - Array of project identifiers to remove
   * @returns Promise<void> - Resolves when all removals complete
   * @throws Error if any deletion fails (operation may be partial)
   */
  removeMany(ids: string[]): Promise<void>

  /**
   * Archives completed projects for long-term storage
   * Business operation for project lifecycle management
   *
   * @param projectIds - Array of completed project identifiers
   * @returns Promise<void> - Resolves when archival is complete
   * @throws Error if projects are not in completed state or archival fails
   */
  archiveCompletedProjects(projectIds: string[]): Promise<void>

  /**
   * Validates aggregate consistency and business rules
   * Infrastructure-level validation supporting domain integrity
   *
   * @param id - Project identifier to validate
   * @returns Promise<boolean> - True if aggregate is consistent
   * @throws Error if validation fails or data access fails
   */
  validateAggregateConsistency(id: string): Promise<boolean>
}

/**
 * Repository-specific exceptions for error handling
 * Enables specific error handling in application services
 */
export class ProjectRepositoryError extends Error {
  constructor(
    message: string,
    public readonly code: string,
    public readonly projectId?: string
  ) {
    super(message)
    this.name = "ProjectRepositoryError"
  }
}

export class ProjectNotFoundError extends ProjectRepositoryError {
  constructor(projectId: string) {
    super(
      `Project with ID '${projectId}' was not found`,
      "PROJECT_NOT_FOUND",
      projectId
    )
    this.name = "ProjectNotFoundError"
  }
}

export class ProjectDuplicateError extends ProjectRepositoryError {
  constructor(projectId: string) {
    super(
      `Project with ID '${projectId}' already exists`,
      "PROJECT_DUPLICATE",
      projectId
    )
    this.name = "ProjectDuplicateError"
  }
}

export class ProjectPersistenceError extends ProjectRepositoryError {
  constructor(message: string, projectId?: string) {
    super(message, "PROJECT_PERSISTENCE_ERROR", projectId)
    this.name = "ProjectPersistenceError"
  }
}