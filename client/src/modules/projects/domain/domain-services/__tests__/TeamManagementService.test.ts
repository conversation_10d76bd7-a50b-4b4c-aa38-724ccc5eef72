/**
 * TeamManagementService Unit Tests
 *
 * Comprehensive test coverage for the TeamManagementService domain service.
 * Tests cover member assignment, role management, expiration handling, and integration scenarios.
 */

import {
  beforeEach,
  describe,
  expect,
  test,
  vi,
  type MockedFunction,
} from "vitest"
import { CreateProjectData, Project, ProjectData } from "../../entities/Project"
import { ProjectMember } from "../../entities/ProjectMember"
import { IProjectRepository } from "../../repositories/IProjectRepository"
import { TeamRole } from "../../value-objects/TeamRole"
import {
  ExternalUserInfo,
  MemberAssignmentError,
  MemberExpirationError,
  RoleManagementError,
  TeamAssignmentContext,
  TeamManagementService,
  UserService,
} from "../TeamManagementService"

describe("TeamManagementService", () => {
  let service: TeamManagementService
  let mockRepository: {
    save: MockedFunction<IProjectRepository["save"]>
    findById: MockedFunction<IProjectRepository["findById"]>
    findByIds: MockedFunction<IProjectRepository["findByIds"]>
    findByOwnerId: MockedFunction<IProjectRepository["findByOwnerId"]>
    findByClientId: MockedFunction<IProjectRepository["findByClientId"]>
    findByMemberUserId: MockedFunction<IProjectRepository["findByMemberUserId"]>
    findByCriteria: MockedFunction<IProjectRepository["findByCriteria"]>
    findAll: MockedFunction<IProjectRepository["findAll"]>
    findProjectsRequiringAttention: MockedFunction<
      IProjectRepository["findProjectsRequiringAttention"]
    >
    findProjectsWithExpiredMembers: MockedFunction<
      IProjectRepository["findProjectsWithExpiredMembers"]
    >
    findOverBudgetProjects: MockedFunction<
      IProjectRepository["findOverBudgetProjects"]
    >
    exists: MockedFunction<IProjectRepository["exists"]>
    count: MockedFunction<IProjectRepository["count"]>
    remove: MockedFunction<IProjectRepository["remove"]>
    removeMany: MockedFunction<IProjectRepository["removeMany"]>
    archiveCompletedProjects: MockedFunction<
      IProjectRepository["archiveCompletedProjects"]
    >
    validateAggregateConsistency: MockedFunction<
      IProjectRepository["validateAggregateConsistency"]
    >
  }
  let mockUserService: {
    getUserInfo: MockedFunction<UserService["getUserInfo"]>
    validateUserExists: MockedFunction<UserService["validateUserExists"]>
    getUserSkills: MockedFunction<UserService["getUserSkills"]>
    getUserAvailability: MockedFunction<UserService["getUserAvailability"]>
    getUserProjectLoad: MockedFunction<UserService["getUserProjectLoad"]>
  }
  let sampleProject: Project
  let sampleContext: TeamAssignmentContext
  let sampleUserInfo: ExternalUserInfo

  beforeEach(() => {
    // Create mock repository
    mockRepository = {
      save: vi.fn(),
      findById: vi.fn(),
      findByIds: vi.fn(),
      findByOwnerId: vi.fn(),
      findByClientId: vi.fn(),
      findByMemberUserId: vi.fn(),
      findByCriteria: vi.fn(),
      findAll: vi.fn(),
      findProjectsRequiringAttention: vi.fn(),
      findProjectsWithExpiredMembers: vi.fn(),
      findOverBudgetProjects: vi.fn(),
      exists: vi.fn(),
      count: vi.fn(),
      remove: vi.fn(),
      removeMany: vi.fn(),
      archiveCompletedProjects: vi.fn(),
      validateAggregateConsistency: vi.fn(),
    }

    // Create mock user service
    mockUserService = {
      getUserInfo: vi.fn(),
      validateUserExists: vi.fn(),
      getUserSkills: vi.fn(),
      getUserAvailability: vi.fn(),
      getUserProjectLoad: vi.fn(),
    }

    service = new TeamManagementService(mockRepository, mockUserService)

    // Sample test data
    const projectCreateData: CreateProjectData = {
      id: "project_001",
      name: "Test Project",
      ownerId: 2001,
      budgetData: { totalAmount: 100000, currency: "EUR" },
      priority: "High",
    }

    sampleProject = Project.create(projectCreateData)

    sampleContext = {
      assignedBy: 2001,
      reason: "Team expansion",
      priority: "High",
      skillsRequired: ["electrical-design"],
      estimatedWorkload: 70,
      isTemporaryAssignment: false,
      requiresApproval: false,
    }

    sampleUserInfo = {
      id: 3001,
      name: "John Designer",
      email: "<EMAIL>",
      isActive: true,
      department: "Engineering",
      skills: ["electrical-design", "cad-software"],
      certifications: ["CAD Certification", "IEC 61508"],
      availabilityStatus: "Available",
      maxProjectLoad: 5,
      currentProjectCount: 2,
    }

    // Setup default mocks
    mockRepository.save.mockResolvedValue(undefined)
    mockUserService.getUserInfo.mockResolvedValue(sampleUserInfo)
    mockRepository.findByMemberUserId.mockResolvedValue({
      projects: [],
      totalCount: 0,
      offset: 0,
      limit: 100,
      hasMore: false,
    })
  })

  describe("Error Classes", () => {
    test("should create team management error with correct properties", () => {
      const error = new MemberAssignmentError("Assignment failed", {
        userId: 3001,
      })

      expect(error.name).toBe("MemberAssignmentError")
      expect(error.message).toBe("Assignment failed")
      expect(error.operation).toBe("MEMBER_ASSIGNMENT")
      expect(error.details).toEqual({ userId: 3001 })
      expect(error instanceof Error).toBe(true)
    })

    test("should create role management error", () => {
      const error = new RoleManagementError("Role assignment failed")

      expect(error.name).toBe("RoleManagementError")
      expect(error.operation).toBe("ROLE_MANAGEMENT")
    })

    test("should create member expiration error", () => {
      const error = new MemberExpirationError("Extension failed")

      expect(error.name).toBe("MemberExpirationError")
      expect(error.operation).toBe("MEMBER_EXPIRATION")
    })
  })

  describe("assignMemberToProject", () => {
    test("should successfully assign member to project", async () => {
      const role = TeamRole.create({ role: "Electrical Engineer" })

      const result = await service.assignMemberToProject(
        sampleProject,
        3001,
        role,
        sampleContext
      )

      expect(result.success).toBe(true)
      expect(result.project).toBeDefined()
      expect(result.affectedMember).toBeDefined()
      expect(result.affectedMember!.userId).toBe(3001)
      expect(result.affectedMember!.role.role).toBe("Electrical Engineer")
      expect(mockRepository.save).toHaveBeenCalledWith(result.project)
    })

    test("should throw error for inactive user", async () => {
      const inactiveUser = { ...sampleUserInfo, isActive: false }
      mockUserService.getUserInfo.mockResolvedValue(inactiveUser)

      const role = TeamRole.create({ role: "Electrical Engineer" })

      await expect(
        service.assignMemberToProject(sampleProject, 3001, role, sampleContext)
      ).rejects.toThrow(
        "User 3001 is not available for assignment: User account is inactive"
      )
    })

    test("should throw error for unavailable user", async () => {
      const unavailableUser = {
        ...sampleUserInfo,
        availabilityStatus: "On Leave" as const,
      }
      mockUserService.getUserInfo.mockResolvedValue(unavailableUser)

      const role = TeamRole.create({ role: "Electrical Engineer" })

      await expect(
        service.assignMemberToProject(sampleProject, 3001, role, sampleContext)
      ).rejects.toThrow(
        "User 3001 is not available for assignment: User is currently on leave"
      )
    })

    test("should throw error for user at maximum project load", async () => {
      const overloadedUser = {
        ...sampleUserInfo,
        currentProjectCount: 5,
        maxProjectLoad: 5,
      }
      mockUserService.getUserInfo.mockResolvedValue(overloadedUser)

      const role = TeamRole.create({ role: "Electrical Engineer" })

      await expect(
        service.assignMemberToProject(sampleProject, 3001, role, sampleContext)
      ).rejects.toThrow(
        "User 3001 is not available for assignment: User has reached maximum project load"
      )
    })

    test("should include warnings for user approaching project load limit", async () => {
      const nearLimitUser = {
        ...sampleUserInfo,
        currentProjectCount: 4,
        maxProjectLoad: 5,
      }
      mockUserService.getUserInfo.mockResolvedValue(nearLimitUser)

      const role = TeamRole.create({ role: "Electrical Engineer" })

      const result = await service.assignMemberToProject(
        sampleProject,
        3001,
        role,
        sampleContext
      )

      expect(result.warnings).toContain(
        "User is approaching maximum project load"
      )
    })

    test("should include warnings for skill compatibility issues", async () => {
      const limitedSkillUser = {
        ...sampleUserInfo,
        skills: ["basic-electrical"], // Missing required skills
      }
      mockUserService.getUserInfo.mockResolvedValue(limitedSkillUser)

      const contextWithSkills = {
        ...sampleContext,
        skillsRequired: ["electrical-design", "cad-software"],
      }

      const role = TeamRole.create({ role: "Electrical Engineer" })

      const result = await service.assignMemberToProject(
        sampleProject,
        3001,
        role,
        contextWithSkills
      )

      expect(
        result.warnings.some((w) => w.includes("may lack required skills"))
      ).toBe(true)
      expect(result.recommendations).toContain(
        "Consider providing additional training or mentorship"
      )
    })

    test("should generate role-specific recommendations", async () => {
      const role = TeamRole.create({ role: "Electrical Engineer" })

      const result = await service.assignMemberToProject(
        sampleProject,
        3001,
        role,
        sampleContext
      )

      expect(result.recommendations).toContain(
        "Ensure designer has access to latest CAD software and standards"
      )
      expect(result.recommendations).toContain(
        "Schedule orientation session with lead engineer"
      )
    })

    test("should generate appropriate next actions", async () => {
      const role = TeamRole.create({ role: "Project Manager" })

      const result = await service.assignMemberToProject(
        sampleProject,
        3001,
        role,
        sampleContext
      )

      expect(result.nextActions).toContain(
        "Send welcome email with project information"
      )
      expect(result.nextActions).toContain(
        "Schedule project orientation session"
      )
      expect(result.nextActions).toContain(
        "Grant administrative access to project systems"
      )
    })

    test("should handle temporary assignment context", async () => {
      const tempContext = { ...sampleContext, isTemporaryAssignment: true }
      const role = TeamRole.create({ role: "Electrical Engineer" })

      const result = await service.assignMemberToProject(
        sampleProject,
        3001,
        role,
        tempContext
      )

      expect(result.recommendations).toContain(
        "Document knowledge transfer requirements before assignment ends"
      )
      expect(result.recommendations).toContain(
        "Plan for permanent replacement if role becomes permanent"
      )
    })

    test("should handle high workload assignments", async () => {
      const highWorkloadContext = { ...sampleContext, estimatedWorkload: 90 }
      const role = TeamRole.create({ role: "Electrical Engineer" })

      const result = await service.assignMemberToProject(
        sampleProject,
        3001,
        role,
        highWorkloadContext
      )

      expect(result.recommendations).toContain(
        "Monitor member workload to prevent burnout"
      )
      expect(result.recommendations).toContain(
        "Consider additional support or resource allocation"
      )
    })

    test("should handle approval-required assignments", async () => {
      const approvalContext = { ...sampleContext, requiresApproval: true }
      const role = TeamRole.create({ role: "Electrical Engineer" })

      const result = await service.assignMemberToProject(
        sampleProject,
        3001,
        role,
        approvalContext
      )

      expect(result.nextActions).toContain(
        "Submit assignment for stakeholder approval"
      )
      expect(result.nextActions).toContain(
        "Hold assignment pending approval confirmation"
      )
    })
  })

  describe("validateRolePermissions", () => {
    test("should validate valid role assignment permissions", async () => {
      const role = TeamRole.create({ role: "Electrical Engineer" })

      await expect(
        service.validateRolePermissions(sampleProject, 2001, role, 3001)
      ).resolves.toBeUndefined()
    })

    test("should throw error for non-member trying to assign roles", async () => {
      const role = TeamRole.create({ role: "Electrical Engineer" })

      await expect(
        service.validateRolePermissions(sampleProject, 9999, role, 3001)
      ).rejects.toThrow(
        "User 9999 is not a member of the project and cannot assign roles"
      )
    })

    test("should throw error for insufficient role assignment permissions", async () => {
      const projectWithViewer = sampleProject.addTeamMember(
        3001,
        TeamRole.createViewer(),
        2001
      )
      const adminRole = TeamRole.createAdministrator()

      await expect(
        service.validateRolePermissions(
          projectWithViewer,
          3001,
          adminRole,
          3002
        )
      ).rejects.toThrow(
        "Administrator role assignment requires project owner approval"
      )
    })

    test("should enforce administrator role requires owner approval", async () => {
      const projectWithPM = sampleProject.addTeamMember(
        3001,
        TeamRole.createProjectManager(),
        2001
      )
      const adminRole = TeamRole.createAdministrator()

      await expect(
        service.validateRolePermissions(projectWithPM, 3001, adminRole, 3002)
      ).rejects.toThrow(
        "Administrator role assignment requires project owner approval"
      )
    })

    test("should prevent assigning higher authority roles", async () => {
      const projectWithDesigner = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Electrical Engineer" }),
        2001
      )
      const pmRole = TeamRole.createProjectManager()

      await expect(
        service.validateRolePermissions(projectWithDesigner, 3001, pmRole, 3002)
      ).rejects.toThrow(
        "Cannot assign role 'Project Manager' with higher authority than current role 'Electrical Engineer'"
      )
    })

    test("should enforce Client assignment restrictions", async () => {
      const projectWithDesigner = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Electrical Engineer" }),
        2001
      )
      const clientRole = TeamRole.create({ role: "Client" })

      await expect(
        service.validateRolePermissions(
          projectWithDesigner,
          3001,
          clientRole,
          3002
        )
      ).rejects.toThrow("Client role can only be assigned by administrators")
    })

    test("should enforce Supplier limits", async () => {
      // Add maximum contractors for high priority project (6)
      let project = sampleProject
      for (let i = 0; i < 6; i++) {
        project = project.addTeamMember(
          4000 + i,
          TeamRole.create({ role: "Supplier" }),
          2001
        )
      }

      const supplierRole = TeamRole.create({ role: "Supplier" })

      await expect(
        service.validateRolePermissions(project, 2001, supplierRole, 5001)
      ).rejects.toThrow(
        "Project has reached maximum Supplier limit (6) for High priority"
      )
    })

    test("should enforce Client uniqueness", async () => {
      const projectWithClient = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Client" }),
        2001
      )
      const newClientRole = TeamRole.create({
        role: "Client",
      })

      await expect(
        service.validateRolePermissions(
          projectWithClient,
          2001,
          newClientRole,
          3002
        )
      ).rejects.toThrow("Only one Client allowed per project")
    })

    test("should enforce project manager limits", async () => {
      let projectWithPMs = sampleProject.addTeamMember(
        3001,
        TeamRole.createProjectManager(),
        2001
      )
      projectWithPMs = projectWithPMs.addTeamMember(
        3002,
        TeamRole.createProjectManager(),
        2001
      )
      const newPMRole = TeamRole.createProjectManager()

      await expect(
        service.validateRolePermissions(projectWithPMs, 2001, newPMRole, 3003)
      ).rejects.toThrow(
        "Project has reached maximum project manager limit (2) for High priority"
      )
    })
  })

  describe("getAvailableRoles", () => {
    test("should return all available roles for administrator", async () => {
      const roles = await service.getAvailableRoles(sampleProject, 2001)

      expect(roles).toHaveLength(11) // All role types
      expect(
        roles.every((role) => role.isAvailable || role.restrictions.length > 0)
      ).toBe(true)

      // Should be sorted by availability and hierarchy
      const availableRoles = roles.filter((r) => r.isAvailable)
      expect(availableRoles[0].role).toBe("Administrator") // Highest hierarchy
    })

    test("should include restrictions for roles requiring higher permissions", async () => {
      const projectWithDesigner = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Electrical Engineer" }),
        2001
      )

      const roles = await service.getAvailableRoles(projectWithDesigner, 3001)

      const adminRole = roles.find((r) => r.role === "Administrator")
      expect(adminRole!.restrictions).toContain(
        "Requires project owner approval"
      )
      expect(adminRole!.requiresApproval).toBe(true)
    })

    test("should show current count and max count for each role", async () => {
      const roles = await service.getAvailableRoles(sampleProject, 2001)

      const adminRole = roles.find((r) => r.role === "Administrator")
      expect(adminRole!.currentCount).toBe(1) // Project owner
      expect(adminRole!.maxCount).toBe(4) // High priority limit

      const viewerRole = roles.find((r) => r.role === "Viewer")
      expect(viewerRole!.currentCount).toBe(0)
      expect(viewerRole!.maxCount).toBe(15) // High priority limit
    })

    test("should include skill requirements for each role", async () => {
      const roles = await service.getAvailableRoles(sampleProject, 2001)

      const designerRole = roles.find((r) => r.role === "Electrical Engineer")
      expect(designerRole!.skillRequirements).toContain("cad-software")
      expect(designerRole!.skillRequirements).toContain("electrical-design")

      const pmRole = roles.find((r) => r.role === "Project Manager")
      expect(pmRole!.skillRequirements).toContain("project-management")
      expect(pmRole!.skillRequirements).toContain("communication")
    })

    test("should mark roles as unavailable when at capacity", async () => {
      // Add maximum project managers (2 for High priority)
      let projectWithPMs = sampleProject.addTeamMember(
        3001,
        TeamRole.createProjectManager(),
        2001
      )
      projectWithPMs = projectWithPMs.addTeamMember(
        3002,
        TeamRole.createProjectManager(),
        2001
      )

      const roles = await service.getAvailableRoles(projectWithPMs, 2001)

      const pmRole = roles.find((r) => r.role === "Project Manager")
      expect(pmRole!.isAvailable).toBe(false)
      expect(pmRole!.currentCount).toBe(2)
      expect(pmRole!.maxCount).toBe(2) // High priority allows 2 PMs
    })
  })

  describe("checkMemberExpiration", () => {
    test("should identify expired members", () => {
      const expiredMember = ProjectMember.create({
        id: "expired_member",
        userId: 3001,
        projectId: "project_001",
        role: TeamRole.create({ role: "Electrical Engineer" }),
        assignedAt: "2024-01-01T00:00:00Z",
        expiresAt: "2024-06-30T23:59:59Z", // Past date
      })

      const members = [expiredMember]
      const summary = service.checkMemberExpiration(members)

      expect(summary.expiredMembers).toHaveLength(1)
      expect(summary.expiredMembers[0].userId).toBe(3001)
      expect(summary.actionRequired).toBe(true)
    })

    test("should identify members approaching expiration", () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 3) // 3 days from now

      const approachingMember = ProjectMember.create({
        id: "approaching_member",
        userId: 3001,
        projectId: "project_001",
        role: TeamRole.create({ role: "Electrical Engineer" }),
        assignedAt: "2024-01-01T00:00:00Z",
        expiresAt: futureDate.toISOString(),
      })

      const members = [approachingMember]
      const summary = service.checkMemberExpiration(members, 7)

      expect(summary.expiringMembers).toHaveLength(1)
      expect(summary.membersRequiringExtension).toHaveLength(1)
      expect(summary.actionRequired).toBe(true)
    })

    test("should calculate average days until expiration", () => {
      const member1Date = new Date()
      member1Date.setDate(member1Date.getDate() + 10)

      const member2Date = new Date()
      member2Date.setDate(member2Date.getDate() + 20)

      const member1 = ProjectMember.create({
        id: "member_1",
        userId: 3001,
        projectId: "project_001",
        role: TeamRole.create({ role: "Electrical Engineer" }),
        assignedAt: "2024-01-01T00:00:00Z",
        expiresAt: member1Date.toISOString(),
      })

      const member2 = ProjectMember.create({
        id: "member_2",
        userId: 3002,
        projectId: "project_001",
        role: TeamRole.create({ role: "Automation Engineer" }),
        assignedAt: "2024-01-01T00:00:00Z",
        expiresAt: member2Date.toISOString(),
      })

      const members = [member1, member2]
      const summary = service.checkMemberExpiration(members)

      expect(summary.averageDaysUntilExpiration).toBe(15) // Average of 10 and 20
    })

    test("should handle members without expiration dates", () => {
      const permanentMember = ProjectMember.create({
        id: "permanent_member",
        userId: 3001,
        projectId: "project_001",
        role: TeamRole.createAdministrator(),
        assignedAt: "2024-01-01T00:00:00Z",
        // No expiresAt
      })

      const members = [permanentMember]
      const summary = service.checkMemberExpiration(members)

      expect(summary.expiredMembers).toHaveLength(0)
      expect(summary.expiringMembers).toHaveLength(0)
      expect(summary.averageDaysUntilExpiration).toBe(0)
      expect(summary.actionRequired).toBe(false)
    })
  })

  describe("extendMemberExpiration", () => {
    test("should successfully extend member expiration", async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 30)

      // Add member to project using addTeamMember instead of restore
      const projectWithMember = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Electrical Engineer" }),
        2001
      )

      // Manually set expiration for the member (simulating existing member with expiration)
      const member = projectWithMember.getMemberByUserId(3001)!
      member["_expiresAt"] = futureDate.toISOString()

      const newExpirationDate = new Date()
      newExpirationDate.setDate(newExpirationDate.getDate() + 120)

      const result = await service.extendMemberExpiration(
        projectWithMember,
        3001,
        newExpirationDate.toISOString(),
        2001,
        "Project extension approved"
      )

      expect(result.success).toBe(true)
      expect(result.affectedMember!.expiresAt).toBe(
        newExpirationDate.toISOString()
      )
      expect(result.nextActions).toContain(
        "Notify team member of expiration extension"
      )
    })

    test("should throw error for non-existent member", async () => {
      const newExpirationDate = new Date()
      newExpirationDate.setDate(newExpirationDate.getDate() + 90)

      await expect(
        service.extendMemberExpiration(
          sampleProject,
          9999,
          newExpirationDate.toISOString(),
          2001
        )
      ).rejects.toThrow("User 9999 is not a member of project project_001")
    })

    test("should throw error for insufficient permissions", async () => {
      // Add both members using addTeamMember to ensure proper integration
      const futureExpiration = new Date()
      futureExpiration.setDate(futureExpiration.getDate() + 30)
      
      let projectWithMembers = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Electrical Engineer" }),
        2001,
        futureExpiration.toISOString()
      )
      
      projectWithMembers = projectWithMembers.addTeamMember(
        3002,
        TeamRole.createViewer(),
        2001,
        undefined // Viewer without expiration
      )

      const newExpirationDate = new Date()
      newExpirationDate.setDate(newExpirationDate.getDate() + 90)

      await expect(
        service.extendMemberExpiration(
          projectWithMembers,
          3001,
          newExpirationDate.toISOString(),
          3002 // Viewer trying to extend
        )
      ).rejects.toThrow(
        "User 3002 does not have permission to extend member expiration"
      )
    })

    test("should include warning for long extensions", async () => {
      const member = ProjectMember.create({
        id: "member_to_extend",
        userId: 3001,
        projectId: sampleProject.id,
        role: TeamRole.create({ role: "Electrical Engineer" }),
        assignedAt: "2024-01-01T00:00:00Z",
        expiresAt: new Date().toISOString(),
      })

      // Use addTeamMember instead of restore to ensure proper member integration
      const futureExpiration = new Date()
      futureExpiration.setDate(futureExpiration.getDate() + 30) // 30 days from now
      const projectWithMember = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Electrical Engineer" }),
        2001,
        futureExpiration.toISOString()
      )

      const newExpirationDate = new Date()
      newExpirationDate.setDate(newExpirationDate.getDate() + 240) // 8 months

      const result = await service.extendMemberExpiration(
        projectWithMember,
        3001,
        newExpirationDate.toISOString(),
        2001
      )

      expect(result.warnings).toContain(
        "Extension exceeds 6 months and may require additional approval"
      )
      expect(result.recommendations).toContain(
        "Consider breaking extension into shorter periods"
      )
    })

    test("should generate mid-term review action for long extensions", async () => {
      // Use addTeamMember instead of restore to ensure proper member integration
      const futureExpiration = new Date()
      futureExpiration.setDate(futureExpiration.getDate() + 30) // 30 days from now
      const projectWithMember = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Electrical Engineer" }),
        2001,
        futureExpiration.toISOString()
      )

      const newExpirationDate = new Date()
      newExpirationDate.setDate(newExpirationDate.getDate() + 120) // 4 months

      const result = await service.extendMemberExpiration(
        projectWithMember,
        3001,
        newExpirationDate.toISOString(),
        2001
      )

      expect(result.nextActions).toContain(
        "Schedule mid-term review of member performance"
      )
    })

    test("should throw error for past expiration date", async () => {
      // Use addTeamMember instead of restore to ensure proper member integration
      const futureExpiration = new Date()
      futureExpiration.setDate(futureExpiration.getDate() + 30) // 30 days from now
      const projectWithMember = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Electrical Engineer" }),
        2001,
        futureExpiration.toISOString()
      )

      const pastDate = new Date()
      pastDate.setDate(pastDate.getDate() - 10)

      await expect(
        service.extendMemberExpiration(
          projectWithMember,
          3001,
          pastDate.toISOString(),
          2001
        )
      ).rejects.toThrow("New expiration date must be in the future")
    })
  })

  describe("processBulkMemberExpiration", () => {
    test("should process members requiring extension", async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 5) // 5 days from now

      // Use addTeamMember to ensure proper member integration
      const projectWithMember = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Electrical Engineer" }),
        2001,
        futureDate.toISOString()
      )

      const summary =
        await service.processBulkMemberExpiration(projectWithMember)

      expect(summary.actionRequired).toBe(true)
      expect(summary.membersRequiringExtension).toHaveLength(1)
    })

    test("should not auto-extend administrator members", async () => {
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 5)

      // Use addTeamMember to ensure proper member integration
      const projectWithAdmin = sampleProject.addTeamMember(
        3001,
        TeamRole.createAdministrator(),
        2001,
        futureDate.toISOString()
      )

      const summary =
        await service.processBulkMemberExpiration(projectWithAdmin)

      expect(summary.membersRequiringExtension).toHaveLength(1)
      // Admin should be identified as requiring extension but not auto-extended
    })

    test("should handle errors in auto-extension gracefully", async () => {
      // Create a scenario where extension would fail
      mockRepository.save.mockRejectedValueOnce(new Error("Database error"))

      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 5)

      // Use addTeamMember to ensure proper member integration
      const projectWithMember = sampleProject.addTeamMember(
        3001,
        TeamRole.create({ role: "Electrical Engineer" }),
        2001,
        futureDate.toISOString()
      )

      // Should not throw error, but log it
      const consoleSpy = vi.spyOn(console, "error").mockImplementation(() => {})

      const summary =
        await service.processBulkMemberExpiration(projectWithMember)

      expect(summary.actionRequired).toBe(true)
      expect(consoleSpy).toHaveBeenCalledWith(
        "Failed to auto-extend member 3001:",
        expect.any(Error)
      )

      consoleSpy.mockRestore()
    })
  })

  describe("Integration Scenarios", () => {
    test("should handle complete member lifecycle", async () => {
      const role = TeamRole.create({ role: "Electrical Engineer" })

      // 1. Assign member
      const assignResult = await service.assignMemberToProject(
        sampleProject,
        3001,
        role,
        sampleContext
      )

      expect(assignResult.success).toBe(true)

      // 2. Check available roles after assignment
      const roles = await service.getAvailableRoles(assignResult.project, 2001)
      const designerRole = roles.find((r) => r.role === "Electrical Engineer")
      expect(designerRole!.currentCount).toBe(1)

      // 3. Extend member expiration
      const futureDate = new Date()
      futureDate.setDate(futureDate.getDate() + 90)

      const extensionResult = await service.extendMemberExpiration(
        assignResult.project,
        3001,
        futureDate.toISOString(),
        2001,
        "Contract extension"
      )

      expect(extensionResult.success).toBe(true)
      expect(extensionResult.affectedMember!.expiresAt).toBe(
        futureDate.toISOString()
      )
    })

    test("should validate complex permission scenarios", async () => {
      // Create project with multiple members of different roles
      const project = sampleProject
        .addTeamMember(3001, TeamRole.createProjectManager(), 2001)
        .addTeamMember(3002, TeamRole.create({ role: "Lead Engineer" }), 2001)
        .addTeamMember(
          3003,
          TeamRole.create({ role: "Electrical Engineer" }),
          3001
        )

      // Project Manager should be able to assign basic roles
      await expect(
        service.validateRolePermissions(
          project,
          3001,
          TeamRole.create({ role: "Automation Engineer" }),
          3004
        )
      ).resolves.toBeUndefined()

      // But not administrator roles
      await expect(
        service.validateRolePermissions(
          project,
          3001,
          TeamRole.createAdministrator(),
          3004
        )
      ).rejects.toThrow(
        "Administrator role assignment requires project owner approval"
      )

      // Lead Engineer should be able to assign designer roles
      await expect(
        service.validateRolePermissions(
          project,
          3002,
          TeamRole.create({ role: "Electrical Engineer" }),
          3004
        )
      ).resolves.toBeUndefined()
    })
  })
})
