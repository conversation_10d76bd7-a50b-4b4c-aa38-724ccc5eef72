/**
 * ProjectValidationService Unit Tests
 *
 * Comprehensive test coverage for the ProjectValidationService domain service.
 * Tests cover all validation scenarios including valid and invalid inputs, edge cases,
 * and error message accuracy.
 */

import {
  beforeEach,
  describe,
  expect,
  test,
  vi,
  type MockedFunction,
} from "vitest"
import { CreateProjectData, Project } from "../../entities/Project"
import { ProjectMember } from "../../entities/ProjectMember"
import {
  IProjectRepository,
  ProjectQueryResult,
} from "../../repositories/IProjectRepository"
import { ProjectBudget } from "../../value-objects/ProjectBudget"
import { ProjectStatus } from "../../value-objects/ProjectStatus"
import { TeamRole } from "../../value-objects/TeamRole"
import {
  BudgetAllocationError,
  ProjectCreationValidationError,
  ProjectStatusTransitionError,
  ProjectValidationConfig,
  ProjectValidationError,
  ProjectValidationService,
  TeamMemberAssignmentError,
} from "../ProjectValidationService"

describe("ProjectValidationService", () => {
  let service: ProjectValidationService
  let mockRepository: {
    save: MockedFunction<IProjectRepository["save"]>
    findById: MockedFunction<IProjectRepository["findById"]>
    findByIds: MockedFunction<IProjectRepository["findByIds"]>
    findByOwnerId: MockedFunction<IProjectRepository["findByOwnerId"]>
    findByClientId: MockedFunction<IProjectRepository["findByClientId"]>
    findByMemberUserId: MockedFunction<IProjectRepository["findByMemberUserId"]>
    findByCriteria: MockedFunction<IProjectRepository["findByCriteria"]>
    findAll: MockedFunction<IProjectRepository["findAll"]>
    findProjectsRequiringAttention: MockedFunction<
      IProjectRepository["findProjectsRequiringAttention"]
    >
    findProjectsWithExpiredMembers: MockedFunction<
      IProjectRepository["findProjectsWithExpiredMembers"]
    >
    findOverBudgetProjects: MockedFunction<
      IProjectRepository["findOverBudgetProjects"]
    >
    exists: MockedFunction<IProjectRepository["exists"]>
    count: MockedFunction<IProjectRepository["count"]>
    remove: MockedFunction<IProjectRepository["remove"]>
    removeMany: MockedFunction<IProjectRepository["removeMany"]>
    archiveCompletedProjects: MockedFunction<
      IProjectRepository["archiveCompletedProjects"]
    >
    validateAggregateConsistency: MockedFunction<
      IProjectRepository["validateAggregateConsistency"]
    >
  }
  let testConfig: ProjectValidationConfig
  let sampleCreateData: CreateProjectData
  let sampleProject: Project

  beforeEach(() => {
    // Create mock repository
    mockRepository = {
      save: vi.fn(),
      findById: vi.fn(),
      findByIds: vi.fn(),
      findByOwnerId: vi.fn(),
      findByClientId: vi.fn(),
      findByMemberUserId: vi.fn(),
      findByCriteria: vi.fn(),
      findAll: vi.fn(),
      findProjectsRequiringAttention: vi.fn(),
      findProjectsWithExpiredMembers: vi.fn(),
      findOverBudgetProjects: vi.fn(),
      exists: vi.fn(),
      count: vi.fn(),
      remove: vi.fn(),
      removeMany: vi.fn(),
      archiveCompletedProjects: vi.fn(),
      validateAggregateConsistency: vi.fn(),
    }

    // Test configuration
    testConfig = {
      enforceUniqueProjectNames: true,
      allowDuplicateNamesWithinClient: false,
      maxProjectsPerOwner: 5,
      maxProjectsPerClient: 10,
      maxBudgetAmountPerPriority: {
        Low: 50000,
        Medium: 100000,
        High: 200000,
        Critical: 500000,
      },
      allowedCurrencies: ["EUR", "USD", "GBP"],
      minBudgetAmount: 1000,
      maxTeamSizeOverride: {},
      allowExpiredMemberAssignment: false,
      requireOwnerApprovalForAdminRoles: true,
      requireCompletionTasksForCompletion: true,
      allowDirectCancellation: false,
      enforceSequentialStatusProgression: false,
    }

    service = new ProjectValidationService(mockRepository, testConfig)

    // Sample test data
    sampleCreateData = {
      id: "project_001",
      name: "Test Project",
      description: "A test project for validation",
      ownerId: 2001,
      clientId: 1001,
      budgetData: {
        totalAmount: 50000,
        currency: "EUR",
      },
      startDate: "2024-02-01T00:00:00Z",
      endDate: "2024-12-31T23:59:59Z",
      tags: ["electrical", "test"],
      priority: "Medium",
      location: "Amsterdam",
    }

    sampleProject = Project.create(sampleCreateData)
  })

  describe("Error Classes", () => {
    test("should create ProjectValidationError with correct properties", () => {
      const error = new ProjectValidationError("Test error", "TEST_TYPE", {
        key: "value",
      })

      expect(error.name).toBe("ProjectValidationError")
      expect(error.message).toBe("Test error")
      expect(error.validationType).toBe("TEST_TYPE")
      expect(error.details).toEqual({ key: "value" })
      expect(error instanceof Error).toBe(true)
    })

    test("should create specific error types", () => {
      const creationError = new ProjectCreationValidationError(
        "Creation failed"
      )
      const statusError = new ProjectStatusTransitionError("Transition failed")
      const teamError = new TeamMemberAssignmentError("Assignment failed")
      const budgetError = new BudgetAllocationError("Budget failed")

      expect(creationError.validationType).toBe("PROJECT_CREATION")
      expect(statusError.validationType).toBe("STATUS_TRANSITION")
      expect(teamError.validationType).toBe("TEAM_MEMBER_ASSIGNMENT")
      expect(budgetError.validationType).toBe("BUDGET_ALLOCATION")
    })
  })

  describe("validateProjectCreation", () => {
    beforeEach(() => {
      mockRepository.count.mockResolvedValue(0) // No existing projects by default
    })

    test("should validate valid project creation data", async () => {
      await expect(
        service.validateProjectCreation(sampleCreateData)
      ).resolves.toBeUndefined()

      expect(mockRepository.count).toHaveBeenCalledWith({
        name: "Test Project",
      })
      expect(mockRepository.count).toHaveBeenCalledWith({ ownerId: 2001 })
      expect(mockRepository.count).toHaveBeenCalledWith({ clientId: 1001 })
    })

    test("should throw error for empty project ID", async () => {
      const invalidData = { ...sampleCreateData, id: "" }

      await expect(
        service.validateProjectCreation(invalidData)
      ).rejects.toThrow(ProjectCreationValidationError)

      await expect(
        service.validateProjectCreation(invalidData)
      ).rejects.toThrow("Project ID cannot be empty")
    })

    test("should throw error for empty project name", async () => {
      const invalidData = { ...sampleCreateData, name: "" }

      await expect(
        service.validateProjectCreation(invalidData)
      ).rejects.toThrow("Project name cannot be empty")
    })

    test("should throw error for invalid owner ID", async () => {
      const invalidData = { ...sampleCreateData, ownerId: 0 }

      await expect(
        service.validateProjectCreation(invalidData)
      ).rejects.toThrow("Valid owner ID is required")
    })

    test("should throw error for invalid project ID format", async () => {
      const invalidData = { ...sampleCreateData, id: "project@001" }

      await expect(
        service.validateProjectCreation(invalidData)
      ).rejects.toThrow(
        "Project ID must contain only alphanumeric characters, hyphens, and underscores"
      )
    })

    test("should throw error for name too short or too long", async () => {
      const shortName = { ...sampleCreateData, name: "AB" }
      const longName = { ...sampleCreateData, name: "A".repeat(201) }

      await expect(service.validateProjectCreation(shortName)).rejects.toThrow(
        "Project name must be between 3 and 200 characters"
      )

      await expect(service.validateProjectCreation(longName)).rejects.toThrow(
        "Project name must be between 3 and 200 characters"
      )
    })

    test("should throw error for duplicate project name when enforced", async () => {
      mockRepository.count.mockResolvedValueOnce(1) // First call for name uniqueness

      await expect(
        service.validateProjectCreation(sampleCreateData)
      ).rejects.toThrow(
        "Project name 'Test Project' already exists system-wide"
      )
    })

    test("should throw error when owner reaches project limit", async () => {
      mockRepository.count
        .mockResolvedValueOnce(0) // Name uniqueness check
        .mockResolvedValueOnce(5) // Owner project count at limit

      await expect(
        service.validateProjectCreation(sampleCreateData)
      ).rejects.toThrow(
        "Owner 2001 has reached the maximum limit of 5 projects"
      )
    })

    test("should throw error when client reaches project limit", async () => {
      mockRepository.count
        .mockResolvedValueOnce(0) // Name uniqueness check
        .mockResolvedValueOnce(0) // Owner project count
        .mockResolvedValueOnce(10) // Client project count at limit

      await expect(
        service.validateProjectCreation(sampleCreateData)
      ).rejects.toThrow(
        "Client 1001 has reached the maximum limit of 10 projects"
      )
    })

    test("should throw error for budget below minimum", async () => {
      const invalidBudget = {
        ...sampleCreateData,
        budgetData: { totalAmount: 500, currency: "EUR" },
      }

      await expect(
        service.validateProjectCreation(invalidBudget)
      ).rejects.toThrow("Budget amount 500 is below minimum of 1000")
    })

    test("should throw error for budget exceeding priority limit", async () => {
      const invalidBudget = {
        ...sampleCreateData,
        budgetData: { totalAmount: 150000, currency: "EUR" },
        priority: "Medium" as const,
      }

      await expect(
        service.validateProjectCreation(invalidBudget)
      ).rejects.toThrow(
        "Budget amount 150000 exceeds maximum of 100000 for Medium priority projects"
      )
    })

    test("should throw error for unsupported currency", async () => {
      const invalidCurrency = {
        ...sampleCreateData,
        budgetData: { totalAmount: 50000, currency: "JPY" },
      }

      await expect(
        service.validateProjectCreation(invalidCurrency)
      ).rejects.toThrow(
        "Currency 'JPY' is not allowed. Supported: EUR, USD, GBP"
      )
    })

    test("should throw error for invalid date ranges", async () => {
      const invalidDates = {
        ...sampleCreateData,
        startDate: "2024-12-31T00:00:00Z",
        endDate: "2024-01-01T00:00:00Z",
      }

      await expect(
        service.validateProjectCreation(invalidDates)
      ).rejects.toThrow("Start date must be before end date")
    })

    test("should throw error for too many tags", async () => {
      const tooManyTags = {
        ...sampleCreateData,
        tags: Array.from({ length: 21 }, (_, i) => `tag${i}`),
      }

      await expect(
        service.validateProjectCreation(tooManyTags)
      ).rejects.toThrow("Project cannot have more than 20 tags")
    })

    test("should throw error for invalid tags", async () => {
      const invalidTags = {
        ...sampleCreateData,
        tags: ["", "valid-tag", "A".repeat(51)],
      }

      await expect(
        service.validateProjectCreation(invalidTags)
      ).rejects.toThrow(
        "Tags must be non-empty and no longer than 50 characters"
      )
    })

    test("should throw error for location too long", async () => {
      const longLocation = {
        ...sampleCreateData,
        location: "A".repeat(201),
      }

      await expect(
        service.validateProjectCreation(longLocation)
      ).rejects.toThrow("Location must not exceed 200 characters")
    })
  })

  describe("validateStatusTransition", () => {
    test("should validate valid status transition", async () => {
      const fromStatus = ProjectStatus.createDraft()

      await expect(
        service.validateStatusTransition(sampleProject, fromStatus, "Active", {
          userId: 2001,
        })
      ).resolves.toBeUndefined()
    })

    test("should throw error for invalid status transition", async () => {
      const fromStatus = ProjectStatus.create({ status: "Completed" })

      await expect(
        service.validateStatusTransition(sampleProject, fromStatus, "Active", {
          userId: 2001,
        })
      ).rejects.toThrow(ProjectStatusTransitionError)
    })

    test("should throw error for insufficient permissions", async () => {
      const project = sampleProject.addTeamMember(
        3001,
        TeamRole.createViewer(),
        2001
      )
      const fromStatus = ProjectStatus.createDraft()

      await expect(
        service.validateStatusTransition(project, fromStatus, "Active", {
          userId: 3001,
        })
      ).rejects.toThrow(
        "User 3001 does not have permission to transition project to 'Active'"
      )
    })

    test("should validate activation requirements", async () => {
      const projectWithoutBudget = Project.create({
        ...sampleCreateData,
        budgetData: undefined,
      })
      const fromStatus = ProjectStatus.createDraft()

      await expect(
        service.validateStatusTransition(
          projectWithoutBudget,
          fromStatus,
          "Active",
          { userId: 2001 }
        )
      ).rejects.toThrow("Project must have a valid budget to be activated")
    })

    test("should validate completion requirements", async () => {
      const overBudgetProject = Project.create({
        ...sampleCreateData,
        budgetData: { totalAmount: 50000, spentAmount: 60000, currency: "EUR" },
      }).activateProject(2001)

      await expect(
        service.validateStatusTransition(
          overBudgetProject,
          overBudgetProject.status,
          "Completed",
          { userId: 2001 }
        )
      ).rejects.toThrow(
        "Cannot complete project that is over budget without approval"
      )
    })

    test("should validate archival requirements", async () => {
      const fromStatus = ProjectStatus.createDraft()

      await expect(
        service.validateStatusTransition(
          sampleProject,
          fromStatus,
          "Archived",
          { userId: 2001 }
        )
      ).rejects.toThrow("Cannot transition from 'Draft' to 'Archived'")
    })

    test("should validate cancellation requirements", async () => {
      const highSpendProject = Project.create({
        ...sampleCreateData,
        budgetData: {
          totalAmount: 100000,
          spentAmount: 60000,
          currency: "EUR",
        },
      }).activateProject(2001)

      await expect(
        service.validateStatusTransition(
          highSpendProject,
          highSpendProject.status,
          "Cancelled",
          { userId: 2001 }
        )
      ).rejects.toThrow(
        "Cannot cancel project with more than 50% budget spent without force flag"
      )
    })

    test("should allow forced cancellation", async () => {
      const highSpendProject = Project.create({
        ...sampleCreateData,
        budgetData: {
          totalAmount: 100000,
          spentAmount: 60000,
          currency: "EUR",
        },
      }).activateProject(2001)

      await expect(
        service.validateStatusTransition(
          highSpendProject,
          highSpendProject.status,
          "Cancelled",
          { userId: 2001, forceTransition: true }
        )
      ).resolves.toBeUndefined()
    })

    test("should validate on hold requirements", async () => {
      const completedProject = sampleProject
        .activateProject(2001)
        .completeProject(2001)

      await expect(
        service.validateStatusTransition(
          completedProject,
          completedProject.status,
          "On Hold",
          { userId: 2001 }
        )
      ).rejects.toThrow("Cannot transition from 'Completed' to 'On Hold'")
    })
  })

  describe("validateTeamMemberAssignment", () => {
    let testMember: ProjectMember

    beforeEach(() => {
      testMember = ProjectMember.createNew(
        "member_001",
        3001,
        "project_001",
        TeamRole.create({ role: "Electrical Engineer" })
      )

      // Mock member availability check
      const emptyResult: ProjectQueryResult = {
        projects: [],
        totalCount: 0,
        offset: 0,
        limit: 100,
        hasMore: false,
      }
      mockRepository.findByMemberUserId.mockResolvedValue(emptyResult)

      // Check if we're correctly handling the result in validateMemberAvailability
      console.log("Mock setup:", {
        findByMemberUserId: mockRepository.findByMemberUserId,
        mockResolvedValue: emptyResult,
      })
    })

    test("should validate valid member assignment", async () => {
      await expect(
        service.validateTeamMemberAssignment(sampleProject, testMember, {
          assignedBy: 2001,
        })
      ).resolves.toBeUndefined()
    })

    test("should throw error for project that cannot add members", async () => {
      const ownerMember = ProjectMember.create({
        id: "owner_member",
        userId: 2001, // Same as ownerId in sampleCreateData
        projectId: "project_001",
        role: TeamRole.createAdministrator(),
        assignedAt: "2024-01-01T00:00:00Z",
        isActive: true,
      })

      const archivedProject = Project.restore({
        ...sampleCreateData,
        status: ProjectStatus.create({ status: "Archived" }),
        members: [ownerMember],
        createdAt: "2024-01-01T00:00:00Z",
        updatedAt: "2024-01-01T00:00:00Z",
      } as any)

      await expect(
        service.validateTeamMemberAssignment(archivedProject, testMember)
      ).rejects.toThrow("Cannot add members to project in 'Archived' state")
    })

    test("should throw error for duplicate member assignment", async () => {
      const existingMember = ProjectMember.createNew(
        "existing",
        2001, // Same user as project owner
        "project_001",
        TeamRole.createAdministrator()
      )

      await expect(
        service.validateTeamMemberAssignment(sampleProject, existingMember)
      ).rejects.toThrow("User 2001 is already a member of project project_001")
    })

    test("should throw error for expired member assignment", async () => {
      const expiredMember = ProjectMember.create({
        id: "expired_member",
        userId: 3001,
        projectId: "project_001",
        role: TeamRole.create({ role: "Electrical Engineer" }),
        assignedAt: "2024-01-01T00:00:00Z",
        expiresAt: "2024-06-30T23:59:59Z", // Past date
      })

      await expect(
        service.validateTeamMemberAssignment(sampleProject, expiredMember)
      ).rejects.toThrow("Cannot assign member with expired assignment")
    })

    test("should throw error for insufficient role assignment permissions", async () => {
      const adminRole = TeamRole.createAdministrator()
      const designerMember = ProjectMember.createNew(
        "designer",
        3001,
        "project_001",
        TeamRole.create({ role: "Electrical Engineer" })
      )
      const projectWithDesigner = sampleProject.addTeamMember(
        3001,
        designerMember.role,
        2001
      )

      const newAdminMember = ProjectMember.createNew(
        "new_admin",
        3002,
        "project_001",
        adminRole
      )

      await expect(
        service.validateTeamMemberAssignment(
          projectWithDesigner,
          newAdminMember,
          { assignedBy: 3001 } // Designer trying to assign admin role
        )
      ).rejects.toThrow(
        "User 3001 does not have permission to assign role 'Administrator'"
      )
    })

    test("should throw error for overcommitted user", async () => {
      // Mock user with many active projects
      const activeProjects = Array.from({ length: 10 }, (_, i) =>
        Project.create({
          id: `active_project_${i}`,
          name: `Active Project ${i}`,
          ownerId: 4001,
        }).activateProject(4001)
      )

      const overcommittedResult: ProjectQueryResult = {
        projects: activeProjects,
        totalCount: 10,
        offset: 0,
        limit: 100,
        hasMore: false,
      }

      mockRepository.findByMemberUserId.mockResolvedValue(overcommittedResult)

      await expect(
        service.validateTeamMemberAssignment(sampleProject, testMember)
      ).rejects.toThrow(
        "User 3001 is already an active member of 10 projects (maximum: 10)"
      )
    })

    test("should throw error for schedule conflicts", async () => {
      const conflictingProject = Project.create({
        id: "conflicting_project",
        name: "Conflicting Project",
        ownerId: 4001,
        startDate: "2024-01-01T00:00:00Z",
        endDate: "2024-06-30T23:59:59Z",
      })

      const conflictingProjects = [
        conflictingProject,
        conflictingProject, // Duplicate to simulate multiple conflicts
        conflictingProject,
      ]

      const conflictResult: ProjectQueryResult = {
        projects: conflictingProjects,
        totalCount: 3,
        offset: 0,
        limit: 100,
        hasMore: false,
      }

      // Ensure the mock is properly set before the test runs
      mockRepository.findByMemberUserId = vi
        .fn()
        .mockResolvedValue(conflictResult)

      await expect(
        service.validateTeamMemberAssignment(sampleProject, testMember)
      ).rejects.toThrow("User 3001 has 3 projects with conflicting schedules")
    })
  })

  describe("validateBudgetAllocation", () => {
    test("should validate valid budget allocation", async () => {
      const validBudget = ProjectBudget.createWithAmount(75000, "EUR")

      await expect(
        service.validateBudgetAllocation(validBudget, sampleProject)
      ).resolves.toBeUndefined()
    })

    test("should throw error for budget below minimum", async () => {
      const lowBudget = ProjectBudget.createWithAmount(500, "EUR")

      await expect(service.validateBudgetAllocation(lowBudget)).rejects.toThrow(
        "Budget amount 500 is below minimum of 1000"
      )
    })

    test("should throw error for unsupported currency", async () => {
      const invalidCurrency = ProjectBudget.createWithAmount(50000, "JPY")

      await expect(
        service.validateBudgetAllocation(invalidCurrency)
      ).rejects.toThrow(
        "Currency 'JPY' is not allowed. Supported: EUR, USD, GBP"
      )
    })

    test("should throw error for budget exceeding priority limit", async () => {
      const highBudget = ProjectBudget.createWithAmount(150000, "EUR")

      await expect(
        service.validateBudgetAllocation(highBudget, sampleProject)
      ).rejects.toThrow(
        "Budget amount 150000 exceeds maximum of 100000 for Medium priority projects"
      )
    })

    test("should throw error for excessive budget reduction in active project", async () => {
      const activeProject = sampleProject.activateProject(2001)
      const reducedBudget = ProjectBudget.createWithAmount(20000, "EUR") // 60% reduction

      await expect(
        service.validateBudgetAllocation(reducedBudget, activeProject)
      ).rejects.toThrow(
        "Cannot reduce budget by more than 50% for active projects"
      )
    })

    test("should throw error when new budget is less than spent amount", async () => {
      const spentBudget = ProjectBudget.create({
        totalAmount: 30000,
        spentAmount: 40000,
        currency: "EUR",
      })

      await expect(
        service.validateBudgetAllocation(spentBudget, sampleProject)
      ).rejects.toThrow(
        "New budget total 30000 cannot be less than already spent amount 40000"
      )
    })
  })

  describe("Configuration Handling", () => {
    test("should use default configuration when none provided", () => {
      const defaultService = new ProjectValidationService(mockRepository)
      expect(defaultService).toBeInstanceOf(ProjectValidationService)
    })

    test("should respect configuration overrides", async () => {
      const customConfig: ProjectValidationConfig = {
        ...testConfig,
        enforceUniqueProjectNames: false,
      }

      const customService = new ProjectValidationService(
        mockRepository,
        customConfig
      )
      mockRepository.count.mockResolvedValue(0)

      // Should not check name uniqueness when disabled
      await expect(
        customService.validateProjectCreation(sampleCreateData)
      ).resolves.toBeUndefined()
      expect(mockRepository.count).not.toHaveBeenCalledWith({
        name: "Test Project",
      })
    })

    test("should handle missing priority in budget limits", async () => {
      const customPriority = {
        ...sampleCreateData,
        priority: "Urgent" as any, // Not in config
        budgetData: { totalAmount: 1000000, currency: "EUR" },
      }

      // Should not throw error for undefined priority limit
      await expect(
        service.validateProjectCreation(customPriority)
      ).resolves.toBeUndefined()
    })
  })

  describe("Edge Cases and Error Details", () => {
    test("should include detailed error information", async () => {
      const invalidData = { ...sampleCreateData, name: "" }

      try {
        await service.validateProjectCreation(invalidData)
        expect.fail("Should have thrown error")
      } catch (error) {
        expect(error).toBeInstanceOf(ProjectCreationValidationError)
        expect((error as ProjectCreationValidationError).validationType).toBe(
          "PROJECT_CREATION"
        )
        expect((error as ProjectCreationValidationError).message).toBe(
          "Project name cannot be empty"
        )
      }
    })

    test("should handle repository errors gracefully", async () => {
      const repositoryError = new Error("Database connection failed")
      mockRepository.count.mockRejectedValue(repositoryError)

      await expect(
        service.validateProjectCreation(sampleCreateData)
      ).rejects.toThrow("Database connection failed")
    })

    test("should validate external project ID format", async () => {
      const invalidExternalId = {
        ...sampleCreateData,
        externalProjectId: "invalid@id",
      }

      await expect(
        service.validateProjectCreation(invalidExternalId)
      ).rejects.toThrow(
        "External project ID must contain only alphanumeric characters, hyphens, and underscores"
      )
    })

    test("should handle null/undefined values gracefully", async () => {
      const dataWithNulls = {
        ...sampleCreateData,
        description: undefined,
        clientId: undefined,
        budgetData: undefined,
        tags: undefined,
        location: undefined,
      }

      await expect(
        service.validateProjectCreation(dataWithNulls)
      ).resolves.toBeUndefined()
    })
  })
})
