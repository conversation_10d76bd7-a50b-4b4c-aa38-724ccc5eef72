/**
 * ProjectValidationService
 *
 * Domain service that encapsulates complex validation logic spanning multiple
 * domain objects and aggregates within the Project Management bounded context.
 *
 * This service orchestrates validation rules that cannot be handled by individual
 * aggregates or value objects alone, often involving cross-aggregate business rules
 * or external dependencies validation.
 */

import { ProjectStatusEnum } from "@/types/api"
import { CreateProjectData, Project } from "../entities/Project"
import { ProjectMember } from "../entities/ProjectMember"
import { IProjectRepository } from "../repositories/IProjectRepository"
import { ProjectBudget } from "../value-objects/ProjectBudget"
import {
  ProjectStatus
} from "../value-objects/ProjectStatus"
import { TeamRole } from "../value-objects/TeamRole"

/**
 * Domain-specific validation errors
 * Provides clear, actionable error messages for validation failures
 */
export class ProjectValidationError extends Error {
  constructor(
    message: string,
    public readonly validationType: string,
    public readonly details?: Record<string, any>
  ) {
    super(message)
    this.name = "ProjectValidationError"
  }
}

export class ProjectCreationValidationError extends ProjectValidationError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, "PROJECT_CREATION", details)
    this.name = "ProjectCreationValidationError"
  }
}

export class ProjectStatusTransitionError extends ProjectValidationError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, "STATUS_TRANSITION", details)
    this.name = "ProjectStatusTransitionError"
  }
}

export class TeamMemberAssignmentError extends ProjectValidationError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, "TEAM_MEMBER_ASSIGNMENT", details)
    this.name = "TeamMemberAssignmentError"
  }
}

export class BudgetAllocationError extends ProjectValidationError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, "BUDGET_ALLOCATION", details)
    this.name = "BudgetAllocationError"
  }
}

/**
 * Configuration for validation policies
 * Allows customization of validation rules based on business requirements
 */
export interface ProjectValidationConfig {
  // Project creation policies
  enforceUniqueProjectNames: boolean
  allowDuplicateNamesWithinClient: boolean
  maxProjectsPerOwner: number
  maxProjectsPerClient: number

  // Budget validation policies
  maxBudgetAmountPerPriority: Record<string, number>
  allowedCurrencies: string[]
  minBudgetAmount: number

  // Team validation policies
  maxTeamSizeOverride: Record<string, number> // Priority-based overrides
  allowExpiredMemberAssignment: boolean
  requireOwnerApprovalForAdminRoles: boolean

  // Status transition policies
  requireCompletionTasksForCompletion: boolean
  allowDirectCancellation: boolean
  enforceSequentialStatusProgression: boolean
}

/**
 * Default validation configuration
 * Production-ready settings with sensible business defaults
 */
const DEFAULT_VALIDATION_CONFIG: ProjectValidationConfig = {
  enforceUniqueProjectNames: true,
  allowDuplicateNamesWithinClient: false,
  maxProjectsPerOwner: 50,
  maxProjectsPerClient: 100,

  maxBudgetAmountPerPriority: {
    Low: 100000,
    Medium: 500000,
    High: 2000000,
    Critical: 10000000,
  },
  allowedCurrencies: ["EUR", "USD", "GBP", "JPY", "CAD", "AUD"],
  minBudgetAmount: 1000,

  maxTeamSizeOverride: {},
  allowExpiredMemberAssignment: false,
  requireOwnerApprovalForAdminRoles: true,

  requireCompletionTasksForCompletion: true,
  allowDirectCancellation: true,
  enforceSequentialStatusProgression: false,
}

/**
 * ProjectValidationService
 *
 * Domain service providing comprehensive validation logic for project operations.
 * Orchestrates business rules that span multiple aggregates and require external validation.
 */
export class ProjectValidationService {
  constructor(
    private readonly repository: IProjectRepository,
    private readonly config: ProjectValidationConfig = DEFAULT_VALIDATION_CONFIG
  ) {}

  /**
   * Validates project creation with comprehensive business rule checking
   *
   * Performs validation that goes beyond what the Project aggregate constructor
   * can handle, including system-wide uniqueness checks and external dependencies.
   *
   * @param data - Project creation data to validate
   * @throws ProjectCreationValidationError if validation fails
   */
  async validateProjectCreation(data: CreateProjectData): Promise<void> {
    // Project ID validation
    if (!data.id || data.id.trim() === "") {
      throw new ProjectCreationValidationError("Project ID cannot be empty")
    }

    if (!/^[a-zA-Z0-9_-]+$/.test(data.id)) {
      throw new ProjectCreationValidationError(
        "Project ID must contain only alphanumeric characters, hyphens, and underscores"
      )
    }

    // Project name validation
    if (!data.name || data.name.trim().length === 0) {
      throw new ProjectCreationValidationError("Project name cannot be empty")
    }

    if (data.name.length < 3 || data.name.length > 200) {
      throw new ProjectCreationValidationError("Project name must be between 3 and 200 characters")
    }

    // Owner validation
    if (!data.ownerId || data.ownerId <= 0) {
      throw new ProjectCreationValidationError("Valid owner ID is required")
    }

    // Check duplicate project names if enforced
    if (this.config.enforceUniqueProjectNames) {
      const existingByName = await this.repository.count({ name: data.name })
      if (existingByName > 0) {
        throw new ProjectCreationValidationError(
          `Project name '${data.name}' already exists system-wide`
        )
      }
    }

    // Check owner project limits
    const ownerProjectCount = await this.repository.count({ ownerId: data.ownerId })
    if (ownerProjectCount >= this.config.maxProjectsPerOwner) {
      throw new ProjectCreationValidationError(
        `Owner ${data.ownerId} has reached the maximum limit of ${this.config.maxProjectsPerOwner} projects`
      )
    }

    // Check client project limits
    if (data.clientId) {
      const clientProjectCount = await this.repository.count({ clientId: data.clientId })
      if (clientProjectCount >= this.config.maxProjectsPerClient) {
        throw new ProjectCreationValidationError(
          `Client ${data.clientId} has reached the maximum limit of ${this.config.maxProjectsPerClient} projects`
        )
      }
    }

    // Budget validation
    if (data.budgetData) {
      if (data.budgetData.totalAmount < this.config.minBudgetAmount) {
        throw new ProjectCreationValidationError(
          `Budget amount ${data.budgetData.totalAmount} is below minimum of ${this.config.minBudgetAmount}`
        )
      }

      const maxBudgetForPriority = this.config.maxBudgetAmountPerPriority[data.priority || "Medium"]
      if (maxBudgetForPriority && data.budgetData.totalAmount > maxBudgetForPriority) {
        throw new ProjectCreationValidationError(
          `Budget amount ${data.budgetData.totalAmount} exceeds maximum of ${maxBudgetForPriority} for ${data.priority} priority projects`
        )
      }

      if (!this.config.allowedCurrencies.includes(data.budgetData.currency)) {
        throw new ProjectCreationValidationError(
          `Currency '${data.budgetData.currency}' is not allowed. Supported: ${this.config.allowedCurrencies.join(", ")}`
        )
      }
    }

    // Date range validation
    if (data.startDate && data.endDate) {
      const startDate = new Date(data.startDate)
      const endDate = new Date(data.endDate)
      if (startDate >= endDate) {
        throw new ProjectCreationValidationError("Start date must be before end date")
      }
    }

    // Tags validation
    if (data.tags && data.tags.length > 20) {
      throw new ProjectCreationValidationError("Project cannot have more than 20 tags")
    }

    if (data.tags) {
      const invalidTags = data.tags.filter(tag => !tag.trim() || tag.length > 50)
      if (invalidTags.length > 0) {
        throw new ProjectCreationValidationError(
          "Tags must be non-empty and no longer than 50 characters"
        )
      }
    }

    // Location validation
    if (data.location && data.location.length > 200) {
      throw new ProjectCreationValidationError("Location must not exceed 200 characters")
    }

    // External project ID validation
    if (data.externalProjectId && !/^[a-zA-Z0-9_-]+$/.test(data.externalProjectId)) {
      throw new ProjectCreationValidationError(
        "External project ID must contain only alphanumeric characters, hyphens, and underscores"
      )
    }
  }

  /**
   * Validates project business rules
   */
  async validateProjectBusinessRules(project: Project): Promise<{ isValid: boolean; violations: string[] }> {
    const violations: string[] = []

    // Business rule validations can be added here
    if (project.name.toLowerCase().includes('test') && project.priority === 'Critical') {
      violations.push("Test projects cannot have Critical priority")
    }

    return {
      isValid: violations.length === 0,
      violations
    }
  }

  /**
   * Validates project updates with comprehensive business rule checking
   *
   * @param existingProject - The current project state
   * @param updatedProject - The proposed updated project
   * @throws ProjectValidationError if update is invalid
   */
  async validateProjectUpdate(
    existingProject: Project,
    updatedProject: Project
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []

    // Validate that critical fields are not changed inappropriately
    if (existingProject.id !== updatedProject.id) {
      errors.push("Project ID cannot be changed")
    }

    // Validate name uniqueness if name is being changed
    if (existingProject.name !== updatedProject.name && this.config.enforceUniqueProjectNames) {
      try {
        await this.validateProjectNameUniqueness(updatedProject.name, updatedProject.clientId)
      } catch (error) {
        errors.push(`Name validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validates project archival eligibility
   *
   * @param project - The project to archive
   * @returns Validation result with eligibility status
   */
  async validateProjectArchival(
    project: Project
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []

    // Only completed or cancelled projects can be archived
    if (!project.status.isCompleted() && !project.status.isCancelled()) {
      errors.push("Only completed or cancelled projects can be archived")
    }

    // Check if enough time has passed since completion/cancellation
    const statusDate = new Date(project.status.changedAt || project.updatedAt)
    const now = new Date()
    const daysSinceStatus = Math.floor((now.getTime() - statusDate.getTime()) / (1000 * 60 * 60 * 24))
    
    if (daysSinceStatus < 30) {
      errors.push("Projects must be completed/cancelled for at least 30 days before archival")
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validates project status changes
   *
   * @param currentProject - The current project state
   * @param newProject - The project with new status
   * @returns Validation result
   */
  async validateProjectStatusChange(
    currentProject: Project,
    newProject: Project
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []

    // Validate that the status transition is valid
    if (!currentProject.status.canTransitionTo(newProject.status.status)) {
      const validTransitions = currentProject.status.getValidTransitions()
      errors.push(
        `Invalid status transition from '${currentProject.status.status}' to '${newProject.status.status}'. ` +
        `Valid transitions: [${validTransitions.join(", ")}]`
      )
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Validates status transitions with comprehensive business rule checking
   *
   * Verifies if a project can legally transition between states, considering
   * the current project state, business rules, and external dependencies.
   *
   * @param project - The project attempting the transition
   * @param fromStatus - Current project status
   * @param toStatus - Target project status
   * @param transitionContext - Additional context for validation
   * @throws ProjectStatusTransitionError if transition is invalid
   */
  async validateStatusTransition(
    project: Project,
    fromStatus: ProjectStatus,
    toStatus: ProjectStatusEnum,
    transitionContext?: {
      userId?: number
      reason?: string
      forceTransition?: boolean
    }
  ): Promise<void> {
    // Check basic status transition rules from ProjectStatus value object
    if (!fromStatus.canTransitionTo(toStatus)) {
      throw new ProjectStatusTransitionError(
        `Cannot transition from '${fromStatus.status}' to '${toStatus}'`,
        {
          currentStatus: fromStatus.status,
          targetStatus: toStatus,
          projectId: project.id,
        }
      )
    }

    // Validate user permissions for status transition
    if (transitionContext?.userId) {
      await this.validateStatusTransitionPermissions(
        project,
        transitionContext.userId,
        toStatus
      )
    }

    // Apply specific validation rules based on target status
    switch (toStatus) {
      case "Active":
        await this.validateActivationRequirements(project)
        break
      case "Completed":
        await this.validateCompletionRequirements(project)
        break
      case "Archived":
        await this.validateArchivalRequirements(project)
        break
      case "Cancelled":
        await this.validateCancellationRequirements(
          project,
          transitionContext?.forceTransition
        )
        break
      case "On Hold":
        await this.validateOnHoldRequirements(project)
        break
    }

    // Validate sequential progression if enforced
    if (this.config.enforceSequentialStatusProgression) {
      this.validateSequentialProgression(fromStatus.status, toStatus)
    }
  }

  /**
   * Validates team member assignment with comprehensive rule checking
   *
   * Performs validation for adding members to projects, including capacity limits,
   * role compatibility, and business constraints.
   *
   * @param project - Target project for member assignment
   * @param member - Project member to be assigned
   * @param assignmentContext - Additional context for validation
   * @throws TeamMemberAssignmentError if assignment is invalid
   */
  async validateTeamMemberAssignment(
    project: Project,
    member: ProjectMember,
    assignmentContext?: {
      assignedBy?: number
      skipExpirationCheck?: boolean
      bypassLimits?: boolean
    }
  ): Promise<void> {
    // Validate project state allows member addition
    if (!project.canAddMembers()) {
      throw new TeamMemberAssignmentError(
        `Cannot add members to project in '${project.status.status}' state`,
        {
          projectId: project.id,
          projectStatus: project.status.status,
          memberUserId: member.userId,
        }
      )
    }

    // Validate member isn't already assigned
    if (project.getMemberByUserId(member.userId)) {
      throw new TeamMemberAssignmentError(
        `User ${member.userId} is already a member of project ${project.id}`,
        {
          projectId: project.id,
          memberUserId: member.userId,
          duplicateAssignment: true,
        }
      )
    }

    // Validate team size limits
    if (!assignmentContext?.bypassLimits) {
      await this.validateTeamSizeLimit(project, member.role)
    }

    // Validate member expiration if applicable
    if (member.expiresAt && !assignmentContext?.skipExpirationCheck) {
      this.validateMemberExpiration(member)
    }

    // Validate role assignment permissions
    if (assignmentContext?.assignedBy) {
      await this.validateRoleAssignmentPermissions(
        project,
        assignmentContext.assignedBy,
        member.role
      )
    }

    // Validate member availability (business rule)
    await this.validateMemberAvailability(member.userId, project)
  }

  // Private validation helper methods

  private async validateBasicProjectData(
    data: CreateProjectData
  ): Promise<void> {
    if (!data.id?.trim()) {
      throw new ProjectCreationValidationError("Project ID cannot be empty")
    }

    if (!data.name?.trim()) {
      throw new ProjectCreationValidationError("Project name cannot be empty")
    }

    if (!data.ownerId || data.ownerId <= 0) {
      throw new ProjectCreationValidationError("Valid owner ID is required")
    }

    // Validate ID format (example: should follow specific pattern)
    if (!/^[a-zA-Z0-9_-]+$/.test(data.id)) {
      throw new ProjectCreationValidationError(
        "Project ID must contain only alphanumeric characters, hyphens, and underscores",
        { invalidId: data.id }
      )
    }

    // Validate name length
    if (data.name.length < 3 || data.name.length > 200) {
      throw new ProjectCreationValidationError(
        "Project name must be between 3 and 200 characters",
        { nameLength: data.name.length }
      )
    }
  }

  private async validateProjectNameUniqueness(
    name: string,
    clientId?: number
  ): Promise<void> {
    const searchCriteria = this.config.allowDuplicateNamesWithinClient
      ? { name, clientId }
      : { name }

    // Note: This would require extending IProjectRepository to support name-based searches
    // For now, we'll use a simplified approach
    const existingCount = await this.repository.count(
      searchCriteria as any
    )

    if (existingCount > 0) {
      const scope = clientId ? `for client ${clientId}` : "system-wide"
      throw new ProjectCreationValidationError(
        `Project name '${name}' already exists ${scope}`,
        { duplicateName: name, clientId, scope }
      )
    }
  }

  private async validateOwnerConstraints(ownerId: number): Promise<void> {
    const ownerProjectCount = await this.repository.count({ ownerId })

    if (ownerProjectCount >= this.config.maxProjectsPerOwner) {
      throw new ProjectCreationValidationError(
        `Owner ${ownerId} has reached the maximum limit of ${this.config.maxProjectsPerOwner} projects`,
        {
          ownerId,
          currentCount: ownerProjectCount,
          maxAllowed: this.config.maxProjectsPerOwner,
        }
      )
    }
  }

  private async validateClientConstraints(clientId: number): Promise<void> {
    const clientProjectCount = await this.repository.count({ clientId })

    if (clientProjectCount >= this.config.maxProjectsPerClient) {
      throw new ProjectCreationValidationError(
        `Client ${clientId} has reached the maximum limit of ${this.config.maxProjectsPerClient} projects`,
        {
          clientId,
          currentCount: clientProjectCount,
          maxAllowed: this.config.maxProjectsPerClient,
        }
      )
    }
  }

  private async validateBudgetConstraints(
    budgetData: any,
    priority: string = "Medium"
  ): Promise<void> {
    if (budgetData.totalAmount < this.config.minBudgetAmount) {
      throw new ProjectCreationValidationError(
        `Budget amount ${budgetData.totalAmount} is below minimum of ${this.config.minBudgetAmount}`,
        { amount: budgetData.totalAmount, minimum: this.config.minBudgetAmount }
      )
    }

    const maxAllowed = this.config.maxBudgetAmountPerPriority[priority]
    if (maxAllowed && budgetData.totalAmount > maxAllowed) {
      throw new ProjectCreationValidationError(
        `Budget amount ${budgetData.totalAmount} exceeds maximum of ${maxAllowed} for ${priority} priority projects`,
        { amount: budgetData.totalAmount, maximum: maxAllowed, priority }
      )
    }

    if (
      budgetData.currency &&
      !this.config.allowedCurrencies.includes(budgetData.currency)
    ) {
      throw new ProjectCreationValidationError(
        `Currency '${budgetData.currency}' is not allowed. Supported: ${this.config.allowedCurrencies.join(", ")}`,
        {
          currency: budgetData.currency,
          allowedCurrencies: this.config.allowedCurrencies,
        }
      )
    }
  }

  private validateDateConstraints(startDate?: string, endDate?: string): void {
    if (startDate && endDate) {
      if (new Date(startDate) >= new Date(endDate)) {
        throw new ProjectCreationValidationError(
          "Start date must be before end date",
          { startDate, endDate }
        )
      }
    }

    // Validate dates are not in the past (with some tolerance)
    const now = new Date()
    const tolerance = 24 * 60 * 60 * 1000 // 24 hours

    if (
      startDate &&
      new Date(startDate).getTime() < now.getTime() - tolerance
    ) {
      throw new ProjectCreationValidationError(
        "Start date cannot be more than 24 hours in the past",
        { startDate, currentTime: now.toISOString() }
      )
    }
  }

  private validateProjectMetadata(data: CreateProjectData): void {
    // Validate tags
    if (data.tags && data.tags.length > 20) {
      throw new ProjectCreationValidationError(
        "Project cannot have more than 20 tags",
        { tagCount: data.tags.length }
      )
    }

    if (data.tags) {
      const invalidTags = data.tags.filter(
        (tag) => !tag.trim() || tag.length > 50
      )
      if (invalidTags.length > 0) {
        throw new ProjectCreationValidationError(
          "Tags must be non-empty and no longer than 50 characters",
          { invalidTags }
        )
      }
    }

    // Validate location
    if (data.location && data.location.length > 200) {
      throw new ProjectCreationValidationError(
        "Location must not exceed 200 characters",
        { locationLength: data.location.length }
      )
    }

    // Validate external project ID format if provided
    if (
      data.externalProjectId &&
      !/^[a-zA-Z0-9_-]+$/.test(data.externalProjectId)
    ) {
      throw new ProjectCreationValidationError(
        "External project ID must contain only alphanumeric characters, hyphens, and underscores",
        { externalProjectId: data.externalProjectId }
      )
    }
  }

  /**
   * Validates budget allocation with comprehensive business constraints
   *
   * Performs validation for budget changes, considering project context,
   * financial policies, and business constraints.
   *
   * @param budget - Budget to validate
   * @param project - Optional project context for additional validation
   * @throws BudgetAllocationError if budget is invalid
   */
  async validateBudgetAllocation(
    budget: ProjectBudget,
    project?: Project
  ): Promise<void> {
    // Validate minimum budget requirements
    if (budget.totalAmount < this.config.minBudgetAmount) {
      throw new BudgetAllocationError(
        `Budget amount ${budget.totalAmount} is below minimum of ${this.config.minBudgetAmount}`,
        { amount: budget.totalAmount, minimum: this.config.minBudgetAmount }
      )
    }

    // Validate currency is allowed
    if (!this.config.allowedCurrencies.includes(budget.currency)) {
      throw new BudgetAllocationError(
        `Currency '${budget.currency}' is not allowed. Supported: ${this.config.allowedCurrencies.join(", ")}`,
        {
          currency: budget.currency,
          allowedCurrencies: this.config.allowedCurrencies,
        }
      )
    }

    // Project-specific validation
    if (project) {
      // Validate priority-based budget limits
      const maxAllowed =
        this.config.maxBudgetAmountPerPriority[project.priority]
      if (maxAllowed && budget.totalAmount > maxAllowed) {
        throw new BudgetAllocationError(
          `Budget amount ${budget.totalAmount} exceeds maximum of ${maxAllowed} for ${project.priority} priority projects`,
          {
            amount: budget.totalAmount,
            maximum: maxAllowed,
            priority: project.priority,
            projectId: project.id,
          }
        )
      }

      // Validate budget reduction limits for active projects
      if (
        project.isActive() &&
        budget.totalAmount < project.budget.totalAmount * 0.5
      ) {
        throw new BudgetAllocationError(
          "Cannot reduce budget by more than 50% for active projects",
          {
            currentAmount: project.budget.totalAmount,
            newAmount: budget.totalAmount,
            reductionPercent:
              ((project.budget.totalAmount - budget.totalAmount) /
                project.budget.totalAmount) *
              100,
            projectId: project.id,
          }
        )
      }

      // Validate spent amount doesn't exceed new total
      if (budget.spentAmount > budget.totalAmount) {
        throw new BudgetAllocationError(
          `New budget total ${budget.totalAmount} cannot be less than already spent amount ${budget.spentAmount}`,
          {
            totalAmount: budget.totalAmount,
            spentAmount: budget.spentAmount,
            projectId: project.id,
          }
        )
      }
    }
  }

  private async validateStatusTransitionPermissions(
    project: Project,
    userId: number,
    targetStatus: ProjectStatusEnum
  ): Promise<void> {
    const member = project.getMemberByUserId(userId)
    if (!member || !member.canManageProject()) {
      throw new ProjectStatusTransitionError(
        `User ${userId} does not have permission to transition project to '${targetStatus}'`,
        {
          userId,
          targetStatus,
          projectId: project.id,
          hasManagePermission: false,
        }
      )
    }

    // Additional permission checks for specific transitions
    if (["Archived", "Cancelled"].includes(targetStatus)) {
      // Only administrators can archive or cancel projects
      if (member.role.role !== "Administrator") {
        throw new ProjectStatusTransitionError(
          `Only administrators can transition project to '${targetStatus}'`,
          {
            userId,
            targetStatus,
            projectId: project.id,
            userRole: member.role.role,
          }
        )
      }
    }
  }

  private async validateActivationRequirements(
    project: Project
  ): Promise<void> {
    // Validate project has minimum required team members
    const activeMembers = project.getActiveMembers()
    if (activeMembers.length < 1) {
      throw new ProjectStatusTransitionError(
        "Project must have at least one active team member to be activated",
        { projectId: project.id, activeMemberCount: activeMembers.length }
      )
    }

    // Validate project has valid budget
    if (project.budget.totalAmount <= 0) {
      throw new ProjectStatusTransitionError(
        "Project must have a valid budget to be activated",
        { projectId: project.id, budgetAmount: project.budget.totalAmount }
      )
    }

    // Validate start date if specified
    if (project.startDate) {
      const startDate = new Date(project.startDate)
      const now = new Date()
      const daysDiff = Math.ceil(
        (startDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24)
      )

      if (daysDiff > 365) {
        throw new ProjectStatusTransitionError(
          "Project cannot be activated more than 365 days before start date",
          {
            projectId: project.id,
            startDate: project.startDate,
            daysUntilStart: daysDiff,
          }
        )
      }
    }
  }

  private async validateCompletionRequirements(
    project: Project
  ): Promise<void> {
    if (this.config.requireCompletionTasksForCompletion) {
      // Validate all critical milestones are complete
      // This would typically involve checking external task/milestone systems
      // For now, we validate basic project state

      if (project.hasExpiredMembers()) {
        throw new ProjectStatusTransitionError(
          "Cannot complete project with expired team members",
          { projectId: project.id, hasExpiredMembers: true }
        )
      }

      // Validate budget status
      if (project.isOverBudget()) {
        throw new ProjectStatusTransitionError(
          "Cannot complete project that is over budget without approval",
          {
            projectId: project.id,
            isOverBudget: true,
            requiresApproval: project.requiresApproval(),
          }
        )
      }
    }
  }

  private async validateArchivalRequirements(project: Project): Promise<void> {
    // Only completed or cancelled projects can be archived
    if (!project.isCompleted() && !project.isCancelled()) {
      throw new ProjectStatusTransitionError(
        "Only completed or cancelled projects can be archived",
        {
          projectId: project.id,
          currentStatus: project.status.status,
          isCompleted: project.isCompleted(),
          isCancelled: project.isCancelled(),
        }
      )
    }

    // Validate minimum time since completion/cancellation
    const statusDate = new Date(
      project.status.changedAt || project.updatedAt
    )
    const now = new Date()
    const daysSinceStatus = Math.floor(
      (now.getTime() - statusDate.getTime()) / (1000 * 60 * 60 * 24)
    )

    if (daysSinceStatus < 30) {
      throw new ProjectStatusTransitionError(
        "Project must be completed or cancelled for at least 30 days before archival",
        {
          projectId: project.id,
          daysSinceStatus,
          minimumDays: 30,
          statusDate: statusDate.toISOString(),
        }
      )
    }
  }

  private async validateCancellationRequirements(
    project: Project,
    forceTransition?: boolean
  ): Promise<void> {
    if (!this.config.allowDirectCancellation && !forceTransition) {
      // Validate project can be safely cancelled
      if (project.budget.spentAmount > project.budget.totalAmount * 0.5) {
        throw new ProjectStatusTransitionError(
          "Cannot cancel project with more than 50% budget spent without force flag",
          {
            projectId: project.id,
            spentAmount: project.budget.spentAmount,
            totalAmount: project.budget.totalAmount,
            spentPercentage:
              (project.budget.spentAmount / project.budget.totalAmount) * 100,
            requiresForce: true,
          }
        )
      }

      // Check if project has active dependencies (would be external check)
      // For now, validate team size as proxy for project complexity
      if (project.getActiveMembers().length > 10) {
        throw new ProjectStatusTransitionError(
          "Cannot cancel large project (>10 members) without force flag",
          {
            projectId: project.id,
            activeMemberCount: project.getActiveMembers().length,
            requiresForce: true,
          }
        )
      }
    }
  }

  private async validateOnHoldRequirements(project: Project): Promise<void> {
    // Projects cannot be put on hold if they're already completed or cancelled
    if (project.isCompleted() || project.isCancelled()) {
      throw new ProjectStatusTransitionError(
        "Cannot put completed or cancelled projects on hold",
        {
          projectId: project.id,
          currentStatus: project.status.status,
          isCompleted: project.isCompleted(),
          isCancelled: project.isCancelled(),
        }
      )
    }

    // Validate there are no critical pending activities
    if (project.requiresApproval()) {
      throw new ProjectStatusTransitionError(
        "Cannot put project on hold while it requires approval",
        { projectId: project.id, requiresApproval: true }
      )
    }
  }

  private validateSequentialProgression(
    fromStatus: ProjectStatusEnum,
    toStatus: ProjectStatusEnum
  ): void {
    // Define valid sequential progressions
    const validProgressions: Record<ProjectStatusEnum, ProjectStatusEnum[]> = {
      "Draft": ["Planning", "Active", "Cancelled"],
      "Planning": ["Active", "On Hold", "Cancelled"],
      "Active": ["On Hold", "Completed", "Cancelled"],
      "On Hold": ["Active", "Cancelled", "Planning"],
      "Completed": ["Archived"],
      "Cancelled": ["Archived"],
      "Archived": [],
      "Design In Progress": ["Procurement In Progress", "On Hold", "Cancelled"],
      "Procurement In Progress": ["Construction In Progress", "On Hold", "Cancelled"],
      "Construction In Progress": ["Commissioning", "On Hold", "Cancelled"],
      "Commissioning": ["Completed", "On Hold"]
    }

    const allowedTransitions = validProgressions[fromStatus] || []
    if (!allowedTransitions.includes(toStatus)) {
      throw new ProjectStatusTransitionError(
        `Sequential progression does not allow transition from '${fromStatus}' to '${toStatus}'`,
        {
          fromStatus,
          toStatus,
          allowedTransitions,
          enforceSequential: this.config.enforceSequentialStatusProgression,
        }
      )
    }
  }

  private async validateTeamSizeLimit(
    project: Project,
    memberRole: TeamRole
  ): Promise<void> {
    const currentSize = project.members.length
    const priorityOverride = this.config.maxTeamSizeOverride[project.priority]
    const maxSize =
      priorityOverride || this.getDefaultMaxTeamSize(project.priority)

    if (currentSize >= maxSize) {
      throw new TeamMemberAssignmentError(
        `Project has reached maximum team size of ${maxSize} for ${project.priority} priority`,
        {
          projectId: project.id,
          currentSize,
          maxSize,
          priority: project.priority,
          newMemberRole: memberRole.role,
        }
      )
    }

    // Role-specific limits
    const roleCount = project.getMembersByRole(memberRole.role).length
    const maxRoleCount = this.getMaxRoleCount(memberRole.role, project.priority)

    if (roleCount >= maxRoleCount) {
      throw new TeamMemberAssignmentError(
        `Project has reached maximum of ${maxRoleCount} members with role '${memberRole.role}'`,
        {
          projectId: project.id,
          role: memberRole.role,
          currentRoleCount: roleCount,
          maxRoleCount,
          priority: project.priority,
        }
      )
    }
  }

  private validateMemberExpiration(member: ProjectMember): void {
    if (!this.config.allowExpiredMemberAssignment && member.hasExpired()) {
      throw new TeamMemberAssignmentError(
        `Cannot assign member with expired assignment (expires: ${member.expiresAt})`,
        {
          memberUserId: member.userId,
          expiresAt: member.expiresAt,
          isExpired: true,
          daysExpired: member.getDaysUntilExpiration(),
        }
      )
    }

    // Warn if member expires soon
    if (member.isApproachingExpiration(7)) {
      const daysUntilExpiration = member.getDaysUntilExpiration()
      if (daysUntilExpiration !== null && daysUntilExpiration < 3) {
        throw new TeamMemberAssignmentError(
          `Member assignment expires in ${daysUntilExpiration} days, assignment may not be practical`,
          {
            memberUserId: member.userId,
            expiresAt: member.expiresAt,
            daysUntilExpiration,
            isApproachingExpiration: true,
          }
        )
      }
    }
  }

  private async validateRoleAssignmentPermissions(
    project: Project,
    assignerId: number,
    targetRole: TeamRole
  ): Promise<void> {
    const assigner = project.getMemberByUserId(assignerId)
    if (!assigner) {
      throw new TeamMemberAssignmentError(
        `User ${assignerId} is not a member of the project and cannot assign roles`,
        { assignerId, projectId: project.id, isProjectMember: false }
      )
    }

    if (!assigner.canAssignRole(targetRole)) {
      throw new TeamMemberAssignmentError(
        `User ${assignerId} does not have permission to assign role '${targetRole.role}'`,
        {
          assignerId,
          assignerRole: assigner.role.role,
          targetRole: targetRole.role,
          canAssignRole: false,
        }
      )
    }

    // Special validation for administrator role assignment
    if (
      targetRole.role === "Administrator" &&
      this.config.requireOwnerApprovalForAdminRoles
    ) {
      if (assignerId !== project.ownerId) {
        throw new TeamMemberAssignmentError(
          "Administrator role assignment requires project owner approval",
          {
            assignerId,
            targetRole: targetRole.role,
            projectOwnerId: project.ownerId,
            requiresOwnerApproval: true,
          }
        )
      }
    }
  }

  private async validateMemberAvailability(
    userId: number,
    project: Project
  ): Promise<void> {
    // Check if user is already overcommitted to other projects
    const userProjects = await this.repository.findByMemberUserId(
      userId,
      { limit: 100 }
    )

    // Safety check for undefined result
    if (!userProjects || !userProjects.projects) {
      console.error('Invalid response from findByMemberUserId:', userProjects);
      throw new Error(`Failed to retrieve project data for user ${userId}`);
    }

    const activeMemberships = userProjects.projects.filter(
      (p) => p.isActive() && p.id !== project.id
    ).length

    // Business rule: users can only be active members of max 10 projects
    if (activeMemberships >= 10) {
      throw new TeamMemberAssignmentError(
        `User ${userId} is already an active member of ${activeMemberships} projects (maximum: 10)`,
        {
          userId,
          activeMemberships,
          maxAllowed: 10,
          projectId: project.id,
        }
      )
    }

    // Check for conflicting project schedules if dates are defined
    if (project.startDate && project.endDate) {
      const conflicts = userProjects.projects.filter((p) => {
        if (!p.startDate || !p.endDate || p.id === project.id) return false

        const projectStart = new Date(project.startDate!)
        const projectEnd = new Date(project.endDate!)
        const existingStart = new Date(p.startDate)
        const existingEnd = new Date(p.endDate)

        // Check for date overlap
        return projectStart <= existingEnd && projectEnd >= existingStart
      })

      if (conflicts.length > 2) {
        // Allow some overlap but limit excessive conflicts
        throw new TeamMemberAssignmentError(
          `User ${userId} has ${conflicts.length} projects with conflicting schedules`,
          {
            userId,
            conflictingProjects: conflicts.length,
            maxAllowedConflicts: 2,
            projectSchedule: { start: project.startDate, end: project.endDate },
          }
        )
      }
    }
  }

  // Helper methods

  private getDefaultMaxTeamSize(priority: string): number {
    const defaultSizes: Record<string, number> = {
      Low: 10,
      Medium: 20,
      High: 30,
      Critical: 50,
    }
    return defaultSizes[priority] || 20
  }

  private getMaxRoleCount(role: string, priority: string): number {
    const roleBasedLimits: Record<string, Record<string, number>> = {
      Administrator: { Low: 2, Medium: 3, High: 4, Critical: 5 },
      "Project Manager": { Low: 1, Medium: 2, High: 2, Critical: 3 },
      "Lead Engineer": { Low: 1, Medium: 2, High: 3, Critical: 4 },
      "Senior Engineer": { Low: 2, Medium: 4, High: 6, Critical: 8 },
      Electrician: { Low: 3, Medium: 6, High: 10, Critical: 15 },
      "Electrical Engineer": { Low: 2, Medium: 4, High: 6, Critical: 10 },
      "Quality Assurance": { Low: 1, Medium: 2, High: 3, Critical: 4 },
      Consultant: { Low: 1, Medium: 2, High: 3, Critical: 5 },
      "Client": { Low: 1, Medium: 1, High: 2, Critical: 2 },
      Viewer: { Low: 5, Medium: 10, High: 15, Critical: 20 },
      "Supplier": { Low: 2, Medium: 4, High: 6, Critical: 10 },
    }

    return roleBasedLimits[role]?.[priority] || 99 // Default to very high limit if not specified
  }
}