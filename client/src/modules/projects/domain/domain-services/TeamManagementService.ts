/**
 * TeamManagementService
 *
 * Domain service that orchestrates team management operations across the Project
 * Management bounded context. Handles complex business logic that spans multiple
 * aggregates and requires coordination with external systems.
 *
 * This service focuses on team composition, role management, and member lifecycle
 * operations that require business logic beyond what individual aggregates provide.
 */

import { UserRoleEnum } from "@/types/api"
import { Project } from "../entities/Project"
import { ProjectMember } from "../entities/ProjectMember"
import { IProjectRepository } from "../repositories/IProjectRepository"
import { TeamRole } from "../value-objects/TeamRole"

/**
 * External user information interface
 * Represents user data from external User Management context
 */
export interface ExternalUserInfo {
  id: number
  name: string
  email: string
  isActive: boolean
  department?: string
  skills: string[]
  certifications: string[]
  availabilityStatus: "Available" | "Busy" | "Unavailable" | "On Leave"
  maxProjectLoad: number
  currentProjectCount: number
}

/**
 * Team assignment context for business decisions
 * Provides additional context for team management operations
 */
export interface TeamAssignmentContext {
  assignedBy: number
  reason?: string
  expiresAt?: string
  priority: "Low" | "Medium" | "High" | "Critical"
  skillsRequired?: string[]
  estimatedWorkload?: number
  isTemporaryAssignment?: boolean
  requiresApproval?: boolean
}

/**
 * Role availability information
 * Describes available roles and their constraints for a project
 */
export interface RoleAvailability {
  role: UserRoleEnum
  isAvailable: boolean
  currentCount: number
  maxCount: number
  restrictions: string[]
  requiresApproval: boolean
  skillRequirements: string[]
}

/**
 * Member expiration summary
 * Aggregates expiration information for project members
 */
export interface MemberExpirationSummary {
  projectId: string
  totalMembers: number
  expiredMembers: ProjectMember[]
  expiringMembers: ProjectMember[]
  averageDaysUntilExpiration: number
  membersRequiringExtension: ProjectMember[]
  actionRequired: boolean
}

/**
 * Team management operation result
 * Standardized result for team management operations
 */
export interface TeamManagementResult {
  success: boolean
  project: Project
  affectedMember?: ProjectMember
  warnings: string[]
  recommendations: string[]
  nextActions: string[]
}

/**
 * Domain-specific team management errors
 * Provides clear, actionable error messages for team operations
 */
export class TeamManagementError extends Error {
  constructor(
    message: string,
    public readonly operation: string,
    public readonly details?: Record<string, any>
  ) {
    super(message)
    this.name = "TeamManagementError"
  }
}

export class MemberAssignmentError extends TeamManagementError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, "MEMBER_ASSIGNMENT", details)
    this.name = "MemberAssignmentError"
  }
}

export class RoleManagementError extends TeamManagementError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, "ROLE_MANAGEMENT", details)
    this.name = "RoleManagementError"
  }
}

export class MemberExpirationError extends TeamManagementError {
  constructor(message: string, details?: Record<string, any>) {
    super(message, "MEMBER_EXPIRATION", details)
    this.name = "MemberExpirationError"
  }
}

/**
 * TeamManagementService
 *
 * Domain service providing comprehensive team management capabilities.
 * Orchestrates team operations that require coordination across aggregates
 * and integration with external systems.
 */
export class TeamManagementService {
  constructor(
    private readonly projectRepository: IProjectRepository,
    private readonly userService?: UserService // External dependency injection
  ) {}

  /**
   * Assigns a member to a project with comprehensive business logic
   *
   * Orchestrates the complete member assignment process including validation,
   * external user verification, and project state updates.
   *
   * @param project - Target project for member assignment
   * @param userId - User ID of the member to assign
   * @param role - Role to assign to the member
   * @param context - Assignment context with business requirements
   * @returns Promise<TeamManagementResult> - Operation result with updated project
   * @throws MemberAssignmentError if assignment fails
   */
  async assignMemberToProject(
    project: Project,
    userId: number,
    role: TeamRole,
    context: TeamAssignmentContext
  ): Promise<TeamManagementResult> {
    const warnings: string[] = []
    const recommendations: string[] = []
    const nextActions: string[] = []

    try {
      // Validate external user existence and availability
      const userInfo = await this.validateAndGetUserInfo(userId)

      // Check user availability and workload
      const availabilityCheck = await this.checkUserAvailability(
        userInfo,
        project,
        context
      )
      if (!availabilityCheck.isAvailable) {
        throw new MemberAssignmentError(
          `User ${userId} is not available for assignment: ${availabilityCheck.reason}`,
          {
            userId,
            projectId: project.id,
            reason: availabilityCheck.reason,
            userStatus: userInfo.availabilityStatus,
            currentLoad: userInfo.currentProjectCount,
            maxLoad: userInfo.maxProjectLoad,
          }
        )
      }

      if (availabilityCheck.warnings.length > 0) {
        warnings.push(...availabilityCheck.warnings)
      }

      // Validate role assignment permissions and availability
      await this.validateRoleAssignment(project, role, context)

      // Check skill compatibility
      const skillCheck = this.validateSkillCompatibility(
        userInfo,
        role,
        context.skillsRequired
      )
      if (!skillCheck.compatible) {
        warnings.push(
          `User may lack required skills: ${skillCheck.missingSkills.join(", ")}`
        )
        recommendations.push(
          "Consider providing additional training or mentorship"
        )
      }

      // Create project member with enhanced member ID generation
      const memberId = this.generateMemberId(project.id, userId)
      const newMember = ProjectMember.createNew(
        memberId,
        userId,
        project.id,
        role,
        context.assignedBy,
        context.expiresAt
      )

      // Add contextual notes
      const memberWithNotes = context.reason
        ? newMember.addNotes(
            `Assignment reason: ${context.reason}`,
            context.assignedBy
          )
        : newMember

      // Add member to project
      const updatedProject = project.addTeamMember(
        userId,
        role,
        context.assignedBy,
        context.expiresAt,
        context.reason
      )

      // Generate recommendations based on assignment
      const assignmentRecommendations = this.generateAssignmentRecommendations(
        updatedProject,
        memberWithNotes,
        context
      )
      recommendations.push(...assignmentRecommendations)

      // Generate next actions
      const projectNextActions = this.generateNextActions(
        updatedProject,
        memberWithNotes,
        context
      )
      nextActions.push(...projectNextActions)

      // Persist the updated project
      await this.projectRepository.save(updatedProject)

      return {
        success: true,
        project: updatedProject,
        affectedMember: memberWithNotes,
        warnings,
        recommendations,
        nextActions,
      }
    } catch (error) {
      if (error instanceof MemberAssignmentError) {
        throw error
      }

      throw new MemberAssignmentError(
        `Failed to assign member to project: ${error instanceof Error ? error.message : "Unknown error"}`,
        {
          userId,
          projectId: project.id,
          role: role.role,
          originalError: error instanceof Error ? error.message : String(error),
        }
      )
    }
  }

  /**
   * Validates role permissions for complex assignment scenarios
   *
   * Provides comprehensive role validation beyond basic permission checks,
   * considering project context, system policies, and business constraints.
   *
   * @param project - Project context for role validation
   * @param assignerId - User ID performing the role assignment
   * @param targetRole - Role being assigned
   * @param targetUserId - User receiving the role assignment
   * @throws RoleManagementError if validation fails
   */
  async validateRolePermissions(
    project: Project,
    assignerId: number,
    targetRole: TeamRole,
    targetUserId?: number
  ): Promise<void> {
    // Get assigner member information
    const assigner = project.getMemberByUserId(assignerId)
    if (!assigner) {
      throw new RoleManagementError(
        `User ${assignerId} is not a member of the project and cannot assign roles`,
        {
          assignerId,
          projectId: project.id,
          targetRole: targetRole.role,
          isProjectMember: false,
        }
      )
    }

    // Advanced permission checks based on role hierarchy (run first for specific error messages)
    await this.validateAdvancedRolePermissions(
      project,
      assigner,
      targetRole,
      targetUserId
    )

    // Validate role-specific business constraints
    await this.validateRoleBusinessConstraints(
      project,
      targetRole,
      targetUserId
    )

    // Basic role assignment permission check (fallback for general cases)
    if (!assigner.canAssignRole(targetRole)) {
      throw new RoleManagementError(
        `User ${assignerId} does not have permission to assign role '${targetRole.role}'`,
        {
          assignerId,
          assignerRole: assigner.role.role,
          targetRole: targetRole.role,
          hasPermission: false,
        }
      )
    }
  }

  /**
   * Validates member assignment to a project
   *
   * @param project - The project to assign member to
   * @param userId - User to assign
   * @param roleId - Role to assign
   * @returns Validation result
   */
  async validateMemberAssignment(
    project: Project,
    userId: number,
    role: TeamRole,
    _assignmentContext?: TeamAssignmentContext
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = []

    // Check if user is already a member
    const existingMember = project.getMemberByUserId(userId)
    if (existingMember) {
      errors.push(`User ${userId} is already a member of this project`)
    }

    // Validate role exists and is valid
    if (!role || !role.role) {
      errors.push("Invalid role specified")
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * Gets available roles for assignment in a project
   *
   * Analyzes project state and returns available roles with their constraints,
   * considering current team composition and business rules.
   *
   * @param project - Project to analyze for role availability
   * @param requesterId - User requesting role information (for permission filtering)
   * @returns Promise<RoleAvailability[]> - Available roles with constraints
   */
  async getAvailableRoles(
    project: Project,
    requesterId?: number
  ): Promise<RoleAvailability[]> {
    const availableRoles: RoleAvailability[] = []
    const requester = requesterId
      ? project.getMemberByUserId(requesterId) ?? null
      : null

    // Define all possible roles
    const allRoles: UserRoleEnum[] = [
      "Administrator", "Project Manager", "Lead Engineer", "Electrical Engineer", "Automation Engineer", "Instrumentation Engineer", "CAD Operator", "Viewer", "Guest", "Client", "Supplier"
    ]

    for (const roleType of allRoles) {
      const role = TeamRole.create({ role: roleType })
      const availability = await this.analyzeRoleAvailability(
        project,
        role,
        requester
      )
      availableRoles.push(availability)
    }

    return availableRoles.sort((a, b) => {
      // Sort by availability first, then by role hierarchy
      if (a.isAvailable !== b.isAvailable) {
        return a.isAvailable ? -1 : 1
      }
      return (
        this.getRoleHierarchyOrder(a.role) - this.getRoleHierarchyOrder(b.role)
      )
    })
  }

  /**
   * Checks member expiration status across multiple members
   *
   * Provides comprehensive expiration analysis for proactive team management
   * and automated expiration handling.
   *
   * @param members - Array of project members to check
   * @param warningDays - Days before expiration to trigger warnings (default: 7)
   * @returns MemberExpirationSummary - Comprehensive expiration analysis
   */
  checkMemberExpiration(
    members: ProjectMember[],
    warningDays: number = 7
  ): MemberExpirationSummary {
    const expiredMembers = members.filter((member) => member.hasExpired())
    const expiringMembers = members.filter(
      (member) =>
        member.isApproachingExpiration(warningDays) && !member.hasExpired()
    )

    // Calculate average days until expiration for active members with expiration dates
    const membersWithExpiration = members.filter(
      (member) => member.expiresAt && !member.hasExpired()
    )

    const totalDaysUntilExpiration = membersWithExpiration.reduce(
      (sum, member) => {
        const days = member.getDaysUntilExpiration()
        return sum + (days || 0)
      },
      0
    )

    const averageDaysUntilExpiration =
      membersWithExpiration.length > 0
        ? Math.round(totalDaysUntilExpiration / membersWithExpiration.length)
        : 0

    // Identify members requiring extension (expiring within warning period)
    const membersRequiringExtension = expiringMembers.filter((member) => {
      const daysUntil = member.getDaysUntilExpiration()
      return daysUntil !== null && daysUntil <= warningDays
    })

    // Determine if action is required
    const actionRequired =
      expiredMembers.length > 0 || membersRequiringExtension.length > 0

    return {
      projectId: members[0]?.projectId || "",
      totalMembers: members.length,
      expiredMembers,
      expiringMembers,
      averageDaysUntilExpiration,
      membersRequiringExtension,
      actionRequired,
    }
  }

  // Private helper methods - will be completed in batch 2

  private async validateAndGetUserInfo(
    userId: number
  ): Promise<ExternalUserInfo> {
    // Placeholder implementation - will be completed in batch 2
    if (this.userService) {
      return await this.userService.getUserInfo(userId)
    }

    // Mock implementation for testing
    return {
      id: userId,
      name: `User ${userId}`,
      email: `user${userId}@example.com`,
      isActive: true,
      skills: ["electrical-design", "project-management"],
      certifications: ["IEC 61508"],
      availabilityStatus: "Available",
      maxProjectLoad: 5,
      currentProjectCount: 2,
    }
  }

  private async checkUserAvailability(
    userInfo: ExternalUserInfo,
    _project: Project,
    _context: TeamAssignmentContext
  ): Promise<{ isAvailable: boolean; reason?: string; warnings: string[] }> {
    // Placeholder implementation - will be completed in batch 2
    const warnings: string[] = []

    if (!userInfo.isActive) {
      return {
        isAvailable: false,
        reason: "User account is inactive",
        warnings,
      }
    }

    if (
      userInfo.availabilityStatus === "Unavailable" ||
      userInfo.availabilityStatus === "On Leave"
    ) {
      return {
        isAvailable: false,
        reason: `User is currently ${userInfo.availabilityStatus.toLowerCase()}`,
        warnings,
      }
    }

    if (userInfo.currentProjectCount >= userInfo.maxProjectLoad) {
      return {
        isAvailable: false,
        reason: "User has reached maximum project load",
        warnings,
      }
    }

    if (userInfo.currentProjectCount >= userInfo.maxProjectLoad * 0.8) {
      warnings.push("User is approaching maximum project load")
    }

    return { isAvailable: true, warnings }
  }

  private generateMemberId(projectId: string, userId: number): string {
    const timestamp = Date.now()
    return `${projectId}_${userId}_${timestamp}`
  }

  private validateSkillCompatibility(
    userInfo: ExternalUserInfo,
    _role: TeamRole,
    requiredSkills?: string[]
  ): { compatible: boolean; missingSkills: string[] } {
    // Placeholder implementation - will be completed in batch 2
    if (!requiredSkills || requiredSkills.length === 0) {
      return { compatible: true, missingSkills: [] }
    }

    const missingSkills = requiredSkills.filter(
      (skill) => !userInfo.skills.includes(skill)
    )

    return {
      compatible: missingSkills.length === 0,
      missingSkills,
    }
  }

  /**
   * Manages member expiration extensions with business logic
   *
   * Provides automated and manual extension capabilities with proper
   * business rule validation and stakeholder notification.
   *
   * @param project - Project containing the member
   * @param userId - User ID of the member to extend
   * @param newExpirationDate - New expiration date
   * @param extendedBy - User performing the extension
   * @param reason - Reason for extension
   * @returns Promise<TeamManagementResult> - Operation result
   */
  async extendMemberExpiration(
    project: Project,
    userId: number,
    newExpirationDate: string,
    extendedBy: number,
    reason?: string
  ): Promise<TeamManagementResult> {
    const warnings: string[] = []
    const recommendations: string[] = []
    const nextActions: string[] = []

    try {
      const member = project.getMemberByUserId(userId)
      if (!member) {
        throw new MemberExpirationError(
          `User ${userId} is not a member of project ${project.id}`,
          { userId, projectId: project.id }
        )
      }

      // Validate extension permissions
      const extender = project.getMemberByUserId(extendedBy)
      if (!extender || !extender.canManageTeam()) {
        throw new MemberExpirationError(
          `User ${extendedBy} does not have permission to extend member expiration`,
          { extendedBy, userId, projectId: project.id }
        )
      }

      // Validate new expiration date
      this.validateExpirationDate(newExpirationDate, member.expiresAt)

      // Check if extension requires approval
      const extensionMonths = this.calculateExpirationExtensionMonths(
        member.expiresAt,
        newExpirationDate
      )
      if (extensionMonths > 6) {
        warnings.push(
          "Extension exceeds 6 months and may require additional approval"
        )
        recommendations.push("Consider breaking extension into shorter periods")
      }

      // Update member with extension
      const extendedMember = member.extendExpiration(
        newExpirationDate,
        extendedBy,
        reason
      )

      // Update project with extended member
      const updatedMembers = project.members.map((m) =>
        m.userId === userId ? extendedMember : m
      )

      const updatedProject = Project.restore({
        id: project.id,
        name: project.name,
        description: project.description,
        clientId: project.clientId,
        ownerId: project.ownerId,
        status: project.status,
        budget: project.budget,
        members: updatedMembers,
        createdAt: project.createdAt,
        updatedAt: new Date().toISOString(),
        startDate: project.startDate,
        endDate: project.endDate,
        tags: project.tags,
        priority: project.priority,
        location: project.location,
        externalProjectId: project.externalProjectId,
      })

      // Generate recommendations
      if (extendedMember.isApproachingExpiration(30)) {
        recommendations.push(
          "Consider setting up automated renewal process for this role"
        )
      }

      nextActions.push("Notify team member of expiration extension")
      if (extensionMonths > 3) {
        nextActions.push("Schedule mid-term review of member performance")
      }

      await this.projectRepository.save(updatedProject)

      return {
        success: true,
        project: updatedProject,
        affectedMember: extendedMember,
        warnings,
        recommendations,
        nextActions,
      }
    } catch (error) {
      if (error instanceof MemberExpirationError) {
        throw error
      }

      throw new MemberExpirationError(
        `Failed to extend member expiration: ${error instanceof Error ? error.message : "Unknown error"}`,
        {
          userId,
          projectId: project.id,
          newExpirationDate,
          originalError: error instanceof Error ? error.message : String(error),
        }
      )
    }
  }

  /**
   * Processes bulk member expiration handling
   *
   * Handles multiple expiring members with automated extensions,
   * notifications, and business rule enforcement.
   *
   * @param project - Project to process
   * @param warningDays - Days before expiration to process (default: 30)
   * @param autoExtendDays - Days to auto-extend if allowed (default: 90)
   * @returns Promise<MemberExpirationSummary> - Processing results
   */
  async processBulkMemberExpiration(
    project: Project,
    warningDays: number = 30,
    autoExtendDays: number = 90
  ): Promise<MemberExpirationSummary> {
    const expirationSummary = this.checkMemberExpiration(
      project.members,
      warningDays
    )

    // Process expired members
    for (const expiredMember of expirationSummary.expiredMembers) {
      // Log expired member for administrative action
      console.warn(
        `Member ${expiredMember.userId} in project ${project.id} has expired`
      )
    }

    // Process members requiring extension
    for (const member of expirationSummary.membersRequiringExtension) {
      // Check if auto-extension is appropriate
      if (this.shouldAutoExtendMember(member, project)) {
        const newExpirationDate = new Date()
        newExpirationDate.setDate(newExpirationDate.getDate() + autoExtendDays)

        try {
          await this.extendMemberExpiration(
            project,
            member.userId,
            newExpirationDate.toISOString(),
            project.ownerId,
            "Automatic extension due to approaching expiration"
          )
        } catch (error) {
          console.error(`Failed to auto-extend member ${member.userId}:`, error)
        }
      }
    }

    return expirationSummary
  }

  // Private helper methods implementation

  private async validateRoleAssignment(
    project: Project,
    role: TeamRole,
    context: TeamAssignmentContext
  ): Promise<void> {
    // Check role-specific requirements
    const roleRequirements = this.getRoleRequirements(
      role.role,
      context.priority
    )

    // Validate certifications if required
    if (
      roleRequirements.requiredCertifications.length > 0 &&
      this.userService
    ) {
      const userInfo = await this.userService.getUserInfo(context.assignedBy)
      const missingCertifications =
        roleRequirements.requiredCertifications.filter(
          (cert) => !userInfo.certifications.includes(cert)
        )

      if (missingCertifications.length > 0) {
        throw new MemberAssignmentError(
          `Role '${role.role}' requires certifications: ${missingCertifications.join(", ")}`,
          {
            role: role.role,
            requiredCertifications: roleRequirements.requiredCertifications,
            missingCertifications,
          }
        )
      }
    }

    // Validate role capacity
    const currentRoleCount = project.getMembersByRole(role.role).length
    const maxRoleCount = this.getMaxRoleCount(role.role, context.priority)

    if (currentRoleCount >= maxRoleCount) {
      throw new MemberAssignmentError(
        `Project has reached maximum capacity for role '${role.role}' (${maxRoleCount})`,
        {
          role: role.role,
          currentCount: currentRoleCount,
          maxCount: maxRoleCount,
          priority: context.priority,
        }
      )
    }
  }

  private generateAssignmentRecommendations(
    _project: Project,
    member: ProjectMember,
    context: TeamAssignmentContext
  ): string[] {
    const recommendations: string[] = []

    // Role-specific recommendations
    if (member.role.role === "Electrical Engineer") {
      recommendations.push(
        "Ensure designer has access to latest CAD software and standards"
      )
      recommendations.push("Schedule orientation session with lead engineer")
    }

    if (member.role.role === "Project Manager") {
      recommendations.push(
        "Provide access to project management tools and dashboards"
      )
      recommendations.push(
        "Schedule handover meeting with previous manager if applicable"
      )
    }

    // Temporary assignment recommendations
    if (context.isTemporaryAssignment) {
      recommendations.push(
        "Document knowledge transfer requirements before assignment ends"
      )
      recommendations.push(
        "Plan for permanent replacement if role becomes permanent"
      )
    }

    // High workload recommendations
    if (context.estimatedWorkload && context.estimatedWorkload > 80) {
      recommendations.push("Monitor member workload to prevent burnout")
      recommendations.push("Consider additional support or resource allocation")
    }

    // Skill gap recommendations
    if (context.skillsRequired) {
      recommendations.push(
        "Verify member has necessary skills through assessment"
      )
      recommendations.push(
        "Provide additional training if skill gaps identified"
      )
    }

    return recommendations
  }

  private generateNextActions(
    _project: Project,
    member: ProjectMember,
    context: TeamAssignmentContext
  ): string[] {
    const nextActions: string[] = []

    // Standard onboarding actions
    nextActions.push("Send welcome email with project information")
    nextActions.push("Schedule project orientation session")
    nextActions.push("Provide access to project documentation and tools")

    // Role-specific actions
    if (
      member.role.role === "Administrator" ||
      member.role.role === "Project Manager"
    ) {
      nextActions.push("Grant administrative access to project systems")
      nextActions.push("Schedule stakeholder introduction meetings")
    }

    // Quality Assurance role checks would go here when that role is added to UserRoleEnum

    // Expiration-based actions
    if (member.expiresAt) {
      const daysUntilExpiration = member.getDaysUntilExpiration()
      if (daysUntilExpiration && daysUntilExpiration <= 90) {
        nextActions.push("Schedule extension review meeting")
      }
    }

    // Approval-required actions
    if (context.requiresApproval) {
      nextActions.push("Submit assignment for stakeholder approval")
      nextActions.push("Hold assignment pending approval confirmation")
    }

    return nextActions
  }

  private async validateAdvancedRolePermissions(
    project: Project,
    assigner: ProjectMember,
    targetRole: TeamRole,
    _targetUserId?: number
  ): Promise<void> {
    // Administrator role requires project owner approval
    if (
      targetRole.role === "Administrator" &&
      assigner.userId !== project.ownerId
    ) {
      throw new RoleManagementError(
        "Administrator role assignment requires project owner approval",
        {
          assignerId: assigner.userId,
          targetRole: targetRole.role,
          projectOwnerId: project.ownerId,
          requiresOwnerApproval: true,
        }
      )
    }

    // Cannot assign higher authority roles than current role
    const assignerHierarchy = this.getRoleHierarchyOrder(assigner.role.role)
    const targetHierarchy = this.getRoleHierarchyOrder(targetRole.role)

    if (targetHierarchy < assignerHierarchy && targetRole.role !== "Viewer") {
      throw new RoleManagementError(
        `Cannot assign role '${targetRole.role}' with higher authority than current role '${assigner.role.role}'`,
        {
          assignerId: assigner.userId,
          assignerRole: assigner.role.role,
          targetRole: targetRole.role,
          assignerHierarchy,
          targetHierarchy,
        }
      )
    }

    // Client can only be assigned by administrators
    if (
      targetRole.role === "Client" &&
      assigner.role.role !== "Administrator"
    ) {
      throw new RoleManagementError(
        "Client role can only be assigned by administrators",
        {
          assignerId: assigner.userId,
          assignerRole: assigner.role.role,
          targetRole: targetRole.role,
        }
      )
    }
  }

  private async validateRoleBusinessConstraints(
    project: Project,
    targetRole: TeamRole,
    _targetUserId?: number
  ): Promise<void> {
    // Supplier limits based on project priority
    if (targetRole.role === "Supplier") {
      const contractorCount = project.getMembersByRole(
        "Supplier"
      ).length
      const maxContractors = this.getMaxContractorCount(project.priority)

      if (contractorCount >= maxContractors) {
        throw new RoleManagementError(
          `Project has reached maximum Supplier limit (${maxContractors}) for ${project.priority} priority`,
          {
            role: targetRole.role,
            currentCount: contractorCount,
            maxCount: maxContractors,
            priority: project.priority,
          }
        )
      }
    }

    // Clients limited to one per project
    if (targetRole.role === "Client") {
      const existingClientReps = project.getMembersByRole(
        "Client"
      )
      if (existingClientReps.length > 0) {
        throw new RoleManagementError(
          "Only one Client allowed per project",
          {
            role: targetRole.role,
            existingRepCount: existingClientReps.length,
            clientId: project.clientId,
          }
        )
      }
    }

    // Project manager limit validation
    if (targetRole.role === "Project Manager") {
      const pmCount = project.getMembersByRole("Project Manager").length
      const maxPMs = this.getMaxRoleCount("Project Manager", project.priority)

      if (pmCount >= maxPMs) {
        throw new RoleManagementError(
          `Project has reached maximum project manager limit (${maxPMs}) for ${project.priority} priority`,
          {
            role: targetRole.role,
            currentCount: pmCount,
            maxCount: maxPMs,
            priority: project.priority,
          }
        )
      }
    }
  }

  private async analyzeRoleAvailability(
    project: Project,
    role: TeamRole,
    requester: ProjectMember | null
  ): Promise<RoleAvailability> {
    const currentCount = project.getMembersByRole(role.role).length
    const maxCount = this.getMaxRoleCount(role.role, project.priority)
    const isAvailable = currentCount < maxCount
    const restrictions: string[] = []
    let requiresApproval = false
    const skillRequirements = this.getRoleSkillRequirements(role.role)

    // Check permission restrictions
    if (requester && !requester.canAssignRole(role)) {
      restrictions.push(
        `Requires ${this.getMinimumRoleForAssignment(role.role)} role or higher to assign`
      )
    }

    // Check role-specific restrictions
    if (
      role.role === "Administrator" &&
      requester?.userId !== project.ownerId
    ) {
      restrictions.push("Requires project owner approval")
      requiresApproval = true
    }

    if (
      role.role === "Client" &&
      project.getMembersByRole("Client").length > 0
    ) {
      restrictions.push("Only one Client allowed per project")
    }

    if (role.role === "Supplier") {
      const contractorLimit = this.getMaxContractorCount(project.priority)
      if (currentCount >= contractorLimit) {
        restrictions.push(
          `Maximum ${contractorLimit} Suppliers for ${project.priority} priority`
        )
      }
    }

    return {
      role: role.role,
      isAvailable: isAvailable && restrictions.length === 0,
      currentCount,
      maxCount,
      restrictions,
      requiresApproval,
      skillRequirements,
    }
  }

  private getRoleHierarchyOrder(role: UserRoleEnum): number {
    const hierarchy: Record<UserRoleEnum, number> = {
      Administrator: 1,
      "Project Manager": 2,
      "Lead Engineer": 3,
      "Electrical Engineer": 4,
      "Automation Engineer": 5,
      "Instrumentation Engineer": 6,
      "Client": 7,
      "Supplier": 8,
      "Guest": 9,
      "Viewer": 10,
      "CAD Operator": 11,
    }
    return hierarchy[role] || 99
  }

  // Additional helper methods

  private validateExpirationDate(newDate: string, currentDate?: string): void {
    const newExpiration = new Date(newDate)
    const now = new Date()

    if (newExpiration <= now) {
      throw new MemberExpirationError(
        "New expiration date must be in the future",
        { newExpirationDate: newDate, currentDate: now.toISOString() }
      )
    }

    if (currentDate && newExpiration <= new Date(currentDate)) {
      throw new MemberExpirationError(
        "New expiration date must be later than current expiration date",
        { newExpirationDate: newDate, currentExpirationDate: currentDate }
      )
    }
  }

  private calculateExpirationExtensionMonths(
    currentDate: string | undefined,
    newDate: string
  ): number {
    if (!currentDate) return 0

    const current = new Date(currentDate)
    const extended = new Date(newDate)
    const diffTime = extended.getTime() - current.getTime()
    const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30))

    return diffMonths
  }

  private shouldAutoExtendMember(
    member: ProjectMember,
    _project: Project
  ): boolean {
    // Auto-extend only for active, non-admin members who are not expired
    return (
      member.isActive &&
      !member.hasExpired() &&
      member.role.role !== "Administrator" &&
      member.isApproachingExpiration(7)
    )
  }

  private getRoleRequirements(
    role: UserRoleEnum,
    priority: string
  ): {
    requiredCertifications: string[]
    minimumExperience: number
    skillsRequired: string[]
  } {
    const requirements = {
      Administrator: {
        requiredCertifications: ["PMP", "IEC 61508"],
        minimumExperience: 5,
        skillsRequired: ["project-management", "leadership"],
      },
      "Project Manager": {
        requiredCertifications: priority === "Critical" ? ["PMP"] : [],
        minimumExperience: 3,
        skillsRequired: ["project-management", "communication"],
      },
      "Lead Engineer": {
        requiredCertifications: ["IEC 61508", "IEEE Standards"],
        minimumExperience: 7,
        skillsRequired: ["electrical-design", "leadership", "technical-review"],
      },
      "Electrical Engineer": {
        requiredCertifications: ["CAD Certification"],
        minimumExperience: 2,
        skillsRequired: ["electrical-design", "cad-software"],
      },
    }

    return (
      requirements[role as keyof typeof requirements] || {
        requiredCertifications: [],
        minimumExperience: 0,
        skillsRequired: [],
      }
    )
  }

  private getMaxRoleCount(role: UserRoleEnum, priority: string): number {
    const limits: Record<string, Record<string, number>> = {
      Administrator: { Low: 2, Medium: 3, High: 4, Critical: 5 },
      "Project Manager": { Low: 1, Medium: 1, High: 2, Critical: 2 },
      "Lead Engineer": { Low: 1, Medium: 2, High: 3, Critical: 4 },
      "Senior Engineer": { Low: 2, Medium: 4, High: 6, Critical: 8 },
      Electrician: { Low: 3, Medium: 6, High: 10, Critical: 15 },
      "Electrical Engineer": { Low: 2, Medium: 4, High: 6, Critical: 10 },
      "Quality Assurance": { Low: 1, Medium: 2, High: 3, Critical: 4 },
      Consultant: { Low: 1, Medium: 2, High: 3, Critical: 5 },
      "Client": { Low: 1, Medium: 1, High: 1, Critical: 1 },
      Viewer: { Low: 5, Medium: 10, High: 15, Critical: 20 },
      "Supplier": { Low: 2, Medium: 4, High: 6, Critical: 10 },
    }

    return limits[role]?.[priority] || 99
  }

  private getMaxContractorCount(priority: string): number {
    const limits = { Low: 2, Medium: 4, High: 6, Critical: 10 }
    return limits[priority as keyof typeof limits] || 4
  }

  private getRoleSkillRequirements(role: UserRoleEnum): string[] {
    const skills: Record<UserRoleEnum, string[]> = {
      "Administrator": ["project-management", "leadership", "strategic-planning"],
      "Project Manager": [
        "project-management",
        "communication",
        "risk-management",
      ],
      "Lead Engineer": [
        "electrical-design",
        "leadership",
        "technical-review",
        "mentoring",
      ],
      "Electrical Engineer": [
        "cad-software",
        "electrical-design",
        "technical-drawing",
      ],
      "Automation Engineer": [
        "automation-systems",
        "plc-programming", 
        "scada-systems",
      ],
      "Instrumentation Engineer": [
        "instrumentation-design",
        "control-systems",
        "calibration",
      ],
      "CAD Operator": [
        "cad-software",
        "technical-drawing",
        "documentation",
      ],
      "Viewer": [],
      "Guest": [],
      "Client": [
        "communication",
        "stakeholder-management",
        "requirements-analysis",
      ],
      "Supplier": ["specialized-skills", "compliance", "reporting"],
    }

    return skills[role] || []
  }

  private getMinimumRoleForAssignment(role: UserRoleEnum): string {
    const minimumRoles: Record<UserRoleEnum, string> = {
      "Administrator": "Project Owner",
      "Project Manager": "Administrator",
      "Lead Engineer": "Administrator",
      "Electrical Engineer": "Lead Engineer",
      "Automation Engineer": "Lead Engineer",
      "Instrumentation Engineer": "Lead Engineer",
      "CAD Operator": "Lead Engineer",
      "Viewer": "Project Manager",
      "Guest": "Project Manager",
      "Client": "Administrator",
      "Supplier": "Administrator",
    }

    return minimumRoles[role] || "Administrator"
  }
}

/**
 * External UserService interface
 * Defines contract for external user management integration
 */
export interface UserService {
  getUserInfo(userId: number): Promise<ExternalUserInfo>
  validateUserExists(userId: number): Promise<boolean>
  getUserSkills(userId: number): Promise<string[]>
  getUserAvailability(
    userId: number
  ): Promise<"Available" | "Busy" | "Unavailable" | "On Leave">
  getUserProjectLoad(
    userId: number
  ): Promise<{ current: number; maximum: number }>
}
