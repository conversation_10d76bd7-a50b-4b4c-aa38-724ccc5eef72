/**
 * Entity ID Generation Utilities
 *
 * Provides utilities for generating unique entity identifiers.
 */

/**
 * Generates a unique identifier for entities
 */
export function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * Validates if a string is a valid entity ID format
 */
export function isValidEntityId(id: string): boolean {
  return typeof id === "string" && id.length > 0
}
