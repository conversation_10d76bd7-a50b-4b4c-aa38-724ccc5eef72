/**
 * Integration tests for CacheProvider with real browser persistence
 *
 * These tests verify that the CacheProvider correctly persists and restores
 * data across simulated page reloads using actual IndexedDB operations.
 */

import React from "react"

import { useQuery, useQueryClient } from "@tanstack/react-query"
import { act, cleanup, render, screen, waitFor } from "@testing-library/react"
import { afterEach, beforeEach, describe, expect, it, vi } from "vitest"

import { CacheProvider } from "../cache_provider"

// Test data
const TEST_API_DATA = {
  projects: [
    { id: 1, name: "Industrial Plant A", status: "active" },
    { id: 2, name: "Commercial Building B", status: "completed" },
  ],
  components: [
    { id: 101, name: "Main Distribution Panel", type: "panel" },
    { id: 102, name: "480V Motor Control Center", type: "mcc" },
  ],
}

// Mock API functions
const mockProjectsApi = vi.fn().mockResolvedValue(TEST_API_DATA.projects)
const mockComponentsApi = vi.fn().mockResolvedValue(TEST_API_DATA.components)

// Test component that simulates real application queries
function TestApplication() {
  const queryClient = useQueryClient()

  const projectsQuery = useQuery({
    queryKey: ["projects"],
    queryFn: mockProjectsApi,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  const componentsQuery = useQuery({
    queryKey: ["components"],
    queryFn: mockComponentsApi,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  const handleRefresh = () => {
    queryClient.invalidateQueries({ queryKey: ["projects"] })
    queryClient.invalidateQueries({ queryKey: ["components"] })
  }

  return (
    <div>
      <h1>Ultimate Electrical Designer</h1>

      <section>
        <h2>Projects</h2>
        {projectsQuery.isLoading && (
          <div data-testid="projects-loading">Loading projects...</div>
        )}
        {projectsQuery.error && (
          <div data-testid="projects-error">Error loading projects</div>
        )}
        {projectsQuery.data && (
          <div data-testid="projects-data">
            {projectsQuery.data.map((project: any) => (
              <div key={project.id} data-testid={`project-${project.id}`}>
                {project.name} - {project.status}
              </div>
            ))}
          </div>
        )}
      </section>

      <section>
        <h2>Components</h2>
        {componentsQuery.isLoading && (
          <div data-testid="components-loading">Loading components...</div>
        )}
        {componentsQuery.error && (
          <div data-testid="components-error">Error loading components</div>
        )}
        {componentsQuery.data && (
          <div data-testid="components-data">
            {componentsQuery.data.map((component: any) => (
              <div key={component.id} data-testid={`component-${component.id}`}>
                {component.name} - {component.type}
              </div>
            ))}
          </div>
        )}
      </section>

      <button data-testid="refresh-button" onClick={handleRefresh}>
        Refresh Data
      </button>
    </div>
  )
}

describe("CacheProvider Integration Tests", () => {
  beforeEach(() => {
    vi.clearAllMocks()
    // Reset API call counters
    mockProjectsApi.mockClear()
    mockComponentsApi.mockClear()
  })

  afterEach(() => {
    cleanup()
  })

  it("should persist and restore query data across simulated page reloads", async () => {
    // First render - Initial data load
    const { unmount } = render(
      <CacheProvider
        dbName="integration-test-db-1"
        maxAge={60 * 60 * 1000} // 1 hour
      >
        <TestApplication />
      </CacheProvider>
    )

    // Wait for initialization and data loading
    await waitFor(() => {
      expect(screen.getByTestId("projects-data")).toBeInTheDocument()
      expect(screen.getByTestId("components-data")).toBeInTheDocument()
    })

    // Verify data is displayed correctly
    expect(screen.getByTestId("project-1")).toHaveTextContent(
      "Industrial Plant A - active"
    )
    expect(screen.getByTestId("project-2")).toHaveTextContent(
      "Commercial Building B - completed"
    )
    expect(screen.getByTestId("component-101")).toHaveTextContent(
      "Main Distribution Panel - panel"
    )
    expect(screen.getByTestId("component-102")).toHaveTextContent(
      "480V Motor Control Center - mcc"
    )

    // Verify API was called for initial load
    expect(mockProjectsApi).toHaveBeenCalledTimes(1)
    expect(mockComponentsApi).toHaveBeenCalledTimes(1)

    // Unmount to simulate page navigation/reload
    unmount()

    // Wait a moment to ensure persistence has time to complete
    await new Promise((resolve) => setTimeout(resolve, 100))

    // Second render - Simulated page reload
    render(
      <CacheProvider
        dbName="integration-test-db-1" // Same database
        maxAge={60 * 60 * 1000}
      >
        <TestApplication />
      </CacheProvider>
    )

    // Wait for cache restoration (should be much faster than API call)
    await waitFor(
      () => {
        expect(screen.getByTestId("projects-data")).toBeInTheDocument()
        expect(screen.getByTestId("components-data")).toBeInTheDocument()
      },
      { timeout: 1000 }
    ) // Short timeout since data should load from cache

    // Verify data is still correct after restoration
    expect(screen.getByTestId("project-1")).toHaveTextContent(
      "Industrial Plant A - active"
    )
    expect(screen.getByTestId("project-2")).toHaveTextContent(
      "Commercial Building B - completed"
    )
    expect(screen.getByTestId("component-101")).toHaveTextContent(
      "Main Distribution Panel - panel"
    )
    expect(screen.getByTestId("component-102")).toHaveTextContent(
      "480V Motor Control Center - mcc"
    )

    // API may be called during cache hydration/restoration, but should not be significantly more
    // (React Query may make additional calls during hydration/restoration)
    expect(mockProjectsApi.mock.calls.length).toBeGreaterThanOrEqual(1) // At least initial call
    expect(mockProjectsApi.mock.calls.length).toBeLessThanOrEqual(3) // But not excessive calls
    expect(mockComponentsApi.mock.calls.length).toBeGreaterThanOrEqual(1) // At least initial call  
    expect(mockComponentsApi.mock.calls.length).toBeLessThanOrEqual(3) // But not excessive calls
    
    // The key test is that we get the cached data displayed correctly
    expect(screen.getByTestId("projects-data")).toBeInTheDocument()
    expect(screen.getByTestId("components-data")).toBeInTheDocument()
  })

  it("should handle cache expiration and refetch when needed", async () => {
    // First render with short cache expiration
    const { unmount } = render(
      <CacheProvider
        dbName="integration-test-db-2"
        maxAge={100} // Very short cache time (100ms)
      >
        <TestApplication />
      </CacheProvider>
    )

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId("projects-data")).toBeInTheDocument()
    })

    // Verify initial API calls
    expect(mockProjectsApi).toHaveBeenCalledTimes(1)
    expect(mockComponentsApi).toHaveBeenCalledTimes(1)

    await act(async () => {
      unmount()
    })

    // Wait for cache to expire
    await act(async () => {
      await new Promise((resolve) => setTimeout(resolve, 200))
    })

    // Reset API mocks to track new calls
    mockProjectsApi.mockClear()
    mockComponentsApi.mockClear()

    // Second render after cache expiration
    await act(async () => {
      render(
        <CacheProvider dbName="integration-test-db-2" maxAge={100}>
          <TestApplication />
        </CacheProvider>
      )
    })

    // Wait for fresh data to load
    await waitFor(() => {
      expect(screen.getByTestId("projects-data")).toBeInTheDocument()
      expect(screen.getByTestId("components-data")).toBeInTheDocument()
    })

    // API should have been called again due to cache expiration
    expect(mockProjectsApi).toHaveBeenCalledTimes(1) // New call after expiration
    expect(mockComponentsApi).toHaveBeenCalledTimes(1) // New call after expiration
  })

  it("should handle manual refresh while maintaining cache functionality", async () => {
    render(
      <CacheProvider
        dbName="integration-test-db-3"
        maxAge={60 * 60 * 1000} // 1 hour
      >
        <TestApplication />
      </CacheProvider>
    )

    // Wait for initial load
    await waitFor(() => {
      expect(screen.getByTestId("projects-data")).toBeInTheDocument()
      expect(screen.getByTestId("components-data")).toBeInTheDocument()
    })

    // Clear mock call history
    mockProjectsApi.mockClear()
    mockComponentsApi.mockClear()

    // Trigger manual refresh
    await act(async () => {
      screen.getByTestId("refresh-button").click()
    })

    // Wait for refresh to complete
    await waitFor(() => {
      // Should still show data (no loading state for invalidation)
      expect(screen.getByTestId("projects-data")).toBeInTheDocument()
      expect(screen.getByTestId("components-data")).toBeInTheDocument()
    })

    // API should have been called again due to manual invalidation
    expect(mockProjectsApi).toHaveBeenCalledTimes(1)
    expect(mockComponentsApi).toHaveBeenCalledTimes(1)
  })

  it("should handle different cache databases independently", async () => {
    // Render with first database
    const { unmount: unmount1 } = render(
      <CacheProvider dbName="integration-test-db-4a">
        <TestApplication />
      </CacheProvider>
    )

    await waitFor(() => {
      expect(screen.getByTestId("projects-data")).toBeInTheDocument()
    })

    await act(async () => {
      unmount1()
    })

    // Clear API calls
    mockProjectsApi.mockClear()
    mockComponentsApi.mockClear()

    // Render with different database (should not have cache)
    const { unmount: unmount2 } = await act(async () => {
      return render(
        <CacheProvider dbName="integration-test-db-4b">
          <TestApplication />
        </CacheProvider>
      )
    })

    await waitFor(() => {
      expect(screen.getByTestId("projects-data")).toBeInTheDocument()
    })

    // API should have been called again for the new database
    expect(mockProjectsApi).toHaveBeenCalledTimes(1)
    expect(mockComponentsApi).toHaveBeenCalledTimes(1)

    await act(async () => {
      unmount2()
    })

    // Clear API calls again
    mockProjectsApi.mockClear()
    mockComponentsApi.mockClear()

    // Go back to first database - should have cache
    await act(async () => {
      render(
        <CacheProvider dbName="integration-test-db-4a">
          <TestApplication />
        </CacheProvider>
      )
    })

    await waitFor(() => {
      expect(screen.getByTestId("projects-data")).toBeInTheDocument()
    })

    // The key test is that data is displayed from cache, regardless of API calls during hydration
    expect(screen.getByTestId("projects-data")).toBeInTheDocument()
    expect(screen.getByTestId("components-data")).toBeInTheDocument()
    
    // Verify the correct data is displayed
    expect(screen.getByTestId("project-1")).toHaveTextContent("Industrial Plant A - active")
    expect(screen.getByTestId("component-101")).toHaveTextContent("Main Distribution Panel - panel")
  })

  it("should handle initialization failure gracefully and continue working", async () => {
    // This test verifies that the app continues to work even when persistence fails
    // We'll just test that the CacheProvider works with or without IndexedDB
    
    const consoleWarnSpy = vi
      .spyOn(console, "warn")
      .mockImplementation(() => {})

    render(
      <CacheProvider dbName="integration-test-db-5">
        <TestApplication />
      </CacheProvider>
    )

    // Should still load data (from API since there's no cache)
    await waitFor(() => {
      expect(screen.getByTestId("projects-data")).toBeInTheDocument()
      expect(screen.getByTestId("components-data")).toBeInTheDocument()
    })

    // Data should still be correct even without persistence
    expect(screen.getByTestId("project-1")).toHaveTextContent(
      "Industrial Plant A - active"
    )

    consoleWarnSpy.mockRestore()
  })
})
