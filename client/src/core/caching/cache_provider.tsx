"use client"

/**
 * CacheProvider - React Query integration with IndexedDBPersister
 *
 * This provider integrates the IndexedDBPersister with React Query to enable
 * client-side persistence of the query cache across page reloads and browser sessions.
 *
 * Features:
 * - IndexedDB-based query cache persistence
 * - Automatic cache restoration on application load
 * - Configurable cache expiration (24 hours default)
 * - Error-resilient with graceful degradation
 * - TypeScript strict mode compliance
 */
import React, { ReactNode, useEffect, useState } from "react"

import { QueryClient, QueryClientProvider } from "@tanstack/react-query"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import { persistQueryClient } from "@tanstack/react-query-persist-client"

import {
  createIndexedDBPersister,
  isIndexedDBSupported,
} from "./indexed_db_persister"

interface CacheProviderProps {
  children: ReactNode
  /**
   * Maximum age of cache in milliseconds
   * Default: 24 hours (24 * 60 * 60 * 1000)
   */
  maxAge?: number
  /**
   * Custom database name for cache persistence
   * Default: 'ultimate-electrical-designer-cache'
   */
  dbName?: string
  /**
   * Enable React Query Devtools in development
   * Default: true in development, false in production
   */
  enableDevtools?: boolean
}

/**
 * CacheProvider component that integrates React Query with IndexedDB persistence
 *
 * @param children - Child components that will have access to the query client
 * @param maxAge - Maximum age of cached data in milliseconds (default: 24 hours)
 * @param dbName - Custom database name for cache storage
 * @param enableDevtools - Whether to show React Query devtools
 */
export function CacheProvider({
  children,
  maxAge = 24 * 60 * 60 * 1000, // 24 hours
  dbName = "ultimate-electrical-designer-cache",
  enableDevtools = process.env.NODE_ENV === "development",
}: CacheProviderProps) {
  const [queryClient] = useState(() => {
    // Create QueryClient with optimized configuration
    const client = new QueryClient({
      defaultOptions: {
        queries: {
          // Stale time: 5 minutes - queries are fresh for 5 minutes
          staleTime: 5 * 60 * 1000,
          // Cache time: 10 minutes - data stays in memory for 10 minutes
          gcTime: 10 * 60 * 1000,
          // Retry failed requests 3 times with exponential backoff
          retry: 3,
          retryDelay: (attemptIndex) =>
            Math.min(1000 * 2 ** attemptIndex, 30000),
          // Refetch on window focus in production
          refetchOnWindowFocus: process.env.NODE_ENV === "production",
          // Don't refetch on reconnect by default
          refetchOnReconnect: false,
          // Network mode for offline support
          networkMode: "online",
        },
        mutations: {
          // Retry failed mutations once
          retry: 1,
          // Retry delay for mutations: 1 second
          retryDelay: 1000,
          // Network mode for mutations
          networkMode: "online",
        },
      },
      
    })

    return client
  })

  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    let isMounted = true

    async function initializePersistence() {
      try {
        // Check if IndexedDB is supported
        if (!isIndexedDBSupported()) {
          console.warn("IndexedDB not supported - running without persistence")
          if (isMounted) {
            setIsInitialized(true)
          }
          return
        }

        // Create IndexedDB persister with custom configuration
        const persister = createIndexedDBPersister({
          dbName,
          dbVersion: 1,
          storeName: "query-cache",
          cacheKey: "react-query-cache",
        })

        // Set up persistence with React Query
        await persistQueryClient({
          queryClient,
          persister,
          maxAge,
          // Buster for cache invalidation on version changes
          buster: process.env.NEXT_PUBLIC_APP_VERSION || "1.0.0",
          // Dehydrate options for serialization
          dehydrateOptions: {
            shouldDehydrateQuery: (query) => {
              // Only persist successful queries and those with data
              return (
                query.state.status === "success" &&
                query.state.data !== undefined
              )
            },
            shouldDehydrateMutation: (mutation) => {
              // Don't persist mutations by default for security
              return false
            },
          },
          // Hydrate options for restoration
          hydrateOptions: {
            // Deserialize dates properly
            deserializeData: (data: any) => {
              if (data && typeof data === "object") {
                // Handle ISO date string restoration
                return JSON.parse(JSON.stringify(data), (key, value) => {
                  if (
                    typeof value === "string" &&
                    /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z$/.test(
                      value
                    )
                  ) {
                    return new Date(value)
                  }
                  return value
                })
              }
              return data
            },
          },
        })

        console.log(`Cache persistence initialized with IndexedDB (${dbName})`)
      } catch (error) {
        console.error("Failed to initialize cache persistence:", error)
        // Continue without persistence - graceful degradation
      } finally {
        if (isMounted) {
          setIsInitialized(true)
        }
      }
    }

    initializePersistence()

    return () => {
      isMounted = false
    }
  }, [queryClient, maxAge, dbName])

  // Don't render children until persistence is initialized to prevent flash
  if (!isInitialized) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-blue-600"></div>
      </div>
    )
  }

  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {enableDevtools && (
        <ReactQueryDevtools
          initialIsOpen={false}
          buttonPosition="bottom-right"
        />
      )}
    </QueryClientProvider>
  )
}

/**
 * Hook to access the query client from within the CacheProvider context
 *
 * @returns The QueryClient instance
 * @throws Error if used outside of CacheProvider
 */
export function useQueryClient() {
  const queryClient = React.useContext(QueryClientProvider as any)

  if (!queryClient) {
    throw new Error("useQueryClient must be used within a CacheProvider")
  }

  return queryClient
}

/**
 * Cache management utilities
 */
export const CacheManager = {
  /**
   * Clear all cached queries
   */
  clearCache: async (queryClient: QueryClient) => {
    await queryClient.clear()
    console.log("Query cache cleared")
  },

  /**
   * Invalidate all queries to refetch fresh data
   */
  invalidateAll: async (queryClient: QueryClient) => {
    await queryClient.invalidateQueries()
    console.log("All queries invalidated")
  },

  /**
   * Remove stale queries from cache
   */
  removeStale: async (queryClient: QueryClient) => {
    queryClient.removeQueries({
      stale: true,
    })
    console.log("Stale queries removed")
  },

  /**
   * Get cache statistics
   */
  getCacheStats: (queryClient: QueryClient) => {
    const cache = queryClient.getQueryCache()
    const queries = cache.getAll()

    return {
      totalQueries: queries.length,
      successfulQueries: queries.filter((q) => q.state.status === "success")
        .length,
      errorQueries: queries.filter((q) => q.state.status === "error").length,
      loadingQueries: queries.filter((q) => q.state.status === "pending")
        .length,
      staleQueries: queries.filter((q) => q.isStale()).length,
    }
  },
}
