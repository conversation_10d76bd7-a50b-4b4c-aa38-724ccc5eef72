# Client-Side Caching Mechanism Design

## 1. Overview

This document outlines the design for a lightweight, volatile client-side caching mechanism. The primary goal is to provide a seamless user experience when a client is temporarily disconnected from the shared local PostgreSQL server's network. This is not a replacement for the primary data store but a transient cache to allow for continued work.

This system will integrate with the existing `@tanstack/react-query` library to cache server state and will use `IndexedDB` for persistent storage in the browser.

## 2. Technology Choice: IndexedDB

**IndexedDB** is chosen for the following reasons:

- **Asynchronous API**: It does not block the main UI thread.
- **Transactional**: Ensures data integrity for all operations.
- **Large Storage Capacity**: Offers significantly more storage than `localStorage`.
- **Structured Data**: Well-suited for storing and querying JSON-like objects, which aligns with the application's data structures.

To simplify the usage of IndexedDB, a lightweight wrapper library like `idb` by <PERSON> is recommended.

## 3. Architecture and Integration with React Query

The caching layer will function as a custom persister for React Query. This approach allows us to leverage React Query's powerful caching, invalidation, and background-syncing capabilities.

### Core Components

1. **`IndexedDBPersister`**: A custom class that implements the `Persister` interface from `@tanstack/react-query-persist-client`. It will handle the logic for reading from and writing to IndexedDB.
2. **`CacheProvider`**: A React context provider that wraps the application. It will initialize the `QueryClient` with the `IndexedDBPersister`.
3. **`useOfflineMutation`**: A custom hook that wraps React Query's `useMutation`. When offline, it will store mutations in an "outbox" in IndexedDB instead of sending them to the server.
4. **`SyncManager`**: A service that runs in the background. It will monitor network status. When the application comes back online, it will process the mutation outbox, sending the stored mutations to the server in the correct order.

### Diagram

```mermaid
graph TD
    subgraph Browser
        subgraph React Application
            Component -->|uses| useQuery
            Component -->|uses| useOfflineMutation
        end

        subgraph React Query
            useQuery -- Reads/Writes --> QueryCache
            useOfflineMutation -- Writes --> MutationOutbox
        end

        subgraph Caching Layer
            QueryCache -- Persists --> IndexedDBPersister
            MutationOutbox -- Persists --> IndexedDBPersister
            IndexedDBPersister -- Interacts with --> IndexedDB
        end
    end

    subgraph Network
        ServerAPI(Backend API)
    end

    SyncManager -- Monitors --> NetworkStatus
    SyncManager -- Reads/Clears --> MutationOutbox
    SyncManager -- Sends Mutations --> ServerAPI

    NetworkStatus -- Online/Offline --> ReactQueryClient
```

## 4. Data Flow

### Caching Queries

- When a `useQuery` hook is called, React Query will first check its in-memory cache.
- If not found, it will check the `IndexedDBPersister`.
- If found in IndexedDB, the data is restored to the in-memory cache and returned to the component.
- If not found in IndexedDB (or if stale), and the network is online, a request is made to the server. The response is then cached in-memory and persisted to IndexedDB.
- If offline, the stale data from the cache is used.

### Handling Mutations (Offline)

- A user performs an action that triggers a mutation (e.g., creating or updating a component).
- The `useOfflineMutation` hook is used. It detects that the application is offline.
- Instead of making a network request, it optimistically updates the local React Query cache to provide immediate UI feedback.
- The mutation (including the function name and its payload) is serialized and stored in an 'outbox' table in IndexedDB.

## 5. Synchronization Strategy

- The `SyncManager` periodically checks the network status using `navigator.onLine`.
- When the status changes from offline to online, the `SyncManager` starts processing the 'outbox' from IndexedDB.
- It will read the mutations in the order they were stored (FIFO).
- For each mutation, it will execute the network request.
- Upon successful execution, the mutation is removed from the outbox. React Query's cache will be automatically updated with the fresh data from the server response, and any relevant queries will be invalidated to refetch.
- If a mutation fails, it will be kept in the outbox for a retry attempt (with an exponential backoff strategy). A user notification system will be implemented to inform the user of persistent sync failures.

## 6. Implementation Details

- **`idb` library**: To be added as a dependency: `pnpm add idb`.
- **`@tanstack/react-query-persist-client`**: To be added as a dependency: `pnpm add @tanstack/react-query-persist-client`.
- **IndexedDB Schema**:
  - `query-cache`: A key-value store for React Query's cache.
  - `mutation-outbox`: An object store for pending mutations, with an auto-incrementing key to maintain order.
- **Zustand for Network State**: A simple Zustand store will be used to globally manage and react to the application's online/offline status.

This design provides a robust and scalable solution for client-side caching that is well-integrated with the existing frontend stack.
