// src/test/reporters/domain-reporter.ts

import type { <PERSON>, Reporter, RunnerTaskResultPack, Vitest } from "vitest/node"

import { writeFileSync } from "node:fs"
import { relative } from "node:path"
import { stdout } from "node:process"
import {
  bgRed,
  black,
  bold,
  cyan,
  dim,
  gray,
  green,
  magenta,
  red,
  yellow,
} from "picocolors"

const domainRegex =
  /src\/(app|components|core|hooks|lib|modules|projects|stores|styles|types|utils)\//

interface DomainReport {
  total: number
  passed: number
  failed: number
  skipped: number
  // Use a Map to store both passed and failed counts per file
  testCountsPerSuite: Map<string, { passed: number; failed: number }>
}

interface DomainReporterOptions {
  outputFile?: string
}

function stripAnsiColors(str: string): string {
  // eslint-disable-next-line no-control-regex
  return str.replace(/\x1b\[[0-9;]*m/g, "")
}

export default class DomainReporter implements Reporter {
  private ctx!: Vitest
  private startTime = 0
  private domains = new Map<string, DomainReport>()
  private currentTestFiles = new Map<string, File>()
  private outputFile: string | undefined

  constructor(options: DomainReporterOptions = {}) {
    this.outputFile = options.outputFile
  }

  onInit(ctx: Vitest) {
    this.ctx = ctx
    this.startTime = Date.now()
    this.domains.clear()
  }

  private getDomainName(filepath: string): string {
    const match = filepath.match(domainRegex)
    return match ? match[1] : "other"
  }

  onCollected(files?: File[]) {
    if (!files) return
    this.currentTestFiles.clear()
    for (const file of files) {
      this.currentTestFiles.set(file.id, file)
    }
  }

  onTaskUpdate(packs: RunnerTaskResultPack[]) {
    for (const [taskId, result] of packs) {
      const file = this.currentTestFiles.get(taskId)
      if (!file) continue

      const domainName = this.getDomainName(file.filepath)

      if (!this.domains.has(domainName)) {
        this.domains.set(domainName, {
          total: 0,
          passed: 0,
          failed: 0,
          skipped: 0,
          testCountsPerSuite: new Map(), // Initialize the new Map
        })
      }

      const report = this.domains.get(domainName)!
      report.total++

      // Get current counts or initialize them
      const currentCounts = report.testCountsPerSuite.get(file.filepath) || {
        passed: 0,
        failed: 0,
      }

      switch (result.state) {
        case "pass":
          report.passed++
          currentCounts.passed++
          break
        case "fail":
          report.failed++
          currentCounts.failed++
          break
        case "skip":
        case "todo":
          report.skipped++
          break
      }
      report.testCountsPerSuite.set(file.filepath, currentCounts)
    }
  }

  onFinished(_files?: File[]) {
    const endTime = Date.now()
    const duration = endTime - this.startTime

    let reportOutput = ""

    reportOutput +=
      "\n" + bold(cyan(`\n--- Test Report by Domain ---\n`)) + "\n"

    for (const [domainName, report] of this.domains.entries()) {
      const allPassed = report.passed === report.total
      const allFailed = report.failed > 0 && report.passed === 0

      let domainStatus = ""
      if (allPassed) {
        domainStatus = bold(green("✔ All Passed"))
      } else if (allFailed) {
        domainStatus = bgRed(black(bold(" FAILED ")))
      } else if (report.failed > 0) {
        domainStatus = bold(yellow("⚠ Partial Failure"))
      } else {
        domainStatus = bold(gray("⟳ Skipped"))
      }

      const domainHeader = bold(
        magenta(`\n${domainName.toUpperCase()} DOMAIN:`)
      )
      reportOutput += `${domainHeader} ${domainStatus}\n`
      reportOutput += gray(`  - Total Tests: ${report.total}\n`)
      reportOutput += green(`  - Passed: ${report.passed}\n`)
      reportOutput += red(`  - Failed: ${report.failed}\n`)
      reportOutput += dim(`  - Skipped: ${report.skipped}\n`)

      if (report.failed > 0) {
        reportOutput += yellow(
          bold(`  --- Failed Suites in ${domainName.toUpperCase()} ---\n`)
        )
        // Iterate over the Map and print both the failed and passed counts
        for (const [filepath, counts] of report.testCountsPerSuite.entries()) {
          if (counts.failed > 0) {
            const relativePath = relative(process.cwd(), filepath)
            const s = counts.failed > 1 ? "s" : ""
            const passedInfo =
              counts.passed > 0 ? `, ${counts.passed} passed` : ""
            reportOutput += `  - ${red(relativePath)} (${counts.failed} failed test${s}${passedInfo})\n`
          }
        }
      }
    }

    reportOutput += bold(cyan(`\n--- Report Finished in ${duration}ms ---\n`))
    stdout.write(reportOutput)

    if (this.outputFile) {
      const plainTextReport = stripAnsiColors(reportOutput)
      writeFileSync(this.outputFile, plainTextReport, "utf-8")
      stdout.write(
        green(`\nReport successfully written to ${this.outputFile}\n`)
      )
    }
  }
}
